// Test BetterPlace bedroom fix - should not extract 3741 bedrooms
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testBetterPlaceBedroomFix() {
  console.log('🔍 Testing BetterPlace Bedroom Fix');
  console.log('='.repeat(50));
  
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02163';
  
  console.log(`📡 Testing URL: ${testUrl}`);
  console.log('');
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('betterplace', [testUrl], {});
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);
    
    console.log(`⏱️ Duration: ${duration}s`);
    
    if (results && results.processedResults && results.processedResults.length > 0) {
      const result = results.processedResults[0];
      if (result && result.ok && result.data) {
        const prop = result.data;
        
        console.log('\n📊 Extracted Data:');
        console.log(`Title: "${prop.title}"`);
        console.log(`Bedrooms: ${prop.bedrooms} ${prop.bedrooms === 3741 ? '❌ STILL BROKEN!' : '✅ FIXED!'}`);
        console.log(`Bathrooms: ${prop.bathrooms}`);
        console.log(`Building Size: ${prop.size_sqft} sqft`);
        console.log(`Lot Size: ${prop.lot_size_sqft} sqft`);
        console.log(`Year Built: ${prop.year_built}`);
        console.log(`Ownership: ${prop.ownership_type}`);
        console.log(`Price: ${prop.price || prop.rent_price}`);
        console.log(`Location: ${prop.city}, ${prop.state}`);
        
        if (prop.description) {
          console.log(`\nDescription: ${prop.description.substring(0, 100)}...`);
        }
        
        // Validation
        if (prop.bedrooms === 3741) {
          console.log('\n❌ BEDROOM FIX FAILED - Still extracting 3741 bedrooms!');
        } else if (prop.bedrooms && prop.bedrooms >= 1 && prop.bedrooms <= 15) {
          console.log(`\n✅ BEDROOM FIX SUCCESS - Reasonable bedroom count: ${prop.bedrooms}`);
        } else if (prop.bedrooms === null || prop.bedrooms === undefined) {
          console.log('\n⚠️ BEDROOM FIX PARTIAL - No bedrooms extracted (better than wrong number)');
        } else {
          console.log(`\n❓ BEDROOM FIX UNCLEAR - Unexpected bedroom count: ${prop.bedrooms}`);
        }
        
      } else {
        console.log('❌ No valid data extracted');
        console.log('Result:', JSON.stringify(result, null, 2));
      }
    } else {
      console.log('❌ No results returned');
      console.log('Results:', JSON.stringify(results, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testBetterPlaceBedroomFix().then(() => process.exit(0));
