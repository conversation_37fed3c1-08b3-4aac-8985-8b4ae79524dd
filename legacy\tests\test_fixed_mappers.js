// Test the fixed mappers to ensure data is properly mapped
require('dotenv').config();

async function testFixedMappers() {
  console.log('🔧 Testing Fixed Mappers');
  console.log('='.repeat(40));
  
  try {
    const { mapBetterPlace, mapBaliVillaRealty, mapBaliHomeImmo } = require('./scrape_worker/mappers');
    
    // Test BetterPlace
    console.log('\n📡 Testing BetterPlace mapper...');
    const betterPlaceResult = await mapBetterPlace({
      markdown: `
# Modern 3 Bedroom Villa with Rooftop in Tumbak Bayuh

**Price:** USD 450,000

**Location:** Tumbak Bayuh, Canggu, Bali

**Property Details:**
- 3 bedrooms
- 2 bathrooms  
- Building size: 180 sqm
- Land size: 250 sqm
- Year built: 2022
- Parking: 2 cars
- Ownership: Freehold

**Description:**
This stunning modern villa features contemporary design with high-quality finishes throughout.

**Amenities:**
- Swimming pool
- Garden
- Air conditioning
- Fully furnished
`,
      url: 'https://betterplace.cc/buy/properties/BPVL02270'
    });
    
    console.log('✅ BetterPlace Result:');
    console.log(`   Title: ${betterPlaceResult?.title || 'MISSING'}`);
    console.log(`   Price: ${betterPlaceResult?.price || 'MISSING'}`);
    console.log(`   Bedrooms: ${betterPlaceResult?.bedrooms || 'MISSING'}`);
    console.log(`   Bathrooms: ${betterPlaceResult?.bathrooms || 'MISSING'}`);
    console.log(`   Address: ${betterPlaceResult?.address || 'MISSING'}`);
    console.log(`   City: ${betterPlaceResult?.city || 'MISSING'}`);
    console.log(`   Size (sqft): ${betterPlaceResult?.size_sqft || 'MISSING'}`);
    console.log(`   Lot Size (sqft): ${betterPlaceResult?.lot_size_sqft || 'MISSING'}`);
    console.log(`   Year Built: ${betterPlaceResult?.year_built || 'MISSING'}`);
    console.log(`   Parking: ${betterPlaceResult?.parking_spaces || 'MISSING'}`);
    console.log(`   External ID: ${betterPlaceResult?.media?.external_id || 'MISSING'}`);
    
    // Test Bali Villa Realty
    console.log('\n📡 Testing Bali Villa Realty mapper...');
    const baliVillaResult = await mapBaliVillaRealty({
      markdown: `
# Brand New 2 Bedroom Villa For Sale in Tumbak Bayuh

**Price:** USD 169,000

**Location:** Tumbak Bayuh, Canggu, Bali

**Property Details:**
- 2 bedrooms
- 2 bathrooms  
- Building size: 120 sqm
- Land size: 150 sqm
- Year built: 2023
- Parking: 1 car
- Ownership: Leasehold

**Description:**
Charming villa in peaceful location near Canggu beaches.
`,
      url: 'https://balivillarealty.com/property/test-villa/'
    });
    
    console.log('✅ Bali Villa Realty Result:');
    console.log(`   Title: ${baliVillaResult?.title || 'MISSING'}`);
    console.log(`   Rent Price: ${baliVillaResult?.rent_price || 'MISSING'}`);
    console.log(`   Bedrooms: ${baliVillaResult?.bedrooms || 'MISSING'}`);
    console.log(`   Bathrooms: ${baliVillaResult?.bathrooms || 'MISSING'}`);
    console.log(`   Address: ${baliVillaResult?.address || 'MISSING'}`);
    console.log(`   City: ${baliVillaResult?.city || 'MISSING'}`);
    console.log(`   Size (sqft): ${baliVillaResult?.size_sqft || 'MISSING'}`);
    console.log(`   Lot Size (sqft): ${baliVillaResult?.lot_size_sqft || 'MISSING'}`);
    console.log(`   Year Built: ${baliVillaResult?.year_built || 'MISSING'}`);
    console.log(`   Parking: ${baliVillaResult?.parking_spaces || 'MISSING'}`);
    console.log(`   External ID: ${baliVillaResult?.media?.external_id || 'MISSING'}`);
    
    // Test Bali Home Immo
    console.log('\n📡 Testing Bali Home Immo mapper...');
    const baliHomeResult = await mapBaliHomeImmo({
      markdown: `
# 2 Bedroom Townhouse For Rent in Berawa

**Price:** IDR 25,000,000 / month

**Location:** Berawa, Canggu, Bali

**Property Details:**
- 2 bedrooms
- 2 bathrooms  
- Building size: 100 sqm
- Land size: 80 sqm
- Year built: 2021
- Parking: 1 car
- Ownership: Leasehold

**Description:**
Modern townhouse perfect for monthly rental.
`,
      url: 'https://bali-home-immo.com/property/test-townhouse/'
    });
    
    console.log('✅ Bali Home Immo Result:');
    console.log(`   Title: ${baliHomeResult?.title || 'MISSING'}`);
    console.log(`   Rent Price: ${baliHomeResult?.rent_price || 'MISSING'}`);
    console.log(`   Bedrooms: ${baliHomeResult?.bedrooms || 'MISSING'}`);
    console.log(`   Bathrooms: ${baliHomeResult?.bathrooms || 'MISSING'}`);
    console.log(`   Address: ${baliHomeResult?.address || 'MISSING'}`);
    console.log(`   City: ${baliHomeResult?.city || 'MISSING'}`);
    console.log(`   Size (sqft): ${baliHomeResult?.size_sqft || 'MISSING'}`);
    console.log(`   Lot Size (sqft): ${baliHomeResult?.lot_size_sqft || 'MISSING'}`);
    console.log(`   Year Built: ${baliHomeResult?.year_built || 'MISSING'}`);
    console.log(`   Parking: ${baliHomeResult?.parking_spaces || 'MISSING'}`);
    console.log(`   External ID: ${baliHomeResult?.media?.external_id || 'MISSING'}`);
    
    console.log('\n🎉 Mapper Testing Complete!');
    console.log('✅ All mappers should now properly extract data from markdown');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

testFixedMappers().catch(console.error);
