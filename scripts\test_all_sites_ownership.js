// Test ownership fields for all 4 websites - 5 URLs each
require('dotenv').config();
const { db, scrapingQueue, properties } = require('../drizzle_client');
const { eq, desc, inArray } = require('drizzle-orm');
const { runExtractBatch } = require('../scrape_worker/run_batch');

async function testAllSitesOwnership() {
  console.log('🚀 Testing Ownership Fields for All 4 Websites (5 URLs each)\n');
  
  const websites = [
    { id: 'villabalisale.com', name: 'Villa Bali Sale', mapper: 'Villa Bali Sale' },
    { id: 'betterplace.co.id', name: 'BetterPlace', mapper: 'BetterPlace' },
    { id: 'balihomeimmo.com', name: 'Bali Home Immo', mapper: 'Bali Home Immo' },
    { id: 'balivillarealty.com', name: 'Bali Villa Realty', mapper: 'Bali Villa Realty' }
  ];
  
  const results = {};
  
  try {
    for (const website of websites) {
      console.log(`\n${'='.repeat(80)}`);
      console.log(`🌐 Testing ${website.name} (${website.id})`);
      console.log(`${'='.repeat(80)}`);
      
      // Get 5 URLs from queue for this website
      const queueItems = await db
        .select()
        .from(scrapingQueue)
        .where(eq(scrapingQueue.website_id, website.id))
        .limit(5);
      
      if (queueItems.length === 0) {
        console.log(`❌ No URLs found in queue for ${website.name}`);
        results[website.id] = { success: 0, failed: 0, urls: [] };
        continue;
      }
      
      console.log(`📊 Found ${queueItems.length} URLs to test:`);
      queueItems.forEach((item, i) => {
        console.log(`   ${i + 1}. ${item.url.substring(0, 70)}...`);
      });
      
      const urls = queueItems.map(item => item.url);
      
      console.log(`\n🔄 Processing ${urls.length} URLs with ${website.mapper} mapper...`);
      
      // Process URLs with appropriate mapper
      const batchResults = await runExtractBatch(website.id, urls, {});
      
      let successCount = 0;
      let failCount = 0;
      const processedUrls = [];
      
      console.log(`\n📊 Results for ${website.name}:`);
      console.log('-'.repeat(60));
      
      batchResults.forEach((result, i) => {
        const url = urls[i];
        const shortUrl = url.substring(0, 50) + '...';
        
        if (result.success) {
          successCount++;
          const property = result.property;
          
          console.log(`\n✅ SUCCESS ${i + 1}/${batchResults.length}:`);
          console.log(`   🔗 URL: ${shortUrl}`);
          console.log(`   🏠 Title: ${property.title}`);
          console.log(`   💰 Price: ${property.price ? 'IDR ' + property.price.toLocaleString() : 'N/A'}`);
          console.log(`   📍 Location: ${property.city}, ${property.country}`);
          console.log(`   🛏️  Specs: ${property.bedrooms || 'N/A'} bed, ${property.bathrooms || 'N/A'} bath`);
          console.log(`   📐 Size: ${property.size_sqft ? Math.round(property.size_sqft) + ' sqft' : 'N/A'}`);
          console.log(`   🌍 Land: ${property.lot_size_sqft ? Math.round(property.lot_size_sqft) + ' sqft' : 'N/A'}`);
          console.log(`   🚗 Parking: ${property.parking_spaces || 'N/A'}`);
          console.log(`   📜 Ownership: ${property.ownership_type || 'N/A'}`);
          console.log(`   ⏰ Lease: ${property.lease_duration_years ? property.lease_duration_years + ' years' : 'N/A'}`);
          console.log(`   🆔 Property ID: ${property.media?.external_id || 'N/A'}`);
          console.log(`   📸 Images: ${property.media?.image_count || 0}`);
          console.log(`   🎯 Amenities: ${property.amenities?.raw_amenities?.length || 0}`);
          
          processedUrls.push({
            url: shortUrl,
            title: property.title,
            ownership: property.ownership_type,
            lease_years: property.lease_duration_years,
            success: true
          });
          
        } else {
          failCount++;
          console.log(`\n❌ FAILED ${i + 1}/${batchResults.length}:`);
          console.log(`   🔗 URL: ${shortUrl}`);
          console.log(`   ❌ Error: ${result.error || 'Unknown error'}`);
          
          processedUrls.push({
            url: shortUrl,
            success: false,
            error: result.error
          });
        }
      });
      
      results[website.id] = {
        name: website.name,
        success: successCount,
        failed: failCount,
        total: batchResults.length,
        successRate: Math.round((successCount / batchResults.length) * 100),
        urls: processedUrls
      };
      
      console.log(`\n📈 ${website.name} Summary:`);
      console.log(`   ✅ Successful: ${successCount}/${batchResults.length}`);
      console.log(`   ❌ Failed: ${failCount}/${batchResults.length}`);
      console.log(`   📊 Success Rate: ${Math.round((successCount / batchResults.length) * 100)}%`);
    }
    
    // Overall summary
    console.log(`\n${'='.repeat(80)}`);
    console.log('🏆 OVERALL TEST RESULTS');
    console.log(`${'='.repeat(80)}`);
    
    let totalSuccess = 0;
    let totalFailed = 0;
    let totalProcessed = 0;
    
    Object.values(results).forEach(result => {
      if (result.success !== undefined) {
        totalSuccess += result.success;
        totalFailed += result.failed;
        totalProcessed += result.total;
        
        console.log(`\n🌐 ${result.name}:`);
        console.log(`   📊 ${result.success}/${result.total} successful (${result.successRate}%)`);
        
        // Show ownership type distribution
        const ownershipTypes = {};
        result.urls.filter(u => u.success && u.ownership).forEach(u => {
          ownershipTypes[u.ownership] = (ownershipTypes[u.ownership] || 0) + 1;
        });
        
        if (Object.keys(ownershipTypes).length > 0) {
          console.log(`   📜 Ownership types found:`);
          Object.entries(ownershipTypes).forEach(([type, count]) => {
            console.log(`      - ${type}: ${count} properties`);
          });
        }
      }
    });
    
    const overallSuccessRate = totalProcessed > 0 ? Math.round((totalSuccess / totalProcessed) * 100) : 0;
    
    console.log(`\n🎯 FINAL SUMMARY:`);
    console.log(`   ✅ Total Successful: ${totalSuccess}/${totalProcessed}`);
    console.log(`   ❌ Total Failed: ${totalFailed}/${totalProcessed}`);
    console.log(`   📊 Overall Success Rate: ${overallSuccessRate}%`);
    
    if (overallSuccessRate >= 80) {
      console.log('\n🏆 EXCELLENT! Ownership fields are working very well across all sites!');
    } else if (overallSuccessRate >= 60) {
      console.log('\n✅ GOOD! Ownership fields are working well with some minor issues.');
    } else {
      console.log('\n⚠️  NEEDS IMPROVEMENT! Several ownership extraction issues detected.');
    }
    
    // Check database for newly created properties
    console.log(`\n🔍 Checking database for new properties...`);
    
    const recentProperties = await db
      .select({
        title: properties.title,
        ownership_type: properties.ownership_type,
        lease_duration_years: properties.lease_duration_years,
        source_id: properties.source_id,
        created_at: properties.created_at
      })
      .from(properties)
      .where(inArray(properties.source_id, ['villa_bali_sale', 'betterplace', 'bali_home_immo', 'bali_villa_realty']))
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`\n📋 Latest 10 properties in database:`);
    recentProperties.forEach((prop, i) => {
      console.log(`   ${i + 1}. ${prop.source_id}: ${prop.title.substring(0, 40)}...`);
      console.log(`      📜 ${prop.ownership_type || 'No ownership'} ${prop.lease_duration_years ? '(' + prop.lease_duration_years + ' years)' : ''}`);
      console.log(`      📅 ${new Date(prop.created_at).toLocaleString()}`);
    });
    
    console.log('\n🎉 Ownership field testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

testAllSitesOwnership();
