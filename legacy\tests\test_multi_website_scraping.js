// Test scraping 2 URLs from each website using queue manager
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { db, properties, scrapingQueue } = require('./drizzle_client');
const { desc, sql, eq, and } = require('drizzle-orm');

async function testMultiWebsiteScraping() {
  console.log('🧪 Multi-Website Scraping Test: 2 URLs per Website');
  console.log('='.repeat(70));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  // Define websites to test
  const websites = [
    { id: 'betterplace', name: 'BetterPlace' },
    { id: 'bali_home_immo.com', name: 'Bali Home Immo' },
    { id: 'bali-villa-realty.com', name: 'Bali Villa Realty' },
    { id: 'villabalisale.com', name: 'Villa Bali Sale' }
  ];
  
  try {
    // Get initial count
    const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`📊 Initial properties in database: ${initialCount[0].count}`);
    
    const results = {};
    let totalProcessed = 0;
    let totalSuccessful = 0;
    let totalFailed = 0;
    
    // Process each website
    for (const website of websites) {
      console.log(`\n${'='.repeat(50)}`);
      console.log(`🌐 Processing ${website.name} (${website.id})`);
      console.log(`${'='.repeat(50)}`);
      
      try {
        // Get 2 URLs from this website
        const queueUrls = await db
          .select({ 
            url: scrapingQueue.url, 
            id: scrapingQueue.id,
            attempts: scrapingQueue.attempts
          })
          .from(scrapingQueue)
          .where(and(
            eq(scrapingQueue.website_id, website.id),
            eq(scrapingQueue.status, 'pending')
          ))
          .limit(2);
        
        if (queueUrls.length === 0) {
          console.log(`⚠️  No pending URLs found for ${website.name}`);
          results[website.id] = {
            name: website.name,
            processed: 0,
            successful: 0,
            failed: 0,
            urls: [],
            error: 'No pending URLs'
          };
          continue;
        }
        
        console.log(`📋 Found ${queueUrls.length} URLs for ${website.name}:`);
        queueUrls.forEach((row, i) => {
          console.log(`   ${i + 1}. ${row.url.substring(0, 80)}...`);
          console.log(`      Queue ID: ${row.id}, Attempts: ${row.attempts}`);
        });
        
        // Extract URLs for processing
        const urls = queueUrls.map(row => row.url);
        
        console.log(`\n🔄 Processing ${urls.length} URLs for ${website.name}...`);
        const startTime = Date.now();
        
        // Use the existing batch processing function
        const batchResults = await runExtractBatch(website.id, urls, {
          concurrency: 1, // Process one at a time for better monitoring
          timeout: 120000  // 2 minute timeout per URL
        });
        
        const duration = ((Date.now() - startTime) / 1000).toFixed(1);
        
        if (batchResults && batchResults.length > 0) {
          const successful = batchResults.filter(r => r.success).length;
          const failed = batchResults.filter(r => !r.success).length;
          
          console.log(`\n✅ ${website.name} completed in ${duration}s:`);
          console.log(`   Processed: ${batchResults.length}`);
          console.log(`   Successful: ${successful}`);
          console.log(`   Failed: ${failed}`);
          
          // Store results
          results[website.id] = {
            name: website.name,
            processed: batchResults.length,
            successful: successful,
            failed: failed,
            duration: duration,
            urls: urls,
            results: batchResults
          };
          
          totalProcessed += batchResults.length;
          totalSuccessful += successful;
          totalFailed += failed;
          
          // Show successful results
          const successfulResults = batchResults.filter(r => r.success);
          if (successfulResults.length > 0) {
            console.log(`\n🏠 Successfully processed properties:`);
            successfulResults.forEach((result, i) => {
              console.log(`\n   ${i + 1}. ✅ ${result.title || 'Property'}`);
              if (result.price) {
                console.log(`      💰 IDR ${result.price.toLocaleString()}`);
              }
              if (result.bedrooms) {
                console.log(`      🏠 ${result.bedrooms} bed | 🚿 ${result.bathrooms} bath`);
              }
              if (result.city) {
                console.log(`      📍 ${result.city}, ${result.state || 'No state'}`);
              }
              if (result.ownership_type) {
                console.log(`      🏛️  ${result.ownership_type}`);
              }
              
              // Check description (NEW!)
              if (result.description && result.description.length > 0) {
                console.log(`      ✅ Description: ${result.description.length} chars`);
                console.log(`      📝 Preview: ${result.description.substring(0, 80)}...`);
              } else {
                console.log(`      ❌ No description`);
              }
              
              // Check amenities
              if (result.amenities && result.amenities.raw_amenities && result.amenities.raw_amenities.length > 0) {
                console.log(`      🎯 Amenities (${result.amenities.raw_amenities.length}): ${result.amenities.raw_amenities.slice(0, 3).join(', ')}${result.amenities.raw_amenities.length > 3 ? '...' : ''}`);
              } else {
                console.log(`      ⚠️  No amenities`);
              }
            });
          }
          
          // Show failed results
          const failedResults = batchResults.filter(r => !r.success);
          if (failedResults.length > 0) {
            console.log(`\n❌ Failed properties:`);
            failedResults.forEach((result, i) => {
              console.log(`   ${i + 1}. ❌ ${result.error || 'Unknown error'}`);
            });
          }
          
        } else {
          console.log(`⚠️  No results returned for ${website.name} (${duration}s)`);
          results[website.id] = {
            name: website.name,
            processed: 0,
            successful: 0,
            failed: 0,
            duration: duration,
            urls: urls,
            error: 'No results returned'
          };
        }
        
      } catch (error) {
        console.error(`❌ Error processing ${website.name}:`, error.message);
        results[website.id] = {
          name: website.name,
          processed: 0,
          successful: 0,
          failed: 0,
          urls: [],
          error: error.message
        };
      }
    }
    
    // Final summary
    console.log(`\n${'='.repeat(70)}`);
    console.log('📊 FINAL SUMMARY');
    console.log(`${'='.repeat(70)}`);
    
    // Check final count
    const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
    const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
    console.log(`\n🆕 New properties added to database: ${newProperties}`);
    
    // Summary by website
    console.log('\n🌐 Results by Website:');
    Object.entries(results).forEach(([websiteId, result]) => {
      console.log(`\n   ${result.name}:`);
      console.log(`      Processed: ${result.processed}`);
      console.log(`      Successful: ${result.successful}`);
      console.log(`      Failed: ${result.failed}`);
      if (result.duration) {
        console.log(`      Duration: ${result.duration}s`);
      }
      if (result.error) {
        console.log(`      Error: ${result.error}`);
      }
    });
    
    // Overall stats
    console.log(`\n📈 Overall Statistics:`);
    console.log(`   Total URLs processed: ${totalProcessed}`);
    console.log(`   Total successful: ${totalSuccessful}`);
    console.log(`   Total failed: ${totalFailed}`);
    console.log(`   Success rate: ${totalProcessed > 0 ? ((totalSuccessful / totalProcessed) * 100).toFixed(1) : 0}%`);
    console.log(`   Properties added to DB: ${newProperties}`);
    
    // Show latest properties with description check
    if (newProperties > 0) {
      console.log('\n🏠 Latest Properties Added (with Description Check):');
      const latestProps = await db
        .select()
        .from(properties)
        .orderBy(desc(properties.created_at))
        .limit(newProperties);
      
      latestProps.forEach((prop, i) => {
        console.log(`\n   ${i + 1}. [${prop.source_id}] ${prop.title}`);
        if (prop.price) {
          console.log(`      💰 IDR ${prop.price.toLocaleString()}`);
        }
        console.log(`      🏠 ${prop.bedrooms} bed | 📍 ${prop.city}, ${prop.state || 'No state'}`);
        
        // Check description in database
        if (prop.description && prop.description.length > 0) {
          console.log(`      ✅ Description in DB: ${prop.description.length} chars`);
          console.log(`      📝 DB Preview: ${prop.description.substring(0, 80)}...`);
        } else {
          console.log(`      ❌ No description in database`);
        }
        
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`      ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        }
        
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
      });
    }
    
    console.log('\n✅ Multi-website scraping test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testMultiWebsiteScraping();
