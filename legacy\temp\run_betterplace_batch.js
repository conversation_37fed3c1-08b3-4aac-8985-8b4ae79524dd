// Run a small batch test for BetterPlace with markdown-only scraping
require('dotenv').config();
const { db, websiteConfigs, properties } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function runBetterPlaceBatch() {
  console.log('🚀 Running BetterPlace Markdown-Only Batch Test');
  console.log('='.repeat(60));
  
  try {
    // Get BetterPlace configuration
    console.log('🔄 Step 1: Getting BetterPlace configuration');
    const config = await db
      .select()
      .from(websiteConfigs)
      .where(eq(websiteConfigs.website_id, 'betterplace'))
      .limit(1);
    
    if (config.length === 0) {
      console.log('❌ BetterPlace configuration not found');
      return;
    }
    
    console.log('✅ Configuration loaded');
    console.log(`   Name: ${config[0].name}`);
    console.log(`   Base URL: ${config[0].base_url}`);
    console.log(`   Crawl Options: ${JSON.stringify(config[0].crawl_options, null, 2)}`);
    console.log(`   Is Active: ${config[0].is_active}`);
    
    // Check if it's using markdown format
    if (!config[0].crawl_options?.formats?.includes('markdown')) {
      console.log('⚠️  Configuration is not set to markdown format');
      console.log('   Current formats:', config[0].crawl_options?.formats);
      return;
    }
    
    console.log('✅ Configuration is set to markdown-only format');
    
    // Run a small batch using the existing batch runner
    console.log('\n🔄 Step 2: Running batch scraping');
    console.log('   This will use the existing batch runner with markdown format');
    
    // Import and run the batch runner for BetterPlace only
    const { runExtractBatch } = require('./scrape_worker/run_batch');

    // Test URLs for BetterPlace
    const testUrls = [
      'https://betterplace.cc/buy/properties/BPVL02270',
      'https://betterplace.cc/buy/properties/BPVL02232'
    ];

    // Run batch for BetterPlace with test URLs
    console.log(`   Processing ${testUrls.length} test URLs...`);
    const batchResult = await runExtractBatch('betterplace', testUrls, {});
    
    console.log('\n📊 Batch Results:');
    console.log(`   Total processed: ${batchResult?.length || 0}`);
    const successful = batchResult?.filter(r => r && r.title) || [];
    const failed = (batchResult?.length || 0) - successful.length;
    console.log(`   Successful: ${successful.length}`);
    console.log(`   Failed: ${failed}`);

    if (successful.length > 0) {
      console.log('   Successfully processed properties:');
      successful.forEach((prop, index) => {
        console.log(`     ${index + 1}. ${prop.title} - ${prop.price} (${prop.bedrooms}bed/${prop.bathrooms}bath)`);
      });
    }
    
    // Verify results in database
    console.log('\n🔄 Step 3: Verifying database results');
    const { desc } = require('drizzle-orm');
    const recentProperties = await db
      .select({
        id: properties.id,
        title: properties.title,
        price: properties.price,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        media: properties.media,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(5);
    
    console.log(`📋 Recent properties (${recentProperties.length}):`);
    recentProperties.forEach((prop, index) => {
      if (prop.media?.source_id === 'betterplace') {
        console.log(`   ${index + 1}. ✅ ${prop.title}`);
        console.log(`      Price: ${prop.price}`);
        console.log(`      Beds/Baths: ${prop.bedrooms}/${prop.bathrooms}`);
        console.log(`      Source: ${prop.media?.source_id}`);
        console.log(`      Created: ${prop.created_at}`);
      }
    });
    
    console.log('\n🎉 BetterPlace markdown-only batch test completed!');
    console.log('💰 Cost savings: Using markdown instead of JSON (5x cheaper)');
    
  } catch (error) {
    console.error('❌ Batch test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the batch test
runBetterPlaceBatch().catch(console.error);
