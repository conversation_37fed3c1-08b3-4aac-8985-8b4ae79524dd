// Debug mapper field extraction for specific failing URLs
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugMapperExtraction() {
  console.log('🔍 Debug Mapper Field Extraction');
  console.log('='.repeat(50));
  
  // Test URLs that recently failed
  const testCases = [
    {
      website: 'betterplace',
      url: 'https://betterplace.cc/buy/properties/BPVL02209',
      expectedBedrooms: 2, // From title: "2-Bedroom Villa Loft"
      expectedBathrooms: 2
    },
    {
      website: 'bali_villa_realty', 
      url: 'https://balivillarealty.com/property/cozy-1-bedroom-villa-in-prime-petitenget-location-exceptional-investment-opportunity/',
      expectedBedrooms: 1, // From title: "1-Bedroom Villa"
      expectedBathrooms: 1
    },
    {
      website: 'bali_home_immo',
      url: 'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/1-bedroom-apartment-for-yearly-rental-in-berawa-bhi1022',
      expectedBedrooms: 1, // From title: "1 bedroom apartment"
      expectedBathrooms: 1
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n🧪 Testing ${testCase.website}`);
    console.log(`📍 URL: ${testCase.url}`);
    console.log(`🎯 Expected: ${testCase.expectedBedrooms} bed / ${testCase.expectedBathrooms} bath`);
    console.log('─'.repeat(80));
    
    try {
      // Enable detailed logging by modifying the mapper temporarily
      console.log('🔄 Running extraction with detailed logging...');
      
      const results = await runExtractBatch(testCase.website, [testCase.url], {
        debug: true,
        verbose: true
      });

      // Handle new return structure
      let extractedData = null;
      if (results && results.extractedData && results.extractedData.length > 0) {
        extractedData = results.extractedData[0];
      } else if (results && results.processedResults && results.processedResults.length > 0) {
        // Try to get data from processedResults
        const processedResult = results.processedResults[0];
        if (processedResult.data) {
          extractedData = processedResult.data;
        }
      } else if (results && Array.isArray(results) && results.length > 0) {
        // Fallback for old format
        extractedData = results[0];
      }

      if (extractedData) {
        console.log('\n📊 EXTRACTION RESULTS:');
        console.log(`   Title: "${extractedData.title}"`);
        console.log(`   Bedrooms: ${extractedData.bedrooms} (expected: ${testCase.expectedBedrooms})`);
        console.log(`   Bathrooms: ${extractedData.bathrooms} (expected: ${testCase.expectedBathrooms})`);
        console.log(`   Size: ${extractedData.size_sqft} sqft`);
        console.log(`   Year: ${extractedData.year_built}`);
        console.log(`   Price: ${extractedData.price || extractedData.rent_price}`);
        console.log(`   Ownership: ${extractedData.ownership_type}`);

        // Check if extraction matches expectations
        const bedroomMatch = extractedData.bedrooms === testCase.expectedBedrooms;
        const bathroomMatch = extractedData.bathrooms === testCase.expectedBathrooms;

        console.log('\n🎯 ACCURACY CHECK:');
        console.log(`   Bedrooms: ${bedroomMatch ? '✅' : '❌'} ${extractedData.bedrooms} vs ${testCase.expectedBedrooms}`);
        console.log(`   Bathrooms: ${bathroomMatch ? '✅' : '❌'} ${extractedData.bathrooms} vs ${testCase.expectedBathrooms}`);

        if (!bedroomMatch || !bathroomMatch) {
          console.log('\n🚨 FIELD EXTRACTION FAILED!');
          console.log('   This confirms the mapper regex patterns need fixing');
        } else {
          console.log('\n✅ FIELD EXTRACTION SUCCESSFUL!');
        }

        // Show description for manual verification
        if (extractedData.description) {
          console.log(`\n📝 Description: "${extractedData.description.substring(0, 200)}..."`);
        }

      } else {
        console.log('❌ No extracted data found in results');
        console.log('Results structure:', JSON.stringify(results, null, 2));
      }
      
    } catch (error) {
      console.log(`❌ Extraction failed: ${error.message}`);
    }
    
    console.log('\n' + '='.repeat(80));
  }
  
  console.log('\n🔧 DIAGNOSIS:');
  console.log('If bedrooms/bathrooms are NULL despite being in the title,');
  console.log('the issue is in the mapper regex patterns in scrape_worker/mappers.js');
  console.log('\nNext steps:');
  console.log('1. Check regex patterns for bedroom/bathroom extraction');
  console.log('2. Test patterns against actual markdown content');
  console.log('3. Fix patterns to handle title-based extraction');
  console.log('4. Add fallback extraction from titles when content fails');
}

// Run debug
if (require.main === module) {
  debugMapperExtraction().catch(console.error);
}

module.exports = { debugMapperExtraction };
