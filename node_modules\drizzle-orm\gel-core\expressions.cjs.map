{"version": 3, "sources": ["../../src/gel-core/expressions.ts"], "sourcesContent": ["import type { GelColumn } from '~/gel-core/columns/index.ts';\nimport { bindIfParam } from '~/sql/expressions/index.ts';\nimport type { Placeholder, SQL, SQLChunk, SQLWrapper } from '~/sql/sql.ts';\nimport { sql } from '~/sql/sql.ts';\n\nexport * from '~/sql/expressions/index.ts';\n\nexport function concat(column: GelColumn | SQL.Aliased, value: string | Placeholder | SQLWrapper): SQL {\n\treturn sql`${column} || ${bindIfParam(value, column)}`;\n}\n\nexport function substring(\n\tcolumn: GelColumn | SQL.Aliased,\n\t{ from, for: _for }: { from?: number | Placeholder | SQLWrapper; for?: number | Placeholder | SQLWrapper },\n): SQL {\n\tconst chunks: SQLChunk[] = [sql`substring(`, column];\n\tif (from !== undefined) {\n\t\tchunks.push(sql` from `, bindIfParam(from, column));\n\t}\n\tif (_for !== undefined) {\n\t\tchunks.push(sql` for `, bindIfParam(_for, column));\n\t}\n\tchunks.push(sql`)`);\n\treturn sql.join(chunks);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,yBAA4B;AAE5B,iBAAoB;AAEpB,gCAAc,uCALd;AAOO,SAAS,OAAO,QAAiC,OAA+C;AACtG,SAAO,iBAAM,MAAM,WAAO,gCAAY,OAAO,MAAM,CAAC;AACrD;AAEO,SAAS,UACf,QACA,EAAE,MAAM,KAAK,KAAK,GACZ;AACN,QAAM,SAAqB,CAAC,4BAAiB,MAAM;AACnD,MAAI,SAAS,QAAW;AACvB,WAAO,KAAK,4BAAa,gCAAY,MAAM,MAAM,CAAC;AAAA,EACnD;AACA,MAAI,SAAS,QAAW;AACvB,WAAO,KAAK,2BAAY,gCAAY,MAAM,MAAM,CAAC;AAAA,EAClD;AACA,SAAO,KAAK,iBAAM;AAClB,SAAO,eAAI,KAAK,MAAM;AACvB;", "names": []}