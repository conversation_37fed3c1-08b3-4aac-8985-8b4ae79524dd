const { db, properties, discoveredUrls } = require('./drizzle_client');
const { eq, desc, isNull } = require('drizzle-orm');

async function debugPropertyIssues() {
  try {
    console.log('🔍 DEBUGGING PROPERTY ISSUES...\n');
    
    // 1. Check specific property
    console.log('1. CHECKING SPECIFIC PROPERTY: 08d10b8f-8000-46c9-9e1a-7f4f729ea6e5');
    const specificProperty = await db
      .select()
      .from(properties)
      .where(eq(properties.id, '08d10b8f-8000-46c9-9e1a-7f4f729ea6e5'));
    
    if (specificProperty.length > 0) {
      console.log('Property found:');
      console.log('- Title:', specificProperty[0].title);
      console.log('- Type:', specificProperty[0].property_type);
      console.log('- Description length:', specificProperty[0].description?.length || 0);
      console.log('- Description preview:', specificProperty[0].description?.substring(0, 100) + '...');
      console.log('- Raw amenities:', specificProperty[0].raw_amenities);
      console.log('- Source URL ID:', specificProperty[0].source_url_id);
      console.log('- Created:', specificProperty[0].created_at);
    } else {
      console.log('❌ Property not found!');
    }
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    // 2. Check LAND properties with amenities
    console.log('2. CHECKING LAND PROPERTIES WITH AMENITIES:');
    const landWithAmenities = await db
      .select()
      .from(properties)
      .where(eq(properties.property_type, 'land'))
      .orderBy(desc(properties.created_at))
      .limit(5);

    landWithAmenities.forEach((prop, index) => {
      console.log(`${index + 1}. ${prop.title}`);
      console.log(`   - Type: ${prop.property_type}`);
      console.log(`   - Amenities: ${prop.raw_amenities}`);
      console.log(`   - Source URL ID: ${prop.source_url_id || 'NULL'}`);
      console.log('');
    });
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    // 3. Check properties with NULL source_url_id
    console.log('3. CHECKING PROPERTIES WITH NULL SOURCE_URL_ID:');
    const nullSourceUrls = await db
      .select()
      .from(properties)
      .where(isNull(properties.source_url_id))
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`Found ${nullSourceUrls.length} properties with NULL source_url_id:`);
    nullSourceUrls.forEach((prop, index) => {
      console.log(`${index + 1}. ${prop.title} (${prop.property_type}) - ${prop.created_at}`);
    });
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    // 4. Check recent discovered_urls
    console.log('4. CHECKING RECENT DISCOVERED_URLS:');
    const recentUrls = await db
      .select()
      .from(discoveredUrls)
      .orderBy(desc(discoveredUrls.created_at))
      .limit(5);
    
    recentUrls.forEach((url, index) => {
      console.log(`${index + 1}. ID: ${url.id}`);
      console.log(`   - URL: ${url.url}`);
      console.log(`   - Status: ${url.scrape_status}`);
      console.log(`   - Created: ${url.created_at}`);
      console.log('');
    });
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    // 5. Check properties without descriptions
    console.log('5. CHECKING PROPERTIES WITHOUT DESCRIPTIONS:');
    const noDescription = await db
      .select()
      .from(properties)
      .where(isNull(properties.description))
      .orderBy(desc(properties.created_at))
      .limit(5);
    
    console.log(`Found ${noDescription.length} properties without descriptions:`);
    noDescription.forEach((prop, index) => {
      console.log(`${index + 1}. ${prop.title} (${prop.property_type})`);
      console.log(`   - Source URL ID: ${prop.source_url_id || 'NULL'}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

debugPropertyIssues();
