// Test 2 URLs per website for quick validation
require('dotenv').config();
const { db, scrapingQueue, properties, closeConnection } = require('../drizzle_client');
const { eq, desc } = require('drizzle-orm');
const { runExtractBatch } = require('../scrape_worker/run_batch');

async function test2UrlsPerWebsite() {
  console.log('🚀 Testing 2 URLs per Website for Quick Validation\n');
  
  const websites = [
    { id: 'betterplace', name: 'BetterPlace' },
    { id: 'bali_home_immo', name: 'Bali Home Immo' },
    { id: 'bali_villa_realty', name: 'Bali Villa Realty' },
    { id: 'villabalisale.com', name: 'Villa Bali Sale' },
    { id: 'bali_coconut_living', name: 'Bali Coconut Living' }
  ];
  
  const results = {};
  let totalSuccess = 0;
  let totalFailed = 0;
  
  try {
    for (const website of websites) {
      console.log(`\n${'='.repeat(60)}`);
      console.log(`🌐 Testing ${website.name} (${website.id})`);
      console.log(`${'='.repeat(60)}`);
      
      // Get 2 URLs from queue for this website
      const queueItems = await db
        .select()
        .from(scrapingQueue)
        .where(eq(scrapingQueue.website_id, website.id))
        .limit(2);
      
      if (queueItems.length === 0) {
        console.log(`❌ No URLs found in queue for ${website.name}`);
        results[website.id] = { 
          name: website.name,
          success: 0, 
          failed: 0, 
          total: 0,
          error: 'No URLs in queue'
        };
        continue;
      }
      
      console.log(`📊 Found ${queueItems.length} URLs to test:`);
      queueItems.forEach((item, i) => {
        console.log(`   ${i + 1}. ${item.url.substring(0, 70)}...`);
      });
      
      const urls = queueItems.map(item => item.url);
      
      console.log(`\n🔄 Processing ${urls.length} URLs...`);
      
      try {
        // Process URLs
        const batchResult = await runExtractBatch(website.id, urls, {});
        
        // Extract results
        const extractedData = batchResult.extractedData || [];
        const processedResults = batchResult.processedResults || [];
        
        let successCount = 0;
        let failCount = 0;
        
        console.log(`\n📊 Results for ${website.name}:`);
        console.log('-'.repeat(40));
        
        extractedData.forEach((result, i) => {
          const url = urls[i];
          const shortUrl = url.substring(0, 50) + '...';
          
          if (result) {
            successCount++;
            console.log(`\n✅ SUCCESS ${i + 1}/${extractedData.length}:`);
            console.log(`   🔗 URL: ${shortUrl}`);
            console.log(`   🏠 Title: ${result.title || 'No title'}`);
            console.log(`   💰 Price: ${result.price ? 'IDR ' + result.price.toLocaleString() : (result.rent_price ? 'IDR ' + result.rent_price.toLocaleString() + '/month' : 'No price')}`);
            console.log(`   📍 Location: ${result.city || 'N/A'}, ${result.country || 'N/A'}`);
            console.log(`   🛏️  Bedrooms: ${result.bedrooms || 'N/A'}`);
            console.log(`   🚿 Bathrooms: ${result.bathrooms || 'N/A'}`);
            console.log(`   📐 Size: ${result.size_sqft ? Math.round(result.size_sqft) + ' sqft' : 'N/A'}`);
            console.log(`   📜 Ownership: ${result.ownership_type || 'N/A'}`);
            console.log(`   🆔 External ID: ${result.external_id || result.media?.external_id || 'N/A'}`);
          } else {
            failCount++;
            console.log(`\n❌ FAILED ${i + 1}/${extractedData.length}:`);
            console.log(`   🔗 URL: ${shortUrl}`);
            console.log(`   ❌ Error: No data extracted`);
          }
        });
        
        totalSuccess += successCount;
        totalFailed += failCount;
        
        results[website.id] = {
          name: website.name,
          success: successCount,
          failed: failCount,
          total: extractedData.length,
          successRate: Math.round((successCount / extractedData.length) * 100)
        };
        
        console.log(`\n📈 ${website.name} Summary:`);
        console.log(`   ✅ Successful: ${successCount}/${extractedData.length}`);
        console.log(`   ❌ Failed: ${failCount}/${extractedData.length}`);
        console.log(`   📊 Success Rate: ${Math.round((successCount / extractedData.length) * 100)}%`);
        
      } catch (error) {
        console.error(`❌ ${website.name} processing failed:`, error.message);
        results[website.id] = {
          name: website.name,
          success: 0,
          failed: 2,
          total: 2,
          error: error.message
        };
        totalFailed += 2;
      }
      
      // Small delay between websites
      if (website !== websites[websites.length - 1]) {
        console.log('\n⏳ Waiting 30 seconds before next website...');
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }
    
    // Overall summary
    console.log(`\n${'='.repeat(60)}`);
    console.log('🏆 OVERALL TEST RESULTS');
    console.log(`${'='.repeat(60)}`);
    
    const totalProcessed = totalSuccess + totalFailed;
    const overallSuccessRate = totalProcessed > 0 ? Math.round((totalSuccess / totalProcessed) * 100) : 0;
    
    console.log(`\n🎯 FINAL SUMMARY:`);
    console.log(`   ✅ Total Successful: ${totalSuccess}/${totalProcessed}`);
    console.log(`   ❌ Total Failed: ${totalFailed}/${totalProcessed}`);
    console.log(`   📊 Overall Success Rate: ${overallSuccessRate}%`);
    
    // Detailed results
    console.log(`\n📊 DETAILED RESULTS BY WEBSITE:`);
    Object.values(results).forEach(result => {
      if (result.total > 0) {
        console.log(`\n🌐 ${result.name}:`);
        console.log(`   📊 Success: ${result.success}/${result.total} (${result.successRate}%)`);
        if (result.error) {
          console.log(`   ❌ Error: ${result.error}`);
        }
      }
    });
    
    // Check database for newly created properties
    console.log(`\n🔍 Checking database for new properties...`);
    
    const recentProperties = await db
      .select({
        title: properties.title,
        source_id: properties.source_id,
        price: properties.price,
        rent_price: properties.rent_price,
        ownership_type: properties.ownership_type,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`\n📋 Latest 10 properties in database:`);
    recentProperties.forEach((prop, i) => {
      const priceStr = prop.price ? `IDR ${prop.price.toLocaleString()}` : (prop.rent_price ? `IDR ${prop.rent_price.toLocaleString()}/mo` : 'No price');
      console.log(`   ${i + 1}. ${prop.source_id}: ${prop.title?.substring(0, 40)}...`);
      console.log(`      💰 ${priceStr} | 📜 ${prop.ownership_type || 'No ownership'}`);
      console.log(`      📅 ${new Date(prop.created_at).toLocaleString()}`);
    });
    
    // Performance evaluation
    if (overallSuccessRate >= 80) {
      console.log('\n🏆 EXCELLENT! Most websites are working very well!');
    } else if (overallSuccessRate >= 60) {
      console.log('\n✅ GOOD! Websites are working with some minor issues.');
    } else if (overallSuccessRate >= 40) {
      console.log('\n⚠️  MODERATE! Some websites have issues that need attention.');
    } else {
      console.log('\n❌ POOR! Multiple websites have significant issues.');
    }
    
    console.log('\n🎉 Quick validation test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test
test2UrlsPerWebsite()
  .then(() => {
    console.log('✅ Quick validation test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
