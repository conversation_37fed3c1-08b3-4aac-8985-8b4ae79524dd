// Update property enums to match full Prisma schema
require('dotenv').config();
const { db } = require('./drizzle_client');

async function updatePropertyEnums() {
  console.log('🔧 Updating property enums to match full Prisma schema...\n');

  try {
    // Add missing values to EPropertyCategory enum
    console.log('📊 Updating EPropertyCategory enum...');
    await db.execute(`
      ALTER TYPE "EPropertyCategory" ADD VALUE IF NOT EXISTS 'INDUSTRIAL';
    `);
    await db.execute(`
      ALTER TYPE "EPropertyCategory" ADD VALUE IF NOT EXISTS 'OTHER';
    `);
    console.log('✅ EPropertyCategory updated: RESIDENTIAL, COMMERCIAL, LAND, INDUSTRIAL, OTHER');

    // Add missing values to EPropertyType enum
    console.log('\n📊 Updating EPropertyType enum...');
    await db.execute(`
      ALTER TYPE "EPropertyType" ADD VALUE IF NOT EXISTS 'APARTMENT';
    `);
    await db.execute(`
      ALTER TYPE "EPropertyType" ADD VALUE IF NOT EXISTS 'CONDO';
    `);
    await db.execute(`
      ALTER TYPE "EPropertyType" ADD VALUE IF NOT EXISTS 'OFFICE';
    `);
    await db.execute(`
      ALTER TYPE "EPropertyType" ADD VALUE IF NOT EXISTS 'RETAIL';
    `);
    await db.execute(`
      ALTER TYPE "EPropertyType" ADD VALUE IF NOT EXISTS 'WAREHOUSE';
    `);
    await db.execute(`
      ALTER TYPE "EPropertyType" ADD VALUE IF NOT EXISTS 'OTHER';
    `);
    console.log('✅ EPropertyType updated: APARTMENT, HOUSE, CONDO, TOWNHOUSE, VILLA, OFFICE, RETAIL, WAREHOUSE, LAND, OTHER');

    // Add missing values to EPropertyStatus enum
    console.log('\n📊 Updating EPropertyStatus enum...');
    await db.execute(`
      ALTER TYPE "EPropertyStatus" ADD VALUE IF NOT EXISTS 'INACTIVE';
    `);
    console.log('✅ EPropertyStatus updated: AVAILABLE, SOLD, RENTED, PENDING, INACTIVE');

    // Verify current enum values
    console.log('\n🔍 Verifying updated enums...');
    const categoryResult = await db.execute(`
      SELECT unnest(enum_range(NULL::EPropertyCategory)) as category;
    `);
    console.log('EPropertyCategory values:', categoryResult.map(r => r.category).join(', '));

    const typeResult = await db.execute(`
      SELECT unnest(enum_range(NULL::EPropertyType)) as type;
    `);
    console.log('EPropertyType values:', typeResult.map(r => r.type).join(', '));

    const statusResult = await db.execute(`
      SELECT unnest(enum_range(NULL::EPropertyStatus)) as status;
    `);
    console.log('EPropertyStatus values:', statusResult.map(r => r.status).join(', '));

    console.log('\n🎉 Property enums successfully updated!');
    console.log('Now "Shop & office" properties can be correctly classified as COMMERCIAL/OFFICE or COMMERCIAL/RETAIL');

  } catch (error) {
    console.error('❌ Error updating enums:', error.message);
    throw error;
  }
}

// Run the update
updatePropertyEnums()
  .then(() => {
    console.log('\n✅ Enum update completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Enum update failed:', error);
    process.exit(1);
  });
