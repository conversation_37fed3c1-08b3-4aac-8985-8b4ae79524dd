// Check crawl status in database
require('dotenv').config();
const { db, crawlJobs, discoveredUrls, scrapingQueue } = require('./drizzle_client');
const { desc } = require('drizzle-orm');

async function checkCrawlStatus() {
  try {
    console.log('🔍 Checking crawl status in database...\n');

    // Get recent crawl jobs
    const jobs = await db.select().from(crawlJobs)
      .orderBy(desc(crawlJobs.created_at))
      .limit(5);

    console.log('📋 Recent Crawl Jobs:');
    if (jobs.length === 0) {
      console.log('   No crawl jobs found');
    } else {
      jobs.forEach(job => {
        console.log(`   ${job.website_id}: ${job.status}`);
        console.log(`      Job ID: ${job.id}`);
        console.log(`      Firecrawl ID: ${job.firecrawl_job_id || 'N/A'}`);
        console.log(`      Progress: ${job.processed_urls}/${job.total_urls}`);
        console.log(`      Properties found: ${job.property_urls_found}`);
        console.log(`      Started: ${job.started_at}`);
        console.log(`      Error: ${job.error_message || 'None'}`);
        console.log('');
      });
    }

    // Get discovered URLs
    const urls = await db.select().from(discoveredUrls)
      .orderBy(desc(discoveredUrls.discovered_at))
      .limit(10);

    console.log(`🔍 Recent Discovered URLs (${urls.length}):`);
    if (urls.length === 0) {
      console.log('   No URLs discovered yet');
    } else {
      urls.forEach(url => {
        console.log(`   ${url.url_type} (${url.is_property_page ? 'Property' : 'Other'}): ${url.url.substring(0, 80)}...`);
        console.log(`      Confidence: ${url.confidence_score || 'N/A'}`);
        console.log(`      Reason: ${url.classification_reason || 'N/A'}`);
        console.log('');
      });
    }

    // Get scraping queue
    const queueItems = await db.select().from(scrapingQueue)
      .orderBy(desc(scrapingQueue.created_at))
      .limit(5);

    console.log(`📋 Scraping Queue (${queueItems.length}):`);
    if (queueItems.length === 0) {
      console.log('   No items in queue');
    } else {
      queueItems.forEach(item => {
        console.log(`   ${item.status}: ${item.url.substring(0, 80)}...`);
        console.log(`      Priority: ${item.priority}`);
        console.log(`      Attempts: ${item.attempts}/${item.max_attempts}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Error checking status:', error.message);
  } finally {
    process.exit(0);
  }
}

checkCrawlStatus();
