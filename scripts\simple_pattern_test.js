// Simple pattern test
console.log('🏠 Testing Villa Bali Sale URL Patterns\n');

const testUrl = 'https://www.villabalisale.com/realestate-property/for-rent/villa/annually/canggu/elegance-three-bedroom-villa-with-enclosed-living-room-in-berawa-yrr3439';

const pattern = /\/realestate-property\/for-rent\/villa\/(annually|monthly|daily|weekly)\/[^\/]+\/[^\/]+-[a-z0-9]+\/?$/;

console.log(`Testing URL: ${testUrl}`);
console.log(`Pattern: ${pattern}`);

const matches = pattern.test(testUrl);
console.log(`Matches: ${matches}`);

if (matches) {
  console.log('✅ Pattern works correctly!');
} else {
  console.log('❌ Pattern needs adjustment');
  
  // Test simpler pattern
  const simplePattern = /\/realestate-property\/for-rent\/villa\/(annually|monthly|daily|weekly)\/[^\/]+\/[^\/]+\/?$/;
  const simpleMatches = simplePattern.test(testUrl);
  console.log(`Simple pattern matches: ${simpleMatches}`);
}
