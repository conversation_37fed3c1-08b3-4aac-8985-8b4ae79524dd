require('dotenv').config();

async function debugBPVL00967() {
  console.log('🔍 DEBUGGING BPVL00967 EXTRACTION...');

  // Let's use the browser to get the actual HTML content
  console.log('🌐 Opening browser to get HTML content...');

  // For now, let's test the CSS patterns with sample HTML that should contain 5 bedrooms and 5 bathrooms
  const sampleHtml = `
    <div class="details_item__icon__Kx4DF" style="mask-image: url(&quot;/_next/static/media/bedrooms.7a6788f7.svg&quot;);"></div>
    <div class="details_item__info__zDFk7">
      <span class="details_item__info__value__ramxJ">5</span>
    </div>
    <div class="details_item__icon__Kx4DF" style="mask-image: url(&quot;/_next/static/media/bathrooms.45d31171.svg&quot;);"></div>
    <div class="details_item__info__zDFk7">
      <span class="details_item__info__value__ramxJ">5</span>
    </div>
  `;

  try {
    console.log('🔍 TESTING CSS PATTERNS WITH SAMPLE HTML:');

    // Test the CSS patterns that should extract 5 bedrooms and 5 bathrooms
    const bedroomPattern = /bedrooms\.7a6788f7\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
    const bedroomMatch = sampleHtml.match(bedroomPattern);
    console.log(`Bedroom CSS pattern: ${bedroomMatch ? `Found "${bedroomMatch[1]}" bedrooms` : 'NOT FOUND'}`);

    // Bathroom pattern
    const bathroomPattern = /bathrooms\.45d31171\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
    const bathroomMatch = sampleHtml.match(bathroomPattern);
    console.log(`Bathroom CSS pattern: ${bathroomMatch ? `Found "${bathroomMatch[1]}" bathrooms` : 'NOT FOUND'}`);

    // Now let's check what the current mapper is actually doing
    console.log('\n🔍 TESTING CURRENT MAPPER LOGIC:');

    // Import the mapper and test it
    const { mapBetterPlace } = require('./scrape_worker/mappers');

    console.log('📝 Testing mapper with sample data...');

    // Create a mock raw data object like the mapper expects
    const mockRawData = {
      markdown: sampleHtml,
      url: 'https://betterplace.cc/buy/properties/BPVL00967'
    };

    try {
      const result = await mapBetterPlace(mockRawData);
      console.log('\n✅ MAPPER RESULT:');
      console.log(`Bedrooms: ${result.bedrooms}`);
      console.log(`Bathrooms: ${result.bathrooms}`);
      console.log(`Title: ${result.title}`);
    } catch (mapperError) {
      console.error('❌ MAPPER ERROR:', mapperError.message);
    }
    
  } catch (error) {
    console.error('❌ ERROR:', error.message);
  }
}

debugBPVL00967();
