// Check specific property by ID
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function checkSpecificProperty() {
  console.log('🔍 Check Specific Property');
  console.log('='.repeat(30));
  
  const propertyId = '9efee4c5-d879-40da-a08d-b8228d23d34d';
  
  try {
    // Get the specific property by ID
    const property = await db
      .select()
      .from(properties)
      .where(eq(properties.id, propertyId))
      .limit(1);
    
    if (property.length === 0) {
      console.log('❌ Property not found');
      return;
    }
    
    const prop = property[0];
    console.log(`🏠 Property: ${prop.title}`);
    console.log(`🌐 Source: ${prop.media?.source_id || 'NULL'}`);
    console.log(`📅 Created: ${new Date(prop.created_at).toLocaleString()}`);
    console.log('');
    
    // Check all our target fields
    console.log('🎯 IMPROVEMENT RESULTS:');
    console.log(`   🛏️  Bedrooms: ${prop.bedrooms || 'NULL'} (expected: 3)`);
    console.log(`   🚿 Bathrooms: ${prop.bathrooms || 'NULL'} (expected: 4)`);
    console.log(`   🚗 Parking: ${prop.parking_spaces || 'NULL'}`);
    console.log(`   🏞️ Lot Size: ${prop.lot_size_sqft || 'NULL'} sqft (expected: ~3229)`);
    console.log(`   📋 Lease Text: ${prop.lease_duration_text || 'NULL'} (expected: "23-year lease")`);
    console.log(`   📋 Lease Years: ${prop.lease_duration_years || 'NULL'} (expected: 23)`);
    console.log(`   🔗 Source URL ID: ${prop.source_url_id || 'NULL'}`);
    console.log('');
    
    // Validation
    console.log('✅ VALIDATION:');
    
    // Bedrooms/Bathrooms
    if (prop.bedrooms === 3 && prop.bathrooms === 4) {
      console.log('   ✅ Bedrooms/Bathrooms: PERFECT (3 bed / 4 bath)');
    } else {
      console.log(`   ❌ Bedrooms/Bathrooms: FAILED (${prop.bedrooms} bed / ${prop.bathrooms} bath)`);
    }
    
    // Lot Size (new improvement)
    if (prop.lot_size_sqft && prop.lot_size_sqft > 3000) {
      console.log(`   ✅ Lot Size: WORKING (${prop.lot_size_sqft} sqft)`);
    } else {
      console.log('   ❌ Lot Size: NOT FOUND or INCORRECT');
    }
    
    // Lease Duration (new improvement)
    if (prop.lease_duration_text && prop.lease_duration_years === 23) {
      console.log(`   ✅ Lease Duration: WORKING (${prop.lease_duration_text})`);
    } else {
      console.log('   ❌ Lease Duration: NOT FOUND or INCORRECT');
    }
    
    // Parking (new improvement)
    if (prop.parking_spaces) {
      console.log(`   ✅ Parking: WORKING (${prop.parking_spaces} spaces)`);
    } else {
      console.log('   ⚠️  Parking: NOT FOUND (expected for this property)');
    }
    
    console.log('\n📊 FULL PROPERTY DETAILS:');
    console.log(`   💰 Price: ${prop.price || 'NULL'} | Rent: ${prop.rent_price || 'NULL'}`);
    console.log(`   📐 Size: ${prop.size_sqft || 'NULL'} sqft`);
    console.log(`   📅 Year: ${prop.year_built || 'NULL'}`);
    console.log(`   🏠 Ownership: ${prop.ownership_type || 'NULL'}`);
    console.log(`   📍 Address: ${prop.address || 'NULL'}`);
    console.log(`   🏙️  City: ${prop.city || 'NULL'}`);
    
    if (prop.description) {
      console.log(`   📝 Description: "${prop.description.substring(0, 100)}..."`);
    }
    
    // Summary
    let improvements = 0;
    let total = 4;
    
    if (prop.bedrooms === 3 && prop.bathrooms === 4) improvements++;
    if (prop.lot_size_sqft && prop.lot_size_sqft > 3000) improvements++;
    if (prop.lease_duration_text && prop.lease_duration_years === 23) improvements++;
    if (prop.parking_spaces) improvements++;
    
    console.log('\n🎯 IMPROVEMENT SUMMARY:');
    console.log(`   ✅ Working improvements: ${improvements}/${total}`);
    console.log(`   📈 Success rate: ${((improvements / total) * 100).toFixed(1)}%`);
    
    if (improvements >= 3) {
      console.log('   🎉 EXCELLENT! Most improvements are working!');
    } else if (improvements >= 2) {
      console.log('   ✅ GOOD! Some improvements are working!');
    } else {
      console.log('   ⚠️  NEEDS WORK! Few improvements are working!');
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
  }
}

// Run check
if (require.main === module) {
  checkSpecificProperty().catch(console.error);
}

module.exports = { checkSpecificProperty };
