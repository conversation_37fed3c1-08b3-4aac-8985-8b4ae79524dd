// Wait for Bali Home Immo crawl to complete and analyze results
require('dotenv').config();
const { db, crawlJobs } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function waitForBaliHomeImmo() {
  console.log('⏳ Waiting for Bali Home Immo crawl to complete...\n');

  const jobId = '062d7b9f-de56-485c-a269-350ad5b47083';
  
  let attempts = 0;
  const maxAttempts = 40; // 20 minutes max
  
  while (attempts < maxAttempts) {
    try {
      // Check database status instead of API (due to key issues)
      const jobs = await db.select().from(crawlJobs)
        .where(eq(crawlJobs.id, jobId));
      
      if (jobs.length === 0) {
        console.log('❌ Job not found in database');
        break;
      }
      
      const job = jobs[0];
      console.log(`📊 Attempt ${attempts + 1}: ${job.status} (${job.processed_urls}/${job.total_urls})`);
      
      if (job.status === 'completed') {
        console.log('\n🎉 Bali Home Immo crawl completed successfully!');
        console.log(`📊 Final stats: ${job.processed_urls}/${job.total_urls} URLs processed`);
        console.log(`🏠 Property URLs found: ${job.property_urls_found}`);
        
        // Check discovered URLs
        const { discoveredUrls } = require('./drizzle_client');
        const { desc } = require('drizzle-orm');
        
        const urls = await db.select().from(discoveredUrls)
          .where(eq(discoveredUrls.crawl_job_id, jobId))
          .orderBy(desc(discoveredUrls.confidence_score))
          .limit(20);
        
        console.log(`\n🔍 Discovered URLs: ${urls.length}`);
        
        const propertyUrls = urls.filter(url => url.is_property_page);
        const listingUrls = urls.filter(url => url.url_type === 'listing');
        const otherUrls = urls.filter(url => !url.is_property_page && url.url_type !== 'listing');
        
        console.log(`   🏠 Property Pages: ${propertyUrls.length}`);
        console.log(`   📄 Listing Pages: ${listingUrls.length}`);
        console.log(`   🔗 Other Pages: ${otherUrls.length}`);
        
        if (propertyUrls.length > 0) {
          console.log('\n🏠 Property URLs Found:');
          propertyUrls.slice(0, 10).forEach((url, index) => {
            console.log(`   ${index + 1}. ${url.url} (confidence: ${url.confidence_score})`);
            console.log(`      Reason: ${url.classification_reason}`);
          });
        }
        
        if (listingUrls.length > 0) {
          console.log('\n📄 Listing URLs Found:');
          listingUrls.slice(0, 5).forEach((url, index) => {
            console.log(`   ${index + 1}. ${url.url} (confidence: ${url.confidence_score})`);
            console.log(`      Reason: ${url.classification_reason}`);
          });
        }
        
        // Check queue
        const { scrapingQueue } = require('./drizzle_client');
        const queueItems = await db.select().from(scrapingQueue)
          .where(eq(scrapingQueue.website_id, 'bali_home_immo'))
          .orderBy(desc(scrapingQueue.priority));
        
        console.log(`\n📋 Items added to scraping queue: ${queueItems.length}`);
        if (queueItems.length > 0) {
          console.log('   Top priority items:');
          queueItems.slice(0, 5).forEach((item, index) => {
            console.log(`   ${index + 1}. ${item.url.substring(0, 80)}... (priority: ${item.priority})`);
          });
        }
        
        break;
        
      } else if (job.status === 'failed') {
        console.log('\n❌ Bali Home Immo crawl failed');
        console.log(`Error: ${job.error_message}`);
        break;
        
      } else {
        // Still running, wait and try again
        await new Promise(resolve => setTimeout(resolve, 30000)); // Wait 30 seconds
      }
      
    } catch (error) {
      console.log(`⚠️  Error checking status: ${error.message}`);
      await new Promise(resolve => setTimeout(resolve, 30000)); // Wait 30 seconds on error
    }
    
    attempts++;
  }
  
  if (attempts >= maxAttempts) {
    console.log('\n⏰ Timeout waiting for crawl to complete');
    
    // Show current status anyway
    try {
      const jobs = await db.select().from(crawlJobs)
        .where(eq(crawlJobs.id, jobId));
      
      if (jobs.length > 0) {
        const job = jobs[0];
        console.log(`📊 Final status: ${job.status} (${job.processed_urls}/${job.total_urls})`);
      }
    } catch (error) {
      console.log('❌ Could not get final status');
    }
  }
  
  process.exit(0);
}

waitForBaliHomeImmo();
