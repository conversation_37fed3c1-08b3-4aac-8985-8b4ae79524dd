// Check recent scraping results
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc, sql } = require('drizzle-orm');

async function checkResults() {
  console.log('🔍 Checking recent scraping results...');
  
  try {
    // Check total count
    const totalCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`📊 Total properties in database: ${totalCount[0].count}`);
    
    // Get latest properties (last 20)
    const recentProps = await db
      .select({
        title: properties.title,
        source_id: properties.source_id,
        price: properties.price,
        rent_price: properties.rent_price,
        city: properties.city,
        bedrooms: properties.bedrooms,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(20);
    
    console.log(`\n🆕 Latest properties: ${recentProps.length}`);
    
    if (recentProps.length > 0) {
      console.log('\n🏠 Recent Properties:');
      recentProps.forEach((prop, i) => {
        const price = prop.price ? `IDR ${prop.price.toLocaleString()}` : 
                     prop.rent_price ? `IDR ${prop.rent_price.toLocaleString()}/month` : 'Price on request';
        console.log(`   ${i + 1}. [${prop.source_id}] ${prop.title.substring(0, 50)}...`);
        console.log(`      💰 ${price} | 🏠 ${prop.bedrooms} bed | 📍 ${prop.city}`);
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
        console.log('');
      });
      
      // Group by source
      const bySource = {};
      recentProps.forEach(prop => {
        if (!bySource[prop.source_id]) bySource[prop.source_id] = 0;
        bySource[prop.source_id]++;
      });
      
      console.log('📊 Properties by source:');
      Object.entries(bySource).forEach(([source, count]) => {
        console.log(`   ${source}: ${count} properties`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    process.exit(0);
  }
}

checkResults();
