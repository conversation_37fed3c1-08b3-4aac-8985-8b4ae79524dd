// Test only the parsing functionality
const mockMarkdown = `
# Modern 3 Bedroom Villa with Rooftop in Tumbak Bayuh

**Price:** USD 450,000

**Location:** Tumbak Bayuh, Canggu, Bali

**Property Details:**
- 3 bedrooms
- 3 bathrooms  
- Building size: 180 sqm
- Land size: 250 sqm
- Year built: 2022
- Parking: 2 cars
- Ownership: Freehold

**Description:**
This stunning modern villa features contemporary design with high-quality finishes throughout. Located in the peaceful area of Tumbak Bayuh, just minutes from Canggu's famous beaches and vibrant dining scene.

**Amenities:**
- Swimming pool
- Garden
- Air conditioning
- Fully furnished
- Kitchen
- WiFi
- Security system
- Balcony

![Villa Image](https://betterplace.cc/image1.jpg)
![Pool View](https://betterplace.cc/image2.jpg)
![Bedroom](https://betterplace.cc/image3.jpg)
`;

// BetterPlace-specific markdown parser (copied from mappers.js)
function parseBetterPlaceMarkdown(markdown, url) {
  try {
    console.log(`   🔍 Parsing BetterPlace markdown (${markdown.length} chars)...`);

    // Extract property ID from URL
    const propertyIdMatch = url.match(/\/([A-Z0-9]+)$/i);
    const property_id = propertyIdMatch ? propertyIdMatch[1] : 'unknown';

    // Extract title - BetterPlace usually has title as first heading
    const titleMatch = markdown.match(/^#\s+(.+)$/m) ||
                      markdown.match(/^##\s+(.+)$/m) ||
                      markdown.match(/!\[([^\]]+)\]/) ||
                      markdown.match(/\*\*([^*]+)\*\*/);
    const title = titleMatch ? titleMatch[1].trim() : 'Property Title Not Found';

    // Extract price - BetterPlace often shows USD prices
    const priceMatch = markdown.match(/(?:USD|US\$|\$)\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i) ||
                      markdown.match(/(?:IDR|Rp)\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i) ||
                      markdown.match(/([\d,\.]+)\s*(?:USD|US\$|\$|IDR|Rp)/i);
    const price = priceMatch ? priceMatch[0] : null;

    // Extract bedrooms - look for various patterns
    const bedroomMatch = markdown.match(/(\d+)\s*(?:bed|bedroom|kamar tidur|BR)(?!\d)/i) ||
                        markdown.match(/bedroom[s]?[:\s]*(\d+)/i) ||
                        markdown.match(/(\d+)\s*BR(?!\d)/i);
    const bedrooms = bedroomMatch ? parseInt(bedroomMatch[1]) : null;

    // Extract bathrooms - improved pattern to avoid large numbers
    const bathroomMatch = markdown.match(/(\d{1,2})\s*(?:bath|bathroom|kamar mandi|BA)(?!\d)/i) ||
                         markdown.match(/bathroom[s]?[:\s]*(\d{1,2})/i) ||
                         markdown.match(/(\d{1,2})\s*BA(?!\d)/i);
    const bathrooms = bathroomMatch ? parseInt(bathroomMatch[1]) : null;

    // Extract location - BetterPlace shows location info
    const locationMatch = markdown.match(/(Canggu|Seminyak|Ubud|Kerobokan|Sanur|Denpasar|Jimbaran|Nusa Dua|Uluwatu|Pecatu|Bukit|Umalas|Berawa|Pererenan|Kedungu|Tanah Lot|Seseh|Cemagi|Tabanan|Badung|Gianyar)/i) ||
                         markdown.match(/Location[:\s]*([^\n]+)/i) ||
                         markdown.match(/Address[:\s]*([^\n]+)/i);
    const location = locationMatch ? locationMatch[1].trim() : 'Bali, Indonesia';

    // Extract images - look for image URLs
    const imageMatches = markdown.match(/!\[.*?\]\((https?:\/\/[^\)]+)\)/g) || [];
    const images = imageMatches.map(match => {
      const urlMatch = match.match(/\((https?:\/\/[^\)]+)\)/);
      return urlMatch ? urlMatch[1] : null;
    }).filter(Boolean);

    // Extract size information
    const buildingSizeMatch = markdown.match(/(?:building|built)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)/i) ||
                             markdown.match(/(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²).*(?:building|built)/i);
    const building_size_sqm = buildingSizeMatch ? parseFloat(buildingSizeMatch[1].replace(',', '')) : null;

    const landSizeMatch = markdown.match(/(?:land|lot)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)/i) ||
                         markdown.match(/(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²).*(?:land|lot)/i);
    const land_size_sqm = landSizeMatch ? parseFloat(landSizeMatch[1].replace(',', '')) : null;

    // Extract amenities - BetterPlace specific features
    const amenityKeywords = [
      'swimming pool', 'pool', 'garden', 'parking', 'garage', 'kitchen', 
      'air conditioning', 'wifi', 'security', 'furnished', 'unfurnished',
      'balcony', 'terrace', 'gym', 'spa', 'jacuzzi', 'bbq', 'maid service',
      'internet', 'cable tv', 'washing machine', 'dryer', 'dishwasher'
    ];
    const amenities = [];
    const lowerMarkdown = markdown.toLowerCase();

    amenityKeywords.forEach(keyword => {
      if (lowerMarkdown.includes(keyword)) {
        amenities.push(keyword.charAt(0).toUpperCase() + keyword.slice(1));
      }
    });

    // Extract ownership type - BetterPlace often mentions this
    let ownership_type = null;
    if (lowerMarkdown.includes('freehold')) {
      ownership_type = 'FREEHOLD';
    } else if (lowerMarkdown.includes('leasehold')) {
      ownership_type = 'LEASEHOLD';
    }

    // Extract parking info
    const parkingMatch = markdown.match(/(\d+)\s*(?:car\s*)?(?:parking|garage)/i) ||
                        markdown.match(/parking[:\s]*(\d+)/i);
    const parking_spaces = parkingMatch ? parseInt(parkingMatch[1]) : null;

    // Extract year built
    const yearMatch = markdown.match(/(?:built|year|constructed)[:\s]*(\d{4})/i) ||
                     markdown.match(/(\d{4})\s*(?:built|year|constructed)/i);
    const year_built = yearMatch ? parseInt(yearMatch[1]) : null;

    const extractedData = {
      title: title,
      price: price,
      location: location,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      images: images.slice(0, 10),
      property_id: property_id,
      detail_url: url,
      property_type: 'villa',
      status: 'available',
      amenities: amenities,
      year_built: year_built,
      parking: parking_spaces,
      ownership_type: ownership_type,
      size: {
        building_size_sqm: building_size_sqm,
        land_size_sqm: land_size_sqm
      }
    };

    console.log(`   ✅ Extracted: ${title} (${bedrooms}bed/${bathrooms}bath) in ${location} - ${price}`);
    return extractedData;

  } catch (error) {
    console.log(`   ❌ BetterPlace markdown parsing failed: ${error.message}`);
    return null;
  }
}

console.log('🧪 Testing BetterPlace Markdown Parsing');
console.log('='.repeat(50));

const testUrl = 'https://betterplace.cc/buy/properties/BPVL02270';
const result = parseBetterPlaceMarkdown(mockMarkdown, testUrl);

console.log('\n📊 Parsing Result:');
console.log(JSON.stringify(result, null, 2));

console.log('\n✅ Test completed!');
console.log('💰 Markdown parsing is ready for cost-effective scraping');
