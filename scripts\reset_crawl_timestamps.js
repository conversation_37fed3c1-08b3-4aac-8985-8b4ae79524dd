// Reset crawl timestamps to allow immediate re-crawling
require('dotenv').config();
const { db, websiteConfigs, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function resetCrawlTimestamps() {
  console.log('🔄 Resetting Crawl Timestamps\n');
  
  try {
    // Reset all timestamps to allow immediate crawling
    const result = await db
      .update(websiteConfigs)
      .set({
        last_sitemap_check: null,
        next_crawl_at: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        updated_at: new Date()
      })
      .returning({ website_id: websiteConfigs.website_id });
    
    console.log('✅ Reset timestamps for websites:');
    result.forEach(row => {
      console.log(`   🌐 ${row.website_id}`);
    });
    
    console.log(`\n📊 Total websites reset: ${result.length}`);
    console.log('✅ All websites can now be crawled immediately');
    
  } catch (error) {
    console.error('❌ Error resetting timestamps:', error.message);
    process.exit(1);
  } finally {
    closeConnection();
  }
}

resetCrawlTimestamps();
