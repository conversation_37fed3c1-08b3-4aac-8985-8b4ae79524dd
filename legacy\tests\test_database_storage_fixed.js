// Test that fixed mappers properly store data in database
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { db, properties } = require('./drizzle_client');
const { desc } = require('drizzle-orm');

async function testDatabaseStorageFixed() {
  console.log('🔧 Testing Database Storage with Fixed Mappers');
  console.log('='.repeat(50));
  
  try {
    // Test one property from each website
    const testData = {
      betterplace: ['https://betterplace.cc/buy/properties/BPVL02270'],
      bali_villa_realty: ['https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/'],
      bali_home_immo: ['https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508']
    };
    
    for (const [websiteId, urls] of Object.entries(testData)) {
      console.log(`\n📡 Testing ${websiteId}...`);
      
      try {
        const results = await runExtractBatch(websiteId, urls, {});
        const successful = results?.filter(r => r && r.title) || [];
        
        console.log(`   ✅ Processed: ${successful.length} properties`);
        
        if (successful.length > 0) {
          const prop = successful[0];
          console.log(`   📋 Extracted Data:`);
          console.log(`      Title: ${prop.title || 'MISSING'}`);
          console.log(`      Price/Rent: ${prop.price || prop.rent_price || 'MISSING'}`);
          console.log(`      Bedrooms: ${prop.bedrooms || 'MISSING'}`);
          console.log(`      Bathrooms: ${prop.bathrooms || 'MISSING'}`);
          console.log(`      Address: ${prop.address || 'MISSING'}`);
          console.log(`      City: ${prop.city || 'MISSING'}`);
          console.log(`      Size (sqft): ${prop.size_sqft || 'MISSING'}`);
          console.log(`      Lot Size (sqft): ${prop.lot_size_sqft || 'MISSING'}`);
          console.log(`      Year Built: ${prop.year_built || 'MISSING'}`);
          console.log(`      Parking: ${prop.parking_spaces || 'MISSING'}`);
          console.log(`      External ID: ${prop.media?.external_id || 'MISSING'}`);
        }
        
      } catch (error) {
        console.log(`   ❌ ${websiteId} failed: ${error.message}`);
      }
    }
    
    // Check database for recent properties
    console.log('\n🔄 Checking database for recent properties...');
    
    const recentProperties = await db
      .select({
        id: properties.id,
        title: properties.title,
        price: properties.price,
        rent_price: properties.rent_price,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        parking_spaces: properties.parking_spaces,
        size_sqft: properties.size_sqft,
        lot_size_sqft: properties.lot_size_sqft,
        year_built: properties.year_built,
        address: properties.address,
        city: properties.city,
        media: properties.media,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`📋 Found ${recentProperties.length} recent properties:`);
    
    let nullCount = 0;
    let totalFields = 0;
    
    recentProperties.forEach((prop, index) => {
      if (prop.media?.source_id && ['betterplace', 'bali_villa_realty', 'bali_home_immo'].includes(prop.media.source_id)) {
        console.log(`\n${index + 1}. ${prop.title || 'UNTITLED'}`);
        console.log(`   Source: ${prop.media?.source_id}`);
        console.log(`   Price: ${prop.price || prop.rent_price || 'NULL'}`);
        console.log(`   Bedrooms: ${prop.bedrooms || 'NULL'}`);
        console.log(`   Bathrooms: ${prop.bathrooms || 'NULL'}`);
        console.log(`   Parking: ${prop.parking_spaces || 'NULL'}`);
        console.log(`   Size (sqft): ${prop.size_sqft || 'NULL'}`);
        console.log(`   Lot Size (sqft): ${prop.lot_size_sqft || 'NULL'}`);
        console.log(`   Year Built: ${prop.year_built || 'NULL'}`);
        console.log(`   Address: ${prop.address || 'NULL'}`);
        console.log(`   City: ${prop.city || 'NULL'}`);
        console.log(`   Created: ${new Date(prop.created_at).toLocaleString()}`);
        
        // Count NULL values
        const fields = [
          prop.price || prop.rent_price,
          prop.bedrooms,
          prop.bathrooms,
          prop.parking_spaces,
          prop.size_sqft,
          prop.lot_size_sqft,
          prop.year_built,
          prop.address,
          prop.city
        ];
        
        fields.forEach(field => {
          totalFields++;
          if (!field) nullCount++;
        });
      }
    });
    
    const nullPercentage = totalFields > 0 ? ((nullCount / totalFields) * 100).toFixed(1) : 0;
    
    console.log('\n📊 Data Quality Analysis:');
    console.log(`   Total fields checked: ${totalFields}`);
    console.log(`   NULL fields: ${nullCount}`);
    console.log(`   NULL percentage: ${nullPercentage}%`);
    console.log(`   Data completeness: ${(100 - nullPercentage).toFixed(1)}%`);
    
    if (nullPercentage < 20) {
      console.log('✅ Data quality is GOOD (< 20% NULL values)');
    } else if (nullPercentage < 50) {
      console.log('⚠️  Data quality is FAIR (20-50% NULL values)');
    } else {
      console.log('❌ Data quality is POOR (> 50% NULL values)');
    }
    
    console.log('\n🎉 Database Storage Test Complete!');
    console.log('✅ Fixed mappers should now properly populate database fields');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

testDatabaseStorageFixed().catch(console.error);
