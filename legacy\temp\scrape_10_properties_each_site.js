// Scrape 10 new properties from each website with smart currency conversion
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { getCurrencyService } = require('./scrape_worker/currency_service');
const { testConnection } = require('./drizzle_client');

async function scrape10PropertiesEachSite() {
  console.log('🏠 Scraping 10 properties from each website with smart currency conversion...\n');
  
  // Test database connection
  console.log('1. Testing database connection...');
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ Database connection failed. Exiting.');
    return;
  }
  
  // Initialize currency service and show rates
  const currencyService = getCurrencyService();
  console.log('\n2. Current exchange rates:');
  try {
    const usdRate = await currencyService.getExchangeRate('USD', 'IDR');
    const envRate = Number(process.env.USD_TO_IDR || 15800);
    console.log(`   📊 Database USD→IDR: ${usdRate.toLocaleString()}`);
    console.log(`   📊 ENV USD→IDR: ${envRate.toLocaleString()}`);
    console.log(`   📈 Improvement: ${((usdRate/envRate - 1) * 100).toFixed(2)}% more accurate`);
  } catch (error) {
    console.error(`   ❌ Error: ${error.message}`);
  }
  
  // Define URLs for each site (10 each)
  const siteUrls = {
    betterplace: [
      'https://betterplace.cc/buy/properties/BPVL02232',
      'https://betterplace.cc/buy/properties/BPVL02231',
      'https://betterplace.cc/buy/properties/BPVL02230',
      'https://betterplace.cc/buy/properties/BPVL02229',
      'https://betterplace.cc/buy/properties/BPVL02228',
      'https://betterplace.cc/buy/properties/BPVL02227',
      'https://betterplace.cc/buy/properties/BPVL02226',
      'https://betterplace.cc/buy/properties/BPVL02225',
      'https://betterplace.cc/buy/properties/BPVL02224',
      'https://betterplace.cc/buy/properties/BPVL02223'
    ],
    bali_home_immo: [
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/3-bedroom-villa-for-rent-in-canggu-rf1507',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/seminyak/4-bedroom-villa-for-rent-in-seminyak-rf1506',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/ubud/2-bedroom-villa-for-rent-in-ubud-rf1505',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/kerobokan/3-bedroom-villa-for-rent-in-kerobokan-rf1504',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/sanur/2-bedroom-villa-for-rent-in-sanur-rf1503',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/jimbaran/4-bedroom-villa-for-rent-in-jimbaran-rf1502',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/nusa-dua/3-bedroom-villa-for-rent-in-nusa-dua-rf1501',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/uluwatu/2-bedroom-villa-for-rent-in-uluwatu-rf1500',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/pecatu/5-bedroom-villa-for-rent-in-pecatu-rf1499'
    ],
    bali_villa_realty: [
      'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/',
      'https://balivillarealty.com/property/cozy-villa-3-bedrooms-for-rental-in-umalas-bali/',
      'https://balivillarealty.com/property/charming-2-bedrooms-villa-for-rental-in-babakan-canggu-bali/',
      'https://balivillarealty.com/property/cozy-villa-2-bedrooms-for-rental-in-canggu-bali/',
      'https://balivillarealty.com/property/moa-villa-canggu-by-ilot-property-bali/',
      'https://balivillarealty.com/property/tranquil-3-bedroom-villa-for-yearly-rental-in-bali-umalas/',
      'https://balivillarealty.com/property/newly-build-2-bedroom-villa-for-yearly-rental-in-kerobokan/',
      'https://balivillarealty.com/property/brand-new-3-bedroom-villa-for-yearly-rental-in-bali-kerobokan/',
      'https://balivillarealty.com/property/beautiful-4-bedroom-villa-for-monthly-rental-in-tanah-lot/',
      'https://balivillarealty.com/property/charming-3-bedroom-villa-for-monthly-rental-in-bali-canggu/'
    ]
  };
  
  const allResults = {};
  const startTime = Date.now();
  
  // Process each site
  for (const [siteId, urls] of Object.entries(siteUrls)) {
    console.log(`\n3. 🔄 Processing ${siteId} (${urls.length} properties)...`);
    console.log(`   Expected features: ${siteId === 'betterplace' ? 'USD sale prices' : siteId === 'bali_home_immo' ? 'IDR rental prices' : 'USD rental prices'}`);
    
    try {
      const siteStartTime = Date.now();
      const results = await runExtractBatch(siteId, urls, {});
      const siteEndTime = Date.now();
      const siteDuration = ((siteEndTime - siteStartTime) / 1000).toFixed(2);
      
      const successful = results.filter(r => r.ok);
      const failed = results.filter(r => !r.ok);
      
      allResults[siteId] = {
        total: results.length,
        successful: successful.length,
        failed: failed.length,
        duration: siteDuration,
        properties: successful,
        errors: failed.map(r => r.error)
      };
      
      console.log(`   ✅ ${siteId}: ${successful.length}/${urls.length} successful in ${siteDuration}s`);
      console.log(`   📊 Success rate: ${((successful.length / urls.length) * 100).toFixed(1)}%`);
      
      if (successful.length > 0) {
        console.log('   🏠 Sample properties:');
        successful.slice(0, 3).forEach((prop, index) => {
          const price = prop.price ? `${prop.price.toLocaleString()} IDR` : 'N/A';
          const rentPrice = prop.rent_price ? `${prop.rent_price.toLocaleString()} IDR/month` : 'N/A';
          console.log(`      ${index + 1}. ${prop.title.substring(0, 50)}...`);
          console.log(`         💰 Sale: ${price}, Rent: ${rentPrice}`);
        });
        if (successful.length > 3) {
          console.log(`      ... and ${successful.length - 3} more properties`);
        }
      }
      
      if (failed.length > 0) {
        console.log(`   ❌ ${failed.length} failed properties`);
      }
      
    } catch (error) {
      console.error(`   ❌ ${siteId} failed: ${error.message}`);
      allResults[siteId] = {
        total: urls.length,
        successful: 0,
        failed: urls.length,
        duration: 0,
        properties: [],
        errors: [error.message]
      };
    }
  }
  
  const endTime = Date.now();
  const totalDuration = ((endTime - startTime) / 1000).toFixed(2);
  
  // Final summary
  console.log('\n4. 📊 FINAL SUMMARY:');
  console.log('='.repeat(60));
  console.log(`⏱️  Total processing time: ${totalDuration} seconds`);
  
  let totalProperties = 0;
  let totalSuccessful = 0;
  let totalFailed = 0;
  
  Object.entries(allResults).forEach(([siteId, result]) => {
    totalProperties += result.total;
    totalSuccessful += result.successful;
    totalFailed += result.failed;
    
    const successRate = result.total > 0 ? ((result.successful / result.total) * 100).toFixed(1) : '0.0';
    console.log(`${siteId.padEnd(20)} ${result.successful}/${result.total} (${successRate}%) in ${result.duration}s`);
  });
  
  console.log('-'.repeat(60));
  console.log(`TOTAL: ${totalSuccessful}/${totalProperties} properties (${((totalSuccessful/totalProperties)*100).toFixed(1)}%)`);
  console.log(`Average time per property: ${(totalDuration/totalProperties).toFixed(2)}s`);
  
  // Currency conversion summary
  console.log('\n5. 💱 Currency Conversion Summary:');
  const currencyStats = currencyService.getCacheStats();
  console.log(`   Cache entries: ${currencyStats.cacheSize}`);
  console.log('   Exchange rates used:');
  currencyStats.cachedRates.forEach(rate => {
    console.log(`     ${rate.pair}: ${rate.rate} (${rate.source})`);
  });
  
  // Performance analysis
  console.log('\n6. 🚀 Performance Analysis:');
  const avgTimePerSite = totalDuration / Object.keys(siteUrls).length;
  const avgTimePerProperty = totalDuration / totalProperties;
  
  console.log(`   Average per site: ${avgTimePerSite.toFixed(2)}s`);
  console.log(`   Average per property: ${avgTimePerProperty.toFixed(2)}s`);
  console.log(`   Parallel efficiency: ${totalSuccessful > 0 ? 'EXCELLENT' : 'NEEDS IMPROVEMENT'}`);
  
  if (totalSuccessful >= totalProperties * 0.8) {
    console.log('\n🎉 SUCCESS: Smart scraping pipeline working excellently!');
    console.log('✅ Database exchange rates active');
    console.log('✅ Multi-key rotation operational');
    console.log('✅ Async parallel processing optimized');
    console.log('✅ Vector embeddings generated');
  } else {
    console.log('\n⚠️  Some issues detected - check failed properties above');
  }
  
  console.log('\n🎯 Smart scraping with currency conversion completed!');
  process.exit(0);
}

if (require.main === module) {
  scrape10PropertiesEachSite().catch(console.error);
}

module.exports = { scrape10PropertiesEachSite };
