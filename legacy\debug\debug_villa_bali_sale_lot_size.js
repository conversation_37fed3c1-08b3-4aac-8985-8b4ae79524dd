// Debug Villa Bali Sale lot size issue - why 7,200 sqm?
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function debugVillaBaliSaleLotSize() {
  console.log('🔍 Debug Villa Bali Sale Lot Size Issue');
  console.log('='.repeat(50));
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold/seminyak/ideal-one-bedroom-roi-property-leasehold-in-central-kayu-aya';
  
  try {
    console.log(`📡 Scraping: ${testUrl}`);
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.markdown) {
      const content = result.data.markdown;
      console.log(`📝 Markdown length: ${content.length} chars`);
      
      // Test land size patterns manually
      console.log('\n🏞️ Testing Land Size Patterns:');
      
      const landSizePatterns = [
        /\*\*Land:\*\*\s*(\d+)\s*Are/i,   // "**Land:** 27 Are"
        /land.*?(\d+)\s*are/i,           // "land 27 are"
        /(\d+)\s*are.*?land/i,           // "27 are land"
        /land.*?(\d+)\s*m2/i,            // "land 2700 m2"
        /(\d+)\s*m2.*?land/i             // "2700 m2 land"
      ];
      
      landSizePatterns.forEach((pattern, i) => {
        const landMatch = content.match(pattern);
        if (landMatch) {
          const value = parseInt(landMatch[1]);
          console.log(`\n✅ Pattern ${i+1} (${pattern.source}) MATCHED:`);
          console.log(`   Raw match: "${landMatch[0]}"`);
          console.log(`   Extracted value: ${value}`);
          
          if (pattern.source.toLowerCase().includes('are') || landMatch[0].toLowerCase().includes('are')) {
            const lot_size_sqft = value * 100 * 10.764;
            console.log(`   🧮 Are Calculation: ${value} Are × 100 × 10.764 = ${lot_size_sqft} sqft`);
            console.log(`   📐 In sqm: ${(lot_size_sqft / 10.764).toFixed(1)} sqm`);
          } else {
            const lot_size_sqft = value * 10.764;
            console.log(`   🧮 M2 Calculation: ${value} m2 × 10.764 = ${lot_size_sqft} sqft`);
          }
        } else {
          console.log(`\n❌ Pattern ${i+1} (${pattern.source}): NO MATCH`);
        }
      });
      
      // Search for any land/lot related content
      console.log('\n🔍 Searching for ALL land/lot mentions:');
      const landMentions = content.match(/land|lot|area|sqm|m2|are/gi);
      if (landMentions) {
        console.log(`Found ${landMentions.length} land/lot mentions`);
        
        // Show context around each mention
        const lines = content.split('\n');
        lines.forEach((line, i) => {
          if (/land|lot|area.*\d+|sqm|m2|are/i.test(line)) {
            console.log(`\nLine ${i+1}: "${line.trim()}"`);
          }
        });
      }
      
      // Show building size patterns for comparison
      console.log('\n🏠 Testing Building Size Patterns:');
      
      const buildingSizePatterns = [
        /\*\*Building:\*\*\s*(\d+)m2/i,  // "**Building:** 400m2"
        /building.*?(\d+)\s*m2/i,        // "building 400 m2"
        /(\d+)\s*m2.*?building/i         // "400 m2 building"
      ];
      
      buildingSizePatterns.forEach((pattern, i) => {
        const sizeMatch = content.match(pattern);
        if (sizeMatch) {
          const value = parseInt(sizeMatch[1]);
          const size_sqft = value * 10.764;
          console.log(`\n✅ Building Pattern ${i+1} MATCHED:`);
          console.log(`   Raw match: "${sizeMatch[0]}"`);
          console.log(`   Value: ${value} m2 → ${size_sqft} sqft`);
        }
      });
      
    } else {
      console.log('❌ Failed to get markdown');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugVillaBaliSaleLotSize().then(() => process.exit(0));
