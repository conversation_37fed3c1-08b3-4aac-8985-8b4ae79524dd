# Website Configuration Guide

This guide explains how to configure and add new websites to the scraping system.

## Current Websites

### BetterPlace (`betterplace`)

- **Domain**: betterplace.cc
- **Type**: specialized
- **Status**: ✅ Active
- **Currency**: USD

**URL Patterns**:
- sale: /buy/properties/
- rent: /rent/properties/
- listing: /buy/properties/, /rent/properties/

**Validation**:
- Required fields: title, price, bedrooms
- Price range: 10000 - 50000000

---

### Bali Villa Realty (`bali_villa_realty`)

- **Domain**: balivillarealty.com
- **Type**: specialized
- **Status**: ✅ Active
- **Currency**: USD

**URL Patterns**:
- sale: /for-sale/, -for-sale-
- rent: /for-rent/, -for-rent-
- listing: /property/

**Validation**:
- Required fields: title, price, bedrooms
- Price range: 50000 - 10000000

---

### Bali Home Immo (`bali_home_immo`)

- **Domain**: bali-home-immo.com
- **Type**: specialized
- **Status**: ✅ Active
- **Currency**: IDR

**URL Patterns**:
- sale: /for-sale/
- rent: /for-rent/
- listing: /realestate-property/

**Validation**:
- Required fields: title, price
- Price range: 1000000 - 50000000000

---

### Villa Bali Sale (`villabalisale`)

- **Domain**: villabalisale.com
- **Type**: specialized
- **Status**: ✅ Active
- **Currency**: mixed

**URL Patterns**:
- sale: /for-sale/
- rent: /for-rent/
- freehold: /freehold/
- leasehold: /leasehold/
- listing: /realestate-property/

**Validation**:
- Required fields: title, price, bedrooms
- Price range: 1000000 - 50000000000

---

## Adding a New Website

### Method 1: Interactive Wizard

```bash
node tools/add_website.js
```

The wizard will guide you through:
1. Basic information (ID, name, domain)
2. Scraping configuration
3. Data extraction patterns
4. Validation rules
5. Testing with a sample URL

### Method 2: Manual Configuration

1. **Add to `website_configs.js`**:

```javascript
new_website: {
  id: 'new_website',
  name: 'New Website',
  domain: 'newwebsite.com',
  baseUrl: 'https://newwebsite.com',
  
  scraping: {
    formats: ['markdown'],
    onlyMainContent: true,
    timeout: 30000
  },
  
  urlPatterns: {
    sale: ['/for-sale/'],
    rent: ['/for-rent/'],
    listing: ['/property/']
  },
  
  extraction: {
    currency: 'USD',
    pricePatterns: [
      /\$\s*([\d,]+)/i
    ]
  },
  
  validation: {
    requiredFields: ['title', 'price', 'bedrooms'],
    priceRange: { min: 10000, max: 10000000 }
  }
}
```

2. **Test the configuration**:

```bash
node tools/test_website.js website new_website <test-url>
```

3. **Add URLs to queue and start scraping**

## Pattern Examples

### Price Patterns
- USD: `/\$\s*([\d,]+)/i`
- IDR: `/IDR\s*([\d,]+)/i`
- Mixed: `/(USD|\$|IDR)\s*([\d,]+)/i`

### Property Details
- Bedrooms: `/([1-9]|1[0-5])\s*(?:bed|bedroom)s?(?!\d)/i`
- Bathrooms: `/([1-9]|1[0-5])\s*(?:bath|bathroom)s?(?!\d)/i`
- Size: `/(\d+)\s*(?:sqft|sqm|m2)/i`

### Skip Phrases
- `['price on request', 'contact for price', 'negotiable']`

Generated on: 2025-08-20T05:29:42.204Z
