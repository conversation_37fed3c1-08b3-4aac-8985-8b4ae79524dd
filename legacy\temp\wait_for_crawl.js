// Wait for crawl to complete and analyze results
require('dotenv').config();
const { SmartCrawler } = require('./scrape_worker/smart_crawler');
const { db, crawlJobs } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function waitForCrawl() {
  console.log('⏳ Waiting for crawl to complete...\n');

  const crawler = new SmartCrawler();
  const jobId = '0f59dd21-a734-426b-9308-82218259257e';
  
  let attempts = 0;
  const maxAttempts = 60; // 30 minutes max
  
  while (attempts < maxAttempts) {
    try {
      const status = await crawler.checkCrawlStatus(jobId);
      
      console.log(`📊 Attempt ${attempts + 1}: ${status.status} (${status.completed}/${status.total})`);
      
      if (status.status === 'completed') {
        console.log('\n🎉 Crawl completed successfully!');
        console.log(`📊 Final stats: ${status.completed}/${status.total} URLs processed`);
        
        // Analyze the results
        if (status.data && status.data.length > 0) {
          console.log('\n🔍 Analyzing crawl results...');
          
          const urlCategories = {
            propertyPages: [],
            listingPages: [],
            otherPages: [],
            noUrl: []
          };
          
          status.data.forEach((item, index) => {
            let url = item.url;
            
            // Extract URL from markdown if missing
            if (!url && item.markdown) {
              const urlMatch = item.markdown.match(/https:\/\/betterplace\.cc\/[^\s\)]+/);
              if (urlMatch) {
                url = urlMatch[0];
              }
            }
            
            if (!url) {
              urlCategories.noUrl.push(index);
              return;
            }
            
            // Categorize URLs
            if (url.includes('/buy/properties/') || url.includes('/rental/properties/')) {
              urlCategories.propertyPages.push(url);
            } else if (
              url.includes('/rental-results') ||
              url.includes('/buy-results') ||
              url.includes('/search') ||
              url.includes('/properties') ||
              url.includes('/listings') ||
              url.includes('/category') ||
              url.includes('/filter') ||
              url.includes('/buy') ||
              url.includes('/rental')
            ) {
              urlCategories.listingPages.push(url);
            } else {
              urlCategories.otherPages.push(url);
            }
          });
          
          console.log('\n📋 URL Categories Found:');
          console.log(`   🏠 Property Pages: ${urlCategories.propertyPages.length}`);
          console.log(`   📄 Listing Pages: ${urlCategories.listingPages.length}`);
          console.log(`   🔗 Other Pages: ${urlCategories.otherPages.length}`);
          console.log(`   ❓ No URL: ${urlCategories.noUrl.length}`);
          
          // Show listing pages found
          if (urlCategories.listingPages.length > 0) {
            console.log('\n📄 Listing Pages Found:');
            urlCategories.listingPages.forEach(url => {
              console.log(`   • ${url}`);
            });
          }
          
          // Show sample property pages
          if (urlCategories.propertyPages.length > 0) {
            console.log(`\n🏠 Sample Property Pages (first 5):`);
            urlCategories.propertyPages.slice(0, 5).forEach(url => {
              console.log(`   • ${url}`);
            });
          }
          
          // Check for the specific pages we wanted
          const importantPages = [
            'rental-results',
            'buy-results',
            'properties',
            'rental',
            'buy'
          ];
          
          console.log('\n🎯 Important Pages Check:');
          importantPages.forEach(page => {
            const found = [...urlCategories.listingPages, ...urlCategories.otherPages]
              .some(url => url.includes(page));
            console.log(`   ${found ? '✅' : '❌'} ${page}`);
          });
        }
        
        break;
        
      } else if (status.status === 'failed') {
        console.log('\n❌ Crawl failed');
        break;
        
      } else {
        // Still running, wait and try again
        await new Promise(resolve => setTimeout(resolve, 30000)); // Wait 30 seconds
      }
      
    } catch (error) {
      console.log(`⚠️  Error checking status: ${error.message}`);
      await new Promise(resolve => setTimeout(resolve, 30000)); // Wait 30 seconds on error
    }
    
    attempts++;
  }
  
  if (attempts >= maxAttempts) {
    console.log('\n⏰ Timeout waiting for crawl to complete');
  }
  
  process.exit(0);
}

waitForCrawl();
