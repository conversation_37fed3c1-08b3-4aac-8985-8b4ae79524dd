// Check Database Status - Show current state of all tables
require('dotenv').config();
const { db, properties, scrapingQueue, discoveredUrls, crawlJobs, websiteConfigs, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function checkDatabaseStatus() {
  console.log('📊 Database Status Check\n');
  
  try {
    // Test connection first
    console.log('🔌 Testing database connection...');
    await db.execute(sql`SELECT 1 as test`);
    console.log('   ✅ Database connection successful\n');
    
    // 1. Check all table counts
    console.log('📋 Table Record Counts:');
    console.log('=' .repeat(50));
    
    const propertyCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    console.log(`   🏠 Properties: ${propertyCount[0]?.count || 0} records`);

    const queueCount = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue`);
    console.log(`   📋 Scraping Queue: ${queueCount[0]?.count || 0} records`);

    const urlsCount = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls`);
    console.log(`   🔗 Discovered URLs: ${urlsCount[0]?.count || 0} records`);

    const jobsCount = await db.execute(sql`SELECT COUNT(*) as count FROM crawl_jobs`);
    console.log(`   🕷️  Crawl Jobs: ${jobsCount[0]?.count || 0} records`);

    const configsCount = await db.execute(sql`SELECT COUNT(*) as count FROM website_configs`);
    console.log(`   ⚙️  Website Configs: ${configsCount[0]?.count || 0} records`);
    
    // 2. Show website configurations
    if (configsCount[0]?.count > 0) {
      console.log('\n⚙️  Website Configurations:');
      console.log('=' .repeat(50));
      
      const configs = await db.select({
        website_id: websiteConfigs.website_id,
        name: websiteConfigs.name,
        base_url: websiteConfigs.base_url,
        sitemap_enabled: websiteConfigs.sitemap_enabled,
        is_active: websiteConfigs.is_active,
        crawl_frequency_hours: websiteConfigs.crawl_frequency_hours
      }).from(websiteConfigs);
      
      configs.forEach((config, i) => {
        const status = config.is_active ? '✅ Active' : '❌ Inactive';
        const sitemap = config.sitemap_enabled ? '🗺️  Enabled' : '📄 Disabled';
        console.log(`   ${i + 1}. ${config.name} (${config.website_id})`);
        console.log(`      🌐 ${config.base_url}`);
        console.log(`      ${status} | ${sitemap} | ⏰ ${config.crawl_frequency_hours}h`);
      });
    }
    
    // 3. Show recent properties if any
    if (propertyCount[0]?.count > 0) {
      console.log('\n🏠 Recent Properties (Last 10):');
      console.log('=' .repeat(50));
      
      const recentProperties = await db.select({
        title: properties.title,
        source_id: properties.source_id,
        price: properties.price,
        rent_price: properties.rent_price,
        bedrooms: properties.bedrooms,
        city: properties.city,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(sql`created_at DESC`)
      .limit(10);
      
      recentProperties.forEach((prop, i) => {
        const priceStr = prop.price 
          ? `IDR ${Number(prop.price).toLocaleString()}` 
          : prop.rent_price 
            ? `IDR ${Number(prop.rent_price).toLocaleString()}/mo` 
            : 'No price';
        
        console.log(`   ${i + 1}. ${prop.title?.substring(0, 40)}...`);
        console.log(`      🌐 ${prop.source_id} | 💰 ${priceStr}`);
        console.log(`      🛏️  ${prop.bedrooms || 'N/A'} bed | 📍 ${prop.city || 'N/A'}`);
        console.log(`      📅 ${new Date(prop.created_at).toLocaleString()}`);
      });
    }
    
    // 4. Show queue status if any
    if (queueCount[0]?.count > 0) {
      console.log('\n📋 Scraping Queue Status:');
      console.log('=' .repeat(50));
      
      const queueStats = await db.execute(sql`
        SELECT 
          website_id,
          status,
          COUNT(*) as count
        FROM scraping_queue 
        GROUP BY website_id, status
        ORDER BY website_id, status
      `);
      
      const groupedStats = {};
      queueStats.rows.forEach(row => {
        if (!groupedStats[row.website_id]) {
          groupedStats[row.website_id] = {};
        }
        groupedStats[row.website_id][row.status] = row.count;
      });
      
      Object.entries(groupedStats).forEach(([websiteId, stats]) => {
        console.log(`   🌐 ${websiteId}:`);
        Object.entries(stats).forEach(([status, count]) => {
          const icon = status === 'completed' ? '✅' : 
                      status === 'failed' ? '❌' : 
                      status === 'processing' ? '🔄' : '⏳';
          console.log(`      ${icon} ${status}: ${count}`);
        });
      });
    }
    
    // 5. Show discovered URLs stats if any
    if (urlsCount[0]?.count > 0) {
      console.log('\n🔗 Discovered URLs Summary:');
      console.log('=' .repeat(50));
      
      const urlStats = await db.execute(sql`
        SELECT 
          website_id,
          url_type,
          is_property_page,
          COUNT(*) as count
        FROM discovered_urls 
        GROUP BY website_id, url_type, is_property_page
        ORDER BY website_id, url_type
      `);
      
      const urlGrouped = {};
      urlStats.rows.forEach(row => {
        if (!urlGrouped[row.website_id]) {
          urlGrouped[row.website_id] = { property: 0, listing: 0, other: 0 };
        }
        if (row.is_property_page) {
          urlGrouped[row.website_id].property += parseInt(row.count);
        } else if (row.url_type === 'listing') {
          urlGrouped[row.website_id].listing += parseInt(row.count);
        } else {
          urlGrouped[row.website_id].other += parseInt(row.count);
        }
      });
      
      Object.entries(urlGrouped).forEach(([websiteId, stats]) => {
        console.log(`   🌐 ${websiteId}:`);
        console.log(`      🏠 Property pages: ${stats.property}`);
        console.log(`      📋 Listing pages: ${stats.listing}`);
        console.log(`      📄 Other pages: ${stats.other}`);
      });
    }
    
    console.log('\n🎉 Database status check completed!');
    
  } catch (error) {
    console.error('❌ Error checking database status:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the status check
checkDatabaseStatus()
  .then(() => {
    console.log('✅ Status check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Status check failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
