require('dotenv').config();
const fetch = require('node-fetch');

// Simple function to extract description directly from BetterPlace markdown
function extractBetterPlaceDescription(markdown) {
  try {
    const lines = markdown.split('\n');
    
    // Look for the "Description" section
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Check if this line contains "Description" as a header
      if (line.toLowerCase().includes('description') && 
          (line.startsWith('#') || line.startsWith('**') || line.length < 50)) {
        
        console.log(`Found description header at line ${i}: "${line}"`);
        
        // Look for the actual description content in the next few lines
        for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
          const contentLine = lines[j].trim();
          
          // Skip empty lines and short lines
          if (contentLine.length < 50) continue;
          
          // Skip lines that are clearly not description content
          if (contentLine.startsWith('#') || 
              contentLine.startsWith('*') || 
              contentLine.startsWith('-') ||
              contentLine.startsWith('|') ||
              contentLine.includes('![')) continue;
          
          // If we find a substantial line, this is likely our description
          if (contentLine.length > 100 && 
              contentLine.toLowerCase().includes('bali') &&
              (contentLine.toLowerCase().includes('land') || 
               contentLine.toLowerCase().includes('property') ||
               contentLine.toLowerCase().includes('development'))) {
            
            console.log(`Found description content at line ${j}: "${contentLine.substring(0, 100)}..."`);
            return contentLine;
          }
        }
      }
      
      // Also check for lines that start with property descriptions directly
      if (line.length > 200 && 
          line.toLowerCase().includes('imagine stepping into') &&
          line.toLowerCase().includes('bali')) {
        console.log(`Found direct description: "${line.substring(0, 100)}..."`);
        return line;
      }
    }
    
    return null;
  } catch (error) {
    console.log(`Description extraction failed: ${error.message}`);
    return null;
  }
}

async function testDirectDescription() {
  try {
    console.log('🎯 TESTING DIRECT DESCRIPTION EXTRACTION...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    
    // Get the raw markdown from Firecrawl
    const firecrawlResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: false,
        waitFor: 3000
      })
    });
    
    const firecrawlData = await firecrawlResponse.json();
    
    if (firecrawlData.success) {
      const markdown = firecrawlData.data.markdown;
      console.log(`✅ Got markdown: ${markdown.length} characters`);
      
      // Test the direct description extraction
      console.log('\n🔍 SEARCHING FOR DESCRIPTION SECTION:');
      const description = extractBetterPlaceDescription(markdown);
      
      if (description) {
        console.log('\n🎉 SUCCESS! Description extracted:');
        console.log(`Length: ${description.length} characters`);
        console.log(`Content: "${description}"`);
        
        // Verify it contains expected content
        const hasExpectedContent = description.toLowerCase().includes('imagine stepping into');
        console.log(`\n✅ Contains "imagine stepping into": ${hasExpectedContent ? 'YES' : 'NO'}`);
        
        if (hasExpectedContent) {
          console.log('🎯 PERFECT! We found the correct BetterPlace property description!');
        }
      } else {
        console.log('\n❌ No description found');
        
        // Debug: show lines that contain "description"
        console.log('\n🔍 DEBUG - Lines containing "description":');
        const lines = markdown.split('\n');
        lines.forEach((line, i) => {
          if (line.toLowerCase().includes('description')) {
            console.log(`Line ${i}: ${line}`);
          }
        });
      }
    } else {
      console.log('❌ Failed to get markdown from Firecrawl');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
  
  process.exit(0);
}

testDirectDescription();
