// Discover and queue Villa Bali Sale property URLs from new sitemap
require('dotenv').config();
const { db, discoveredUrls, scrapingQueue, closeConnection } = require('../drizzle_client');
const { eq, sql } = require('drizzle-orm');

async function discoverVillaBaliSaleProperties() {
  try {
    console.log('🔍 Discovering Villa Bali Sale Property URLs\n');
    
    const sitemapUrl = 'https://www.villabalisale.com/sitemap_property.xml';
    console.log(`📡 Fetching sitemap: ${sitemapUrl}`);
    
    // Fetch sitemap content
    const response = await fetch(sitemapUrl);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const sitemapContent = await response.text();
    console.log(`✅ Sitemap fetched (${sitemapContent.length} chars)`);
    
    // Extract URLs from sitemap XML
    const urlMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
    const urls = urlMatches.map(match => match.replace(/<\/?loc>/g, ''));
    console.log(`🔍 Found ${urls.length} URLs in sitemap`);
    
    // Define our property patterns
    const propertyPatterns = [
      /\/realestate-property\/for-sale\/villa\/(freehold|leasehold)\/[^\/]+\/[^\/]+-[a-z0-9]+\/?$/,
      /\/realestate-property\/for-rent\/villa\/(annually|monthly|daily|weekly)\/[^\/]+\/[^\/]+-[a-z0-9]+\/?$/,
      /\/realestate-property\/for-sale\/villa\/(freehold|leasehold)\/[^\/]+\/[^\/]+\/?$/,
      /\/realestate-property\/for-rent\/villa\/(annually|monthly|daily|weekly)\/[^\/]+\/[^\/]+\/?$/,
      /\/unique-villas\/[^\/]+\/?$/
    ];
    
    const excludePatterns = [
      /\/fr\//,  // French URLs
      /\/id\//,  // Indonesian URLs
      /\/blog\//,
      /\/news\//,
      /\/search/,
      /\/filter/,
      /\?page=/,
      /\/page\//
    ];
    
    // Filter URLs
    console.log('\n🔍 Filtering URLs...');
    
    let propertyUrls = [];
    let excludedUrls = 0;
    
    for (const url of urls) {
      // Check if excluded
      const isExcluded = excludePatterns.some(pattern => pattern.test(url));
      if (isExcluded) {
        excludedUrls++;
        continue;
      }
      
      // Check if matches property patterns
      const isProperty = propertyPatterns.some(pattern => pattern.test(url));
      if (isProperty) {
        propertyUrls.push(url);
      }
    }
    
    console.log(`   ✅ Property URLs found: ${propertyUrls.length}`);
    console.log(`   ❌ Excluded URLs: ${excludedUrls}`);
    console.log(`   📊 Total processed: ${urls.length}`);
    
    if (propertyUrls.length === 0) {
      console.log('⚠️  No property URLs found to process');
      return;
    }
    
    // Show examples
    console.log('\n📋 First 10 property URLs:');
    propertyUrls.slice(0, 10).forEach((url, i) => {
      console.log(`   ${i + 1}. ${url}`);
    });
    
    // Check existing URLs in discovered_urls
    console.log('\n🔍 Checking for existing URLs...');
    
    const existingDiscovered = await db
      .select({ url: discoveredUrls.url })
      .from(discoveredUrls)
      .where(eq(discoveredUrls.website_id, 'villabalisale.com'));
    
    const existingDiscoveredSet = new Set(existingDiscovered.map(row => row.url));
    const newDiscoveredUrls = propertyUrls.filter(url => !existingDiscoveredSet.has(url));
    
    console.log(`   📊 Existing discovered URLs: ${existingDiscovered.length}`);
    console.log(`   ➕ New URLs to discover: ${newDiscoveredUrls.length}`);
    
    // Add new URLs to discovered_urls
    if (newDiscoveredUrls.length > 0) {
      console.log('\n➕ Adding new URLs to discovered_urls...');
      
      const discoveredRecords = newDiscoveredUrls.map(url => ({
        url: url,
        website_id: 'villabalisale.com',
        url_type: 'property',
        discovered_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      }));
      
      // Insert in batches
      const batchSize = 100;
      for (let i = 0; i < discoveredRecords.length; i += batchSize) {
        const batch = discoveredRecords.slice(i, i + batchSize);
        await db.insert(discoveredUrls).values(batch);
        console.log(`   ✅ Added batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(discoveredRecords.length / batchSize)}`);
      }
      
      console.log(`✅ Added ${newDiscoveredUrls.length} new URLs to discovered_urls`);
    }
    
    // Check existing URLs in scraping_queue
    console.log('\n🔍 Checking scraping queue...');
    
    const existingQueue = await db
      .select({ url: scrapingQueue.url })
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'villabalisale.com'));
    
    const existingQueueSet = new Set(existingQueue.map(row => row.url));
    const newQueueUrls = propertyUrls.filter(url => !existingQueueSet.has(url));
    
    console.log(`   📊 Existing queue URLs: ${existingQueue.length}`);
    console.log(`   ➕ New URLs to queue: ${newQueueUrls.length}`);
    
    // Add new URLs to scraping_queue
    if (newQueueUrls.length > 0) {
      console.log('\n➕ Adding new URLs to scraping queue...');
      
      const queueRecords = newQueueUrls.map(url => ({
        url: url,
        website_id: 'villabalisale.com',
        priority: 1,
        created_at: new Date(),
        updated_at: new Date()
      }));
      
      // Insert in batches
      const batchSize = 100;
      for (let i = 0; i < queueRecords.length; i += batchSize) {
        const batch = queueRecords.slice(i, i + batchSize);
        await db.insert(scrapingQueue).values(batch);
        console.log(`   ✅ Added batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(queueRecords.length / batchSize)}`);
      }
      
      console.log(`✅ Added ${newQueueUrls.length} new URLs to scraping queue`);
    }
    
    // Final status
    const finalQueueCount = await db
      .select({ count: sql`count(*)` })
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'villabalisale.com'));
    
    const finalDiscoveredCount = await db
      .select({ count: sql`count(*)` })
      .from(discoveredUrls)
      .where(eq(discoveredUrls.website_id, 'villabalisale.com'));
    
    console.log('\n🎯 Final Status:');
    console.log(`   📊 Total discovered URLs: ${finalDiscoveredCount[0].count}`);
    console.log(`   🔄 Total queue URLs: ${finalQueueCount[0].count}`);
    console.log(`   ✅ Property URLs found: ${propertyUrls.length}`);
    console.log(`   ➕ New URLs added: ${newQueueUrls.length}`);
    
    if (finalQueueCount[0].count > 0) {
      console.log('\n🎉 SUCCESS! Villa Bali Sale now has property URLs in the queue!');
      console.log('✅ Ready for scraping and property extraction');
      console.log(`🏠 ${finalQueueCount[0].count} properties ready to be scraped`);
    } else {
      console.log('\n⚠️  No URLs were added to the queue');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    closeConnection();
  }
}

discoverVillaBaliSaleProperties();
