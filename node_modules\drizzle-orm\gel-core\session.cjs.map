{"version": 3, "sources": ["../../src/gel-core/session.ts"], "sourcesContent": ["import { type Cache, hashQuery, NoopCache } from '~/cache/core/cache.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { DrizzleQueryError, TransactionRollbackError } from '~/errors.ts';\nimport type { TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport type { Query, SQL } from '~/sql/index.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { NeonAuthToken } from '~/utils.ts';\nimport { GelDatabase } from './db.ts';\nimport type { GelDialect } from './dialect.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport interface PreparedQueryConfig {\n\texecute: unknown;\n\tall: unknown;\n\tvalues: unknown;\n}\n\nexport abstract class GelPreparedQuery<T extends PreparedQueryConfig> implements PreparedQuery {\n\tconstructor(\n\t\tprotected query: Query,\n\t\tprivate cache?: Cache,\n\t\t// per query related metadata\n\t\tprivate queryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\t// config that was passed through $withCache\n\t\tprivate cacheConfig?: WithCacheConfig,\n\t) {\n\t\t// it means that no $withCache options were passed and it should be just enabled\n\t\tif (cache && cache.strategy() === 'all' && cacheConfig === undefined) {\n\t\t\tthis.cacheConfig = { enable: true, autoInvalidate: true };\n\t\t}\n\t\tif (!this.cacheConfig?.enable) {\n\t\t\tthis.cacheConfig = undefined;\n\t\t}\n\t}\n\n\t/** @internal */\n\tprotected async queryWithCache<T>(\n\t\tqueryString: string,\n\t\tparams: any[],\n\t\tquery: () => Promise<T>,\n\t): Promise<T> {\n\t\tif (this.cache === undefined || is(this.cache, NoopCache) || this.queryMetadata === undefined) {\n\t\t\ttry {\n\t\t\t\treturn await query();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\t// don't do any mutations, if globally is false\n\t\tif (this.cacheConfig && !this.cacheConfig.enable) {\n\t\t\ttry {\n\t\t\t\treturn await query();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\t// For mutate queries, we should query the database, wait for a response, and then perform invalidation\n\t\tif (\n\t\t\t(\n\t\t\t\tthis.queryMetadata.type === 'insert' || this.queryMetadata.type === 'update'\n\t\t\t\t|| this.queryMetadata.type === 'delete'\n\t\t\t) && this.queryMetadata.tables.length > 0\n\t\t) {\n\t\t\ttry {\n\t\t\t\tconst [res] = await Promise.all([\n\t\t\t\t\tquery(),\n\t\t\t\t\tthis.cache.onMutate({ tables: this.queryMetadata.tables }),\n\t\t\t\t]);\n\t\t\t\treturn res;\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\t// don't do any reads if globally disabled\n\t\tif (!this.cacheConfig) {\n\t\t\ttry {\n\t\t\t\treturn await query();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\tif (this.queryMetadata.type === 'select') {\n\t\t\tconst fromCache = await this.cache.get(\n\t\t\t\tthis.cacheConfig.tag ?? await hashQuery(queryString, params),\n\t\t\t\tthis.queryMetadata.tables,\n\t\t\t\tthis.cacheConfig.tag !== undefined,\n\t\t\t\tthis.cacheConfig.autoInvalidate,\n\t\t\t);\n\t\t\tif (fromCache === undefined) {\n\t\t\t\tlet result;\n\t\t\t\ttry {\n\t\t\t\t\tresult = await query();\n\t\t\t\t} catch (e) {\n\t\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t\t}\n\n\t\t\t\t// put actual key\n\t\t\t\tawait this.cache.put(\n\t\t\t\t\tthis.cacheConfig.tag ?? await hashQuery(queryString, params),\n\t\t\t\t\tresult,\n\t\t\t\t\t// make sure we send tables that were used in a query only if user wants to invalidate it on each write\n\t\t\t\t\tthis.cacheConfig.autoInvalidate ? this.queryMetadata.tables : [],\n\t\t\t\t\tthis.cacheConfig.tag !== undefined,\n\t\t\t\t\tthis.cacheConfig.config,\n\t\t\t\t);\n\t\t\t\t// put flag if we should invalidate or not\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\treturn fromCache as unknown as T;\n\t\t}\n\t\ttry {\n\t\t\treturn await query();\n\t\t} catch (e) {\n\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t}\n\t}\n\n\tprotected authToken?: NeonAuthToken;\n\n\tgetQuery(): Query {\n\t\treturn this.query;\n\t}\n\n\tmapResult(response: unknown, _isFromBatch?: boolean): unknown {\n\t\treturn response;\n\t}\n\n\tstatic readonly [entityKind]: string = 'GelPreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\tabstract execute(placeholderValues?: Record<string, unknown>): Promise<T['execute']>;\n\n\t/** @internal */\n\tabstract all(placeholderValues?: Record<string, unknown>): Promise<T['all']>;\n\n\t/** @internal */\n\tabstract isResponseInArrayMode(): boolean;\n}\n\nexport abstract class GelSession<\n\tTQueryResult extends GelQueryResultHKT = any, // TO\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> {\n\tstatic readonly [entityKind]: string = 'GelSession';\n\n\tconstructor(protected dialect: GelDialect) {}\n\n\tabstract prepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => T['execute'],\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): GelPreparedQuery<T>;\n\n\texecute<T>(query: SQL): Promise<T> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\tconst prepared = tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\t\treturn this.prepareQuery<PreparedQueryConfig & { execute: T }>(\n\t\t\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\t\t\tundefined,\n\t\t\t\t\tundefined,\n\t\t\t\t\tfalse,\n\t\t\t\t);\n\t\t\t});\n\n\t\t\treturn prepared.execute(undefined);\n\t\t});\n\t}\n\n\tall<T = unknown>(query: SQL): Promise<T[]> {\n\t\treturn this.prepareQuery<PreparedQueryConfig & { all: T[] }>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t).all();\n\t}\n\n\tasync count(sql: SQL): Promise<number> {\n\t\tconst res = await this.execute<[{ count: string }]>(sql);\n\n\t\treturn Number(\n\t\t\tres[0]['count'],\n\t\t);\n\t}\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: GelTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T>;\n}\n\nexport abstract class GelTransaction<\n\tTQueryResult extends GelQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> extends GelDatabase<TQueryResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'GelTransaction';\n\n\tconstructor(\n\t\tdialect: GelDialect,\n\t\tsession: GelSession<any, any, any>,\n\t\tprotected schema: {\n\t\t\tfullSchema: Record<string, unknown>;\n\t\t\tschema: TSchema;\n\t\t\ttableNamesMap: Record<string, string>;\n\t\t} | undefined,\n\t) {\n\t\tsuper(dialect, session, schema);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n\n\tabstract override transaction<T>(\n\t\ttransaction: (tx: GelTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T>;\n}\n\nexport interface GelQueryResultHKT {\n\treadonly $brand: 'GelQueryResultHKT';\n\treadonly row: unknown;\n\treadonly type: unknown;\n}\n\nexport type GelQueryResultKind<TKind extends GelQueryResultHKT, TRow> = (TKind & {\n\treadonly row: TRow;\n})['type'];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAiD;AAEjD,oBAA+B;AAC/B,oBAA4D;AAI5D,qBAAuB;AAEvB,gBAA4B;AAUrB,MAAe,iBAAyE;AAAA,EAC9F,YACW,OACF,OAEA,eAKA,aACP;AATS;AACF;AAEA;AAKA;AAGR,QAAI,SAAS,MAAM,SAAS,MAAM,SAAS,gBAAgB,QAAW;AACrE,WAAK,cAAc,EAAE,QAAQ,MAAM,gBAAgB,KAAK;AAAA,IACzD;AACA,QAAI,CAAC,KAAK,aAAa,QAAQ;AAC9B,WAAK,cAAc;AAAA,IACpB;AAAA,EACD;AAAA;AAAA,EAGA,MAAgB,eACf,aACA,QACA,OACa;AACb,QAAI,KAAK,UAAU,cAAa,kBAAG,KAAK,OAAO,sBAAS,KAAK,KAAK,kBAAkB,QAAW;AAC9F,UAAI;AACH,eAAO,MAAM,MAAM;AAAA,MACpB,SAAS,GAAG;AACX,cAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAGA,QAAI,KAAK,eAAe,CAAC,KAAK,YAAY,QAAQ;AACjD,UAAI;AACH,eAAO,MAAM,MAAM;AAAA,MACpB,SAAS,GAAG;AACX,cAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAGA,SAEE,KAAK,cAAc,SAAS,YAAY,KAAK,cAAc,SAAS,YACjE,KAAK,cAAc,SAAS,aAC3B,KAAK,cAAc,OAAO,SAAS,GACvC;AACD,UAAI;AACH,cAAM,CAAC,GAAG,IAAI,MAAM,QAAQ,IAAI;AAAA,UAC/B,MAAM;AAAA,UACN,KAAK,MAAM,SAAS,EAAE,QAAQ,KAAK,cAAc,OAAO,CAAC;AAAA,QAC1D,CAAC;AACD,eAAO;AAAA,MACR,SAAS,GAAG;AACX,cAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAGA,QAAI,CAAC,KAAK,aAAa;AACtB,UAAI;AACH,eAAO,MAAM,MAAM;AAAA,MACpB,SAAS,GAAG;AACX,cAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAEA,QAAI,KAAK,cAAc,SAAS,UAAU;AACzC,YAAM,YAAY,MAAM,KAAK,MAAM;AAAA,QAClC,KAAK,YAAY,OAAO,UAAM,wBAAU,aAAa,MAAM;AAAA,QAC3D,KAAK,cAAc;AAAA,QACnB,KAAK,YAAY,QAAQ;AAAA,QACzB,KAAK,YAAY;AAAA,MAClB;AACA,UAAI,cAAc,QAAW;AAC5B,YAAI;AACJ,YAAI;AACH,mBAAS,MAAM,MAAM;AAAA,QACtB,SAAS,GAAG;AACX,gBAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,QAC5D;AAGA,cAAM,KAAK,MAAM;AAAA,UAChB,KAAK,YAAY,OAAO,UAAM,wBAAU,aAAa,MAAM;AAAA,UAC3D;AAAA;AAAA,UAEA,KAAK,YAAY,iBAAiB,KAAK,cAAc,SAAS,CAAC;AAAA,UAC/D,KAAK,YAAY,QAAQ;AAAA,UACzB,KAAK,YAAY;AAAA,QAClB;AAEA,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACR;AACA,QAAI;AACH,aAAO,MAAM,MAAM;AAAA,IACpB,SAAS,GAAG;AACX,YAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,IAC5D;AAAA,EACD;AAAA,EAEU;AAAA,EAEV,WAAkB;AACjB,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,UAAU,UAAmB,cAAiC;AAC7D,WAAO;AAAA,EACR;AAAA,EAEA,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AASD;AAEO,MAAe,WAIpB;AAAA,EAGD,YAAsB,SAAqB;AAArB;AAAA,EAAsB;AAAA,EAF5C,QAAiB,wBAAU,IAAY;AAAA,EAiBvC,QAAW,OAAwB;AAClC,WAAO,sBAAO,gBAAgB,qBAAqB,MAAM;AACxD,YAAM,WAAW,sBAAO,gBAAgB,wBAAwB,MAAM;AACrE,eAAO,KAAK;AAAA,UACX,KAAK,QAAQ,WAAW,KAAK;AAAA,UAC7B;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACD,CAAC;AAED,aAAO,SAAS,QAAQ,MAAS;AAAA,IAClC,CAAC;AAAA,EACF;AAAA,EAEA,IAAiB,OAA0B;AAC1C,WAAO,KAAK;AAAA,MACX,KAAK,QAAQ,WAAW,KAAK;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,IACD,EAAE,IAAI;AAAA,EACP;AAAA,EAEA,MAAM,MAAM,KAA2B;AACtC,UAAM,MAAM,MAAM,KAAK,QAA6B,GAAG;AAEvD,WAAO;AAAA,MACN,IAAI,CAAC,EAAE,OAAO;AAAA,IACf;AAAA,EACD;AAKD;AAEO,MAAe,uBAIZ,sBAAgD;AAAA,EAGzD,YACC,SACA,SACU,QAKT;AACD,UAAM,SAAS,SAAS,MAAM;AANpB;AAAA,EAOX;AAAA,EAZA,QAA0B,wBAAU,IAAY;AAAA,EAchD,WAAkB;AACjB,UAAM,IAAI,uCAAyB;AAAA,EACpC;AAKD;", "names": []}