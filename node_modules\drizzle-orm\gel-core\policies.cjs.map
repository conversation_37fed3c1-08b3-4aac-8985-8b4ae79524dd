{"version": 3, "sources": ["../../src/gel-core/policies.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { GelRole } from './roles.ts';\nimport type { GelTable } from './table.ts';\n\nexport type GelPolicyToOption =\n\t| 'public'\n\t| 'current_role'\n\t| 'current_user'\n\t| 'session_user'\n\t| (string & {})\n\t| GelPolicyToOption[]\n\t| GelRole;\n\nexport interface GelPolicyConfig {\n\tas?: 'permissive' | 'restrictive';\n\tfor?: 'all' | 'select' | 'insert' | 'update' | 'delete';\n\tto?: GelPolicyToOption;\n\tusing?: SQL;\n\twithCheck?: SQL;\n}\n\nexport class GelPolicy implements GelPolicyConfig {\n\tstatic readonly [entityKind]: string = 'GelPolicy';\n\n\treadonly as: GelPolicyConfig['as'];\n\treadonly for: GelPolicyConfig['for'];\n\treadonly to: GelPolicyConfig['to'];\n\treadonly using: GelPolicyConfig['using'];\n\treadonly withCheck: GelPolicyConfig['withCheck'];\n\n\t/** @internal */\n\t_linkedTable?: GelTable;\n\n\tconstructor(\n\t\treadonly name: string,\n\t\tconfig?: GelPolicyConfig,\n\t) {\n\t\tif (config) {\n\t\t\tthis.as = config.as;\n\t\t\tthis.for = config.for;\n\t\t\tthis.to = config.to;\n\t\t\tthis.using = config.using;\n\t\t\tthis.withCheck = config.withCheck;\n\t\t}\n\t}\n\n\tlink(table: GelTable): this {\n\t\tthis._linkedTable = table;\n\t\treturn this;\n\t}\n}\n\nexport function gelPolicy(name: string, config?: GelPolicyConfig) {\n\treturn new GelPolicy(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAsBpB,MAAM,UAAqC;AAAA,EAYjD,YACU,MACT,QACC;AAFQ;AAGT,QAAI,QAAQ;AACX,WAAK,KAAK,OAAO;AACjB,WAAK,MAAM,OAAO;AAClB,WAAK,KAAK,OAAO;AACjB,WAAK,QAAQ,OAAO;AACpB,WAAK,YAAY,OAAO;AAAA,IACzB;AAAA,EACD;AAAA,EAtBA,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAGT;AAAA,EAeA,KAAK,OAAuB;AAC3B,SAAK,eAAe;AACpB,WAAO;AAAA,EACR;AACD;AAEO,SAAS,UAAU,MAAc,QAA0B;AACjE,SAAO,IAAI,UAAU,MAAM,MAAM;AAClC;", "names": []}