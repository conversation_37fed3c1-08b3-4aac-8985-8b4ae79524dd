// Quick test of all sites with async scraping
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function quickTestAllSites() {
  console.log('⚡ Quick test of all sites with async scraping...\n');
  
  // Test URLs for each supported site
  const testSites = {
    betterplace: ['https://betterplace.cc/buy/properties/BPVL02232'],
    bali_home_immo: ['https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508'],
    bali_villa_realty: ['https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/']
  };
  
  const results = {};
  
  for (const [siteId, urls] of Object.entries(testSites)) {
    console.log(`🔄 Testing ${siteId}...`);
    
    try {
      const startTime = Date.now();
      const siteResults = await runExtractBatch(siteId, urls, {});
      const endTime = Date.now();
      const duration = ((endTime - startTime) / 1000).toFixed(2);
      
      const successful = siteResults.filter(r => r.ok);
      const failed = siteResults.filter(r => !r.ok);
      
      results[siteId] = {
        success: successful.length > 0,
        duration: duration,
        title: successful[0]?.title || 'N/A'
      };
      
      if (successful.length > 0) {
        console.log(`   ✅ SUCCESS in ${duration}s: ${successful[0].title.substring(0, 50)}...`);
      } else {
        console.log(`   ❌ FAILED in ${duration}s`);
      }
      
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
      results[siteId] = { success: false, error: error.message };
    }
  }
  
  // Summary
  console.log('\n📊 FINAL RESULTS:');
  console.log('='.repeat(60));
  
  const successCount = Object.values(results).filter(r => r.success).length;
  const totalCount = Object.keys(testSites).length;
  
  console.log(`✅ Working Sites: ${successCount}/${totalCount}`);
  
  Object.entries(results).forEach(([siteId, result]) => {
    const status = result.success ? '✅' : '❌';
    const info = result.success ? `${result.duration}s` : 'FAILED';
    console.log(`   ${status} ${siteId}: ${info}`);
  });
  
  if (successCount === totalCount) {
    console.log('\n🎉 ALL SITES WORKING WITH ASYNC SCRAPING!');
    console.log('🔑 Multi-key rotation system operational');
    console.log('📊 Markdown fallback system working');
    console.log('🚀 Production ready!');
  } else {
    console.log(`\n⚠️  ${totalCount - successCount} site(s) still need fixing`);
  }
}

if (require.main === module) {
  quickTestAllSites().catch(console.error);
}

module.exports = { quickTestAllSites };
