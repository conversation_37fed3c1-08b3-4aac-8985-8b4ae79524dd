{"version": 3, "sources": ["../../../src/mysql-core/columns/boolean.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlBooleanBuilderInitial<TName extends string> = MySqlBooleanBuilder<{\n\tname: TName;\n\tdataType: 'boolean';\n\tcolumnType: 'MySqlBoolean';\n\tdata: boolean;\n\tdriverParam: number | boolean;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlBooleanBuilder<T extends ColumnBuilderBaseConfig<'boolean', 'MySqlBoolean'>>\n\textends MySqlColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlBooleanBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'boolean', 'MySqlBoolean');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlBoolean<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlBoolean<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlBoolean<T extends ColumnBaseConfig<'boolean', 'MySqlBoolean'>> extends MySqlColumn<T> {\n\tstatic override readonly [entityKind]: string = 'MySqlBoolean';\n\n\tgetSQLType(): string {\n\t\treturn 'boolean';\n\t}\n\n\toverride mapFromDriverValue(value: number | boolean): boolean {\n\t\tif (typeof value === 'boolean') {\n\t\t\treturn value;\n\t\t}\n\t\treturn value === 1;\n\t}\n}\n\nexport function boolean(): MySqlBooleanBuilderInitial<''>;\nexport function boolean<TName extends string>(name: TName): MySqlBooleanBuilderInitial<TName>;\nexport function boolean(name?: string) {\n\treturn new MySqlBooleanBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAAgD;AAWzC,MAAM,4BACJ,iCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,WAAW,cAAc;AAAA,EACtC;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBAA4E,0BAAe;AAAA,EACvG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAkC;AAC7D,QAAI,OAAO,UAAU,WAAW;AAC/B,aAAO;AAAA,IACR;AACA,WAAO,UAAU;AAAA,EAClB;AACD;AAIO,SAAS,QAAQ,MAAe;AACtC,SAAO,IAAI,oBAAoB,QAAQ,EAAE;AAC1C;", "names": []}