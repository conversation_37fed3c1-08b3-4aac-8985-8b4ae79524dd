require('dotenv').config();
const fetch = require('node-fetch');

async function debugSpecificContent() {
  try {
    console.log('🔍 DEBUGGING SPECIFIC CONTENT EXTRACTION...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    
    // Get the raw markdown from Firecrawl
    const firecrawlResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: false,
        waitFor: 3000
      })
    });
    
    const firecrawlData = await firecrawlResponse.json();
    
    if (firecrawlData.success) {
      const markdown = firecrawlData.data.markdown;
      console.log(`✅ Got markdown: ${markdown.length} characters`);
      
      // Search for the specific content we know exists
      console.log('\n1. SEARCHING FOR SPECIFIC CONTENT:');
      
      const searchTerms = [
        'imagine stepping into',
        'bali\'s most promising',
        '4,230 sqm freehold',
        'bubug karangasem',
        'virgin beach',
        'east bali'
      ];
      
      searchTerms.forEach(term => {
        const found = markdown.toLowerCase().includes(term.toLowerCase());
        console.log(`${found ? '✅' : '❌'} "${term}"`);
        
        if (found) {
          // Find the line containing this term
          const lines = markdown.split('\n');
          const matchingLine = lines.find(line => line.toLowerCase().includes(term.toLowerCase()));
          if (matchingLine) {
            console.log(`   Line: ${matchingLine.substring(0, 100)}...`);
            console.log(`   Length: ${matchingLine.length} chars`);
          }
        }
      });
      
      // Look for lines that contain "Imagine stepping into"
      console.log('\n2. FINDING LINES WITH "IMAGINE STEPPING INTO":');
      const lines = markdown.split('\n');
      const imagineLines = lines.filter(line => 
        line.toLowerCase().includes('imagine stepping into')
      );
      
      console.log(`Found ${imagineLines.length} lines with "imagine stepping into":`);
      imagineLines.forEach((line, i) => {
        console.log(`\n${i + 1}. Length: ${line.length} chars`);
        console.log(`   Content: ${line}`);
        
        // Check why this line might be filtered out
        const trimmed = line.trim();
        console.log(`   Trimmed length: ${trimmed.length}`);
        console.log(`   Contains IDR: ${trimmed.toLowerCase().includes('idr')}`);
        console.log(`   Contains freehold: ${trimmed.toLowerCase().includes('freehold')}`);
        console.log(`   Contains sqm: ${trimmed.toLowerCase().includes('sqm')}`);
        console.log(`   Starts with special char: ${trimmed.startsWith('#') || trimmed.startsWith('*') || trimmed.startsWith('-')}`);
      });
      
      // Look for the full description paragraph
      console.log('\n3. LOOKING FOR FULL DESCRIPTION PARAGRAPH:');
      const fullDescLines = lines.filter(line => {
        const lower = line.toLowerCase();
        return lower.includes('imagine stepping into') && 
               lower.includes('bali') && 
               line.length > 200;
      });
      
      console.log(`Found ${fullDescLines.length} full description lines:`);
      fullDescLines.forEach((line, i) => {
        console.log(`\n${i + 1}. FULL DESCRIPTION FOUND:`);
        console.log(`   Length: ${line.length} chars`);
        console.log(`   Content: ${line}`);
      });
      
    } else {
      console.log('❌ Failed to get markdown from Firecrawl');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
  
  process.exit(0);
}

debugSpecificContent();
