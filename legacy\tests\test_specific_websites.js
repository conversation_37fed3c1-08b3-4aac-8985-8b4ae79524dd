// Test specific URLs from BetterPlace, Bali Villa Realty, and Villa Bali Sale
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { db, properties } = require('./drizzle_client');
const { desc, sql } = require('drizzle-orm');

async function testSpecificWebsites() {
  console.log('🧪 Testing Specific Websites: 3 URLs each');
  console.log('='.repeat(60));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  // Get initial count
  const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
  console.log(`📊 Initial properties in database: ${initialCount[0].count}`);
  
  // Test URLs for each website
  const testUrls = {
    betterplace: [
      'https://betterplace.cc/buy/properties/BPVL02348',
      'https://betterplace.cc/buy/properties/BPVL02347', 
      'https://betterplace.cc/buy/properties/BPVL02349'
    ],
    bali_villa_realty: [
      'https://balivillarealty.com/property/tranquil-3-bedroom-villa-for-yearly-rental-in-bali-umalas-2/',
      'https://balivillarealty.com/property/tranquil-3-bedroom-villa-for-sale-leasehold-in-bali-umalas/',
      'https://balivillarealty.com/property/2-bedroom-villa-for-sale-leasehold-in-tumbak-bayuh/'
    ],
    villa_bali_sale: [
      'https://www.villabalisale.com/realestate-property/for-sale/villa/all/canggu/villa-for-sale-in-canggu-berawa-bali-vbs001',
      'https://www.villabalisale.com/realestate-property/for-sale/villa/all/canggu/villa-for-sale-in-canggu-bali-vbs002',
      'https://www.villabalisale.com/realestate-property/for-sale/villa/all/seminyak/luxury-villa-for-sale-in-seminyak-bali-vbs003'
    ]
  };
  
  let totalProcessed = 0;
  let totalSuccessful = 0;
  const results = {};
  
  for (const [website, urls] of Object.entries(testUrls)) {
    console.log(`\n🌐 Testing ${website.toUpperCase()}`);
    console.log('-'.repeat(40));
    console.log(`📋 URLs to test:`);
    urls.forEach((url, i) => {
      console.log(`   ${i + 1}. ${url.substring(0, 70)}...`);
    });
    
    try {
      console.log(`🔄 Processing ${urls.length} URLs...`);
      const startTime = Date.now();
      
      // Use the existing batch processing function
      const batchResults = await runExtractBatch(website, urls, {
        concurrency: 2,
        timeout: 60000
      });
      
      const duration = ((Date.now() - startTime) / 1000).toFixed(1);
      
      if (batchResults && batchResults.length > 0) {
        const successful = batchResults.filter(r => r.success).length;
        const failed = batchResults.filter(r => !r.success).length;
        
        console.log(`✅ Completed in ${duration}s:`);
        console.log(`   Successful: ${successful}`);
        console.log(`   Failed: ${failed}`);
        
        totalProcessed += batchResults.length;
        totalSuccessful += successful;
        
        results[website] = {
          processed: batchResults.length,
          successful: successful,
          failed: failed,
          duration: duration
        };
        
        // Show successful results with amenities check
        const successfulResults = batchResults.filter(r => r.success);
        if (successfulResults.length > 0) {
          console.log(`\n   🏠 Successfully scraped properties:`);
          successfulResults.forEach((result, i) => {
            console.log(`   ${i + 1}. ✅ ${result.title || 'Property'}`);
            if (result.price || result.rent_price) {
              const price = result.price ? `IDR ${result.price.toLocaleString()}` : 
                           result.rent_price ? `IDR ${result.rent_price.toLocaleString()}/month` : '';
              console.log(`      💰 ${price}`);
            }
            if (result.bedrooms) {
              console.log(`      🏠 ${result.bedrooms} bedrooms`);
            }
            // Check amenities
            if (result.amenities && result.amenities.raw_amenities && result.amenities.raw_amenities.length > 0) {
              console.log(`      🎯 Amenities: ${result.amenities.raw_amenities.slice(0, 3).join(', ')}${result.amenities.raw_amenities.length > 3 ? '...' : ''}`);
            } else {
              console.log(`      ⚠️  No amenities found`);
            }
          });
        }
        
        // Show failed results
        const failedResults = batchResults.filter(r => !r.success);
        if (failedResults.length > 0) {
          console.log(`\n   ❌ Failed properties:`);
          failedResults.forEach((result, i) => {
            console.log(`   ${i + 1}. ❌ ${result.error || 'Unknown error'}`);
          });
        }
        
      } else {
        console.log(`⚠️  No results returned (${duration}s)`);
        results[website] = {
          processed: 0,
          successful: 0,
          failed: 0,
          duration: duration
        };
      }
      
    } catch (error) {
      console.error(`❌ Error processing ${website}:`, error.message);
      results[website] = {
        processed: 0,
        successful: 0,
        failed: 0,
        error: error.message
      };
    }
    
    // Wait between websites
    if (website !== Object.keys(testUrls)[Object.keys(testUrls).length - 1]) {
      console.log('⏳ Waiting 15 seconds before next website...');
      await new Promise(resolve => setTimeout(resolve, 15000));
    }
  }
  
  // Final summary
  console.log('\n📊 FINAL SUMMARY');
  console.log('='.repeat(60));
  console.log(`⏰ Completed at: ${new Date().toLocaleTimeString()}`);
  console.log(`📈 Total URLs processed: ${totalProcessed}`);
  console.log(`✅ Total successful: ${totalSuccessful}`);
  console.log(`❌ Total failed: ${totalProcessed - totalSuccessful}`);
  
  if (totalProcessed > 0) {
    console.log(`📊 Success rate: ${((totalSuccessful / totalProcessed) * 100).toFixed(1)}%`);
  }
  
  // Website breakdown
  console.log('\n📋 Results by Website:');
  Object.entries(results).forEach(([website, result]) => {
    console.log(`   ${website.toUpperCase()}:`);
    console.log(`     Processed: ${result.processed}`);
    console.log(`     Successful: ${result.successful}`);
    console.log(`     Failed: ${result.failed}`);
    console.log(`     Duration: ${result.duration}s`);
    if (result.error) {
      console.log(`     Error: ${result.error}`);
    }
  });
  
  // Check final count
  const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
  const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
  console.log(`\n🆕 New properties added to database: ${newProperties}`);
  
  console.log('\n✅ Website-specific test completed!');
  process.exit(0);
}

testSpecificWebsites().catch(error => {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
  process.exit(1);
});
