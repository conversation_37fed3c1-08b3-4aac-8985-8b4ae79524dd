// Test optimized scraping with proper rate limits (10 requests/minute)
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testOptimizedScraping() {
  console.log('🚀 Testing optimized scraping (10 requests/minute rate limit)...\n');
  
  // Test with 10 URLs to fully utilize rate limit
  const testUrls = [
    'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/',
    'https://balivillarealty.com/property/cozy-villa-3-bedrooms-for-rental-in-umalas-bali/',
    'https://balivillarealty.com/property/charming-2-bedrooms-villa-for-rental-in-babakan-canggu-bali/',
    'https://balivillarealty.com/property/cozy-villa-2-bedrooms-for-rental-in-canggu-bali/',
    'https://balivillarealty.com/property/moa-villa-canggu-by-ilot-property-bali/',
    'https://balivillarealty.com/property/tranquil-3-bedroom-villa-for-yearly-rental-in-bali-umalas/',
    'https://balivillarealty.com/property/newly-build-2-bedroom-villa-for-yearly-rental-in-kerobokan/',
    'https://balivillarealty.com/property/brand-new-3-bedroom-villa-for-yearly-rental-in-bali-kerobokan/',
    'https://balivillarealty.com/property/beautiful-4-bedroom-villa-for-monthly-rental-in-tanah-lot/',
    'https://balivillarealty.com/property/charming-3-bedroom-villa-for-monthly-rental-in-bali-canggu/'
  ];
  
  console.log(`📊 Testing with ${testUrls.length} URLs (full rate limit utilization)`);
  console.log('⏰ Expected behavior:');
  console.log('   - All 10 crawl jobs start within 60 seconds (6s intervals)');
  console.log('   - Polling every 10-30 seconds (not every 5s)');
  console.log('   - Jobs complete in 30-60 seconds each');
  console.log('   - Total time: ~60-90 seconds (not 300+ seconds)\n');
  
  try {
    const startTime = Date.now();
    console.log(`⏰ Start time: ${new Date(startTime).toLocaleTimeString()}`);
    
    const results = await runExtractBatch('bali_villa_realty', testUrls, {});
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n⏰ End time: ${new Date(endTime).toLocaleTimeString()}`);
    console.log(`⚡ Total duration: ${duration} seconds`);
    console.log(`📊 Average per URL: ${(duration / testUrls.length).toFixed(2)} seconds`);
    
    // Performance analysis
    console.log('\n📈 Performance Analysis:');
    const expectedOptimalTime = 90; // ~90 seconds for 10 URLs with optimized settings
    const expectedOldTime = 300; // ~300 seconds with old conservative settings
    
    if (duration < expectedOptimalTime * 1.5) {
      console.log('   ✅ EXCELLENT - Optimized settings working perfectly!');
      console.log(`   🎯 Target time: ~${expectedOptimalTime}s`);
      console.log(`   ⚡ Actual time: ${duration}s`);
      console.log(`   📊 Efficiency: ${((expectedOptimalTime / duration) * 100).toFixed(1)}%`);
    } else if (duration < expectedOldTime * 0.7) {
      console.log('   ✅ GOOD - Significant improvement over old settings');
      console.log(`   📈 Old expected: ~${expectedOldTime}s`);
      console.log(`   ⚡ New actual: ${duration}s`);
      console.log(`   📊 Improvement: ${((expectedOldTime / duration)).toFixed(1)}x faster`);
    } else {
      console.log('   ⚠️  May need further optimization');
      console.log(`   📊 Current: ${duration}s vs target: ${expectedOptimalTime}s`);
    }
    
    // Results summary
    console.log('\n📊 Results Summary:');
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}/${testUrls.length}`);
    console.log(`   ❌ Failed: ${failed.length}/${testUrls.length}`);
    console.log(`   📈 Success Rate: ${((successful.length / testUrls.length) * 100).toFixed(1)}%`);
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully processed properties:');
      successful.slice(0, 5).forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title.substring(0, 50)}...`);
      });
      if (successful.length > 5) {
        console.log(`   ... and ${successful.length - 5} more`);
      }
    }
    
    // Rate limit analysis
    console.log('\n🔍 Rate Limit Analysis:');
    if (failed.length === 0) {
      console.log('   ✅ No rate limit errors - optimal batch sizing');
    } else {
      const rateLimitErrors = failed.filter(r => r.error && r.error.includes('429'));
      if (rateLimitErrors.length > 0) {
        console.log(`   ⚠️  ${rateLimitErrors.length} rate limit errors - may need adjustment`);
      }
    }
    
  } catch (error) {
    console.error(`❌ Test failed: ${error.message}`);
  }
  
  console.log('\n🎯 Optimized scraping test completed!');
}

if (require.main === module) {
  testOptimizedScraping().catch(console.error);
}

module.exports = { testOptimizedScraping };
