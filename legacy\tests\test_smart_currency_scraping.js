// Test smart currency conversion in scraping pipeline
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { getCurrencyService } = require('./scrape_worker/currency_service');

async function testSmartCurrencyScraping() {
  console.log('💱 Testing smart currency conversion in scraping...\n');
  
  const currencyService = getCurrencyService();
  
  // Show current exchange rates
  console.log('1. Current exchange rates:');
  try {
    const usdRate = await currencyService.getExchangeRate('USD', 'IDR');
    const envRate = Number(process.env.USD_TO_IDR || 15800);
    
    console.log(`   Database USD→IDR: ${usdRate}`);
    console.log(`   ENV USD→IDR: ${envRate}`);
    console.log(`   Difference: ${(usdRate - envRate).toFixed(2)} IDR (${((usdRate/envRate - 1) * 100).toFixed(2)}%)`);
  } catch (error) {
    console.error(`   ❌ Error getting rates: ${error.message}`);
  }
  
  // Test with BetterPlace (USD prices)
  console.log('\n2. Testing BetterPlace with USD prices...');
  const betterplaceUrl = 'https://betterplace.cc/buy/properties/BPVL02232';
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('betterplace', [betterplaceUrl], {});
    const endTime = Date.now();
    
    console.log(`   ⏱️  Processing time: ${((endTime - startTime) / 1000).toFixed(2)}s`);
    
    if (results.length > 0 && results[0].ok) {
      const property = results[0];
      console.log(`   ✅ Property: ${property.title}`);
      console.log(`   💰 Price: ${property.price ? property.price.toLocaleString() : 'N/A'} IDR`);
      
      // Show currency conversion details
      const stats = currencyService.getCacheStats();
      console.log(`   📊 Currency cache: ${stats.cacheSize} entries`);
      stats.cachedRates.forEach(rate => {
        console.log(`      ${rate.pair}: ${rate.rate} (${rate.source})`);
      });
      
    } else {
      console.log('   ❌ No successful results');
    }
    
  } catch (error) {
    console.error(`   ❌ BetterPlace test failed: ${error.message}`);
  }
  
  // Test with Bali Villa Realty (USD rental prices)
  console.log('\n3. Testing Bali Villa Realty with USD rental prices...');
  const bvrUrl = 'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/';
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('bali_villa_realty', [bvrUrl], {});
    const endTime = Date.now();
    
    console.log(`   ⏱️  Processing time: ${((endTime - startTime) / 1000).toFixed(2)}s`);
    
    if (results.length > 0 && results[0].ok) {
      const property = results[0];
      console.log(`   ✅ Property: ${property.title}`);
      console.log(`   💰 Rent Price: ${property.rent_price ? property.rent_price.toLocaleString() : 'N/A'} IDR`);
      console.log(`   💰 Sale Price: ${property.price ? property.price.toLocaleString() : 'N/A'} IDR`);
      
    } else {
      console.log('   ❌ No successful results');
    }
    
  } catch (error) {
    console.error(`   ❌ Bali Villa Realty test failed: ${error.message}`);
  }
  
  // Compare old vs new conversion method
  console.log('\n4. Conversion method comparison:');
  const testPrices = ['$2,500', 'USD 1,800', '$5,000'];
  
  for (const testPrice of testPrices) {
    try {
      // Smart conversion (database)
      const smartPrice = await currencyService.convertPriceToIDR(testPrice);
      
      // Old conversion (ENV)
      const envRate = Number(process.env.USD_TO_IDR || 15800);
      const usdAmount = parseFloat(testPrice.replace(/[^\d.]/g, ''));
      const oldPrice = usdAmount * envRate;
      
      console.log(`   "${testPrice}":`);
      console.log(`     Smart method: ${smartPrice ? smartPrice.toLocaleString() : 'N/A'} IDR`);
      console.log(`     Old method: ${oldPrice.toLocaleString()} IDR`);
      
      if (smartPrice) {
        const difference = smartPrice - oldPrice;
        const percentDiff = ((smartPrice / oldPrice - 1) * 100).toFixed(2);
        console.log(`     Difference: ${difference.toLocaleString()} IDR (${percentDiff}%)`);
      }
      
    } catch (error) {
      console.error(`     ❌ Comparison failed: ${error.message}`);
    }
  }
  
  // Final cache stats
  console.log('\n5. Final currency service statistics:');
  const finalStats = currencyService.getCacheStats();
  console.log(`   Cache entries: ${finalStats.cacheSize}`);
  console.log(`   Last update: ${finalStats.lastUpdate}`);
  console.log('   Cached rates:');
  finalStats.cachedRates.forEach(rate => {
    console.log(`     ${rate.pair}: ${rate.rate} (${rate.source}, ${rate.date})`);
  });
  
  console.log('\n🎯 Smart currency scraping test completed!');
  console.log('\n💡 Benefits of smart currency conversion:');
  console.log('   ✅ Uses real-time database exchange rates');
  console.log('   ✅ Automatic fallback to ENV rates');
  console.log('   ✅ Performance caching during batch processing');
  console.log('   ✅ More accurate price conversions');
  
  process.exit(0);
}

if (require.main === module) {
  testSmartCurrencyScraping().catch(console.error);
}

module.exports = { testSmartCurrencyScraping };
