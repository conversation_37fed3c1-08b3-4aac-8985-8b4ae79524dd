// Check if villa_bali_sale URLs exist in scraping queue
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq, desc, sql } = require('drizzle-orm');

async function checkVillaBaliSaleQueue() {
  console.log('🔍 Checking Villa Bali Sale Queue Status');
  console.log('='.repeat(50));
  
  try {
    // Check queue stats for villa_bali_sale
    const queueStats = await db
      .select({
        status: scrapingQueue.status,
        count: sql`count(*)`.as('count')
      })
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'villa_bali_sale'))
      .groupBy(scrapingQueue.status);
    
    console.log('📊 Villa Bali Sale Queue Statistics:');
    if (queueStats.length === 0) {
      console.log('   ❌ No villa_bali_sale URLs found in queue');
    } else {
      queueStats.forEach(stat => {
        console.log(`   ${stat.status}: ${stat.count}`);
      });
    }
    
    // Get some sample URLs
    const sampleUrls = await db
      .select({
        id: scrapingQueue.id,
        url: scrapingQueue.url,
        status: scrapingQueue.status,
        priority: scrapingQueue.priority,
        created_at: scrapingQueue.created_at
      })
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'villa_bali_sale'))
      .orderBy(desc(scrapingQueue.priority), scrapingQueue.created_at)
      .limit(5);
    
    if (sampleUrls.length > 0) {
      console.log('\n📋 Sample Villa Bali Sale URLs:');
      sampleUrls.forEach((url, i) => {
        console.log(`${i+1}. ${url.url}`);
        console.log(`   Status: ${url.status}, Priority: ${url.priority}`);
        console.log(`   Created: ${new Date(url.created_at).toLocaleString()}`);
        console.log('');
      });
    }
    
    // Check all website_ids to see what's available
    console.log('\n🌐 All Website IDs in Queue:');
    const allWebsites = await db
      .select({
        website_id: scrapingQueue.website_id,
        count: sql`count(*)`.as('count')
      })
      .from(scrapingQueue)
      .groupBy(scrapingQueue.website_id)
      .orderBy(desc(sql`count(*)`));
    
    allWebsites.forEach(site => {
      console.log(`   ${site.website_id}: ${site.count} URLs`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkVillaBaliSaleQueue().then(() => process.exit(0));
