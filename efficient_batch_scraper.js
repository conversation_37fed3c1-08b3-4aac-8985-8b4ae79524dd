// Efficient Batch Scraper - Uses TRUE batch scraping instead of individual calls
require('dotenv').config();
const { db, properties, scrapingQueue } = require('./drizzle_client');
const { desc, sql, eq, and } = require('drizzle-orm');
const { chooseMapper } = require('./scrape_worker/mappers');

class EfficientBatchScraper {
  constructor() {
    // Load environment variables if not already loaded
    if (!process.env.FIRECRAWL_API_KEY) {
      require('dotenv').config();
    }
    this.apiKey = process.env.FIRECRAWL_API_KEY;
    if (!this.apiKey) {
      throw new Error('FIRECRAWL_API_KEY not found in environment variables');
    }
  }

  async processQueueEfficiently(batchSize = 25, maxBatches = 1, preselectedUrls = null) {
    console.log('🚀 Starting Efficient Batch Scraping');
    console.log(`   Batch size: ${batchSize} URLs per batch`);
    console.log(`   Max batches: ${maxBatches}`);
    console.log('='.repeat(50));

    const results = {
      totalProcessed: 0,
      totalSuccessful: 0,
      totalFailed: 0,
      batches: []
    };

    try {
      // Get initial count
      const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
      console.log(`📊 Initial properties in database: ${initialCount[0].count}`);

      for (let batchNum = 1; batchNum <= maxBatches; batchNum++) {
        console.log(`\n${'='.repeat(30)}`);
        console.log(`📦 Processing Batch ${batchNum}/${maxBatches}`);
        console.log(`${'='.repeat(30)}`);

        let queueUrls;

        if (preselectedUrls && preselectedUrls.length > 0) {
          // Use preselected URLs (for balanced processing)
          const startIndex = (batchNum - 1) * batchSize;
          const endIndex = startIndex + batchSize;
          const selectedBatch = preselectedUrls.slice(startIndex, endIndex);

          if (selectedBatch.length === 0) {
            console.log('⚠️  No more preselected URLs for this batch');
            break;
          }

          queueUrls = selectedBatch.map(url => ({
            url: url.url,
            id: url.id,
            website_id: url.website_id
          }));
        } else {
          // Get URLs from database (original behavior)
          queueUrls = await db
            .select({
              url: scrapingQueue.url,
              id: scrapingQueue.id,
              website_id: scrapingQueue.website_id
            })
            .from(scrapingQueue)
            .where(eq(scrapingQueue.status, 'pending'))
            .limit(batchSize);

          if (queueUrls.length === 0) {
            console.log('⚠️  No more pending URLs found');
            break;
          }
        }

        console.log(`📋 Found ${queueUrls.length} URLs for batch ${batchNum}:`);
        queueUrls.forEach((row, i) => {
          console.log(`   ${i + 1}. [${row.website_id}] ${row.url.substring(0, 60)}...`);
        });

        // Process this batch efficiently
        const batchResult = await this.processBatch(queueUrls, batchNum);
        
        results.batches.push(batchResult);
        results.totalProcessed += batchResult.processed;
        results.totalSuccessful += batchResult.successful;
        results.totalFailed += batchResult.failed;

        console.log(`\n✅ Batch ${batchNum} completed:`);
        console.log(`   Processed: ${batchResult.processed}`);
        console.log(`   Successful: ${batchResult.successful}`);
        console.log(`   Failed: ${batchResult.failed}`);
        console.log(`   Duration: ${batchResult.duration}s`);
      }

      // Final summary
      console.log(`\n${'='.repeat(50)}`);
      console.log('📊 FINAL SUMMARY');
      console.log(`${'='.repeat(50)}`);

      const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
      const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);

      console.log(`\n🆕 New properties added to database: ${newProperties}`);
      console.log(`📈 Overall Statistics:`);
      console.log(`   Total URLs processed: ${results.totalProcessed}`);
      console.log(`   Total successful: ${results.totalSuccessful}`);
      console.log(`   Total failed: ${results.totalFailed}`);
      console.log(`   Success rate: ${results.totalProcessed > 0 ? ((results.totalSuccessful / results.totalProcessed) * 100).toFixed(1) : 0}%`);

      // Show latest properties with description check
      if (newProperties > 0) {
        console.log('\n🏠 Latest Properties Added (with Description Check):');
        const latestProps = await db
          .select()
          .from(properties)
          .orderBy(desc(properties.created_at))
          .limit(Math.min(newProperties, 5)); // Show max 5

        latestProps.forEach((prop, i) => {
          console.log(`\n   ${i + 1}. [${prop.source_id}] ${prop.title}`);
          console.log(`      🆔 External ID: ${prop.external_id}`);
          if (prop.price) {
            console.log(`      💰 IDR ${prop.price.toLocaleString()}`);
          }
          console.log(`      🏠 ${prop.bedrooms} bed | 🚿 ${prop.bathrooms} bath`);
          console.log(`      📍 ${prop.city}, ${prop.state || 'No state'}`);

          // Check description in database (MAIN TEST!)
          if (prop.description && prop.description.length > 0) {
            console.log(`      ✅ Description: ${prop.description.length} chars`);
            console.log(`      📝 Preview: ${prop.description.substring(0, 80)}...`);
          } else {
            console.log(`      ❌ No description`);
          }

          if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
            console.log(`      ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
          }
        });

        // Description success rate
        const propsWithDescription = latestProps.filter(p => p.description && p.description.length > 0);
        console.log(`\n📊 Description Test Results:`);
        console.log(`   Properties with description: ${propsWithDescription.length}/${latestProps.length}`);
        console.log(`   Description success rate: ${((propsWithDescription.length / latestProps.length) * 100).toFixed(1)}%`);
      }

      return results;

    } catch (error) {
      console.error('❌ Efficient batch scraping failed:', error.message);
      throw error;
    }
  }

  async processBatch(queueUrls, batchNum) {
    const startTime = Date.now();
    const urls = queueUrls.map(row => row.url);

    console.log(`\n🔄 Starting TRUE batch scrape for ${urls.length} URLs...`);

    try {
      // Start the REAL batch scrape job (multiple URLs in one call)
      const scrapeResponse = await fetch('https://api.firecrawl.dev/v1/batch/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          urls: urls, // MULTIPLE URLs in one batch - this is the key difference!
          formats: ['json'], // Back to JSON with better prompts (5 credits but better data)
          jsonOptions: {
            prompt: `Extract ALL property details from this real estate listing. Be thorough and extract:

REQUIRED FIELDS:
- title: Property title/name
- price: Exact price with currency (USD, IDR, etc.)
- location: Full address or area (city, district, province)
- bedrooms: Number of bedrooms (extract from text like "3 bed", "3BR", "3 bedroom")
- bathrooms: Number of bathrooms (extract from text like "2 bath", "2BR", "2 bathroom")
- description: Full property description (combine all descriptive text)
- property_type: villa, house, apartment, land, etc.
- status: for-sale, for-rent, sold, etc.

OPTIONAL FIELDS:
- images: Array of image URLs
- property_id: Unique property ID from the website
- detail_url: URL to the property page
- size: {land_size_sqm: number, building_size_sqm: number}
- amenities: Array of amenities/features
- year_built: Construction year
- parking: Parking spaces
- furnished: furnished/unfurnished/semi-furnished

IMPORTANT:
- Extract bedrooms/bathrooms from ANY text format (3BR, 3 bed, 3 bedroom, etc.)
- Include ALL descriptive text in description field
- Be precise with price and currency
- Extract location details (area, city, province)`
          },
          onlyMainContent: true,
          timeout: 60000,
          maxConcurrency: 5,
          ignoreInvalidURLs: true,
          blockAds: true,
          proxy: 'auto',
          waitFor: 2000,
          removeBase64Images: true
        })
      });

      if (!scrapeResponse.ok) {
        const errorText = await scrapeResponse.text();
        throw new Error(`HTTP ${scrapeResponse.status}: ${errorText}`);
      }

      const scrapeData = await scrapeResponse.json();
      if (!scrapeData.success || !scrapeData.id) {
        throw new Error(`Failed to start batch scrape job: ${JSON.stringify(scrapeData)}`);
      }

      const jobId = scrapeData.id;
      console.log(`   🎯 Batch job started: ${jobId}`);

      // Poll for completion
      const maxRetries = 20;
      const intervals = [5000, 10000, 15000, 20000, 30000]; // Progressive intervals
      let attempts = 0;

      while (attempts < maxRetries) {
        const currentInterval = intervals[attempts] || 30000;
        console.log(`   ⏳ Waiting ${currentInterval/1000}s before checking status...`);
        await new Promise(resolve => setTimeout(resolve, currentInterval));

        const statusResponse = await fetch(`https://api.firecrawl.dev/v1/batch/scrape/${jobId}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`
          }
        });

        if (!statusResponse.ok) {
          throw new Error(`Status check failed: HTTP ${statusResponse.status}`);
        }

        const statusData = await statusResponse.json();
        console.log(`   📊 Status: ${statusData.status} (${statusData.completed || 0}/${statusData.total || 0})`);

        if (statusData.status === 'completed') {
          console.log(`   ✅ Batch scrape completed!`);
          
          // Process the results
          const processedResults = await this.processResults(statusData.data, queueUrls);
          
          const duration = ((Date.now() - startTime) / 1000).toFixed(1);
          return {
            processed: processedResults.length,
            successful: processedResults.filter(r => r.success).length,
            failed: processedResults.filter(r => !r.success).length,
            duration: duration,
            results: processedResults
          };
        }

        if (statusData.status === 'failed') {
          throw new Error(`Batch scrape failed: ${statusData.error || 'Unknown error'}`);
        }

        attempts++;
      }

      throw new Error('Batch scrape timed out after maximum retries');

    } catch (error) {
      const duration = ((Date.now() - startTime) / 1000).toFixed(1);
      console.error(`   ❌ Batch ${batchNum} failed: ${error.message}`);
      
      return {
        processed: urls.length,
        successful: 0,
        failed: urls.length,
        duration: duration,
        error: error.message
      };
    }
  }

  async processResults(scrapedData, queueUrls) {
    const results = [];

    for (let i = 0; i < scrapedData.length; i++) {
      const raw = scrapedData[i];
      const queueUrl = queueUrls[i];

      try {
        if (!raw || !raw.json) {
          throw new Error('No JSON data in scraped result');
        }

        // Choose the appropriate mapper based on website
        const mapper = chooseMapper(queueUrl.website_id);
        if (!mapper) {
          throw new Error(`No mapper found for website: ${queueUrl.website_id}`);
        }

        // Map the data using improved JSON extraction
        const mappedProperty = await mapper(raw.json, raw);

        // Insert into database
        const insertData = {
          title: mappedProperty.title || '',
          category: mappedProperty.category || null,
          type: mappedProperty.type || null,
          status: mappedProperty.status || 'AVAILABLE',
          address: mappedProperty.address || '',
          city: mappedProperty.city || '',
          state: mappedProperty.state || null,
          country: mappedProperty.country || 'Indonesia',
          description: mappedProperty.description || null, // NEW: Description field!
          price: mappedProperty.price || null,
          rent_price: mappedProperty.rent_price || null,
          bedrooms: mappedProperty.bedrooms || null,
          bathrooms: mappedProperty.bathrooms || null,
          parking_spaces: mappedProperty.parking_spaces || null,
          size_sqft: mappedProperty.size_sqft || null,
          lot_size_sqft: mappedProperty.lot_size_sqft || null,
          year_built: mappedProperty.year_built || null,
          ownership_type: mappedProperty.ownership_type || null,
          lease_duration_years: mappedProperty.lease_duration_years || null,
          lease_duration_text: mappedProperty.lease_duration_text || null,
          amenities: mappedProperty.amenities || null,
          media: mappedProperty.media || null,
          source_id: mappedProperty.source_id || queueUrl.website_id,
          external_id: mappedProperty.external_id || `BATCH_${Date.now()}_${i}`,
          source_url: queueUrl.url
        };

        const insertResult = await db.insert(properties).values(insertData).returning({ id: properties.id });

        // Update queue status
        await db.update(scrapingQueue)
          .set({ status: 'processed', processed_at: new Date() })
          .where(eq(scrapingQueue.id, queueUrl.id));

        results.push({
          success: true,
          id: insertResult[0]?.id,
          title: mappedProperty.title,
          description: mappedProperty.description,
          url: queueUrl.url
        });

        console.log(`   ✅ [${queueUrl.website_id}] ${mappedProperty.title}`);

      } catch (error) {
        // Update queue status as failed
        await db.update(scrapingQueue)
          .set({ 
            status: 'failed', 
            error_message: error.message,
            attempts: sql`attempts + 1`,
            processed_at: new Date()
          })
          .where(eq(scrapingQueue.id, queueUrl.id));

        results.push({
          success: false,
          error: error.message,
          url: queueUrl.url
        });

        console.log(`   ❌ [${queueUrl.website_id}] ${error.message}`);
      }
    }

    return results;
  }
}

// Test the efficient batch scraper
async function testEfficientBatchScraper() {
  const scraper = new EfficientBatchScraper();
  
  try {
    // Process 1 batch of 25 URLs (much more efficient!)
    const results = await scraper.processQueueEfficiently(25, 1);
    
    console.log('\n🎉 Efficient batch scraping test completed!');
    console.log(`📊 Final Results: ${results.totalSuccessful}/${results.totalProcessed} successful`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    process.exit(0);
  }
}

if (require.main === module) {
  testEfficientBatchScraper();
}

module.exports = { EfficientBatchScraper };
