// Quick test of mapper extraction for one URL
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function quickMapperTest() {
  console.log('🧪 Quick Mapper Test');
  console.log('='.repeat(30));
  
  try {
    // Test one URL that should have clear bedroom/bathroom info
    const testUrl = 'https://balivillarealty.com/property/cozy-1-bedroom-villa-in-prime-petitenget-location-exceptional-investment-opportunity/';
    
    console.log(`🔄 Testing: ${testUrl}`);
    console.log('Expected: 1 bedroom (from title)');
    
    const results = await runExtractBatch('bali_villa_realty', [testUrl], {});
    
    console.log('\n📊 Results structure:');
    console.log('Type:', typeof results);
    console.log('Keys:', Object.keys(results || {}));
    
    if (results && results.extractedData) {
      console.log('\n✅ Found extractedData:');
      const data = results.extractedData[0];
      if (data) {
        console.log(`   Title: "${data.title}"`);
        console.log(`   Bedrooms: ${data.bedrooms}`);
        console.log(`   Bathrooms: ${data.bathrooms}`);
        console.log(`   Size: ${data.size_sqft} sqft`);
        console.log(`   Price: ${data.price || data.rent_price}`);
        console.log(`   Year: ${data.year_built}`);
        console.log(`   Ownership: ${data.ownership_type}`);
        
        // Check if bedroom extraction worked
        if (data.bedrooms === 1) {
          console.log('\n🎉 SUCCESS: Bedroom extraction worked!');
        } else {
          console.log('\n❌ FAILED: Bedroom extraction failed');
          console.log('   Expected: 1, Got:', data.bedrooms);
        }
      } else {
        console.log('❌ No data in extractedData[0]');
      }
    } else {
      console.log('❌ No extractedData found');
    }
    
    if (results && results.processedResults) {
      console.log('\n📋 ProcessedResults:');
      results.processedResults.forEach((result, index) => {
        console.log(`   ${index + 1}. OK: ${result.ok}, Title: ${result.title}`);
        if (result.data) {
          console.log(`      Bedrooms: ${result.data.bedrooms}, Bathrooms: ${result.data.bathrooms}`);
        }
      });
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run test
if (require.main === module) {
  quickMapperTest().catch(console.error);
}

module.exports = { quickMapperTest };
