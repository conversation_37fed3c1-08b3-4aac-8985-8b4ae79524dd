// Investigate specific properties by ID
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq, or } = require('drizzle-orm');

async function investigateSpecificProperties() {
  console.log('🔍 Investigate Specific Properties');
  console.log('='.repeat(50));
  
  const propertyIds = [
    '0cb2d1f1-3174-4968-8d1d-2e94341b1161',
    'ac1bd083-918e-4b2c-80b0-ad730e949de5'
  ];
  
  try {
    // Get the specific properties by ID
    const foundProperties = await db
      .select()
      .from(properties)
      .where(or(
        eq(properties.id, propertyIds[0]),
        eq(properties.id, propertyIds[1])
      ));
    
    if (foundProperties.length === 0) {
      console.log('❌ No properties found with these IDs');
      return;
    }
    
    console.log(`📋 Found ${foundProperties.length} properties\n`);
    
    foundProperties.forEach((prop, index) => {
      console.log(`${index + 1}. 🏠 ${prop.title}`);
      console.log(`   🆔 ID: ${prop.id}`);
      console.log(`   🌐 Source: ${prop.media?.source_id || 'NULL'}`);
      console.log(`   📅 Created: ${new Date(prop.created_at).toLocaleString()}`);
      console.log('');
      
      // Check all critical fields
      console.log('   🎯 CRITICAL FIELDS:');
      console.log(`   🛏️  Bedrooms: ${prop.bedrooms || 'NULL'}`);
      console.log(`   🚿 Bathrooms: ${prop.bathrooms || 'NULL'}`);
      console.log(`   🚗 Parking: ${prop.parking_spaces || 'NULL'}`);
      console.log(`   🏞️ Lot Size: ${prop.lot_size_sqft || 'NULL'} sqft`);
      console.log(`   📋 Lease Text: ${prop.lease_duration_text || 'NULL'}`);
      console.log(`   📋 Lease Years: ${prop.lease_duration_years || 'NULL'}`);
      console.log(`   🔗 Source URL ID: ${prop.source_url_id || 'NULL'}`);
      console.log('');
      
      // Check data quality issues
      console.log('   📊 DATA QUALITY CHECK:');
      
      // Price validation
      const hasValidPrice = (prop.price && prop.price > 0) || (prop.rent_price && prop.rent_price > 0);
      console.log(`   💰 Price: ${prop.price || 'NULL'} | Rent: ${prop.rent_price || 'NULL'} - ${hasValidPrice ? '✅ Valid' : '❌ Invalid'}`);
      
      // Size validation
      const hasValidSize = prop.size_sqft && prop.size_sqft > 0;
      console.log(`   📐 Size: ${prop.size_sqft || 'NULL'} sqft - ${hasValidSize ? '✅ Valid' : '❌ Invalid'}`);
      
      // Bedroom/Bathroom validation
      const hasValidBedBath = prop.bedrooms && prop.bedrooms > 0 && prop.bathrooms && prop.bathrooms > 0;
      console.log(`   🛏️  Bed/Bath: ${prop.bedrooms}/${prop.bathrooms} - ${hasValidBedBath ? '✅ Valid' : '❌ Invalid'}`);
      
      // Year validation
      const currentYear = new Date().getFullYear();
      const hasValidYear = prop.year_built && prop.year_built >= 1900 && prop.year_built <= currentYear + 5;
      console.log(`   📅 Year: ${prop.year_built || 'NULL'} - ${hasValidYear ? '✅ Valid' : prop.year_built ? '⚠️ Suspicious' : '❌ Missing'}`);
      
      // Ownership validation
      const hasValidOwnership = prop.ownership_type && ['FREEHOLD', 'LEASEHOLD', 'RENT'].includes(prop.ownership_type);
      console.log(`   🏠 Ownership: ${prop.ownership_type || 'NULL'} - ${hasValidOwnership ? '✅ Valid' : '❌ Invalid'}`);
      
      // Location validation
      const hasValidLocation = prop.city && prop.city.length > 0;
      console.log(`   📍 Location: ${prop.city || 'NULL'} - ${hasValidLocation ? '✅ Valid' : '❌ Invalid'}`);
      
      // Description quality
      if (prop.description) {
        const descLength = prop.description.length;
        const hasProblems = prop.description.includes('Property description not available') || 
                           prop.description.includes('WhatsApp') ||
                           prop.description.includes('https://') ||
                           prop.description.includes('wp-content') ||
                           prop.description.includes('ANSWER FEW QUESTIONS');
        
        console.log(`   📝 Description: ${descLength} chars - ${hasProblems ? '❌ Has issues' : '✅ Clean'}`);
        if (hasProblems) {
          console.log(`      Preview: "${prop.description.substring(0, 100)}..."`);
        }
      } else {
        console.log(`   📝 Description: NULL - ❌ Missing`);
      }
      
      // Media validation
      const mediaInfo = prop.media || {};
      const imageCount = mediaInfo.image_count || 0;
      console.log(`   🖼️  Images: ${imageCount} - ${imageCount > 0 ? '✅ Has images' : '❌ No images'}`);
      
      // External ID validation
      const hasExternalId = prop.external_id && prop.external_id.length > 0;
      console.log(`   🔗 External ID: ${prop.external_id || 'NULL'} - ${hasExternalId ? '✅ Valid' : '❌ Missing'}`);
      
      console.log('');
      
      // Overall quality score
      let qualityScore = 0;
      let totalChecks = 9;
      
      if (hasValidPrice) qualityScore++;
      if (hasValidSize) qualityScore++;
      if (hasValidBedBath) qualityScore++;
      if (hasValidYear) qualityScore++;
      if (hasValidOwnership) qualityScore++;
      if (hasValidLocation) qualityScore++;
      if (prop.description && !prop.description.includes('Property description not available')) qualityScore++;
      if (imageCount > 0) qualityScore++;
      if (hasExternalId) qualityScore++;
      
      const qualityPercentage = ((qualityScore / totalChecks) * 100).toFixed(1);
      console.log(`   📊 QUALITY SCORE: ${qualityScore}/${totalChecks} (${qualityPercentage}%)`);
      
      if (qualityPercentage >= 80) {
        console.log(`   🎉 EXCELLENT quality!`);
      } else if (qualityPercentage >= 60) {
        console.log(`   ✅ GOOD quality`);
      } else if (qualityPercentage >= 40) {
        console.log(`   ⚠️  FAIR quality - needs improvement`);
      } else {
        console.log(`   ❌ POOR quality - major issues`);
      }
      
      console.log('\n' + '─'.repeat(80) + '\n');
    });
    
    // Summary
    console.log('📊 INVESTIGATION SUMMARY:');
    console.log('='.repeat(30));
    
    const avgQuality = foundProperties.reduce((sum, prop) => {
      let score = 0;
      if ((prop.price && prop.price > 0) || (prop.rent_price && prop.rent_price > 0)) score++;
      if (prop.size_sqft && prop.size_sqft > 0) score++;
      if (prop.bedrooms && prop.bedrooms > 0 && prop.bathrooms && prop.bathrooms > 0) score++;
      if (prop.year_built && prop.year_built >= 1900 && prop.year_built <= new Date().getFullYear() + 5) score++;
      if (prop.ownership_type && ['FREEHOLD', 'LEASEHOLD', 'RENT'].includes(prop.ownership_type)) score++;
      if (prop.city && prop.city.length > 0) score++;
      if (prop.description && !prop.description.includes('Property description not available')) score++;
      if (prop.media?.image_count > 0) score++;
      if (prop.external_id && prop.external_id.length > 0) score++;
      return sum + (score / 9 * 100);
    }, 0) / foundProperties.length;
    
    console.log(`Average Quality: ${avgQuality.toFixed(1)}%`);
    
    if (avgQuality >= 80) {
      console.log('✅ Properties are in excellent condition');
    } else if (avgQuality >= 60) {
      console.log('✅ Properties are in good condition');
    } else {
      console.log('⚠️  Properties need improvement');
      console.log('\n🔧 RECOMMENDED FIXES:');
      console.log('   - Check field extraction patterns');
      console.log('   - Verify data validation rules');
      console.log('   - Review mapper logic');
      console.log('   - Test with more diverse property types');
    }
    
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run investigation
if (require.main === module) {
  investigateSpecificProperties().catch(console.error);
}

module.exports = { investigateSpecificProperties };
