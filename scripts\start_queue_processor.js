// Start the queue processor to scrape all URLs from the queue
require('dotenv').config();
const { QueueManager } = require('../scrape_worker/queue_manager');

async function startQueueProcessor() {
  console.log('🚀 Starting Production Queue Processor\n');
  
  console.log('📊 Configuration:');
  console.log(`   ⚡ Concurrency: ${process.env.SCRAPE_CONCURRENCY || 40} requests/minute`);
  console.log(`   🔑 Firecrawl API Key: ${process.env.FIRECRAWL_API_KEY ? 'Configured' : 'Missing'}`);
  
  try {
    const queueManager = new QueueManager();
    
    console.log('\n🔄 Starting queue processor...');
    console.log('📋 This will process ALL URLs in the scraping queue');
    console.log('⏱️  Estimated time: ~30 minutes for 14,835 URLs');
    console.log('🛑 Press Ctrl+C to stop gracefully\n');
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Received shutdown signal, stopping queue processor...');
      queueManager.stop();
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      console.log('\n🛑 Received termination signal, stopping queue processor...');
      queueManager.stop();
      process.exit(0);
    });
    
    // Start processing all items in queue
    await queueManager.processAllQueue();
    
    console.log('\n🎉 Queue processing completed!');
    console.log('✅ All URLs have been processed');
    
  } catch (error) {
    console.error('❌ Error starting queue processor:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

startQueueProcessor();
