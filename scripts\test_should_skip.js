// Test shouldSkipProperty function directly
function shouldSkipProperty(status, skipSoldProperties = true) {
  console.log(`   🔍 Input: status="${status}", skipSoldProperties=${skipSoldProperties}`);
  console.log(`   🔍 Type of status: ${typeof status}`);
  console.log(`   🔍 Status === 'SOLD': ${status === 'SOLD'}`);
  console.log(`   🔍 Status === 'RENTED': ${status === 'RENTED'}`);
  console.log(`   🔍 Status === 'INACTIVE': ${status === 'INACTIVE'}`);
  
  if (skipSoldProperties && (status === 'SOLD' || status === 'RENTED' || status === 'INACTIVE')) {
    console.log(`   ✅ Should skip: true`);
    return true;
  }
  
  console.log(`   ❌ Should skip: false`);
  return false;
}

console.log('🔍 Testing shouldSkipProperty function\n');

console.log('Test 1: SOLD status');
const result1 = shouldSkipProperty('SOLD');
console.log(`Result: ${result1}\n`);

console.log('Test 2: AVAILABLE status');
const result2 = shouldSkipProperty('AVAILABLE');
console.log(`Result: ${result2}\n`);

console.log('Test 3: RENTED status');
const result3 = shouldSkipProperty('RENTED');
console.log(`Result: ${result3}\n`);

console.log('Test 4: INACTIVE status');
const result4 = shouldSkipProperty('INACTIVE');
console.log(`Result: ${result4}\n`);
