// Analyze what URLs were actually crawled and why we're missing listing pages
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function analyzeCrawlCoverage() {
  console.log('🔍 Analyzing crawl coverage and missing listing pages...\n');

  const keyManager = getKeyManager();
  
  // Use the most recent completed crawl
  const firecrawlJobId = 'b01d84cd-dc57-4542-9c5a-b76e844eac9f';
  
  try {
    const currentKey = keyManager.getCurrentKey();
    console.log(`🔑 Using key ${currentKey.index + 1}/${keyManager.keys.length}`);
    
    const response = await fetch(`https://api.firecrawl.dev/v1/crawl/${firecrawlJobId}`, {
      headers: {
        'Authorization': `Bearer ${currentKey.key}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${await response.text()}`);
    }

    const data = await response.json();
    
    console.log('📊 Crawl Overview:');
    console.log(`   Status: ${data.status}`);
    console.log(`   Total URLs: ${data.total}`);
    console.log(`   Completed: ${data.completed}`);
    console.log(`   Credits used: ${data.creditsUsed}`);
    
    if (data.data && data.data.length > 0) {
      console.log(`\n🔗 All Crawled URLs (${data.data.length}):`);
      
      const urlCategories = {
        propertyPages: [],
        listingPages: [],
        otherPages: [],
        noUrl: []
      };
      
      data.data.forEach((item, index) => {
        let url = item.url;
        
        // Extract URL from markdown if missing
        if (!url && item.markdown) {
          const urlMatch = item.markdown.match(/https:\/\/betterplace\.cc\/[^\s\)]+/);
          if (urlMatch) {
            url = urlMatch[0];
          }
        }
        
        if (!url) {
          urlCategories.noUrl.push(index);
          return;
        }
        
        // Categorize URLs
        if (url.includes('/buy/properties/') || url.includes('/rental/properties/')) {
          urlCategories.propertyPages.push(url);
        } else if (
          url.includes('/rental-results') ||
          url.includes('/buy-results') ||
          url.includes('/search') ||
          url.includes('/properties') ||
          url.includes('/listings') ||
          url.includes('/category') ||
          url.includes('/filter')
        ) {
          urlCategories.listingPages.push(url);
        } else {
          urlCategories.otherPages.push(url);
        }
      });
      
      console.log('\n📋 URL Categories:');
      console.log(`   🏠 Property Pages: ${urlCategories.propertyPages.length}`);
      console.log(`   📄 Listing Pages: ${urlCategories.listingPages.length}`);
      console.log(`   🔗 Other Pages: ${urlCategories.otherPages.length}`);
      console.log(`   ❓ No URL: ${urlCategories.noUrl.length}`);
      
      // Show listing pages found
      if (urlCategories.listingPages.length > 0) {
        console.log('\n📄 Listing Pages Found:');
        urlCategories.listingPages.forEach(url => {
          console.log(`   • ${url}`);
        });
      } else {
        console.log('\n❌ No listing pages found!');
      }
      
      // Show other pages to understand what was crawled
      console.log('\n🔗 Other Pages (first 10):');
      urlCategories.otherPages.slice(0, 10).forEach(url => {
        console.log(`   • ${url}`);
      });
      
      // Check if specific important pages were missed
      console.log('\n🎯 Checking for Important Missing Pages:');
      const importantPages = [
        'https://betterplace.cc/rental-results',
        'https://betterplace.cc/buy-results',
        'https://betterplace.cc/properties',
        'https://betterplace.cc/search',
        'https://betterplace.cc/rental',
        'https://betterplace.cc/buy'
      ];
      
      importantPages.forEach(page => {
        const found = [...urlCategories.propertyPages, ...urlCategories.listingPages, ...urlCategories.otherPages]
          .some(url => url.includes(page.split('/').pop()));
        console.log(`   ${found ? '✅' : '❌'} ${page}`);
      });
      
      // Analyze crawl patterns and exclusions
      console.log('\n🔍 Crawl Pattern Analysis:');
      console.log('Current include patterns: ["/buy/properties/*"]');
      console.log('Current exclude patterns: ["/search*", "/filter*", "/api/*"]');
      console.log('\n💡 Issues Identified:');
      console.log('   ❌ Include pattern too restrictive - only allows /buy/properties/*');
      console.log('   ❌ Exclude pattern blocks /search* which may include important listing pages');
      console.log('   ❌ Missing /rental-results, /buy-results, /rental/* patterns');
      
      // Test a specific URL manually
      console.log('\n🧪 Testing rental-results page manually...');
      try {
        const testResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${currentKey.key}`
          },
          body: JSON.stringify({
            url: 'https://betterplace.cc/rental-results',
            formats: ['markdown']
          })
        });
        
        if (testResponse.ok) {
          const testData = await testResponse.json();
          console.log('   ✅ rental-results page is accessible');
          console.log(`   📄 Content length: ${testData.data?.markdown?.length || 0} chars`);
          
          if (testData.data?.markdown) {
            const propertyLinks = testData.data.markdown.match(/https:\/\/betterplace\.cc\/rental\/properties\/[A-Z0-9]+/g);
            console.log(`   🏠 Property links found: ${propertyLinks ? propertyLinks.length : 0}`);
            if (propertyLinks) {
              console.log('   📋 Sample property links:');
              propertyLinks.slice(0, 3).forEach(link => console.log(`      • ${link}`));
            }
          }
        } else {
          console.log(`   ❌ rental-results page not accessible: ${testResponse.status}`);
        }
      } catch (error) {
        console.log(`   ❌ Error testing rental-results: ${error.message}`);
      }

    } else {
      console.log('\n❌ No crawl data available');
    }

  } catch (error) {
    console.error('❌ Error analyzing crawl coverage:', error.message);
  } finally {
    process.exit(0);
  }
}

analyzeCrawlCoverage();
