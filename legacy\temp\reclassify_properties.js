// Reclassify existing properties with new smart classification
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

// Import the classification function from mappers
function classifyBaliHomeImmoProperty(title, description) {
  const text = `${title} ${description}`.toLowerCase();

  // Commercial property detection - use more precise patterns to avoid false positives
  const commercialPatterns = [
    /\b(retail\s+shop|shop\s+for\s+sale|shop\s+for\s+rent)\b/,
    /\b(office\s+building|office\s+space|office\s+for\s+sale|office\s+for\s+rent)\b/,
    /\b(commercial\s+property|commercial\s+building|commercial\s+space)\b/,
    /\b(store\s+for\s+sale|store\s+for\s+rent|retail\s+store)\b/,
    /\b(business\s+premises|business\s+property)\b/
  ];

  // Check if any commercial pattern matches
  const isCommercial = commercialPatterns.some(pattern => pattern.test(text));

  if (isCommercial) {
    if (/\b(office\s+building|office\s+space|office\s+for\s+sale|office\s+for\s+rent)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'OFFICE' };
    }
    if (/\b(retail\s+shop|shop\s+for\s+sale|shop\s+for\s+rent|store\s+for\s+sale|store\s+for\s+rent|retail\s+store)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'RETAIL' };
    }
    return { category: 'COMMERCIAL', type: 'OTHER' };
  }
  
  // Residential property detection
  if (text.includes('villa')) return { category: 'RESIDENTIAL', type: 'VILLA' };
  if (text.includes('apartment') || text.includes('apt')) return { category: 'RESIDENTIAL', type: 'APARTMENT' };
  if (text.includes('house') || text.includes('home')) return { category: 'RESIDENTIAL', type: 'HOUSE' };
  if (text.includes('condo') || text.includes('condominium')) return { category: 'RESIDENTIAL', type: 'CONDO' };
  if (text.includes('townhouse')) return { category: 'RESIDENTIAL', type: 'TOWNHOUSE' };
  
  // Land detection
  if (text.includes('land') || text.includes('plot') || text.includes('lot')) return { category: 'LAND', type: 'LAND' };
  
  // Default to residential villa for Bali properties
  return { category: 'RESIDENTIAL', type: 'VILLA' };
}

async function reclassifyProperties() {
  console.log('🔄 Reclassifying existing properties with smart classification...\n');

  try {
    // Get all properties
    const allProperties = await db.select().from(properties);
    console.log(`📊 Found ${allProperties.length} properties to reclassify`);

    let updated = 0;
    let unchanged = 0;

    for (const property of allProperties) {
      // Get new classification
      const { category: newCategory, type: newType } = classifyBaliHomeImmoProperty(
        property.title || '', 
        '' // We don't have description in database, use empty string
      );

      // Check if classification changed
      if (property.category !== newCategory || property.type !== newType) {
        console.log(`🔄 Updating: "${property.title}"`);
        console.log(`   Old: ${property.category}/${property.type}`);
        console.log(`   New: ${newCategory}/${newType}`);

        // Update the property
        await db.update(properties)
          .set({ 
            category: newCategory, 
            type: newType 
          })
          .where(eq(properties.id, property.id));

        updated++;
      } else {
        unchanged++;
      }
    }

    console.log(`\n✅ Reclassification completed:`);
    console.log(`   📊 Total properties: ${allProperties.length}`);
    console.log(`   🔄 Updated: ${updated}`);
    console.log(`   ✅ Unchanged: ${unchanged}`);

    // Show summary by category
    console.log('\n📊 Properties by category after reclassification:');
    const summary = await db.execute(`
      SELECT category, type, COUNT(*) as count 
      FROM property 
      GROUP BY category, type 
      ORDER BY category, type
    `);

    summary.forEach(row => {
      console.log(`   ${row.category}/${row.type}: ${row.count}`);
    });

  } catch (error) {
    console.error('❌ Error reclassifying properties:', error.message);
    throw error;
  }
}

// Run the reclassification
reclassifyProperties()
  .then(() => {
    console.log('\n🎉 Property reclassification completed successfully!');
    console.log('Now all properties are correctly categorized as RESIDENTIAL, COMMERCIAL, or LAND');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Reclassification failed:', error);
    process.exit(1);
  });
