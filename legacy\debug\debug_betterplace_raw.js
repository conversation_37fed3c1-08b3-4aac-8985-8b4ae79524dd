// Debug BetterPlace with raw scraping (no JSON extraction)
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function debugBetterPlaceRaw() {
  console.log('🔍 Debugging BetterPlace with raw scraping...\n');
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();
  
  const url = 'https://betterplace.cc/buy/properties/BPVL02232';
  
  console.log(`🔑 Using key: ${currentKey.maskedKey}`);
  console.log(`🌐 Testing URL: ${url}\n`);
  
  try {
    // Test with raw scraping (no JSON extraction)
    console.log('1. Testing raw scraping without JSON extraction...');
    
    const crawlResponse = await fetch('https://api.firecrawl.dev/v1/crawl', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${currentKey.key}`
      },
      body: JSON.stringify({
        url: url,
        limit: 1,
        scrapeOptions: {
          formats: ['markdown', 'html'], // No JSON extraction
          onlyMainContent: true,
          timeout: 60000
        }
      })
    });
    
    console.log(`   Response status: ${crawlResponse.status}`);
    
    if (!crawlResponse.ok) {
      const errorText = await crawlResponse.text();
      console.log(`   ❌ Error response: ${errorText}`);
      return;
    }
    
    const crawlData = await crawlResponse.json();
    console.log(`   ✅ Crawl started successfully`);
    console.log(`   Job ID: ${crawlData.id}`);
    
    const jobId = crawlData.id;
    
    // Poll for completion
    console.log('\n2. Polling for completion...');
    let attempts = 0;
    const maxAttempts = 15;
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 5000));
      attempts++;
      
      console.log(`   Attempt ${attempts}/${maxAttempts}...`);
      
      const statusResponse = await fetch(`https://api.firecrawl.dev/v1/crawl/${jobId}`, {
        headers: {
          'Authorization': `Bearer ${currentKey.key}`
        }
      });
      
      if (!statusResponse.ok) {
        console.log(`   ⚠️  Status check failed: ${statusResponse.status}`);
        continue;
      }
      
      const statusData = await statusResponse.json();
      console.log(`   Status: ${statusData.status}`);
      
      if (statusData.status === 'completed') {
        console.log('\n3. ✅ Crawl completed! Analyzing raw data...');
        
        console.log(`   Total results: ${statusData.data?.length || 0}`);
        
        if (statusData.data && statusData.data.length > 0) {
          const result = statusData.data[0];
          console.log(`   Has markdown: ${!!result.markdown}`);
          console.log(`   Has HTML: ${!!result.html}`);
          console.log(`   URL: ${result.url}`);
          
          if (result.markdown) {
            console.log(`   Markdown length: ${result.markdown.length} characters`);
            console.log('\n📄 Markdown content (first 1000 chars):');
            console.log(result.markdown.substring(0, 1000));
            console.log('\n...(truncated)');
            
            // Look for property-specific content
            const hasPropertyInfo = result.markdown.toLowerCase().includes('bedroom') || 
                                  result.markdown.toLowerCase().includes('bathroom') ||
                                  result.markdown.toLowerCase().includes('price') ||
                                  result.markdown.toLowerCase().includes('villa');
            
            console.log(`\n🏠 Contains property info: ${hasPropertyInfo ? 'YES' : 'NO'}`);
            
            if (hasPropertyInfo) {
              console.log('\n4. ✅ BetterPlace content is accessible!');
              console.log('   Problem: JSON extraction is failing, but raw content works');
              console.log('   Solution: Use markdown parsing instead of JSON extraction');
            } else {
              console.log('\n4. ❌ BetterPlace may be blocking or redirecting');
            }
            
          } else {
            console.log('\n❌ No markdown content');
          }
        } else {
          console.log('\n❌ No data returned at all');
        }
        
        return;
        
      } else if (statusData.status === 'failed') {
        console.log(`   ❌ Crawl failed: ${statusData.error || 'Unknown error'}`);
        return;
      }
    }
    
    console.log('\n⏰ Polling timeout reached');
    
  } catch (error) {
    console.error(`❌ Debug failed: ${error.message}`);
  }
}

if (require.main === module) {
  debugBetterPlaceRaw().catch(console.error);
}

module.exports = { debugBetterPlaceRaw };
