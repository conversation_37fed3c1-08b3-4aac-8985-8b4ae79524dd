// Debug SOLD property filtering
require('dotenv').config();

// Copy the functions locally for debugging
function detectPropertyStatus(title, description) {
  const text = (title + ' ' + description).toLowerCase();
  console.log(`   🔍 Combined text: "${text}"`);

  // Check for sold indicators
  if (text.includes('(sold)') || text.includes('sold out') || text.includes('no longer available')) {
    console.log('   ✅ Detected SOLD');
    return 'SOLD';
  }

  // Check for rented indicators
  if (text.includes('(rented)') || text.includes('rented out') || text.includes('no longer for rent')) {
    console.log('   ✅ Detected RENTED');
    return 'RENTED';
  }

  // Check for pending indicators
  if (text.includes('(pending)') || text.includes('under offer') || text.includes('reserved')) {
    console.log('   ✅ Detected PENDING');
    return 'PENDING';
  }

  // Check for unavailable indicators (be specific to avoid false positives)
  if (text.includes('not available') || text.includes('no longer available') || 
      text.includes('unavailable') || text.includes('off market') ||
      text.includes('withdrawn from market') || text.includes('removed from market')) {
    console.log('   ⚠️  Detected INACTIVE');
    return 'INACTIVE';
  }

  // Default to available
  console.log('   ✅ Defaulting to AVAILABLE');
  return 'AVAILABLE';
}

function shouldSkipProperty(status, skipSoldProperties = true) {
  console.log(`   🔍 Checking if should skip: status="${status}", skipSoldProperties=${skipSoldProperties}`);
  
  if (skipSoldProperties && (status === 'SOLD' || status === 'RENTED' || status === 'INACTIVE')) {
    console.log(`   ✅ Should skip: true`);
    return true;
  }
  
  console.log(`   ❌ Should skip: false`);
  return false;
}

async function debugSoldFiltering() {
  console.log('🔍 Debugging SOLD Property Filtering\n');
  
  const title = 'Kerobokan 4 Bedroom Private Pool Villa (SOLD)';
  const description = 'This beautiful villa has been sold and is no longer available.';
  
  console.log('Step 1: Status Detection');
  console.log(`Title: "${title}"`);
  console.log(`Description: "${description}"`);
  
  const status = detectPropertyStatus(title, description);
  console.log(`Final status: ${status}`);
  
  console.log('\nStep 2: Skip Check');
  const shouldSkip = shouldSkipProperty(status);
  console.log(`Should skip: ${shouldSkip}`);
  
  console.log('\nStep 3: Expected Behavior');
  if (shouldSkip) {
    console.log('✅ Property should be filtered out (return null)');
  } else {
    console.log('❌ Property should NOT be filtered out');
  }
}

debugSoldFiltering();
