// Analyze markdown content to see what information is available
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function analyzeMarkdownContent() {
  console.log('🔍 Analyze Markdown Content');
  console.log('='.repeat(40));
  
  const keyManager = getKeyManager();
  const testUrl = 'https://balivillarealty.com/property/stunning-1-bedroom-villa-for-sale-leasehold-in-prime-petitenget/';
  
  console.log(`📍 URL: ${testUrl}`);
  console.log('🔍 Analyzing raw markdown content...\n');
  
  try {
    const key = keyManager.getKey();
    const app = new FirecrawlApp({ apiKey: key.key });
    
    console.log('⏳ Scraping content...');
    const scrapeResult = await app.scrapeUrl(testUrl, {
      formats: ['markdown'],
      onlyMainContent: true
    });
    
    if (!scrapeResult.success) {
      console.log('❌ Scraping failed');
      return;
    }
    
    const markdown = scrapeResult.markdown;
    console.log(`📄 Markdown length: ${markdown.length} characters\n`);
    
    // Search for lot size related terms
    console.log('🏞️ LOT SIZE ANALYSIS:');
    const lotSizeTerms = [
      'lot size', 'land size', 'plot size', 'site area', 'land area',
      'total area', 'lot area', 'ground area', 'property size',
      'luas tanah', 'tanah', 'are', 'sqm', 'm2', 'm²'
    ];
    
    let foundLotTerms = [];
    lotSizeTerms.forEach(term => {
      const regex = new RegExp(term, 'gi');
      const matches = markdown.match(regex);
      if (matches) {
        foundLotTerms.push(`${term}: ${matches.length} occurrences`);
      }
    });
    
    if (foundLotTerms.length > 0) {
      console.log('   ✅ Found lot size related terms:');
      foundLotTerms.forEach(term => console.log(`      - ${term}`));
      
      // Show context around lot size terms
      const lotContext = markdown.match(/.{0,50}(?:lot|land|plot|site|area|tanah|sqm|m2|m²).{0,50}/gi);
      if (lotContext) {
        console.log('\n   📝 Context examples:');
        lotContext.slice(0, 5).forEach((context, i) => {
          console.log(`      ${i+1}. "${context.trim()}"`);
        });
      }
    } else {
      console.log('   ❌ No lot size related terms found');
    }
    
    // Search for lease duration related terms
    console.log('\n📋 LEASE DURATION ANALYSIS:');
    const leaseTerms = [
      'lease', 'leasehold', 'year', 'years', 'tenure', 'term',
      'remaining', 'left', 'duration', 'period'
    ];
    
    let foundLeaseTerms = [];
    leaseTerms.forEach(term => {
      const regex = new RegExp(term, 'gi');
      const matches = markdown.match(regex);
      if (matches) {
        foundLeaseTerms.push(`${term}: ${matches.length} occurrences`);
      }
    });
    
    if (foundLeaseTerms.length > 0) {
      console.log('   ✅ Found lease related terms:');
      foundLeaseTerms.forEach(term => console.log(`      - ${term}`));
      
      // Show context around lease terms
      const leaseContext = markdown.match(/.{0,50}(?:lease|year|tenure|term|remaining|left).{0,50}/gi);
      if (leaseContext) {
        console.log('\n   📝 Context examples:');
        leaseContext.slice(0, 5).forEach((context, i) => {
          console.log(`      ${i+1}. "${context.trim()}"`);
        });
      }
    } else {
      console.log('   ❌ No lease duration related terms found');
    }
    
    // Search for parking related terms
    console.log('\n🚗 PARKING ANALYSIS:');
    const parkingTerms = [
      'parking', 'garage', 'carport', 'car park', 'car space',
      'covered parking', 'secure parking', 'tempat parkir', 'garasi'
    ];
    
    let foundParkingTerms = [];
    parkingTerms.forEach(term => {
      const regex = new RegExp(term, 'gi');
      const matches = markdown.match(regex);
      if (matches) {
        foundParkingTerms.push(`${term}: ${matches.length} occurrences`);
      }
    });
    
    if (foundParkingTerms.length > 0) {
      console.log('   ✅ Found parking related terms:');
      foundParkingTerms.forEach(term => console.log(`      - ${term}`));
      
      // Show context around parking terms
      const parkingContext = markdown.match(/.{0,50}(?:parking|garage|carport|car).{0,50}/gi);
      if (parkingContext) {
        console.log('\n   📝 Context examples:');
        parkingContext.slice(0, 5).forEach((context, i) => {
          console.log(`      ${i+1}. "${context.trim()}"`);
        });
      }
    } else {
      console.log('   ❌ No parking related terms found');
    }
    
    // Show a sample of the content structure
    console.log('\n📄 CONTENT STRUCTURE SAMPLE:');
    const lines = markdown.split('\n').filter(line => line.trim().length > 0);
    console.log(`   Total lines: ${lines.length}`);
    console.log('\n   First 20 lines:');
    lines.slice(0, 20).forEach((line, i) => {
      console.log(`   ${(i+1).toString().padStart(2)}. ${line.substring(0, 80)}${line.length > 80 ? '...' : ''}`);
    });
    
    // Look for structured data sections
    console.log('\n🔍 STRUCTURED DATA SECTIONS:');
    const structuredSections = [
      'Property Details', 'Specifications', 'Features', 'Amenities',
      'Property Information', 'Details', 'Overview', 'Description'
    ];
    
    structuredSections.forEach(section => {
      const regex = new RegExp(section, 'gi');
      if (markdown.match(regex)) {
        console.log(`   ✅ Found section: ${section}`);
        
        // Try to extract content after this section
        const sectionRegex = new RegExp(`${section}[\\s\\S]{0,500}`, 'gi');
        const sectionContent = markdown.match(sectionRegex);
        if (sectionContent) {
          console.log(`      Preview: "${sectionContent[0].substring(0, 200)}..."`);
        }
      }
    });
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
  }
}

// Run analysis
if (require.main === module) {
  analyzeMarkdownContent().catch(console.error);
}

module.exports = { analyzeMarkdownContent };
