// Direct URL Crawler - Bypasses Firecrawl for URL discovery
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');

class DirectUrlCrawler {
  constructor() {
    this.baseUrls = {
      bali_home_immo: [
        'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/',
        'https://bali-home-immo.com/realestate-property/for-rent/villa/yearly/',
        'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/',
        'https://bali-home-immo.com/realestate-property/for-sale/villa/leasehold/',
        'https://bali-home-immo.com/realestate-property/for-rent/other/monthly/',
        'https://bali-home-immo.com/realestate-property/for-rent/other/yearly/'
      ],
      betterplace: [
        'https://betterplace.cc/buy/properties/BPVL02320',
        'https://betterplace.cc/buy/properties/BPVL02321',
        'https://betterplace.cc/buy/properties/BPVL02322',
        'https://betterplace.cc/buy/properties/BPVL02323',
        'https://betterplace.cc/buy/properties/BPVL02324',
        'https://betterplace.cc/buy/properties/BPVL02325',
        'https://betterplace.cc/buy/properties/BPVL02326',
        'https://betterplace.cc/buy/properties/BPVL02327',
        'https://betterplace.cc/buy/properties/BPVL02328',
        'https://betterplace.cc/buy/properties/BPVL02329'
      ]
    };
    
    this.locationVariations = {
      bali_home_immo: [
        'canggu_north', 'canggu_south', 'canggu_center', 'canggu_berawa',
        'seminyak_center', 'seminyak_north', 'seminyak_beach',
        'ubud_center', 'ubud_north', 'ubud_tegalalang',
        'uluwatu_center', 'uluwatu_bingin', 'uluwatu_dreamland',
        'jimbaran_center', 'jimbaran_beach', 'jimbaran_hills',
        'kerobokan_center', 'kerobokan_north',
        'denpasar_center', 'sanur_center', 'nusa_dua'
      ]
    };
  }

  // Generate property URLs based on patterns
  generatePropertyUrls(websiteId, count = 50) {
    const urls = [];
    
    if (websiteId === 'bali_home_immo') {
      const basePatterns = this.baseUrls[websiteId];
      const locations = this.locationVariations[websiteId];
      
      for (let i = 0; i < count && urls.length < count; i++) {
        const basePattern = basePatterns[i % basePatterns.length];
        const location = locations[i % locations.length];
        
        // Generate URL with location
        const url = basePattern + location;
        urls.push({
          website_id: websiteId,
          url: url,
          priority: 7,
          source: 'pattern_generated'
        });
      }
    }
    
    if (websiteId === 'betterplace') {
      const baseUrls = this.baseUrls[websiteId];
      
      // Generate sequential property IDs
      for (let i = 0; i < count; i++) {
        const propertyId = 2320 + i; // Start from BPVL02320
        const url = `https://betterplace.cc/buy/properties/BPVL${propertyId.toString().padStart(5, '0')}`;
        
        urls.push({
          website_id: websiteId,
          url: url,
          priority: 7,
          source: 'sequential_generated'
        });
      }
    }
    
    return urls;
  }

  // Add URLs directly to queue
  async addUrlsToQueue(websiteId, count = 30) {
    console.log(`➕ Generating ${count} URLs for ${websiteId}...`);
    
    const urls = this.generatePropertyUrls(websiteId, count);
    let added = 0;
    let skipped = 0;
    
    for (const urlData of urls) {
      try {
        await db.insert(scrapingQueue).values({
          website_id: urlData.website_id,
          url: urlData.url,
          priority: urlData.priority,
          status: 'pending',
          attempts: 0,
          created_at: new Date(),
          updated_at: new Date()
        });
        
        added++;
        console.log(`✅ Added: ${urlData.url.substring(0, 70)}...`);
        
      } catch (error) {
        if (error.message.includes('duplicate') || error.message.includes('UNIQUE')) {
          skipped++;
          // console.log(`⚠️  Skipped (duplicate): ${urlData.url.substring(0, 70)}...`);
        } else {
          console.log(`❌ Error adding ${urlData.url}: ${error.message}`);
        }
      }
    }
    
    console.log(`\n📊 ${websiteId} Results:`);
    console.log(`   ✅ Added: ${added} URLs`);
    console.log(`   ⚠️  Skipped (duplicates): ${skipped} URLs`);
    
    return { added, skipped };
  }

  // Bulk add URLs for all websites
  async bulkAddUrls() {
    console.log('🚀 Starting bulk URL addition...');
    
    const websites = ['bali_home_immo', 'betterplace'];
    const results = {};
    
    for (const websiteId of websites) {
      console.log(`\n🔍 Processing ${websiteId}...`);
      results[websiteId] = await this.addUrlsToQueue(websiteId, 50);
      
      // Wait between websites
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    const totalAdded = Object.values(results).reduce((sum, result) => sum + result.added, 0);
    const totalSkipped = Object.values(results).reduce((sum, result) => sum + result.skipped, 0);
    
    console.log(`\n🎯 TOTAL RESULTS:`);
    console.log(`   ✅ Total URLs added: ${totalAdded}`);
    console.log(`   ⚠️  Total skipped: ${totalSkipped}`);
    console.log(`   📋 Queue should now have ${totalAdded} new pending URLs`);
    
    return results;
  }

  // Check current queue status
  async checkQueueStatus() {
    const { eq } = require('drizzle-orm');
    
    const pending = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'pending'));
    const processing = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'processing'));
    
    console.log(`\n📊 Current Queue Status:`);
    console.log(`   📋 Pending: ${pending.length}`);
    console.log(`   ⚡ Processing: ${processing.length}`);
    
    if (pending.length > 0) {
      console.log(`\n📋 Next URLs to process:`);
      pending.slice(0, 5).forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.website_id}: ${item.url.substring(0, 60)}...`);
      });
    }
    
    return { pending: pending.length, processing: processing.length };
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const websiteId = args[1];

  const crawler = new DirectUrlCrawler();

  try {
    switch (command) {
      case 'bulk':
        await crawler.bulkAddUrls();
        await crawler.checkQueueStatus();
        break;

      case 'add':
        if (!websiteId) {
          console.log('Usage: node direct_url_crawler.js add <website_id> [count]');
          process.exit(1);
        }
        const count = parseInt(args[2]) || 30;
        await crawler.addUrlsToQueue(websiteId, count);
        await crawler.checkQueueStatus();
        break;

      case 'status':
        await crawler.checkQueueStatus();
        break;

      default:
        console.log('Direct URL Crawler');
        console.log('');
        console.log('Commands:');
        console.log('  bulk                    - Add URLs for all websites');
        console.log('  add <website> [count]   - Add URLs for specific website');
        console.log('  status                  - Check current queue status');
        console.log('');
        console.log('Examples:');
        console.log('  node direct_url_crawler.js bulk');
        console.log('  node direct_url_crawler.js add bali_home_immo 50');
        console.log('  node direct_url_crawler.js status');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }

  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = { DirectUrlCrawler };
