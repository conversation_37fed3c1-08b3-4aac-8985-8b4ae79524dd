// Find successful Bali Home Immo properties
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq, desc, sql } = require('drizzle-orm');

async function findSuccessfulBaliHomeImmo() {
  console.log('🔍 Finding Successful Bali Home Immo Properties');
  console.log('='.repeat(50));
  
  try {
    // Find Bali Home Immo properties with prices
    const baliHomeImmoProps = await db
      .select({
        id: properties.id,
        title: properties.title,
        price: properties.price,
        rent_price: properties.rent_price,
        size_sqft: properties.size_sqft,
        lot_size_sqft: properties.lot_size_sqft,
        media: properties.media,
        created_at: properties.created_at
      })
      .from(properties)
      .where(sql`media->>'source_id' = 'bali_home_immo'`)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`📋 Found ${baliHomeImmoProps.length} Bali Home Immo properties:`);
    
    baliHomeImmoProps.forEach((prop, i) => {
      const hasPrice = prop.price || prop.rent_price;
      const priceStatus = hasPrice ? '✅' : '❌';
      const priceValue = prop.price || prop.rent_price || 'NULL';
      
      console.log(`\n${i+1}. ${priceStatus} ${prop.title}`);
      console.log(`   💰 Price: ${priceValue}`);
      console.log(`   🏠 Building: ${prop.size_sqft} sqft`);
      console.log(`   🏞️ Lot: ${prop.lot_size_sqft} sqft`);
      console.log(`   🗓️ Created: ${new Date(prop.created_at).toLocaleString()}`);
      
      if (hasPrice) {
        console.log(`   🎯 This property has a price - good for testing!`);
      }
    });
    
    // Count successful vs failed
    const withPrice = baliHomeImmoProps.filter(p => p.price || p.rent_price);
    const withoutPrice = baliHomeImmoProps.filter(p => !p.price && !p.rent_price);
    
    console.log(`\n📊 Summary:`);
    console.log(`   ✅ With Price: ${withPrice.length}`);
    console.log(`   ❌ Without Price: ${withoutPrice.length}`);
    console.log(`   📈 Success Rate: ${((withPrice.length / baliHomeImmoProps.length) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

findSuccessfulBaliHomeImmo().then(() => process.exit(0));
