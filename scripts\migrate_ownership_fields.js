// Add ownership fields to database
require('dotenv').config();
const { db } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function migrateOwnershipFields() {
  console.log('🔄 Adding ownership fields to property table...\n');
  
  try {
    // Add ownership_type column
    console.log('1. Adding ownership_type column...');
    await db.execute(sql`
      ALTER TABLE property 
      ADD COLUMN IF NOT EXISTS ownership_type TEXT 
      CHECK (ownership_type IN ('FREEHOLD', 'LEASEHOLD', 'RENT'))
    `);
    console.log('✅ ownership_type column added');
    
    // Add lease_duration_years column
    console.log('2. Adding lease_duration_years column...');
    await db.execute(sql`
      ALTER TABLE property 
      ADD COLUMN IF NOT EXISTS lease_duration_years INTEGER
    `);
    console.log('✅ lease_duration_years column added');
    
    // Add lease_duration_text column
    console.log('3. Adding lease_duration_text column...');
    await db.execute(sql`
      ALTER TABLE property 
      ADD COLUMN IF NOT EXISTS lease_duration_text TEXT
    `);
    console.log('✅ lease_duration_text column added');
    
    // Create indexes
    console.log('4. Creating indexes...');
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_property_ownership_type 
      ON property(ownership_type)
    `);
    console.log('✅ ownership_type index created');
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_property_lease_duration 
      ON property(lease_duration_years) 
      WHERE ownership_type = 'LEASEHOLD'
    `);
    console.log('✅ lease_duration index created');
    
    // Verify the changes
    console.log('\n5. Verifying table structure...');
    const result = await db.execute(sql`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'property' 
      AND column_name IN ('ownership_type', 'lease_duration_years', 'lease_duration_text')
      ORDER BY column_name
    `);
    
    console.log('📊 New columns:');
    result.forEach(row => {
      console.log(`   ✅ ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('💡 New ownership fields are ready for use:');
    console.log('   - ownership_type: FREEHOLD | LEASEHOLD | RENT');
    console.log('   - lease_duration_years: Integer (years)');
    console.log('   - lease_duration_text: Original text');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

migrateOwnershipFields();
