require('dotenv').config();
const fetch = require('node-fetch');

async function debugBedroomBathroom() {
  try {
    console.log('🔍 DEBUGGING BEDROOM/BATHROOM EXTRACTION...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPVL01072';
    
    // Get the raw markdown from Firecrawl
    console.log('1. GETTING RAW MARKDOWN:');
    
    const firecrawlResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: false,
        waitFor: 3000
      })
    });
    
    const firecrawlData = await firecrawlResponse.json();
    
    if (firecrawlData.success) {
      const markdown = firecrawlData.data.markdown;
      console.log(`✅ Got markdown: ${markdown.length} characters`);
      
      // Search for bedroom/bathroom patterns in markdown
      console.log('\n2. SEARCHING FOR BEDROOM/BATHROOM PATTERNS:');
      
      const lines = markdown.split('\n');
      
      // Look for lines containing bedroom/bathroom info
      const bedroomLines = lines.filter(line => 
        line.toLowerCase().includes('bedroom') || 
        line.toLowerCase().includes('bed')
      );
      
      const bathroomLines = lines.filter(line => 
        line.toLowerCase().includes('bathroom') || 
        line.toLowerCase().includes('bath')
      );
      
      console.log(`\n📝 LINES CONTAINING "BEDROOM" (${bedroomLines.length}):`);
      bedroomLines.forEach((line, i) => {
        console.log(`${i + 1}: ${line}`);
      });
      
      console.log(`\n🚿 LINES CONTAINING "BATHROOM" (${bathroomLines.length}):`);
      bathroomLines.forEach((line, i) => {
        console.log(`${i + 1}: ${line}`);
      });
      
      // Test current extraction patterns
      console.log('\n3. TESTING CURRENT EXTRACTION PATTERNS:');
      
      // Current bedroom patterns from the mapper
      const bedroomPatterns = [
        /(\d+)\s*bedroom/i,
        /bedroom[s]?[:\s]*(\d+)/i,
        /(\d+)\s*bed[s]?(?!\w)/i,
        /bed[s]?[:\s]*(\d+)/i
      ];
      
      const bathroomPatterns = [
        /(\d+)\s*bathroom/i,
        /bathroom[s]?[:\s]*(\d+)/i,
        /(\d+)\s*bath[s]?(?!\w)/i,
        /bath[s]?[:\s]*(\d+)/i
      ];
      
      console.log('🛏️ BEDROOM PATTERN MATCHES:');
      bedroomPatterns.forEach((pattern, i) => {
        const match = markdown.match(pattern);
        if (match) {
          console.log(`Pattern ${i + 1} (${pattern}): Found "${match[0]}" -> ${match[1]} bedrooms`);
        } else {
          console.log(`Pattern ${i + 1} (${pattern}): No match`);
        }
      });
      
      console.log('\n🚿 BATHROOM PATTERN MATCHES:');
      bathroomPatterns.forEach((pattern, i) => {
        const match = markdown.match(pattern);
        if (match) {
          console.log(`Pattern ${i + 1} (${pattern}): Found "${match[0]}" -> ${match[1]} bathrooms`);
        } else {
          console.log(`Pattern ${i + 1} (${pattern}): No match`);
        }
      });
      
      // Look for the specific HTML structure we know exists
      console.log('\n4. SEARCHING FOR SPECIFIC HTML STRUCTURE:');
      
      // Look for the details_item__info__value pattern
      const valuePattern = /details_item__info__value__ramxJ">(\d+)</g;
      const valueMatches = [...markdown.matchAll(valuePattern)];
      
      console.log(`Found ${valueMatches.length} value matches:`);
      valueMatches.forEach((match, i) => {
        console.log(`${i + 1}: ${match[1]}`);
      });
      
      // Look for bedroom/bathroom icons in context
      const bedroomIconPattern = /bedrooms\.7a6788f7\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
      const bathroomIconPattern = /bathrooms\.45d31171\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
      
      const bedroomIconMatch = markdown.match(bedroomIconPattern);
      const bathroomIconMatch = markdown.match(bathroomIconPattern);
      
      console.log('\n🔍 SEARCHING FOR "DETAILS" SECTION:');
      const detailsIndex = markdown.indexOf('Details');
      console.log(`Details section found at index: ${detailsIndex}`);

      if (detailsIndex !== -1) {
        const detailsSection = markdown.substring(detailsIndex, detailsIndex + 500);
        console.log('Details section content (first 500 chars):');
        console.log(detailsSection);
      } else {
        console.log('❌ No "Details" section found in markdown');

        // Let's search for other possible section names
        const possibleSections = ['Detail', 'Property Details', 'Property Info', 'Info', 'Specifications', 'Features'];
        for (const section of possibleSections) {
          const index = markdown.indexOf(section);
          if (index !== -1) {
            console.log(`✅ Found "${section}" at index ${index}`);
          }
        }
      }

      console.log(`\n🎯 ICON-BASED EXTRACTION:`);
      console.log(`Bedroom icon match: ${bedroomIconMatch ? bedroomIconMatch[1] + ' bedrooms' : 'Not found'}`);
      console.log(`Bathroom icon match: ${bathroomIconMatch ? bathroomIconMatch[1] + ' bathrooms' : 'Not found'}`);
      
      // Test the full mapper to see what it extracts
      console.log('\n5. TESTING FULL MAPPER:');
      const { mapBetterPlace } = require('./scrape_worker/mappers');
      
      const mappedData = await mapBetterPlace({
        markdown: markdown,
        url: testUrl
      });
      
      if (mappedData) {
        console.log(`Mapper result:`);
        console.log(`  Bedrooms: ${mappedData.bedrooms}`);
        console.log(`  Bathrooms: ${mappedData.bathrooms}`);
        console.log(`  Title: ${mappedData.title}`);
      } else {
        console.log('Mapper returned null');
      }
      
    } else {
      console.log('❌ Failed to get markdown from Firecrawl');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

debugBedroomBathroom();
