// Debug BetterPlace bathroom extraction issue
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugBetterPlaceBathroom() {
  console.log('🔍 Debug BetterPlace Bathroom Extraction');
  console.log('='.repeat(50));
  
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02174';
  
  console.log(`📡 Testing URL: ${testUrl}`);
  console.log('');
  
  try {
    // Override console.log temporarily to capture mapper debug info
    const originalLog = console.log;
    const logs = [];
    console.log = (...args) => {
      const message = args.join(' ');
      logs.push(message);
      originalLog(...args);
    };
    
    const results = await runExtractBatch('betterplace', [testUrl], {});
    
    // Restore console.log
    console.log = originalLog;
    
    if (results && results.processedResults && results.processedResults.length > 0) {
      const result = results.processedResults[0];
      if (result && result.ok && result.data) {
        const prop = result.data;
        
        console.log('\n📊 Extracted Data:');
        console.log(`Title: ${prop.title}`);
        console.log(`Bedrooms: ${prop.bedrooms}`);
        console.log(`Bathrooms: ${prop.bathrooms}`);
        console.log(`Building Size: ${prop.size_sqft} sqft`);
        console.log(`Lot Size: ${prop.lot_size_sqft} sqft`);
        console.log(`Year Built: ${prop.year_built}`);
        console.log(`Ownership: ${prop.ownership_type}`);
        
        // Look for bathroom related logs
        console.log('\n🚿 Bathroom Debug Logs:');
        const bathroomLogs = logs.filter(log => 
          log.includes('bathroom') || 
          log.includes('Bathroom') || 
          log.includes('bath') || 
          log.includes('🚿') ||
          log.includes('BA')
        );
        
        bathroomLogs.forEach(log => console.log(`   ${log}`));
        
        if (!prop.bathrooms) {
          console.log('\n❌ Bathrooms not extracted! Need to check patterns...');
        }
        
      } else {
        console.log('❌ No valid data extracted');
      }
    } else {
      console.log('❌ No results returned');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugBetterPlaceBathroom().then(() => process.exit(0));
