-- Add description column to property table
-- Migration script to add missing description field

-- Add description column
ALTER TABLE property 
ADD COLUMN IF NOT EXISTS description TEXT;

-- Add comment for documentation
COMMENT ON COLUMN property.description IS 'Property description text extracted from listings';

-- Verify the column was added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'property' 
AND column_name = 'description';
