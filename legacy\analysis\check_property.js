// Check specific property for lot_size_sqft issue
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function checkProperty() {
  console.log('🔍 Checking property f35821b7-6db6-4e87-91c3-2827f8e39fe8...');
  
  try {
    const property = await db
      .select()
      .from(properties)
      .where(eq(properties.id, 'f35821b7-6db6-4e87-91c3-2827f8e39fe8'))
      .limit(1);
    
    if (property.length === 0) {
      console.log('❌ Property not found');
      return;
    }
    
    const prop = property[0];
    console.log('\n📋 Property Details:');
    console.log(`Title: ${prop.title}`);
    console.log(`Source: ${prop.media?.source_id}`);
    console.log(`Building Size: ${prop.size_sqft} sqft`);
    console.log(`Lot Size: ${prop.lot_size_sqft} sqft`);
    console.log(`Year Built: ${prop.year_built}`);
    console.log(`Ownership: ${prop.ownership_type}`);
    console.log(`Lease Duration: ${prop.lease_duration_years} years`);
    console.log(`External ID: ${prop.media?.external_id}`);
    
    // Check if this is a recent property from our test
    const recentProps = await db
      .select({
        id: properties.id,
        title: properties.title,
        lot_size_sqft: properties.lot_size_sqft,
        size_sqft: properties.size_sqft,
        media: properties.media,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(properties.created_at)
      .limit(5);
    
    console.log('\n📋 Recent Properties (last 5):');
    recentProps.forEach((p, i) => {
      console.log(`${i+1}. ${p.title}`);
      console.log(`   Source: ${p.media?.source_id}`);
      console.log(`   Building: ${p.size_sqft} sqft`);
      console.log(`   Lot: ${p.lot_size_sqft} sqft`);
      console.log(`   Created: ${new Date(p.created_at).toLocaleString()}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkProperty().then(() => process.exit(0));
