require('dotenv').config();
const fetch = require('node-fetch');

async function debugMissingDescription() {
  try {
    console.log('🔍 DEBUGGING MISSING DESCRIPTION...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    
    // Test different Firecrawl extraction methods
    console.log('1. TESTING DIFFERENT FIRECRAWL METHODS:');
    
    // Method 1: Standard markdown extraction
    console.log('\n📄 Method 1: Standard Markdown');
    const response1 = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const data1 = await response1.json();
    if (data1.success) {
      console.log(`✅ Standard markdown: ${data1.data.markdown.length} chars`);
      
      // Search for key phrases from the description
      const markdown = data1.data.markdown.toLowerCase();
      const keyPhrases = [
        'imagine stepping into',
        'bali\'s most promising',
        '4,230 sqm freehold',
        'bubug karangasem',
        'idr 350 million',
        'virgin beach',
        'east bali\'s allure'
      ];
      
      console.log('Key phrases found:');
      keyPhrases.forEach(phrase => {
        const found = markdown.includes(phrase.toLowerCase());
        console.log(`  ${found ? '✅' : '❌'} "${phrase}"`);
      });
    } else {
      console.log('❌ Standard markdown failed');
    }
    
    // Method 2: Include all content (not just main)
    console.log('\n📄 Method 2: Full Content');
    const response2 = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: false
      })
    });
    
    const data2 = await response2.json();
    if (data2.success) {
      console.log(`✅ Full content: ${data2.data.markdown.length} chars`);
      
      const fullMarkdown = data2.data.markdown.toLowerCase();
      console.log('Key phrases in full content:');
      const keyPhrases = [
        'imagine stepping into',
        'bali\'s most promising',
        '4,230 sqm freehold',
        'bubug karangasem',
        'idr 350 million',
        'virgin beach',
        'east bali\'s allure'
      ];
      keyPhrases.forEach(phrase => {
        const found = fullMarkdown.includes(phrase.toLowerCase());
        console.log(`  ${found ? '✅' : '❌'} "${phrase}"`);
        
        if (found) {
          // Show context
          const index = fullMarkdown.indexOf(phrase.toLowerCase());
          const context = data2.data.markdown.substring(Math.max(0, index - 50), index + phrase.length + 50);
          console.log(`    Context: ...${context}...`);
        }
      });
    } else {
      console.log('❌ Full content failed');
    }
    
    // Method 3: Try with HTML format
    console.log('\n📄 Method 3: HTML Format');
    const response3 = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['html'],
        onlyMainContent: false
      })
    });
    
    const data3 = await response3.json();
    if (data3.success) {
      console.log(`✅ HTML content: ${data3.data.html.length} chars`);
      
      const htmlContent = data3.data.html.toLowerCase();
      console.log('Key phrases in HTML:');
      keyPhrases.slice(0, 3).forEach(phrase => {
        const found = htmlContent.includes(phrase.toLowerCase());
        console.log(`  ${found ? '✅' : '❌'} "${phrase}"`);
      });
    } else {
      console.log('❌ HTML content failed');
    }
    
    // Method 4: Try with waitFor to ensure JS content loads
    console.log('\n📄 Method 4: With Wait Time');
    const response4 = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: false,
        waitFor: 3000 // Wait 3 seconds for JS to load
      })
    });
    
    const data4 = await response4.json();
    if (data4.success) {
      console.log(`✅ With wait time: ${data4.data.markdown.length} chars`);
      
      const waitMarkdown = data4.data.markdown.toLowerCase();
      console.log('Key phrases with wait time:');
      keyPhrases.slice(0, 3).forEach(phrase => {
        const found = waitMarkdown.includes(phrase.toLowerCase());
        console.log(`  ${found ? '✅' : '❌'} "${phrase}"`);
        
        if (found) {
          console.log(`    ✅ FOUND THE DESCRIPTION CONTENT!`);
        }
      });
      
      // If we found content, show a sample
      if (waitMarkdown.includes('imagine stepping into')) {
        const index = waitMarkdown.indexOf('imagine stepping into');
        const sample = data4.data.markdown.substring(index, index + 200);
        console.log(`\n📝 SAMPLE DESCRIPTION CONTENT:`);
        console.log(sample + '...');
      }
    } else {
      console.log('❌ With wait time failed');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

debugMissingDescription();
