// Simple monitoring for all websites
require('dotenv').config();
const { db, properties, scrapingQueue, closeConnection } = require('../drizzle_client');
const { sql, desc } = require('drizzle-orm');

async function simpleAllWebsitesMonitor() {
  try {
    console.log('🌐 All Websites Monitoring Dashboard\n');
    console.log('='.repeat(80));
    
    // Get total counts
    const totalPropsResult = await db
      .select({ count: sql`count(*)` })
      .from(properties);
    
    const totalQueueResult = await db
      .select({ count: sql`count(*)` })
      .from(scrapingQueue);
    
    const totalProps = totalPropsResult[0]?.count || 0;
    const totalQueue = totalQueueResult[0]?.count || 0;
    
    console.log(`📊 OVERALL STATUS:`);
    console.log(`   📈 Total Properties: ${totalProps}`);
    console.log(`   🔄 URLs in Queue: ${totalQueue}`);
    console.log(`   📉 Progress: ${((14835 - totalQueue) / 14835 * 100).toFixed(1)}% complete`);
    
    if (totalProps === 0) {
      console.log('\n⚠️ No properties in database yet');
      return;
    }
    
    // Get properties by source (simple version)
    try {
      const sourceResults = await db
        .select({
          source: properties.source,
          count: sql`count(*)`.as('count')
        })
        .from(properties)
        .groupBy(properties.source)
        .orderBy(sql`count(*) DESC`);
      
      console.log('\n🌐 PROPERTIES BY WEBSITE:');
      console.log('-'.repeat(40));
      
      sourceResults.forEach(result => {
        const websiteName = getWebsiteName(result.source);
        console.log(`   ${websiteName}: ${result.count} properties`);
      });
      
    } catch (error) {
      console.log('⚠️ Could not get source breakdown:', error.message);
    }
    
    // Get queue status by website
    try {
      const queueResults = await db
        .select({
          website_id: scrapingQueue.website_id,
          count: sql`count(*)`.as('count')
        })
        .from(scrapingQueue)
        .groupBy(scrapingQueue.website_id)
        .orderBy(sql`count(*) DESC`);
      
      console.log('\n🔄 REMAINING QUEUE BY WEBSITE:');
      console.log('-'.repeat(40));
      
      queueResults.forEach(result => {
        const websiteName = getWebsiteName(result.website_id);
        const originalCount = getOriginalCount(result.website_id);
        const processed = originalCount - result.count;
        const progress = ((processed / originalCount) * 100).toFixed(1);
        console.log(`   ${websiteName}: ${result.count} remaining (${progress}% done)`);
      });
      
    } catch (error) {
      console.log('⚠️ Could not get queue status:', error.message);
    }
    
    // Get latest 10 properties
    try {
      const latestProps = await db
        .select({
          title: properties.title,
          source: properties.source,
          price: properties.price,
          rent_price: properties.rent_price,
          bedrooms: properties.bedrooms,
          city: properties.city,
          created_at: properties.created_at
        })
        .from(properties)
        .orderBy(desc(properties.created_at))
        .limit(10);
      
      console.log('\n🔍 LATEST 10 PROPERTIES:');
      console.log('-'.repeat(80));
      
      latestProps.forEach((prop, i) => {
        const websiteName = getWebsiteName(prop.source);
        console.log(`\n${i + 1}. ${prop.title?.substring(0, 50)}...`);
        console.log(`   🌐 Source: ${websiteName}`);
        
        if (prop.price) {
          console.log(`   💰 Sale: IDR ${prop.price.toLocaleString()}`);
        } else if (prop.rent_price) {
          console.log(`   🏡 Rent: IDR ${prop.rent_price.toLocaleString()}/month`);
        }
        
        console.log(`   🏠 ${prop.bedrooms || 'N/A'} bed | 📍 ${prop.city || 'N/A'}`);
        console.log(`   ⏰ ${prop.created_at?.toLocaleString()}`);
      });
      
    } catch (error) {
      console.log('⚠️ Could not get latest properties:', error.message);
    }
    
    // Check recent activity
    try {
      const recentResult = await db
        .select({ count: sql`count(*)` })
        .from(properties)
        .where(sql`created_at > NOW() - INTERVAL '5 minutes'`);
      
      const recentCount = recentResult[0]?.count || 0;
      
      console.log('\n⏱️ RECENT ACTIVITY:');
      console.log('-'.repeat(30));
      console.log(`   Properties added in last 5 minutes: ${recentCount}`);
      
      if (recentCount > 0) {
        console.log('   ✅ Scraping is actively running!');
      } else {
        console.log('   ⚠️ No recent activity');
      }
      
    } catch (error) {
      console.log('⚠️ Could not check recent activity:', error.message);
    }
    
    // Simple quality check
    try {
      const qualityResult = await db
        .select({
          with_price: sql`count(case when price is not null or rent_price is not null then 1 end)`.as('with_price'),
          with_bedrooms: sql`count(case when bedrooms is not null then 1 end)`.as('with_bedrooms'),
          with_location: sql`count(case when city is not null then 1 end)`.as('with_location'),
          total: sql`count(*)`.as('total')
        })
        .from(properties);
      
      const quality = qualityResult[0];
      if (quality && quality.total > 0) {
        console.log('\n📊 DATA QUALITY:');
        console.log('-'.repeat(30));
        console.log(`   Properties with price: ${quality.with_price}/${quality.total} (${((quality.with_price / quality.total) * 100).toFixed(1)}%)`);
        console.log(`   Properties with bedrooms: ${quality.with_bedrooms}/${quality.total} (${((quality.with_bedrooms / quality.total) * 100).toFixed(1)}%)`);
        console.log(`   Properties with location: ${quality.with_location}/${quality.total} (${((quality.with_location / quality.total) * 100).toFixed(1)}%)`);
        
        const avgQuality = ((quality.with_price + quality.with_bedrooms + quality.with_location) / (quality.total * 3)) * 100;
        console.log(`   Overall Quality: ${avgQuality.toFixed(1)}%`);
        
        if (avgQuality >= 80) {
          console.log('   🎉 EXCELLENT data quality!');
        } else if (avgQuality >= 60) {
          console.log('   ✅ GOOD data quality');
        } else {
          console.log('   ⚠️ Data quality needs improvement');
        }
      }
      
    } catch (error) {
      console.log('⚠️ Could not check data quality:', error.message);
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('🔄 Monitoring complete - run again to see updates');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    closeConnection();
  }
}

function getWebsiteName(source) {
  const names = {
    'bali_villa_realty': 'Bali Villa Realty',
    'betterplace': 'BetterPlace', 
    'bali_home_immo': 'Bali Home Immo',
    'villabalisale': 'Villa Bali Sale',
    'villabalisale.com': 'Villa Bali Sale',
    'bali_coconut_living': 'Bali Coconut Living'
  };
  return names[source] || source || 'Unknown';
}

function getOriginalCount(websiteId) {
  const counts = {
    'bali_home_immo': 8373,
    'villabalisale.com': 2867,
    'betterplace': 2382, 
    'bali_villa_realty': 1149,
    'bali_coconut_living': 64
  };
  return counts[websiteId] || 1000;
}

simpleAllWebsitesMonitor();
