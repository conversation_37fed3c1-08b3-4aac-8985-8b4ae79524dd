// Add Real Property URLs to Queue
require('dotenv').config();
const { RealPropertyFinder } = require('./real_property_finder');
const { db, scrapingQueue } = require('./drizzle_client');

async function addRealUrls() {
  console.log('🚀 Finding and adding REAL property URLs...');
  
  const finder = new RealPropertyFinder();
  const results = await finder.testWithKnownListings();
  
  console.log(`\n➕ Adding ${results.allPropertyUrls.length} real property URLs to queue...`);
  
  let added = 0;
  for (const url of results.allPropertyUrls) {
    try {
      const websiteId = url.includes('betterplace.cc') ? 'betterplace' : 'bali_home_immo';
      
      await db.insert(scrapingQueue).values({
        website_id: websiteId,
        url: url,
        priority: 9, // High priority for real URLs
        status: 'pending',
        attempts: 0,
        created_at: new Date(),
        updated_at: new Date()
      });
      
      added++;
      console.log(`✅ ${added}: ${url.substring(0, 80)}...`);
      
    } catch (error) {
      if (!error.message.includes('duplicate')) {
        console.log(`❌ Error: ${error.message}`);
      }
    }
  }
  
  console.log(`\n🎯 Added ${added} REAL property URLs to queue!`);
  
  // Check queue status
  const { eq } = require('drizzle-orm');
  const pending = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'pending'));
  console.log(`📋 Total pending URLs in queue: ${pending.length}`);
}

addRealUrls()
  .then(() => {
    console.log('✅ Real URLs added successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Error:', error);
    process.exit(1);
  });
