// Scrape 30 URLs from Bali Home Immo and Bali Villa Realty
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');

async function scrape30URLs() {
  console.log('🚀 Scraping 30 URLs from Bali Home Immo and Bali Villa Realty...\n');

  const qm = new QueueManager();

  try {
    // First, scrape 30 Bali Home Immo URLs
    console.log('🏠 Processing 30 Bali Home Immo URLs...');
    
    for (let batch = 1; batch <= 3; batch++) {
      console.log(`\n📦 Batch ${batch}/3 (10 URLs each):`);
      
      const result = await qm.processQueue('bali_home_immo', 10);
      
      console.log(`✅ Batch ${batch} completed:`);
      console.log(`   Processed: ${result.processed}`);
      console.log(`   Successful: ${result.successful}`);
      console.log(`   Failed: ${result.failed}`);
      console.log(`   Skipped: ${result.skipped}`);
      
      if (result.processed === 0) {
        console.log('⚠️  No more URLs to process for Bali Home Immo');
        break;
      }
      
      // Wait between batches to respect rate limits
      if (batch < 3) {
        console.log('⏳ Waiting 60 seconds before next batch...');
        await new Promise(resolve => setTimeout(resolve, 60000));
      }
    }

    // Check if Bali Villa Realty crawl has completed and URLs are available
    console.log('\n🏠 Checking Bali Villa Realty queue...');
    const { db, scrapingQueue } = require('./drizzle_client');
    const { eq } = require('drizzle-orm');
    
    const baliRealtyQueue = await db.select().from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'bali_villa_realty'))
      .limit(1);
    
    if (baliRealtyQueue.length > 0) {
      console.log('✅ Bali Villa Realty URLs found! Processing...');
      
      for (let batch = 1; batch <= 3; batch++) {
        console.log(`\n📦 Bali Villa Realty Batch ${batch}/3 (10 URLs each):`);
        
        const result = await qm.processQueue('bali_villa_realty', 10);
        
        console.log(`✅ Batch ${batch} completed:`);
        console.log(`   Processed: ${result.processed}`);
        console.log(`   Successful: ${result.successful}`);
        console.log(`   Failed: ${result.failed}`);
        console.log(`   Skipped: ${result.skipped}`);
        
        if (result.processed === 0) {
          console.log('⚠️  No more URLs to process for Bali Villa Realty');
          break;
        }
        
        // Wait between batches
        if (batch < 3) {
          console.log('⏳ Waiting 60 seconds before next batch...');
          await new Promise(resolve => setTimeout(resolve, 60000));
        }
      }
    } else {
      console.log('⚠️  No Bali Villa Realty URLs available yet. Crawl may still be running.');
      console.log('   You can run this script again later when the crawl completes.');
    }

    // Final summary
    console.log('\n📊 Final Summary:');
    const { properties } = require('./drizzle_client');
    const totalProps = await db.select().from(properties);
    
    const bySource = {};
    totalProps.forEach(prop => {
      bySource[prop.source_id] = (bySource[prop.source_id] || 0) + 1;
    });
    
    console.log(`   Total properties: ${totalProps.length}`);
    Object.entries(bySource).forEach(([source, count]) => {
      console.log(`   ${source}: ${count} properties`);
    });

  } catch (error) {
    console.error('❌ Error during scraping:', error.message);
    throw error;
  }
}

// Run the scraping
scrape30URLs()
  .then(() => {
    console.log('\n🎉 30 URL scraping completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Scraping failed:', error);
    process.exit(1);
  });
