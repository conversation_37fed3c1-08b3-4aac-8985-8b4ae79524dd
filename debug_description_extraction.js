require('dotenv').config();
const fetch = require('node-fetch');
const { extractCleanDescription } = require('./scrape_worker/mappers');

async function debugDescriptionExtraction() {
  try {
    console.log('🔍 DEBUGGING DESCRIPTION EXTRACTION...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    
    // Get the raw markdown from Firecrawl
    console.log('1. GETTING RAW MARKDOWN:');
    
    const firecrawlResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const firecrawlData = await firecrawlResponse.json();
    
    if (firecrawlData.success && firecrawlData.data?.markdown) {
      const markdown = firecrawlData.data.markdown;
      console.log('✅ Got markdown:', markdown.length, 'characters');
      
      // Show the markdown structure
      console.log('\n2. MARKDOWN STRUCTURE:');
      const lines = markdown.split('\n');
      console.log('Total lines:', lines.length);
      
      // Show first 20 non-empty lines
      const nonEmptyLines = lines.filter(line => line.trim().length > 0);
      console.log('Non-empty lines:', nonEmptyLines.length);
      console.log('\nFirst 20 non-empty lines:');
      nonEmptyLines.slice(0, 20).forEach((line, i) => {
        console.log(`${i + 1}: ${line.substring(0, 100)}${line.length > 100 ? '...' : ''}`);
      });
      
      // Test the extractCleanDescription function
      console.log('\n3. TESTING DESCRIPTION EXTRACTION:');
      const extractedDescription = extractCleanDescription(markdown);
      
      if (extractedDescription) {
        console.log('✅ Description extracted:');
        console.log(`Length: ${extractedDescription.length} characters`);
        console.log(`Content: ${extractedDescription}`);
      } else {
        console.log('❌ No description extracted');
        
        // Let's see what lines might be candidates
        console.log('\n4. ANALYZING POTENTIAL DESCRIPTION LINES:');
        const potentialLines = lines.filter(line => {
          const trimmed = line.trim();
          return trimmed.length > 30 && 
                 trimmed.length < 500 &&
                 !trimmed.startsWith('!') && // Not image
                 !trimmed.startsWith('http') && // Not URL
                 !trimmed.includes('digitalocean') &&
                 (trimmed.includes('land') || 
                  trimmed.includes('property') || 
                  trimmed.includes('investment') ||
                  trimmed.includes('development') ||
                  trimmed.includes('location') ||
                  trimmed.includes('area'));
        });
        
        console.log(`Found ${potentialLines.length} potential description lines:`);
        potentialLines.slice(0, 10).forEach((line, i) => {
          console.log(`${i + 1}: ${line}`);
        });
        
        // Try to create a simple description from title and basic info
        console.log('\n5. CREATING FALLBACK DESCRIPTION:');
        const title = markdown.match(/^#\s+(.+)$/m)?.[1] || 
                     markdown.match(/^##\s+(.+)$/m)?.[1] ||
                     'Property';
        
        const hasLand = title.toLowerCase().includes('land');
        const hasAre = title.toLowerCase().includes('are');
        const hasFreehold = title.toLowerCase().includes('freehold');
        const hasLocation = title.toLowerCase().includes('bali') || 
                           title.toLowerCase().includes('karangasem') ||
                           title.toLowerCase().includes('bukit');
        
        if (hasLand) {
          let fallbackDesc = `This is a ${hasFreehold ? 'freehold' : ''} land property`;
          if (hasAre) {
            const areMatch = title.match(/(\d+(?:\.\d+)?)\s*are/i);
            if (areMatch) {
              fallbackDesc += ` spanning ${areMatch[1]} are`;
            }
          }
          if (hasLocation) {
            fallbackDesc += ' located in Bali, Indonesia';
          }
          fallbackDesc += '. This property offers excellent investment potential for development or future use.';
          
          console.log('Suggested fallback description:');
          console.log(fallbackDesc);
        }
      }
      
    } else {
      console.log('❌ Failed to get markdown from Firecrawl');
      console.log('Error:', firecrawlData.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

debugDescriptionExtraction();
