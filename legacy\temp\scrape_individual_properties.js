// Scrape individual Bali Villa Realty property pages (not collection pages)
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { testConnection } = require('./drizzle_client');

async function scrapeIndividualProperties() {
  console.log('🏠 Scraping individual Bali Villa Realty property pages...\n');
  
  // Test database connection first
  console.log('1. Testing database connection...');
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ Database connection failed. Exiting.');
    return;
  }
  
  // Individual property URLs (not collection pages)
  const urls = [
    // Example from your message
    'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/',
    
    // More individual property URLs (these are examples - in real scenario you'd get these from the collection page)
    'https://balivillarealty.com/property/cozy-villa-3-bedrooms-for-rental-in-umalas-bali/',
    'https://balivillarealty.com/property/charming-2-bedrooms-villa-for-rental-in-babakan-canggu-bali/',
    'https://balivillarealty.com/property/cozy-villa-2-bedrooms-for-rental-in-canggu-bali/',
    'https://balivillarealty.com/property/moa-villa-canggu-by-ilot-property-bali/',
    'https://balivillarealty.com/property/tranquil-3-bedroom-villa-for-yearly-rental-in-bali-umalas/',
    'https://balivillarealty.com/property/newly-build-2-bedroom-villa-for-yearly-rental-in-kerobokan/',
    'https://balivillarealty.com/property/brand-new-3-bedroom-villa-for-yearly-rental-in-bali-kerobokan/',
    'https://balivillarealty.com/property/beautiful-4-bedroom-villa-for-monthly-rental-in-tanah-lot/',
    'https://balivillarealty.com/property/charming-3-bedroom-villa-for-monthly-rental-in-bali-canggu/'
  ];
  
  console.log(`\n2. Scraping ${urls.length} individual property URLs...`);
  console.log('Individual property URLs to scrape:');
  urls.forEach((url, index) => {
    console.log(`   ${index + 1}. ${url}`);
  });
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('bali_villa_realty', urls, {});
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n3. ✅ Scraping completed in ${duration} seconds!`);
    console.log('\n📊 Results Summary:');
    
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}`);
    console.log(`   ❌ Failed: ${failed.length}`);
    console.log(`   📈 Success Rate: ${((successful.length / results.length) * 100).toFixed(1)}%`);
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully scraped properties:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title}`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ Failed properties:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. Error: ${result.error}`);
      });
    }
    
    // Show what new data we got
    console.log('\n📋 Checking for new properties with complete data...');
    
  } catch (error) {
    console.error(`❌ Batch scraping failed:`, error.message);
  }
  
  console.log('\n🎉 Individual property scraping completed!');
}

// Run the scraping
if (require.main === module) {
  scrapeIndividualProperties().catch(console.error);
}

module.exports = { scrapeIndividualProperties };
