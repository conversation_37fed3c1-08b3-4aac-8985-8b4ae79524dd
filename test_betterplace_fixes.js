const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testBetterPlaceFixes() {
  try {
    console.log('🧪 TESTING BETTERPLACE FIXES...\n');
    
    // Test the problematic LAND property
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    console.log('Testing URL:', testUrl);
    console.log('Expected: LAND property with proper description and amenities\n');
    
    const results = await runExtractBatch('betterplace', [testUrl], {});
    
    if (results && results.extractedData && results.extractedData.length > 0) {
      const property = results.extractedData[0];
      
      console.log('✅ EXTRACTION RESULTS:');
      console.log('- Title:', property.title);
      console.log('- Type:', property.type);
      console.log('- Category:', property.category);
      console.log('- Status:', property.status);
      console.log('- Description length:', property.description?.length || 0);
      console.log('- Description preview:', property.description?.substring(0, 100) + '...');
      console.log('- Amenities type:', typeof property.amenities);
      console.log('- Amenities:', property.amenities);
      console.log('- Price:', property.price);
      console.log('- Location:', property.address);
      console.log('- City:', property.city);
      console.log('- State:', property.state);
      console.log('- Ownership:', property.ownership_type);
      console.log('- Land Size (sqft):', property.lot_size_sqft);
      
      console.log('\n📊 VALIDATION:');
      
      // Check if fixes worked
      const fixes = {
        'Type is LAND': property.type === 'LAND',
        'Category is LAND': property.category === 'LAND',
        'Has description': property.description && property.description.length > 30,
        'Amenities is array': Array.isArray(property.amenities),
        'Has price': property.price && property.price > 0,
        'Has location': property.address && property.address.length > 0
      };
      
      Object.entries(fixes).forEach(([check, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${check}`);
      });
      
      const passedCount = Object.values(fixes).filter(Boolean).length;
      const totalCount = Object.keys(fixes).length;
      
      console.log(`\n🎯 SCORE: ${passedCount}/${totalCount} fixes working`);
      
      if (passedCount === totalCount) {
        console.log('🎉 ALL FIXES WORKING PERFECTLY!');
      } else {
        console.log('⚠️  Some fixes still need work');
      }
      
    } else {
      console.log('❌ No results returned from extraction');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

testBetterPlaceFixes();
