require('dotenv').config();
const { Pool } = require('pg');

async function checkQueueStatus() {
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    console.log('🔍 Checking queue status values...');
    
    // Check status distribution
    const statusResult = await pool.query(`
      SELECT status, COUNT(*) as count
      FROM scraping_queue 
      GROUP BY status
      ORDER BY count DESC
    `);
    
    console.log('📊 Status distribution:');
    statusResult.rows.forEach(row => {
      console.log(`  - ${row.status || 'NULL'}: ${row.count} items`);
    });
    
    // Check recent items
    const recentResult = await pool.query(`
      SELECT url, status, scheduled_for, attempts, error_message
      FROM scraping_queue 
      ORDER BY updated_at DESC NULLS LAST
      LIMIT 5
    `);
    
    console.log('\n📋 Recent queue items:');
    recentResult.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.url}`);
      console.log(`   Status: ${row.status || 'NULL'}`);
      console.log(`   Scheduled: ${row.scheduled_for || 'NULL'}`);
      console.log(`   Attempts: ${row.attempts || 0}`);
      console.log(`   Error: ${row.error_message || 'None'}`);
    });
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkQueueStatus();
