// Debug BetterPlace scraping issues
require('dotenv').config();
const Firecrawl = require('firecrawl').default;
const { mapBetterPlace } = require('./scrape_worker/mappers');
const { validateProperty } = require('./scrape_worker/validate');

async function debugBetterPlaceScraping() {
  console.log('🔍 Debugging BetterPlace Scraping Issues');
  console.log('='.repeat(60));
  
  const firecrawl = new Firecrawl({ apiKey: process.env.FIRECRAWL_API_KEY });
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02348';
  
  try {
    console.log(`🔄 Testing URL: ${testUrl}`);
    
    // Step 1: Test Firecrawl extraction
    console.log('\n📡 Step 1: Firecrawl Extraction');
    const result = await firecrawl.scrapeUrl(testUrl, {
      formats: ['json'],
      jsonOptions: {
        prompt: "Extract property details including title, price, bedrooms, bathrooms, location, description, amenities, images, property_id, ownership_type, furnishing, pool_type, parking_type, size information",
        schema: {
          type: "object",
          properties: {
            title: { type: "string" },
            price: { type: "string" },
            location: { type: "string" },
            bedrooms: { type: "integer" },
            bathrooms: { type: "integer" },
            description: { type: "string" },
            amenities: { type: "array", items: { type: "string" } },
            images: { type: "array", items: { type: "string" } },
            property_id: { type: "string" },
            ownership_type: { type: "string" },
            furnishing: { type: "string" },
            pool_type: { type: "string" },
            parking_type: { type: "string" },
            size: {
              type: "object",
              properties: {
                building_size_sqm: { type: "number" },
                land_size_sqm: { type: "number" }
              }
            }
          }
        }
      },
      onlyMainContent: true,
      timeout: 60000
    });
    
    if (result.success && result.data) {
      console.log('✅ Firecrawl extraction successful');
      console.log('📊 Raw data structure:');
      console.log('Keys:', Object.keys(result.data));
      console.log('Title:', result.data.title);
      console.log('Price:', result.data.price);
      console.log('Location:', result.data.location);
      console.log('Bedrooms:', result.data.bedrooms);
      console.log('Bathrooms:', result.data.bathrooms);
      console.log('Description length:', result.data.description?.length || 0);
      console.log('Amenities:', result.data.amenities);
      console.log('Property ID:', result.data.property_id);
      console.log('Furnishing:', result.data.furnishing);
      console.log('Pool Type:', result.data.pool_type);
      console.log('Parking Type:', result.data.parking_type);
      console.log('Size:', result.data.size);
      
      // Step 2: Test mapper
      console.log('\n🗺️  Step 2: Mapper Processing');
      const mappedData = await mapBetterPlace(result.data);
      console.log('✅ Mapper processing successful');
      console.log('📊 Mapped data:');
      console.log('Title:', mappedData.title);
      console.log('Price:', mappedData.price);
      console.log('City:', mappedData.city);
      console.log('Bedrooms:', mappedData.bedrooms);
      console.log('Bathrooms:', mappedData.bathrooms);
      console.log('Description length:', mappedData.description?.length || 0);
      console.log('Amenities count:', mappedData.amenities?.raw_amenities?.length || 0);
      console.log('Amenities:', mappedData.amenities?.raw_amenities);
      console.log('Ownership type:', mappedData.ownership_type);
      
      // Step 3: Test validation
      console.log('\n✅ Step 3: Validation');
      const validation = validateProperty(mappedData);
      if (validation.isValid) {
        console.log('✅ Validation passed');
      } else {
        console.log('❌ Validation failed:');
        validation.errors.forEach(error => {
          console.log(`   - ${error}`);
        });
      }
      
      // Step 4: Show full mapped object for debugging
      console.log('\n📋 Step 4: Full Mapped Object');
      console.log(JSON.stringify(mappedData, null, 2));
      
    } else {
      console.log('❌ Firecrawl extraction failed');
      console.log('Error:', result.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

debugBetterPlaceScraping();
