// Test rate limit fix with 3 URLs
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testRateLimitFix() {
  console.log('🔧 Testing rate limit fix with 3 URLs...\n');
  
  // Test with 3 URLs - should take ~18 seconds (6s intervals + processing time)
  const testUrls = [
    'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/',
    'https://balivillarealty.com/property/cozy-villa-3-bedrooms-for-rental-in-umalas-bali/',
    'https://balivillarealty.com/property/charming-2-bedrooms-villa-for-rental-in-babakan-canggu-bali/'
  ];
  
  console.log('⏰ Expected behavior:');
  console.log('   - 6 second intervals between crawl starts');
  console.log('   - No 429 rate limit errors');
  console.log('   - Total time: ~60-90 seconds (not 300+)');
  console.log('   - Sequential processing with proper delays\n');
  
  try {
    const startTime = Date.now();
    console.log(`⏰ Start time: ${new Date(startTime).toLocaleTimeString()}`);
    
    const results = await runExtractBatch('bali_villa_realty', testUrls, {});
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n⏰ End time: ${new Date(endTime).toLocaleTimeString()}`);
    console.log(`⚡ Total duration: ${duration} seconds`);
    console.log(`📊 Average per URL: ${(duration / testUrls.length).toFixed(2)} seconds`);
    
    // Results summary
    console.log('\n📊 Results Summary:');
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}/${testUrls.length}`);
    console.log(`   ❌ Failed: ${failed.length}/${testUrls.length}`);
    console.log(`   📈 Success Rate: ${((successful.length / testUrls.length) * 100).toFixed(1)}%`);
    
    // Rate limit check
    const rateLimitErrors = failed.filter(r => r.error && r.error.includes('429'));
    if (rateLimitErrors.length === 0) {
      console.log('   ✅ No rate limit errors - fix working!');
    } else {
      console.log(`   ❌ ${rateLimitErrors.length} rate limit errors - needs more adjustment`);
    }
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully processed properties:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title.substring(0, 50)}...`);
      });
    }
    
  } catch (error) {
    console.error(`❌ Test failed: ${error.message}`);
  }
  
  console.log('\n🎯 Rate limit fix test completed!');
}

if (require.main === module) {
  testRateLimitFix().catch(console.error);
}

module.exports = { testRateLimitFix };
