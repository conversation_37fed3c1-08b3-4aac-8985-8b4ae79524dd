// Test status detection for SOLD properties
require('dotenv').config();

// Import the detectPropertyStatus function
function detectPropertyStatus(title, description) {
  const text = (title + ' ' + description).toLowerCase();

  // Check for sold indicators
  if (text.includes('(sold)') || text.includes('sold out') || text.includes('no longer available')) {
    return 'SOLD';
  }

  // Check for rented indicators
  if (text.includes('(rented)') || text.includes('rented out') || text.includes('no longer for rent')) {
    return 'RENTED';
  }

  // Check for pending indicators
  if (text.includes('(pending)') || text.includes('under offer') || text.includes('reserved')) {
    return 'PENDING';
  }

  // Default to available
  return 'AVAILABLE';
}

function testStatusDetection() {
  console.log('🔍 Testing Property Status Detection\n');
  
  const testCases = [
    {
      title: 'Off-Plan 3 Bedroom Villa for Sale Leasehold in Bali Seseh (SOLD)',
      description: 'Beautiful villa in Seseh',
      expected: 'SOLD'
    },
    {
      title: 'Kerobokan 4 Bedroom Private Pool Villa (SOLD)',
      description: 'Modern villa with pool',
      expected: 'SOLD'
    },
    {
      title: '3 Bedroom Villa for Sale Leasehold in Bali Tabanan',
      description: 'Available villa in Tabanan',
      expected: 'AVAILABLE'
    },
    {
      title: 'Madura Villa 4 Bedrooms Canggu Berawa',
      description: 'Luxury villa for sale',
      expected: 'AVAILABLE'
    },
    {
      title: 'Beautiful Villa in Seminyak',
      description: 'This property is sold out and no longer available',
      expected: 'SOLD'
    },
    {
      title: 'Cozy Apartment in Ubud (RENTED)',
      description: 'Nice apartment for rent',
      expected: 'RENTED'
    },
    {
      title: 'Villa in Canggu (PENDING)',
      description: 'Under offer',
      expected: 'PENDING'
    }
  ];

  console.log('📊 Testing status detection patterns:\n');

  let passed = 0;
  let failed = 0;

  testCases.forEach((testCase, i) => {
    const detected = detectPropertyStatus(testCase.title, testCase.description);
    const isCorrect = detected === testCase.expected;
    
    console.log(`${i + 1}. ${isCorrect ? '✅' : '❌'} "${testCase.title}"`);
    console.log(`   Expected: ${testCase.expected}`);
    console.log(`   Detected: ${detected}`);
    
    if (testCase.description && testCase.description !== 'Beautiful villa in Seseh') {
      console.log(`   Description: "${testCase.description}"`);
    }
    
    console.log('');
    
    if (isCorrect) {
      passed++;
    } else {
      failed++;
    }
  });

  console.log('📈 Results:');
  console.log(`   ✅ Passed: ${passed}/${testCases.length}`);
  console.log(`   ❌ Failed: ${failed}/${testCases.length}`);
  console.log(`   📊 Success Rate: ${Math.round((passed / testCases.length) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All status detection tests passed!');
  } else {
    console.log('\n⚠️  Some tests failed - status detection needs improvement');
  }
}

testStatusDetection();
