// Analyze BetterPlace markdown for bathroom patterns
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function analyzeBetterPlaceMarkdown() {
  console.log('🔍 Analyzing BetterPlace Markdown for Bathroom Patterns');
  console.log('='.repeat(50));
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();
  
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02174';
  
  try {
    console.log(`📡 Scraping: ${testUrl}`);
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.markdown) {
      const markdown = result.data.markdown;
      console.log(`📝 Markdown length: ${markdown.length} chars`);
      
      // Look for bathroom-related content
      console.log('\n🚿 Searching for bathroom patterns...');
      
      // Search for various bathroom patterns
      const bathroomPatterns = [
        /(\d+)\s*bath/gi,
        /(\d+)\s*bathroom/gi,
        /bath.*?(\d+)/gi,
        /bathroom.*?(\d+)/gi,
        /(\d+)\s*BA/gi,
        /BA.*?(\d+)/gi,
        /toilet/gi,
        /wc/gi,
        /shower/gi,
        /ensuite/gi
      ];
      
      bathroomPatterns.forEach((pattern, i) => {
        const matches = [...markdown.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\nPattern ${i+1} (${pattern.source}):`);
          matches.slice(0, 5).forEach(match => {
            const start = Math.max(0, match.index - 30);
            const end = Math.min(markdown.length, match.index + match[0].length + 30);
            const context = markdown.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      // Look for bedroom/bathroom combinations
      console.log('\n🛏️ Looking for bedroom/bathroom combinations...');
      
      const comboPatterns = [
        /(\d+)\s*bed.*?(\d+)\s*bath/gi,
        /(\d+)\s*bedroom.*?(\d+)\s*bathroom/gi,
        /(\d+)\/(\d+)/gi,  // "2/1" format
        /(\d+)\s*BR.*?(\d+)\s*BA/gi
      ];
      
      comboPatterns.forEach(pattern => {
        const matches = [...markdown.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\n${pattern.source} found ${matches.length} times:`);
          matches.slice(0, 3).forEach(match => {
            const start = Math.max(0, match.index - 50);
            const end = Math.min(markdown.length, match.index + match[0].length + 50);
            const context = markdown.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      // Show a sample of the markdown
      console.log('\n📄 Markdown Sample (first 1000 chars):');
      console.log(markdown.substring(0, 1000));
      
    } else {
      console.log('❌ Failed to get markdown');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

analyzeBetterPlaceMarkdown().then(() => process.exit(0));
