// Get working URLs from queue
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq, and } = require('drizzle-orm');

async function getWorkingUrls() {
  console.log('🔍 Finding working URLs from queue...');
  
  const websites = ['betterplace', 'bali_home_immo', 'bali_villa_realty', 'villa_bali_sale'];
  
  for (const website of websites) {
    const urls = await db
      .select({ url: scrapingQueue.url })
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, website),
        eq(scrapingQueue.status, 'pending')
      ))
      .limit(3);
    
    console.log(`\n${website.toUpperCase()}:`);
    urls.forEach((row, i) => {
      console.log(`   ${i + 1}. ${row.url}`);
    });
  }
  
  process.exit(0);
}

getWorkingUrls().catch(console.error);
