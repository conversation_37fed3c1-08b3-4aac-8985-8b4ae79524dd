// Check Villa Bali Sale URLs in queue
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function checkVillaBaliSale() {
  try {
    console.log('🏠 Checking Villa Bali Sale URLs in queue...');
    
    const urls = await db
      .select({ url: scrapingQueue.url, status: scrapingQueue.status })
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'villa_bali_sale'))
      .limit(10);
    
    console.log(`Total found: ${urls.length}`);
    
    if (urls.length > 0) {
      const byStatus = {};
      urls.forEach(url => {
        if (!byStatus[url.status]) byStatus[url.status] = 0;
        byStatus[url.status]++;
      });
      
      console.log('By status:', byStatus);
      
      urls.forEach((url, i) => {
        console.log(`${i + 1}. [${url.status}] ${url.url.substring(0, 70)}...`);
      });
    } else {
      console.log('❌ No Villa Bali Sale URLs found in queue');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

checkVillaBaliSale();
