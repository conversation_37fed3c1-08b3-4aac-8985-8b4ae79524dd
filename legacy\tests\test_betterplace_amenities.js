// Test BetterPlace URLs with enhanced amenities extraction
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { db, properties, scrapingQueue } = require('./drizzle_client');
const { desc, sql, eq, and } = require('drizzle-orm');

async function testBetterPlaceAmenities() {
  console.log('🧪 Testing BetterPlace URLs with Enhanced Amenities');
  console.log('='.repeat(60));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  try {
    // Get 3 BetterPlace URLs from queue
    const queueUrls = await db
      .select({ url: scrapingQueue.url, id: scrapingQueue.id })
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, 'betterplace'),
        eq(scrapingQueue.status, 'pending')
      ))
      .limit(3);
    
    if (queueUrls.length === 0) {
      console.log('❌ No BetterPlace URLs found in queue');
      process.exit(1);
    }
    
    console.log(`📋 Found ${queueUrls.length} BetterPlace URLs to test:`);
    queueUrls.forEach((row, i) => {
      console.log(`   ${i + 1}. ${row.url}`);
    });
    
    // Get initial count
    const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`\n📊 Initial properties in database: ${initialCount[0].count}`);
    
    // Extract URLs for processing
    const urls = queueUrls.map(row => row.url);
    
    console.log(`\n🔄 Processing ${urls.length} BetterPlace URLs...`);
    const startTime = Date.now();
    
    // Use the existing batch processing function
    const results = await runExtractBatch('betterplace', urls, {
      concurrency: 1, // Process one at a time for better debugging
      timeout: 90000  // Longer timeout for BetterPlace
    });
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    
    if (results && results.length > 0) {
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      
      console.log(`\n✅ Completed in ${duration}s:`);
      console.log(`   Processed: ${results.length}`);
      console.log(`   Successful: ${successful}`);
      console.log(`   Failed: ${failed}`);
      
      // Show successful results with detailed amenities check
      const successfulResults = results.filter(r => r.success);
      if (successfulResults.length > 0) {
        console.log(`\n🏠 Successfully scraped properties:`);
        successfulResults.forEach((result, i) => {
          console.log(`\n   ${i + 1}. ✅ ${result.title || 'Property'}`);
          if (result.price) {
            console.log(`      💰 IDR ${result.price.toLocaleString()}`);
          }
          if (result.bedrooms) {
            console.log(`      🏠 ${result.bedrooms} bedrooms`);
          }
          if (result.city) {
            console.log(`      📍 ${result.city}`);
          }
          
          // Detailed amenities check
          if (result.amenities && result.amenities.raw_amenities && result.amenities.raw_amenities.length > 0) {
            console.log(`      🎯 Amenities (${result.amenities.raw_amenities.length}):`);
            result.amenities.raw_amenities.forEach((amenity, idx) => {
              console.log(`         ${idx + 1}. ${amenity}`);
            });
          } else {
            console.log(`      ⚠️  No amenities extracted`);
          }
          
          if (result.description) {
            console.log(`      📝 Description: ${result.description.substring(0, 100)}...`);
          }
        });
      }
      
      // Show failed results
      const failedResults = results.filter(r => !r.success);
      if (failedResults.length > 0) {
        console.log(`\n❌ Failed properties:`);
        failedResults.forEach((result, i) => {
          console.log(`   ${i + 1}. ❌ ${result.error || 'Unknown error'}`);
          if (result.url) {
            console.log(`      URL: ${result.url}`);
          }
        });
      }
      
    } else {
      console.log(`⚠️  No results returned (${duration}s)`);
    }
    
    // Check final count and show new properties
    const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
    const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
    console.log(`\n🆕 New properties added to database: ${newProperties}`);
    
    if (newProperties > 0) {
      console.log('\n🏠 Latest BetterPlace Properties in Database:');
      const latestProps = await db
        .select({
          id: properties.id,
          title: properties.title,
          price: properties.price,
          bedrooms: properties.bedrooms,
          city: properties.city,
          amenities: properties.amenities,
          description: properties.description,
          created_at: properties.created_at
        })
        .from(properties)
        .where(eq(properties.source_id, 'betterplace'))
        .orderBy(desc(properties.created_at))
        .limit(5);
      
      latestProps.forEach((prop, i) => {
        console.log(`\n   ${i + 1}. [${prop.id}] ${prop.title}`);
        if (prop.price) {
          console.log(`      💰 IDR ${prop.price.toLocaleString()}`);
        }
        console.log(`      🏠 ${prop.bedrooms} bed | 📍 ${prop.city}`);
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
        
        // Check amenities in database
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`      ✅ Amenities in DB (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        } else {
          console.log(`      ❌ No amenities in database`);
        }
        
        if (prop.description) {
          console.log(`      📝 Description: ${prop.description.substring(0, 80)}...`);
        } else {
          console.log(`      ⚠️  No description in database`);
        }
      });
    }
    
    console.log('\n✅ BetterPlace amenities test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testBetterPlaceAmenities();
