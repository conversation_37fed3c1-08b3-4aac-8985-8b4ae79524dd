// Batch runner: Firecrawl async extract + parallel batches + per-site mapping + validation + insert
// Safe version without extra installs (no p-limit). Uses simple in-process concurrency.

require('dotenv').config();
const Firecrawl = require('firecrawl').default;
const OpenAI = require('openai');
const { getKeyManager } = require('./key_manager');
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Initialize key manager
const keyManager = getKeyManager();
const { mapBetterPlace, mapBaliHomeImmo, mapBaliVillaRealty, mapVillaBaliSale } = require('./mappers');
const { websiteRegistry } = require('./website_registry');
const { validateProperty } = require('./validate');
const { db, closeConnection, properties } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

const CONCURRENCY = Number(process.env.SCRAPE_CONCURRENCY || 3);
const POLL_INTERVAL_MS = 3000;
const POLL_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes

function sleep(ms){ return new Promise(r => setTimeout(r, ms)); }

function chooseMapper(sourceId){
  // Try to get mapper from website registry first
  try {
    return websiteRegistry.getMapper(sourceId);
  } catch (error) {
    // Fallback to legacy mappers for backward compatibility
    switch(sourceId){
      case 'betterplace': return mapBetterPlace;
      case 'bali_home_immo': return mapBaliHomeImmo;
      case 'bali_villa_realty': return mapBaliVillaRealty;
      case 'villa_bali_sale': return mapVillaBaliSale;
      case 'villabalisale': return mapVillaBaliSale; // Support database format
      case 'villabalisale.com': return mapVillaBaliSale; // Support both formats
      default: throw new Error('Unknown sourceId: '+sourceId);
    }
  }
}

async function pollJob(jobId){
  const start = Date.now();
  while(true){
    const st = await app.getExtractStatus(jobId);
    if (st.status === 'completed') return st.data;
    if (st.status === 'failed' || st.status === 'cancelled') {
      throw new Error(`Job ${jobId} ${st.status}`);
    }
    if (Date.now() - start > POLL_TIMEOUT_MS) throw new Error(`Job ${jobId} timeout`);
    await sleep(POLL_INTERVAL_MS);
  }
}

function getPropertySchema(){
  return {
    type: 'object',
    properties: {
      title: { type: 'string' },
      price: { type: 'string' },
      location: { type: 'string' },
      bedrooms: { type: 'integer' },
      bathrooms: { type: 'integer' },
      description: { type: 'string' },
      property_id: { type: 'string' },
      detail_url: { type: 'string' },
      property_type: { type: 'string' },
      status: { type: 'string' },
      size: {
        type: 'object',
        properties: {
          land_size_sqm: { type: 'integer' },
          building_size_sqm: { type: 'integer' }
        }
      },
      amenities: { type: 'array', items: { type: 'string' } },
      images: { type: 'array', items: { type: 'string' } },
      year_built: { type: 'integer' },
      parking: { type: 'string', description: 'Parking information (e.g., "2 car garage", "open parking", "covered parking")' }
    },
    required: ['title','location','property_id','detail_url']
  };
}

function generateDescriptionText(mappedProperty, rawData) {
  // Create a comprehensive text description for embedding
  const parts = [];

  if (mappedProperty.title) parts.push(`Title: ${mappedProperty.title}`);
  if (mappedProperty.address) parts.push(`Location: ${mappedProperty.address}`);
  if (mappedProperty.bedrooms) parts.push(`${mappedProperty.bedrooms} bedrooms`);
  if (mappedProperty.bathrooms) parts.push(`${mappedProperty.bathrooms} bathrooms`);
  if (mappedProperty.parking_spaces) parts.push(`${mappedProperty.parking_spaces} parking spaces`);
  if (mappedProperty.size_sqft) parts.push(`${Math.round(mappedProperty.size_sqft)} sqft building`);
  if (mappedProperty.lot_size_sqft) parts.push(`${Math.round(mappedProperty.lot_size_sqft)} sqft lot`);
  if (mappedProperty.year_built) parts.push(`Built in ${mappedProperty.year_built}`);

  // Add price information
  if (mappedProperty.price) parts.push(`Sale price: IDR ${mappedProperty.price.toLocaleString()}`);
  if (mappedProperty.rent_price) parts.push(`Rent: IDR ${mappedProperty.rent_price.toLocaleString()}/month`);

  // Add amenities
  if (rawData?.amenities?.length > 0) {
    parts.push(`Amenities: ${rawData.amenities.join(', ')}`);
  }

  // Add description if available
  if (rawData?.description) {
    parts.push(`Description: ${rawData.description}`);
  }

  return parts.join('. ');
}

async function generateOpenAIEmbedding(text) {
  try {
    const response = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: text,
      encoding_format: "float",
    });

    return response.data[0].embedding;
  } catch (error) {
    console.log(`❌ OpenAI embedding failed: ${error.message}`);
    return null;
  }
}

// Helper function to parse and normalize price strings
function parsePrice(priceString) {
  if (!priceString) return null;

  console.log(`   💰 Parsing price: "${priceString}"`);

  // Remove currency symbols and clean the string
  let cleanPrice = priceString.replace(/[IDR|Rp|\$|,]/gi, '').trim();

  // Extract number and multiplier
  const numberMatch = cleanPrice.match(/([\d\.]+)\s*(million|billion|juta|miliar)?/i);
  if (!numberMatch) {
    console.log(`   ⚠️  Could not parse price: ${priceString}`);
    return null;
  }

  let baseNumber = parseFloat(numberMatch[1]);
  const multiplier = numberMatch[2] ? numberMatch[2].toLowerCase() : null;

  // Apply multipliers
  if (multiplier) {
    if (multiplier === 'million' || multiplier === 'juta') {
      baseNumber = baseNumber * 1_000_000;
    } else if (multiplier === 'billion' || multiplier === 'miliar') {
      baseNumber = baseNumber * 1_000_000_000;
    }
  }

  // If the number is very small (< 1000), assume it's in millions IDR
  if (baseNumber < 1000 && !multiplier) {
    console.log(`   🔄 Small number detected (${baseNumber}), assuming millions IDR`);
    baseNumber = baseNumber * 1_000_000;
  }

  // If it's still very small (< 100,000), it might be in USD - convert to IDR
  if (baseNumber < 100_000) {
    console.log(`   💱 Very small number (${baseNumber}), assuming USD - converting to IDR`);
    baseNumber = baseNumber * 15_800; // Approximate USD to IDR rate
  }

  console.log(`   ✅ Parsed price: ${baseNumber} IDR`);
  return Math.round(baseNumber);
}

function parseMarkdownForProperty(markdown, url) {
  try {
    console.log(`   🔍 Parsing markdown (${markdown.length} chars) for property data...`);

    // Extract property ID from URL
    const propertyIdMatch = url.match(/\/([A-Z0-9]+)$/i) || url.match(/properties\/([A-Z0-9]+)/i);
    const property_id = propertyIdMatch ? propertyIdMatch[1] : 'unknown';

    // Extract title (usually the first heading or image alt text)
    const titleMatch = markdown.match(/^#\s+(.+)$/m) ||
                      markdown.match(/!\[([^\]]+)\]/);
    const title = titleMatch ? titleMatch[1].trim() : 'Property Title Not Found';

    // Extract price (look for currency symbols and numbers with various patterns)
    const priceMatch = markdown.match(/(?:price|harga|sale|for sale|dijual)[\s:]*(?:IDR|Rp|USD|\$)?\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i) ||
                      markdown.match(/(?:IDR|Rp|USD|\$)\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i) ||
                      markdown.match(/([\d,\.]+)\s*(?:million|billion|juta|miliar)\s*(?:IDR|Rp|USD|\$)?/i) ||
                      markdown.match(/([\d,\.]+)\s*(?:IDR|Rp|USD|\$)/i) ||
                      markdown.match(/\b(\d{1,3}(?:[,\.]\d{3})*(?:\.\d{2})?)\s*(?:million|billion|juta|miliar)?\b/i);

    const priceString = priceMatch ? priceMatch[0] : null;
    const price = parsePrice(priceString);

    // If no price found, try to find rental price
    let rentPrice = null;
    if (!price) {
      const rentMatch = markdown.match(/(?:rent|rental|sewa|monthly|yearly)[\s:]*(?:IDR|Rp|USD|\$)?\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i) ||
                       markdown.match(/(?:per month|per year|\/month|\/year)[\s:]*(?:IDR|Rp|USD|\$)?\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i);
      const rentString = rentMatch ? rentMatch[0] : null;
      rentPrice = parsePrice(rentString);
    }

    // Extract bedrooms
    const bedroomMatch = markdown.match(/(\d+)\s*(?:bed|bedroom|kamar tidur)/i);
    const bedrooms = bedroomMatch ? parseInt(bedroomMatch[1]) : null;

    // Extract bathrooms
    const bathroomMatch = markdown.match(/(\d+)\s*(?:bath|bathroom|kamar mandi)/i);
    const bathrooms = bathroomMatch ? parseInt(bathroomMatch[1]) : null;

    // Extract location (look for common Bali locations)
    const locationMatch = markdown.match(/(Canggu|Seminyak|Ubud|Kerobokan|Sanur|Denpasar|Jimbaran|Nusa Dua|Uluwatu|Pecatu|Bukit|Umalas|Berawa|Pererenan|Kedungu|Tanah Lot|Seseh|Cemagi)/i);
    const location = locationMatch ? locationMatch[1] : 'Bali, Indonesia';

    // Extract images (look for image URLs)
    const imageMatches = markdown.match(/!\[.*?\]\((https?:\/\/[^\)]+)\)/g) || [];
    const images = imageMatches.map(match => {
      const urlMatch = match.match(/\((https?:\/\/[^\)]+)\)/);
      return urlMatch ? urlMatch[1] : null;
    }).filter(Boolean);

    // Extract amenities (look for common property features)
    const amenityKeywords = ['swimming pool', 'pool', 'garden', 'parking', 'garage', 'kitchen', 'air conditioning', 'wifi', 'security', 'furnished'];
    const amenities = [];
    const lowerMarkdown = markdown.toLowerCase();

    amenityKeywords.forEach(keyword => {
      if (lowerMarkdown.includes(keyword)) {
        amenities.push(keyword.charAt(0).toUpperCase() + keyword.slice(1));
      }
    });

    // Extract parking info
    const parkingMatch = markdown.match(/(\d+)\s*(?:car\s*)?(?:parking|garage)/i) ||
                        markdown.match(/parking|garage/i);
    const parking = parkingMatch ? parkingMatch[0] : null;

    // Extract year built
    const yearMatch = markdown.match(/(?:built|year|tahun)\s*:?\s*(\d{4})/i) ||
                     markdown.match(/(\d{4})\s*(?:built|year)/i);
    const year_built = yearMatch ? parseInt(yearMatch[1]) : null;

    const extractedData = {
      title: title,
      price: price,
      rent_price: rentPrice,
      location: location,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      description: markdown.substring(0, 500) + '...', // First 500 chars as description
      images: images.slice(0, 10), // Max 10 images
      property_id: property_id,
      detail_url: url,
      property_type: 'villa', // Default for these sites
      status: 'available', // Default status
      amenities: amenities,
      year_built: year_built,
      parking: parking
    };

    const priceInfo = price ? `${price} IDR` : (rentPrice ? `${rentPrice} IDR/month` : 'No price');
    console.log(`   ✅ Extracted: ${title} (${bedrooms}bed/${bathrooms}bath) in ${location} - ${priceInfo}`);
    return extractedData;

  } catch (error) {
    console.log(`   ❌ Markdown parsing failed: ${error.message}`);
    return null;
  }
}

async function scrapeUrlAsync(url, maxRetries = 8, pollInterval = 15000) {
  console.log(`   🔄 Starting async scrape for: ${url}`);

  let lastError = null;

  // Try with different keys if needed
  for (let keyAttempt = 0; keyAttempt < keyManager.keys.length; keyAttempt++) {
    const currentKey = keyManager.getCurrentKey();
    console.log(`   🔑 Using key ${currentKey.index + 1}/${keyManager.keys.length} (${currentKey.maskedKey})`);

    try {
      // No initial delay needed - staggering is handled at batch level

      // Start the async batch scrape job (single URL)
      const scrapeResponse = await fetch('https://api.firecrawl.dev/v1/batch/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentKey.key}`
        },
        body: JSON.stringify({
          urls: [url], // Single URL in array for batch scrape
          formats: ['markdown'], // Use markdown-only for cost savings
          // Remove jsonOptions since we're using markdown-only
          onlyMainContent: true,
          timeout: 90000, // 90 second timeout for better success rate
          maxConcurrency: 3, // Reduced to 3 for better stability and fewer server errors
          ignoreInvalidURLs: true, // Continue processing even if some URLs fail
          blockAds: true, // Block ads and popups
          proxy: 'auto', // Use auto proxy selection for better success rate
          waitFor: 5000, // Wait 5 seconds for page to load (better for JS-heavy sites)
          removeBase64Images: true // Remove base64 images to reduce response size
        })
      });

      if (!scrapeResponse.ok) {
        const errorText = await scrapeResponse.text();
        const error = new Error(`HTTP ${scrapeResponse.status}: ${errorText}`);

        // Handle rate limiting, auth errors, and server errors
        if (scrapeResponse.status === 429 || scrapeResponse.status === 401 || scrapeResponse.status === 403) {
          keyManager.markKeyError(currentKey.index, error);
          console.log(`   ⚠️  Key ${currentKey.index + 1} failed (${scrapeResponse.status}), trying next key...`);
          lastError = error;
          continue; // Try next key
        }

        // Handle server errors (500, 502, 503) - retry with exponential backoff
        if (scrapeResponse.status >= 500 && scrapeResponse.status < 600) {
          const retryDelay = Math.min(10000 * Math.pow(2, keyAttempt), 60000); // Max 60 seconds
          console.log(`   ⚠️  Server error ${scrapeResponse.status}, retrying in ${retryDelay/1000}s (attempt ${keyAttempt + 1})...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          lastError = error;
          continue; // Retry with same key
        }

        throw error;
      }

      const scrapeData = await scrapeResponse.json();
      if (!scrapeData.success || !scrapeData.id) {
        const error = new Error(`Failed to start batch scrape job: ${JSON.stringify(scrapeData)}`);
        keyManager.markKeyError(currentKey.index, error);
        lastError = error;
        continue; // Try next key
      }

      const jobId = scrapeData.id;
      console.log(`   ⏳ Polling job ${jobId}...`);

      // Poll for completion with optimized intervals (batch jobs typically take 30-60 seconds)
      let attempts = 0;
      const intervals = [3000, 5000, 8000, 12000, 18000, 25000, 35000, 45000]; // Adaptive intervals for better success rate

      while (attempts < maxRetries) {
        const currentInterval = intervals[attempts] || 30000;
        await new Promise(resolve => setTimeout(resolve, currentInterval));

        const statusResponse = await fetch(`https://api.firecrawl.dev/v1/batch/scrape/${jobId}`, {
          headers: {
            'Authorization': `Bearer ${currentKey.key}`
          }
        });

        if (!statusResponse.ok) {
          console.log(`   ⚠️  Status check failed: ${statusResponse.status}, retrying...`);

          // For server errors, add exponential backoff
          if (statusResponse.status >= 500 && statusResponse.status < 600) {
            const serverErrorDelay = Math.min(15000 * Math.pow(1.5, Math.floor(attempts / 3)), 120000); // Max 2 minutes
            console.log(`   🔄 Server error during polling, waiting ${serverErrorDelay/1000}s...`);
            await new Promise(resolve => setTimeout(resolve, serverErrorDelay));
          }

          attempts++;
          currentInterval = Math.min(currentInterval * 1.2, 45000); // Max 45 seconds, slower growth
          continue;
        }

        const statusData = await statusResponse.json();

        if (statusData.status === 'completed') {
          console.log(`   ✅ Async scrape completed for: ${url}`);
          keyManager.markKeySuccess(currentKey.index);

          console.log(`   📊 Crawl data: ${statusData.data?.length || 0} results`);

          const result = statusData.data?.[0];
          if (!result) {
            console.log(`   ❌ No result data: ${JSON.stringify(statusData, null, 2)}`);
            throw new Error('No data returned from crawl');
          }

          console.log(`   📋 Result has: json=${!!result.json}, markdown=${!!result.markdown}, html=${!!result.html}`);

          // For markdown-only scraping, pass markdown directly to mapper
          if (result.markdown && !result.json) {
            console.log(`   📝 Passing markdown data to mapper...`);
            return {
              success: true,
              data: {
                markdown: result.markdown,
                url: url
              },
              markdown: result.markdown,
              url: url
            };
          }

          // Try JSON first, fallback to markdown parsing (legacy)
          let extractedData = result.json;

          if (!extractedData && result.markdown) {
            console.log(`   🔄 JSON extraction failed, parsing markdown...`);
            extractedData = parseMarkdownForProperty(result.markdown, url);
          }

          if (!extractedData) {
            throw new Error('No structured data could be extracted');
          }

          return {
            success: true,
            data: { json: extractedData },
            json: extractedData
          };
        } else if (statusData.status === 'failed') {
          const errorMsg = statusData.error || 'Unknown crawl failure';
          console.log(`   ❌ Crawl failed: ${errorMsg}`);
          const error = new Error(`Crawl job failed: ${errorMsg}`);
          keyManager.markKeyError(currentKey.index, error);
          throw error;
        } else if (statusData.status === 'cancelled') {
          const error = new Error('Crawl job was cancelled');
          console.log(`   ❌ Crawl cancelled`);
          throw error;
        }

        console.log(`   ⏳ Status: ${statusData.status}, attempt ${attempts + 1}/${maxRetries} (${statusData.completed || 0}/${statusData.total || 1}) - next check in ${Math.round(currentInterval/1000)}s`);
        attempts++;
      }

      // If we get here, polling timed out for this key - don't mark as key error, it's a job timeout
      const timeoutError = new Error('Polling timeout - job did not complete in time');
      console.log(`   ⏰ Job polling timeout for key ${currentKey.index + 1}, trying next key...`);
      lastError = timeoutError;

    } catch (error) {
      console.log(`   ❌ Key ${currentKey.index + 1} failed: ${error.message}`);
      keyManager.markKeyError(currentKey.index, error);
      lastError = error;
      // Continue to try next key
    }
  }

  // If we've tried all keys and none worked
  throw lastError || new Error('All API keys failed');
}

async function runExtractBatch(sourceId, urls, extractOptions){
  const mapper = chooseMapper(sourceId);
  const results = [];

  // Process URLs in parallel batches with improved concurrency (50 concurrent requests)
  const batchSize = parseInt(process.env.SCRAPE_CONCURRENCY) || 10; // Use SCRAPE_CONCURRENCY from .env

  for (let i = 0; i < urls.length; i += batchSize) {
    const batch = urls.slice(i, i + batchSize);
    console.log(`\n📦 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(urls.length/batchSize)} (${batch.length} URLs)`);

    // Start crawl jobs with 10-second staggered intervals
    const batchPromises = batch.map((url, index) => {
      return new Promise(async (resolve) => {
        try {
          // Stagger start times: 0s, 1s, 2s, 3s, 4s... (1 second intervals for better throughput)
          const delay = index * 1000;
          console.log(`   🔄 Scheduling crawl ${i + index + 1}/${urls.length} in ${delay/1000}s: ${url}`);

          await new Promise(r => setTimeout(r, delay));
          console.log(`   🚀 Starting crawl ${i + index + 1}/${urls.length}: ${url}`);

          const scraped = await scrapeUrlAsync(url, 8, 15000); // Increased retries, reduced interval

          if (!scraped.success) {
            throw new Error('Scraping failed');
          }

          // Use mapper to process the scraped data
          let extracted;

          // Always use mapper for all sites to ensure consistent data structure and ownership extraction
          const rawData = {
            url: url,
            json: scraped.json,
            markdown: scraped.markdown,
            html: scraped.html
          };
          extracted = await mapper(rawData);

          extracted.detail_url = url;

          console.log(`   ✅ Completed ${i + index + 1}/${urls.length}: ${extracted.title || 'Unknown title'}`);
          resolve(extracted);

        } catch (error) {
          console.error(`   ❌ Failed ${i + index + 1}/${urls.length}: ${url} - ${error.message}`);
          resolve(null);
        }
      });
    });

    // Wait for all jobs in this batch to complete
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults.filter(Boolean));

    // Intelligent wait between batches based on results
    if (i + batchSize < urls.length) {
      const successfulInBatch = batchResults.filter(Boolean).length;
      const totalInBatch = batchResults.length;

      if (successfulInBatch === 0) {
        // All requests failed/timed out, shorter wait
        console.log(`   ⏳ Batch had no successes. Waiting 30 seconds before next batch...`);
        await new Promise(resolve => setTimeout(resolve, 30000));
      } else {
        // Normal rate limiting for successful batch
        console.log(`   ⏳ Batch complete (${successfulInBatch}/${totalInBatch} successful). Waiting 45 seconds for rate limit reset...`);
        await new Promise(resolve => setTimeout(resolve, 45000));
      }
    }
  }

  const extracted = results;

  const processedResults = [];
  for (let index = 0; index < extracted.length; index++){
    const raw = extracted[index];
    try{
      // Raw is already processed by mapper in the scraping loop, just validate
      const property = raw;

      const v = validateProperty(property);
      if (!v.ok) {
        console.log(`   ❌ Validation failed for ${property.title}:`);
        v.errors.forEach(error => console.log(`      - ${error}`));
        throw new Error('Validation failed: '+v.errors.join(', '));
      }

      // Insert/Upsert with (source_id, external_id)
      const mediaJson = JSON.stringify(property.media || {});
      const amenitiesJson = JSON.stringify(property.amenities || {});
      const srcId = property.source_id || property.media?.source_id || null;
      const extId = property.external_id || property.media?.external_id || null;
      const srcUrl = property.source_url || property.media?.source_url || null;

      // Use Drizzle insert with proper parameter binding to avoid SQL injection and parameter issues
      const insertData = {
        title: property.title || '',
        category: property.category || null,
        type: property.type || null,
        status: property.status || 'AVAILABLE',
        address: property.address || '',
        city: property.city || '',
        state: property.state || null,
        country: property.country || 'Indonesia',
        description: property.description || null,
        price: property.price || null,
        rent_price: property.rent_price || null,
        bedrooms: property.bedrooms || null,
        bathrooms: property.bathrooms || null,
        parking_spaces: property.parking_spaces || null,
        size_sqft: property.size_sqft || null,
        lot_size_sqft: property.lot_size_sqft || null,
        year_built: property.year_built || null,
        ownership_type: property.ownership_type || null,
        lease_duration_years: property.lease_duration_years || null,
        lease_duration_text: property.lease_duration_text || null,
        amenities: amenitiesJson,
        media: mediaJson,
        source_id: srcId,
        external_id: extId,
        source_url: srcUrl,
        source_url_id: extractOptions.source_url_ids ? extractOptions.source_url_ids[index] : (extractOptions.source_url_id || null) // Support both array and single ID
      };

      const insertResult = await db.insert(properties).values(insertData)
        .onConflictDoUpdate({
          target: [properties.source_id, properties.external_id],
          set: {
            title: insertData.title,
            status: insertData.status,
            address: insertData.address,
            city: insertData.city,
            state: insertData.state,
            country: insertData.country,
            description: insertData.description,
            price: insertData.price,
            rent_price: insertData.rent_price,
            bedrooms: insertData.bedrooms,
            bathrooms: insertData.bathrooms,
            parking_spaces: insertData.parking_spaces,
            size_sqft: insertData.size_sqft,
            lot_size_sqft: insertData.lot_size_sqft,
            year_built: insertData.year_built,
            ownership_type: insertData.ownership_type,
            lease_duration_years: insertData.lease_duration_years,
            lease_duration_text: insertData.lease_duration_text,
            amenities: insertData.amenities,
            media: insertData.media,
            updated_at: new Date()
          }
        })
        .returning({ id: properties.id });

      // Generate and save vector embedding
      const propertyId = insertResult[0]?.id;
      if (propertyId) {
        try {
          const descriptionText = generateDescriptionText(property, raw);
          const embedding = await generateOpenAIEmbedding(descriptionText);

          if (embedding) {
            await db.execute(sql`
              UPDATE property
              SET vector = ${JSON.stringify(embedding)}::vector
              WHERE id = ${propertyId}
            `);
            console.log(`   🔍 Vector generated for: ${property.title}`);
          }
        } catch (vectorError) {
          console.log(`   ⚠️  Vector generation failed for ${property.title}: ${vectorError.message}`);
        }
      }

      processedResults.push({
        ok: true,
        id: propertyId,
        title: property.title,
        // Include the full extracted property data for debugging/testing
        data: property
      });
    } catch (e) {
      processedResults.push({ ok: false, error: e.message });
    }
  }

  // For debugging/testing purposes, also return the raw extracted data
  // This allows debug scripts to access the actual mapper output
  return {
    processedResults,
    extractedData: extracted
  };
}

async function runParallel(sourceId, allUrls, extractOptions){
  const batches = [];
  const batchSize = 10;
  for (let i=0; i<allUrls.length; i+=batchSize) batches.push(allUrls.slice(i, i+batchSize));

  const queue = batches.slice();
  const outcomes = [];

  async function worker(){
    while(queue.length){
      const urls = queue.shift();
      try {
        const res = await runExtractBatch(sourceId, urls, extractOptions);
        // Handle new return structure - use processedResults for backwards compatibility
        if (res.processedResults) {
          outcomes.push(...res.processedResults);
        } else {
          // Fallback for old format
          outcomes.push(...res);
        }
      } catch (e) {
        outcomes.push({ ok: false, error: e.message, urls });
      }
    }
  }

  const workers = Array.from({ length: Math.min(CONCURRENCY, batches.length) }, () => worker());
  await Promise.all(workers);
  return outcomes;
}

async function main(){
  try {
    const sourceId = process.argv[2];
    const urls = process.argv.slice(3);
    if (!sourceId || urls.length === 0) {
      console.log('Usage: node scrape_worker/run_batch.js <sourceId> <url1> <url2> ...');
      process.exit(1);
    }

    const res = await runParallel(sourceId, urls, { /* prompt/schema optional */ });
    const ok = res.filter(r => r.ok).length;
    const fail = res.length - ok;
    console.log(`Done. Success: ${ok}, Failed: ${fail}`);
    if (fail) console.dir(res.filter(r => !r.ok).slice(0,10));

    // Show key manager stats
    keyManager.printStats();
  } finally {
    await closeConnection();
  }
}

if (require.main === module) {
  main().catch(e => { console.error(e); process.exit(1); });
}

module.exports = { runParallel, runExtractBatch };

