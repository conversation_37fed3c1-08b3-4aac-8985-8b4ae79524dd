// Website Registry System
// Dynamic registration and management of website scrapers

const { getWebsiteConfig, getAllWebsiteIds, isValidWebsiteId } = require('./website_configs');
const { GenericMapper } = require('./generic_mapper');

// Import existing specialized mappers
const { 
  mapBetterPlace, 
  mapBaliHomeImmo, 
  mapBaliVillaRealty, 
  mapVillaBaliSale 
} = require('./mappers');

class WebsiteRegistry {
  constructor() {
    this.registeredWebsites = new Map();
    this.mappers = new Map();
    this.initializeDefaultWebsites();
  }

  // Initialize with existing working websites
  initializeDefaultWebsites() {
    // Register existing specialized mappers
    this.registerWebsite('betterplace', {
      mapper: mapBetterPlace,
      type: 'specialized',
      active: true
    });

    this.registerWebsite('bali_villa_realty', {
      mapper: mapBaliVillaRealty,
      type: 'specialized', 
      active: true
    });

    this.registerWebsite('bali_home_immo', {
      mapper: mapBaliHomeImmo,
      type: 'specialized',
      active: true
    });

    this.registerWebsite('villabalisale', {
      mapper: mapVillaBaliSale,
      type: 'specialized',
      active: true
    });

    // Register new website with generic mapper
    this.registerWebsite('bali_coconut_living', {
      type: 'generic',
      active: true
    });

    console.log(`✅ Initialized ${this.registeredWebsites.size} default websites`);
  }

  // Register a new website
  registerWebsite(websiteId, options = {}) {
    if (!isValidWebsiteId(websiteId)) {
      throw new Error(`Invalid website ID: ${websiteId}. Must be configured in website_configs.js`);
    }

    const config = getWebsiteConfig(websiteId);
    const registration = {
      id: websiteId,
      config,
      mapper: options.mapper || this.createGenericMapper(websiteId),
      type: options.type || 'generic',
      active: options.active !== false,
      registeredAt: new Date(),
      ...options
    };

    this.registeredWebsites.set(websiteId, registration);
    console.log(`✅ Registered website: ${websiteId} (${registration.type})`);

    return registration;
  }

  // Create a generic mapper for a website
  createGenericMapper(websiteId) {
    const genericMapper = new GenericMapper(websiteId);
    return (rawData) => genericMapper.mapProperty(rawData);
  }

  // Get mapper for a website
  getMapper(websiteId) {
    const registration = this.registeredWebsites.get(websiteId);
    if (!registration) {
      throw new Error(`Website not registered: ${websiteId}`);
    }

    if (!registration.active) {
      throw new Error(`Website is inactive: ${websiteId}`);
    }

    return registration.mapper;
  }

  // Get all active websites
  getActiveWebsites() {
    return Array.from(this.registeredWebsites.values())
      .filter(reg => reg.active)
      .map(reg => reg.id);
  }

  // Get website registration info
  getWebsiteInfo(websiteId) {
    return this.registeredWebsites.get(websiteId);
  }

  // Update website status
  setWebsiteActive(websiteId, active = true) {
    const registration = this.registeredWebsites.get(websiteId);
    if (!registration) {
      throw new Error(`Website not registered: ${websiteId}`);
    }

    registration.active = active;
    console.log(`${active ? '✅' : '❌'} ${websiteId} ${active ? 'activated' : 'deactivated'}`);
  }

  // Add a new website with configuration
  addNewWebsite(websiteConfig, mapperOptions = {}) {
    const websiteId = websiteConfig.id;

    // Validate configuration
    if (!websiteConfig.id || !websiteConfig.name || !websiteConfig.domain) {
      throw new Error('Website configuration must include id, name, and domain');
    }

    // Add to website configs (this would typically update the config file)
    // For now, we'll add it to the runtime registry
    const registration = this.registerWebsite(websiteId, {
      ...mapperOptions,
      config: websiteConfig
    });

    return registration;
  }

  // Test a website mapper
  async testWebsiteMapper(websiteId, testData) {
    const mapper = this.getMapper(websiteId);
    const config = getWebsiteConfig(websiteId);

    console.log(`🧪 Testing mapper for ${websiteId}...`);

    try {
      const result = await mapper(testData);
      
      // Validate result
      const validation = this.validateMappedProperty(result, config);
      
      return {
        success: true,
        result,
        validation,
        websiteId,
        testedAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        websiteId,
        testedAt: new Date()
      };
    }
  }

  // Validate mapped property against website config
  validateMappedProperty(property, config) {
    const errors = [];
    const warnings = [];

    // Check required fields
    const requiredFields = config.validation?.requiredFields || [];
    for (const field of requiredFields) {
      if (!property[field]) {
        errors.push(`Missing required field: ${field}`);
      }
    }

    // Check price range
    if (property.price || property.rent_price) {
      const price = property.price || property.rent_price;
      const range = config.validation?.priceRange;
      if (range && (price < range.min || price > range.max)) {
        warnings.push(`Price ${price} outside typical range ${range.min}-${range.max}`);
      }
    }

    // Check bedroom range
    if (property.bedrooms) {
      const range = config.validation?.bedroomRange || { min: 1, max: 15 };
      if (property.bedrooms < range.min || property.bedrooms > range.max) {
        errors.push(`Bedrooms ${property.bedrooms} outside valid range ${range.min}-${range.max}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: this.calculateQualityScore(property)
    };
  }

  // Calculate quality score for a property
  calculateQualityScore(property) {
    let score = 0;
    const maxScore = 12;

    // Basic fields (6 points)
    if (property.title && property.title.length > 10) score += 1;
    if (property.bedrooms) score += 1;
    if (property.bathrooms) score += 1;
    if (property.price || property.rent_price) score += 1;
    if (property.size_sqft) score += 1;
    if (property.description && property.description.length > 50) score += 1;

    // Advanced fields (4 points)
    if (property.lot_size_sqft) score += 1;
    if (property.year_built) score += 1;
    if (property.ownership_type) score += 1;
    if (property.amenities?.raw_amenities?.length > 0) score += 1;

    // Media fields (2 points)
    if (property.media?.images?.length > 0) score += 1;
    if (property.media?.external_id) score += 1;

    return Math.round((score / maxScore) * 100);
  }

  // Get registry statistics
  getStats() {
    const total = this.registeredWebsites.size;
    const active = this.getActiveWebsites().length;
    const inactive = total - active;

    const byType = {};
    for (const reg of this.registeredWebsites.values()) {
      byType[reg.type] = (byType[reg.type] || 0) + 1;
    }

    return {
      total,
      active,
      inactive,
      byType,
      websites: Array.from(this.registeredWebsites.keys())
    };
  }
}

// Singleton instance
const websiteRegistry = new WebsiteRegistry();

module.exports = {
  WebsiteRegistry,
  websiteRegistry
};
