// Deep investigation of database issues and field extraction problems
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc, eq } = require('drizzle-orm');

async function investigateDatabaseIssues() {
  console.log('🔍 Deep Database Investigation');
  console.log('='.repeat(50));
  
  try {
    // Get the most recent properties
    const recentProperties = await db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`📋 Found ${recentProperties.length} recent properties\n`);
    
    recentProperties.forEach((prop, index) => {
      console.log(`${index + 1}. 🏠 ${prop.title || 'UNTITLED'}`);
      console.log(`   🆔 ID: ${prop.id}`);
      console.log(`   🌐 Source: ${prop.media?.source_id || 'NULL'}`);
      console.log(`   🔗 External ID: ${prop.media?.external_id || 'NULL'}`);
      console.log(`   📅 Created: ${new Date(prop.created_at).toLocaleString()}`);
      console.log('');
      
      // Check ALL fields systematically
      console.log('   📊 FIELD ANALYSIS:');
      console.log(`   💰 Price: ${prop.price || 'NULL'} | Rent: ${prop.rent_price || 'NULL'}`);
      console.log(`   🛏️  Bedrooms: ${prop.bedrooms || 'NULL'}`);
      console.log(`   🚿 Bathrooms: ${prop.bathrooms || 'NULL'}`);
      console.log(`   🚗 Parking: ${prop.parking_spaces || 'NULL'}`);
      console.log(`   📐 Size (sqft): ${prop.size_sqft || 'NULL'}`);
      console.log(`   📏 Lot Size (sqft): ${prop.lot_size_sqft || 'NULL'}`);
      console.log(`   📅 Year Built: ${prop.year_built || 'NULL'}`);
      console.log(`   🏠 Ownership: ${prop.ownership_type || 'NULL'}`);
      console.log(`   📋 Lease Years: ${prop.lease_duration_years || 'NULL'}`);
      console.log(`   📝 Lease Text: ${prop.lease_duration_text || 'NULL'}`);
      console.log(`   📍 Address: ${prop.address || 'NULL'}`);
      console.log(`   🏙️  City: ${prop.city || 'NULL'}`);
      console.log(`   🌍 State: ${prop.state || 'NULL'}`);
      console.log(`   🌎 Country: ${prop.country || 'NULL'}`);
      console.log(`   🏷️  Property Type: ${prop.property_type || 'NULL'}`);
      console.log(`   📊 Status: ${prop.status || 'NULL'}`);
      
      // Description analysis
      if (prop.description) {
        const desc = prop.description.substring(0, 150);
        console.log(`   📝 Description: "${desc}${prop.description.length > 150 ? '...' : ''}"`);
        console.log(`   📏 Description Length: ${prop.description.length} chars`);
        
        // Check for problematic content
        const problems = [];
        if (prop.description.includes('WhatsApp')) problems.push('WhatsApp');
        if (prop.description.includes('https://')) problems.push('URLs');
        if (prop.description.includes('wp-content')) problems.push('wp-content');
        if (prop.description.includes('![')) problems.push('Images');
        if (prop.description.includes('[![')) problems.push('Linked Images');
        if (prop.description.includes('Online')) problems.push('Online text');
        
        if (problems.length > 0) {
          console.log(`   ⚠️  Description Issues: ${problems.join(', ')}`);
        } else {
          console.log(`   ✅ Description Clean`);
        }
      } else {
        console.log(`   📝 Description: NULL`);
      }
      
      // Amenities analysis
      if (prop.amenities) {
        const amenitiesCount = Array.isArray(prop.amenities) ? prop.amenities.length : 0;
        console.log(`   🏊 Amenities: ${amenitiesCount} items`);
        if (amenitiesCount > 0) {
          console.log(`   🏊 Amenities List: ${JSON.stringify(prop.amenities).substring(0, 100)}...`);
        }
      } else {
        console.log(`   🏊 Amenities: NULL`);
      }
      
      // Media analysis
      if (prop.media) {
        console.log(`   📸 Images: ${prop.media.image_count || 0}`);
        console.log(`   🔗 Source URL: ${prop.media.source_url ? 'Present' : 'NULL'}`);
        if (prop.media.images && prop.media.images.length > 0) {
          console.log(`   📸 First Image: ${prop.media.images[0].substring(0, 80)}...`);
        }
      } else {
        console.log(`   📸 Media: NULL`);
      }
      
      // Calculate completeness score
      const fields = [
        prop.title, prop.price || prop.rent_price, prop.bedrooms, prop.bathrooms,
        prop.size_sqft, prop.year_built, prop.ownership_type, prop.address,
        prop.city, prop.description
      ];
      const populatedFields = fields.filter(f => f !== null && f !== undefined && f !== '').length;
      const completeness = ((populatedFields / fields.length) * 100).toFixed(1);
      
      console.log(`   📊 Completeness: ${completeness}% (${populatedFields}/${fields.length} fields)`);
      
      console.log('   ' + '─'.repeat(80));
      console.log('');
    });
    
    // Summary analysis
    console.log('📊 SUMMARY ANALYSIS');
    console.log('='.repeat(50));
    
    const sourceStats = {};
    const fieldStats = {
      title: 0, price: 0, bedrooms: 0, bathrooms: 0, size_sqft: 0,
      year_built: 0, ownership_type: 0, address: 0, city: 0, description: 0
    };
    
    recentProperties.forEach(prop => {
      const source = prop.media?.source_id || 'unknown';
      if (!sourceStats[source]) {
        sourceStats[source] = { count: 0, completeness: [] };
      }
      sourceStats[source].count++;
      
      // Count populated fields
      if (prop.title) fieldStats.title++;
      if (prop.price || prop.rent_price) fieldStats.price++;
      if (prop.bedrooms) fieldStats.bedrooms++;
      if (prop.bathrooms) fieldStats.bathrooms++;
      if (prop.size_sqft) fieldStats.size_sqft++;
      if (prop.year_built) fieldStats.year_built++;
      if (prop.ownership_type) fieldStats.ownership_type++;
      if (prop.address) fieldStats.address++;
      if (prop.city) fieldStats.city++;
      if (prop.description) fieldStats.description++;
      
      const fields = [
        prop.title, prop.price || prop.rent_price, prop.bedrooms, prop.bathrooms,
        prop.size_sqft, prop.year_built, prop.ownership_type, prop.address,
        prop.city, prop.description
      ];
      const populatedFields = fields.filter(f => f !== null && f !== undefined && f !== '').length;
      const completeness = (populatedFields / fields.length) * 100;
      sourceStats[source].completeness.push(completeness);
    });
    
    console.log('🌐 By Source:');
    Object.entries(sourceStats).forEach(([source, stats]) => {
      const avgCompleteness = stats.completeness.reduce((a, b) => a + b, 0) / stats.completeness.length;
      console.log(`   ${source}: ${stats.count} properties, ${avgCompleteness.toFixed(1)}% avg completeness`);
    });
    
    console.log('\n📋 Field Population Rates:');
    const total = recentProperties.length;
    Object.entries(fieldStats).forEach(([field, count]) => {
      const percentage = ((count / total) * 100).toFixed(1);
      const status = percentage >= 80 ? '✅' : percentage >= 50 ? '⚠️' : '❌';
      console.log(`   ${status} ${field}: ${percentage}% (${count}/${total})`);
    });
    
    // Identify specific issues
    console.log('\n🚨 CRITICAL ISSUES IDENTIFIED:');
    
    const issues = [];
    if (fieldStats.bedrooms / total < 0.5) issues.push('Bedroom extraction failing');
    if (fieldStats.bathrooms / total < 0.5) issues.push('Bathroom extraction failing');
    if (fieldStats.size_sqft / total < 0.5) issues.push('Size extraction failing');
    if (fieldStats.year_built / total < 0.3) issues.push('Year built extraction very low');
    if (fieldStats.ownership_type / total < 0.5) issues.push('Ownership type extraction failing');
    
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ❌ ${issue}`);
    });
    
    if (issues.length === 0) {
      console.log('   ✅ No critical issues found');
    }
    
    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Investigate mapper field extraction logic');
    console.log('2. Check markdown content quality');
    console.log('3. Debug specific failing URLs');
    console.log('4. Fix regex patterns for field extraction');
    
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
    console.error(error.stack);
  }
}

// Run investigation
if (require.main === module) {
  investigateDatabaseIssues().catch(console.error);
}

module.exports = { investigateDatabaseIssues };
