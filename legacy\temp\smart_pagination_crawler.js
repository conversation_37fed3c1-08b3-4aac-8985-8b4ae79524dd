// Smart Pagination-Aware Crawler
require('dotenv').config();
const { db, websiteConfigs, crawlJobs, discoveredUrls } = require('./drizzle_client');
const { eq, desc, and, gte } = require('drizzle-orm');
const { SmartCrawler } = require('./scrape_worker/smart_crawler');

class SmartPaginationCrawler {
  constructor() {
    this.crawler = new SmartCrawler();
  }

  // Discover pagination URLs for a website
  async discoverPaginationUrls(websiteId) {
    console.log(`🔍 Discovering pagination URLs for ${websiteId}...`);
    
    const paginationPatterns = {
      bali_home_immo: [
        'https://bali-home-immo.com/realestate-property/for-rent/page/2/',
        'https://bali-home-immo.com/realestate-property/for-rent/page/3/',
        'https://bali-home-immo.com/realestate-property/for-rent/page/4/',
        'https://bali-home-immo.com/realestate-property/for-rent/page/5/',
        'https://bali-home-immo.com/realestate-property/for-sale/page/2/',
        'https://bali-home-immo.com/realestate-property/for-sale/page/3/',
      ],
      betterplace: [
        'https://betterplace.cc/buy/properties?page=2',
        'https://betterplace.cc/buy/properties?page=3',
        'https://betterplace.cc/buy/properties?page=4',
        'https://betterplace.cc/buy/properties?page=5',
      ],
      bali_villa_realty: [
        'https://balivillarealty.com/search-results/page/2/',
        'https://balivillarealty.com/search-results/page/3/',
        'https://balivillarealty.com/villa/page/2/',
        'https://balivillarealty.com/villa/page/3/',
        'https://balivillarealty.com/apartment/page/2/',
      ]
    };

    return paginationPatterns[websiteId] || [];
  }

  // Get already crawled URLs to avoid duplicates
  async getAlreadyCrawledUrls(websiteId) {
    const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
    
    const recentJobs = await db.select()
      .from(crawlJobs)
      .where(and(
        eq(crawlJobs.website_id, websiteId),
        gte(crawlJobs.created_at, threeDaysAgo)
      ));

    const crawledUrls = new Set();
    
    for (const job of recentJobs) {
      const urls = await db.select()
        .from(discoveredUrls)
        .where(eq(discoveredUrls.crawl_job_id, job.id));
      
      urls.forEach(url => crawledUrls.add(url.url));
    }

    return crawledUrls;
  }

  // Smart crawl with pagination awareness
  async smartCrawl(websiteId, options = {}) {
    console.log(`🚀 Starting smart crawl for ${websiteId}...`);

    const config = await db.select()
      .from(websiteConfigs)
      .where(eq(websiteConfigs.website_id, websiteId))
      .limit(1);

    if (config.length === 0) {
      throw new Error(`No configuration found for ${websiteId}`);
    }

    const websiteConfig = config[0];
    
    // Get already crawled URLs
    const alreadyCrawled = await this.getAlreadyCrawledUrls(websiteId);
    console.log(`📊 Already crawled ${alreadyCrawled.size} URLs in last 3 days`);

    // Discover pagination URLs
    const paginationUrls = await this.discoverPaginationUrls(websiteId);
    console.log(`📄 Found ${paginationUrls.length} pagination URLs to try`);

    // Find uncrawled pagination URLs
    const uncrawledPagination = paginationUrls.filter(url => !alreadyCrawled.has(url));
    console.log(`🆕 ${uncrawledPagination.length} uncrawled pagination URLs found`);

    if (uncrawledPagination.length === 0) {
      console.log('⚠️  No new pagination URLs to crawl. Consider:');
      console.log('   1. Increasing pagination range');
      console.log('   2. Adding category/filter URLs');
      console.log('   3. Reducing freshness check period');
      return null;
    }

    // Start with the first uncrawled pagination URL
    const startUrl = uncrawledPagination[0];
    console.log(`🎯 Starting crawl from: ${startUrl}`);

    // Update website config to start from pagination URL
    await db.update(websiteConfigs)
      .set({
        base_url: startUrl,
        max_pages: Math.min(50, options.maxPages || 50), // Smaller batches for pagination
        next_crawl_at: new Date(Date.now() - 1000)
      })
      .where(eq(websiteConfigs.website_id, websiteId));

    // Start the crawl
    const jobId = await this.crawler.startWebsiteCrawl(websiteId);
    
    // Reset base_url back to original after crawl
    setTimeout(async () => {
      const originalUrls = {
        bali_home_immo: 'https://bali-home-immo.com',
        betterplace: 'https://betterplace.cc',
        bali_villa_realty: 'https://balivillarealty.com'
      };
      
      await db.update(websiteConfigs)
        .set({ base_url: originalUrls[websiteId] })
        .where(eq(websiteConfigs.website_id, websiteId));
    }, 5000);

    return jobId;
  }

  // Analyze website structure
  async analyzeWebsiteStructure(websiteId) {
    console.log(`🔍 Analyzing website structure for ${websiteId}...`);

    const recentJobs = await db.select()
      .from(crawlJobs)
      .where(eq(crawlJobs.website_id, websiteId))
      .orderBy(desc(crawlJobs.created_at))
      .limit(3);

    if (recentJobs.length === 0) {
      console.log('❌ No crawl jobs found for analysis');
      return;
    }

    console.log(`\n📊 Analysis of ${recentJobs.length} recent crawls:`);

    for (const job of recentJobs) {
      console.log(`\n🔍 Job ${job.id.substring(0, 8)}... (${job.status}):`);
      console.log(`   Total URLs: ${job.total_urls}`);
      console.log(`   Property URLs: ${job.property_urls_found}`);
      console.log(`   Success rate: ${((job.property_urls_found / job.total_urls) * 100).toFixed(1)}%`);

      const urls = await db.select()
        .from(discoveredUrls)
        .where(eq(discoveredUrls.crawl_job_id, job.id))
        .orderBy(desc(discoveredUrls.confidence_score))
        .limit(10);

      console.log(`   Top URLs found:`);
      urls.forEach((url, index) => {
        const type = url.is_property_page ? '🏠 Property' : '📄 Listing';
        console.log(`     ${index + 1}. ${type}: ${url.url.substring(0, 60)}...`);
      });
    }

    // Suggest next steps
    console.log(`\n💡 Suggestions for ${websiteId}:`);
    const avgPropertyRate = recentJobs.reduce((sum, job) => 
      sum + (job.property_urls_found / job.total_urls), 0) / recentJobs.length;

    if (avgPropertyRate < 0.1) {
      console.log('   ⚠️  Low property discovery rate - consider:');
      console.log('     • Adjusting crawl patterns');
      console.log('     • Adding specific category URLs');
      console.log('     • Increasing max_pages limit');
    } else {
      console.log('   ✅ Good property discovery rate');
      console.log('   💡 Try pagination crawling for more properties');
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const websiteId = args[1];

  const crawler = new SmartPaginationCrawler();

  try {
    switch (command) {
      case 'smart-crawl':
        if (!websiteId) {
          console.log('Usage: node smart_pagination_crawler.js smart-crawl <website_id>');
          process.exit(1);
        }
        await crawler.smartCrawl(websiteId);
        break;

      case 'analyze':
        if (!websiteId) {
          console.log('Usage: node smart_pagination_crawler.js analyze <website_id>');
          process.exit(1);
        }
        await crawler.analyzeWebsiteStructure(websiteId);
        break;

      default:
        console.log('Available commands:');
        console.log('  smart-crawl <website_id> - Smart crawl with pagination awareness');
        console.log('  analyze <website_id>     - Analyze website crawl patterns');
        console.log('');
        console.log('Available websites: bali_home_immo, betterplace, bali_villa_realty');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }

  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = { SmartPaginationCrawler };
