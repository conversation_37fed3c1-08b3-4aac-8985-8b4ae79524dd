// Test script: Scrape 2 URLs per website and store in database
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, scrapingQueue, properties } = require('./drizzle_client');
const { eq, desc, and, inArray } = require('drizzle-orm');

async function test2UrlsPerWebsite() {
  console.log('🧪 Testing: 2 URLs per website scraping');
  console.log('='.repeat(60));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  const queueManager = new QueueManager();
  const websites = ['betterplace', 'bali_home_immo', 'bali_villa_realty', 'villa_bali_sale'];
  
  try {
    // Check initial database count
    const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`📊 Initial properties in database: ${initialCount[0].count}`);
    
    let totalProcessed = 0;
    let totalSuccessful = 0;
    
    for (const website of websites) {
      console.log(`\n🌐 Processing ${website.toUpperCase()}`);
      console.log('-'.repeat(40));
      
      try {
        // Check pending URLs for this website
        const pendingUrls = await db
          .select()
          .from(scrapingQueue)
          .where(and(
            eq(scrapingQueue.website_id, website),
            eq(scrapingQueue.status, 'pending')
          ))
          .limit(5); // Get 5 to show options
        
        if (pendingUrls.length === 0) {
          console.log(`   ❌ No pending URLs found for ${website}`);
          continue;
        }
        
        console.log(`   📋 Found ${pendingUrls.length} pending URLs`);
        console.log(`   🎯 Will process 2 URLs:`);
        pendingUrls.slice(0, 2).forEach((url, i) => {
          console.log(`      ${i + 1}. ${url.url.substring(0, 70)}...`);
        });
        
        // Process 2 URLs for this website
        console.log(`   🔄 Processing 2 URLs...`);
        const startTime = Date.now();
        
        const result = await queueManager.processQueue(website, 2);
        
        const duration = ((Date.now() - startTime) / 1000).toFixed(1);
        
        if (result && result.processed > 0) {
          console.log(`   ✅ Successfully processed ${result.processed} URLs in ${duration}s`);
          console.log(`      Successful: ${result.successful || 0}`);
          console.log(`      Failed: ${result.failed || 0}`);
          console.log(`      Skipped: ${result.skipped || 0}`);
          
          totalProcessed += result.processed;
          totalSuccessful += (result.successful || 0);
          
          // Show newly created properties
          await showNewProperties(website);
          
        } else {
          console.log(`   ⚠️  No URLs were processed (${duration}s)`);
        }
        
      } catch (error) {
        console.error(`   ❌ Error processing ${website}:`, error.message);
      }
      
      // Wait between websites to avoid rate limits
      if (website !== websites[websites.length - 1]) {
        console.log('   ⏳ Waiting 10 seconds before next website...');
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }
    
    // Final summary
    console.log('\n📊 FINAL SUMMARY');
    console.log('='.repeat(60));
    console.log(`⏰ Completed at: ${new Date().toLocaleTimeString()}`);
    console.log(`📈 Total URLs processed: ${totalProcessed}`);
    console.log(`✅ Total successful: ${totalSuccessful}`);
    console.log(`❌ Total failed: ${totalProcessed - totalSuccessful}`);
    
    // Check final database count
    const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
    const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
    console.log(`🆕 New properties added to database: ${newProperties}`);
    
    // Show latest properties
    if (newProperties > 0) {
      console.log('\n🏠 Latest Properties Added:');
      const latestProps = await db
        .select({
          title: properties.title,
          source_id: properties.source_id,
          price: properties.price,
          rent_price: properties.rent_price,
          city: properties.city,
          bedrooms: properties.bedrooms,
          created_at: properties.created_at
        })
        .from(properties)
        .orderBy(desc(properties.created_at))
        .limit(8); // Show up to 8 latest (2 per website)
      
      latestProps.forEach((prop, i) => {
        const price = prop.price ? `IDR ${prop.price.toLocaleString()}` : 
                     prop.rent_price ? `IDR ${prop.rent_price.toLocaleString()}/month` : 'Price on request';
        console.log(`   ${i + 1}. [${prop.source_id}] ${prop.title}`);
        console.log(`      💰 ${price} | 🏠 ${prop.bedrooms} bed | 📍 ${prop.city}`);
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
        console.log('');
      });
    }
    
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

async function showNewProperties(website) {
  try {
    // Get the most recent properties for this website (last 5 minutes)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    const recentProps = await db
      .select({
        title: properties.title,
        price: properties.price,
        rent_price: properties.rent_price,
        bedrooms: properties.bedrooms,
        city: properties.city,
        created_at: properties.created_at
      })
      .from(properties)
      .where(and(
        eq(properties.source_id, website),
        sql`${properties.created_at} > ${fiveMinutesAgo}`
      ))
      .orderBy(desc(properties.created_at))
      .limit(3);
    
    if (recentProps.length > 0) {
      console.log(`   🏠 New properties created:`);
      recentProps.forEach((prop, i) => {
        const price = prop.price ? `IDR ${prop.price.toLocaleString()}` : 
                     prop.rent_price ? `IDR ${prop.rent_price.toLocaleString()}/month` : 'Price on request';
        console.log(`      ${i + 1}. ${prop.title.substring(0, 50)}...`);
        console.log(`         💰 ${price} | 🏠 ${prop.bedrooms} bed | 📍 ${prop.city}`);
      });
    }
  } catch (error) {
    console.log(`   ⚠️  Could not fetch new properties: ${error.message}`);
  }
}

// Import sql for count queries
const { sql } = require('drizzle-orm');

// Run the test
test2UrlsPerWebsite();
