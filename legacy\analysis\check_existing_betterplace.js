// Check existing BetterPlace properties
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq, desc } = require('drizzle-orm');

async function checkExistingBetterPlace() {
  try {
    console.log('🏠 Checking existing BetterPlace properties...');
    
    const betterPlaceProps = await db
      .select({
        id: properties.id,
        title: properties.title,
        external_id: properties.external_id,
        source_url: properties.source_url,
        amenities: properties.amenities,
        description: properties.description,
        created_at: properties.created_at
      })
      .from(properties)
      .where(eq(properties.source_id, 'betterplace'))
      .orderBy(desc(properties.created_at))
      .limit(3);
    
    console.log(`Found ${betterPlaceProps.length} BetterPlace properties:`);
    
    betterPlaceProps.forEach((prop, i) => {
      console.log(`\n${i + 1}. ${prop.title}`);
      console.log(`   ID: ${prop.id}`);
      console.log(`   External ID: ${prop.external_id}`);
      console.log(`   Source URL: ${prop.source_url}`);
      console.log(`   Description length: ${prop.description?.length || 0}`);
      console.log(`   Amenities: ${prop.amenities?.raw_amenities?.length || 0} items`);
      if (prop.amenities?.raw_amenities?.length > 0) {
        console.log(`   Amenities list: ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}`);
      }
      console.log(`   Created: ${new Date(prop.created_at).toLocaleString()}`);
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

checkExistingBetterPlace();
