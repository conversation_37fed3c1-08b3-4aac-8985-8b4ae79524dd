// Test Bali Home Immo markdown scraping
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { db, properties } = require('./drizzle_client');
const { desc } = require('drizzle-orm');

async function testBaliHomeImmoMarkdown() {
  console.log('🧪 Testing Bali Home Immo Markdown-Only Scraping');
  console.log('='.repeat(60));
  
  try {
    // Test URLs for Bali Home Immo
    const testUrls = [
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/3-bedroom-villa-for-rent-in-canggu-rf1507'
    ];
    
    console.log(`🔄 Step 1: Processing ${testUrls.length} Bali Home Immo URLs...`);
    
    // Run batch extraction
    const batchResult = await runExtractBatch('bali_home_immo', testUrls, {});
    
    console.log('\n📊 Batch Results:');
    console.log(`   Total processed: ${batchResult?.length || 0}`);
    const successful = batchResult?.filter(r => r && r.title) || [];
    const failed = (batchResult?.length || 0) - successful.length;
    console.log(`   Successful: ${successful.length}`);
    console.log(`   Failed: ${failed}`);
    
    if (successful.length > 0) {
      console.log('   Successfully processed properties:');
      successful.forEach((prop, index) => {
        console.log(`     ${index + 1}. ${prop.title}`);
        console.log(`        Price: ${prop.price || 'N/A'}`);
        console.log(`        Beds/Baths: ${prop.bedrooms || 'N/A'}/${prop.bathrooms || 'N/A'}`);
        console.log(`        Location: ${prop.address || prop.location || 'N/A'}`);
        console.log(`        Size: ${prop.size_sqft || 'N/A'} sqft`);
        console.log(`        Ownership: ${prop.ownership_type || 'N/A'}`);
      });
    }
    
    // Verify results in database
    console.log('\n🔄 Step 2: Verifying database results');
    const recentProperties = await db
      .select({
        id: properties.id,
        title: properties.title,
        price: properties.price,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        media: properties.media,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`📋 Recent properties (${recentProperties.length}):`);
    recentProperties.forEach((prop, index) => {
      if (prop.media?.source_id === 'bali_home_immo') {
        console.log(`   ${index + 1}. ✅ ${prop.title}`);
        console.log(`      Price: ${prop.price}`);
        console.log(`      Beds/Baths: ${prop.bedrooms}/${prop.bathrooms}`);
        console.log(`      Source: ${prop.media?.source_id}`);
        console.log(`      Created: ${prop.created_at}`);
      }
    });
    
    console.log('\n🎉 Bali Home Immo markdown-only test completed!');
    console.log('💰 Cost savings: Using markdown instead of JSON (5x cheaper)');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testBaliHomeImmoMarkdown().catch(console.error);
