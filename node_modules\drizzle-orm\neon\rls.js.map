{"version": 3, "sources": ["../../src/neon/rls.ts"], "sourcesContent": ["import { is } from '~/entity.ts';\nimport { type AnyPgColumn, pgPolicy, type PgPolicyToOption } from '~/pg-core/index.ts';\nimport { PgRole, pgRole } from '~/pg-core/roles.ts';\nimport { type SQL, sql } from '~/sql/sql.ts';\n\n/**\n * Generates a set of PostgreSQL row-level security (RLS) policies for CRUD operations based on the provided options.\n *\n * @param options - An object containing the policy configuration.\n * @param options.role - The PostgreSQL role(s) to apply the policy to. Can be a single `PgRole` instance or an array of `PgRole` instances or role names.\n * @param options.read - The SQL expression or boolean value that defines the read policy. Set to `true` to allow all reads, `false` to deny all reads, or provide a custom SQL expression. Set to `null` to prevent the policy from being generated.\n * @param options.modify - The SQL expression or boolean value that defines the modify (insert, update, delete) policies. Set to `true` to allow all modifications, `false` to deny all modifications, or provide a custom SQL expression. Set to `null` to prevent policies from being generated.\n * @returns An array of PostgreSQL policy definitions, one for each CRUD operation.\n */\nexport const crudPolicy = (options: {\n\trole: PgPolicyToOption;\n\tread: SQL | boolean | null;\n\tmodify: SQL | boolean | null;\n}) => {\n\tif (options.read === undefined) {\n\t\tthrow new Error('crudPolicy requires a read policy');\n\t}\n\n\tif (options.modify === undefined) {\n\t\tthrow new Error('crudPolicy requires a modify policy');\n\t}\n\n\tlet read: SQL | undefined;\n\tif (options.read === true) {\n\t\tread = sql`true`;\n\t} else if (options.read === false) {\n\t\tread = sql`false`;\n\t} else if (options.read !== null) {\n\t\tread = options.read;\n\t}\n\n\tlet modify: SQL | undefined;\n\tif (options.modify === true) {\n\t\tmodify = sql`true`;\n\t} else if (options.modify === false) {\n\t\tmodify = sql`false`;\n\t} else if (options.modify !== null) {\n\t\tmodify = options.modify;\n\t}\n\n\tlet rolesName = '';\n\tif (Array.isArray(options.role)) {\n\t\trolesName = options.role\n\t\t\t.map((it) => {\n\t\t\t\treturn is(it, PgRole) ? it.name : (it as string);\n\t\t\t})\n\t\t\t.join('-');\n\t} else {\n\t\trolesName = is(options.role, PgRole)\n\t\t\t? options.role.name\n\t\t\t: (options.role as string);\n\t}\n\n\treturn [\n\t\tread\n\t\t&& pgPolicy(`crud-${rolesName}-policy-select`, {\n\t\t\tfor: 'select',\n\t\t\tto: options.role,\n\t\t\tusing: read,\n\t\t}),\n\n\t\tmodify\n\t\t&& pgPolicy(`crud-${rolesName}-policy-insert`, {\n\t\t\tfor: 'insert',\n\t\t\tto: options.role,\n\t\t\twithCheck: modify,\n\t\t}),\n\t\tmodify\n\t\t&& pgPolicy(`crud-${rolesName}-policy-update`, {\n\t\t\tfor: 'update',\n\t\t\tto: options.role,\n\t\t\tusing: modify,\n\t\t\twithCheck: modify,\n\t\t}),\n\t\tmodify\n\t\t&& pgPolicy(`crud-${rolesName}-policy-delete`, {\n\t\t\tfor: 'delete',\n\t\t\tto: options.role,\n\t\t\tusing: modify,\n\t\t}),\n\t].filter(Boolean);\n};\n\n// These are default roles that Neon will set up.\nexport const authenticatedRole = pgRole('authenticated').existing();\nexport const anonymousRole = pgRole('anonymous').existing();\n\nexport const authUid = (userIdColumn: AnyPgColumn) => sql`(select auth.user_id() = ${userIdColumn})`;\n"], "mappings": "AAAA,SAAS,UAAU;AACnB,SAA2B,gBAAuC;AAClE,SAAS,QAAQ,cAAc;AAC/B,SAAmB,WAAW;AAWvB,MAAM,aAAa,CAAC,YAIrB;AACL,MAAI,QAAQ,SAAS,QAAW;AAC/B,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACpD;AAEA,MAAI,QAAQ,WAAW,QAAW;AACjC,UAAM,IAAI,MAAM,qCAAqC;AAAA,EACtD;AAEA,MAAI;AACJ,MAAI,QAAQ,SAAS,MAAM;AAC1B,WAAO;AAAA,EACR,WAAW,QAAQ,SAAS,OAAO;AAClC,WAAO;AAAA,EACR,WAAW,QAAQ,SAAS,MAAM;AACjC,WAAO,QAAQ;AAAA,EAChB;AAEA,MAAI;AACJ,MAAI,QAAQ,WAAW,MAAM;AAC5B,aAAS;AAAA,EACV,WAAW,QAAQ,WAAW,OAAO;AACpC,aAAS;AAAA,EACV,WAAW,QAAQ,WAAW,MAAM;AACnC,aAAS,QAAQ;AAAA,EAClB;AAEA,MAAI,YAAY;AAChB,MAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG;AAChC,gBAAY,QAAQ,KAClB,IAAI,CAAC,OAAO;AACZ,aAAO,GAAG,IAAI,MAAM,IAAI,GAAG,OAAQ;AAAA,IACpC,CAAC,EACA,KAAK,GAAG;AAAA,EACX,OAAO;AACN,gBAAY,GAAG,QAAQ,MAAM,MAAM,IAChC,QAAQ,KAAK,OACZ,QAAQ;AAAA,EACb;AAEA,SAAO;AAAA,IACN,QACG,SAAS,QAAQ,SAAS,kBAAkB;AAAA,MAC9C,KAAK;AAAA,MACL,IAAI,QAAQ;AAAA,MACZ,OAAO;AAAA,IACR,CAAC;AAAA,IAED,UACG,SAAS,QAAQ,SAAS,kBAAkB;AAAA,MAC9C,KAAK;AAAA,MACL,IAAI,QAAQ;AAAA,MACZ,WAAW;AAAA,IACZ,CAAC;AAAA,IACD,UACG,SAAS,QAAQ,SAAS,kBAAkB;AAAA,MAC9C,KAAK;AAAA,MACL,IAAI,QAAQ;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,IACZ,CAAC;AAAA,IACD,UACG,SAAS,QAAQ,SAAS,kBAAkB;AAAA,MAC9C,KAAK;AAAA,MACL,IAAI,QAAQ;AAAA,MACZ,OAAO;AAAA,IACR,CAAC;AAAA,EACF,EAAE,OAAO,OAAO;AACjB;AAGO,MAAM,oBAAoB,OAAO,eAAe,EAAE,SAAS;AAC3D,MAAM,gBAAgB,OAAO,WAAW,EAAE,SAAS;AAEnD,MAAM,UAAU,CAAC,iBAA8B,+BAA+B,YAAY;", "names": []}