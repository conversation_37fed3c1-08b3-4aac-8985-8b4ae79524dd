{"version": 3, "sources": ["../../src/mysql-core/foreign-keys.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { AnyMySqlColumn, MySqlColumn } from './columns/index.ts';\nimport type { MySqlTable } from './table.ts';\n\nexport type UpdateDeleteAction = 'cascade' | 'restrict' | 'no action' | 'set null' | 'set default';\n\nexport type Reference = () => {\n\treadonly name?: string;\n\treadonly columns: MySqlColumn[];\n\treadonly foreignTable: MySqlTable;\n\treadonly foreignColumns: MySqlColumn[];\n};\n\nexport class ForeignKeyBuilder {\n\tstatic readonly [entityKind]: string = 'MySqlForeignKeyBuilder';\n\n\t/** @internal */\n\treference: Reference;\n\n\t/** @internal */\n\t_onUpdate: UpdateDeleteAction | undefined;\n\n\t/** @internal */\n\t_onDelete: UpdateDeleteAction | undefined;\n\n\tconstructor(\n\t\tconfig: () => {\n\t\t\tname?: string;\n\t\t\tcolumns: MySqlColumn[];\n\t\t\tforeignColumns: MySqlColumn[];\n\t\t},\n\t\tactions?: {\n\t\t\tonUpdate?: UpdateDeleteAction;\n\t\t\tonDelete?: UpdateDeleteAction;\n\t\t} | undefined,\n\t) {\n\t\tthis.reference = () => {\n\t\t\tconst { name, columns, foreignColumns } = config();\n\t\t\treturn { name, columns, foreignTable: foreignColumns[0]!.table as MySqlTable, foreignColumns };\n\t\t};\n\t\tif (actions) {\n\t\t\tthis._onUpdate = actions.onUpdate;\n\t\t\tthis._onDelete = actions.onDelete;\n\t\t}\n\t}\n\n\tonUpdate(action: UpdateDeleteAction): this {\n\t\tthis._onUpdate = action;\n\t\treturn this;\n\t}\n\n\tonDelete(action: UpdateDeleteAction): this {\n\t\tthis._onDelete = action;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: MySqlTable): ForeignKey {\n\t\treturn new ForeignKey(table, this);\n\t}\n}\n\nexport type AnyForeignKeyBuilder = ForeignKeyBuilder;\n\nexport class ForeignKey {\n\tstatic readonly [entityKind]: string = 'MySqlForeignKey';\n\n\treadonly reference: Reference;\n\treadonly onUpdate: UpdateDeleteAction | undefined;\n\treadonly onDelete: UpdateDeleteAction | undefined;\n\n\tconstructor(readonly table: MySqlTable, builder: ForeignKeyBuilder) {\n\t\tthis.reference = builder.reference;\n\t\tthis.onUpdate = builder._onUpdate;\n\t\tthis.onDelete = builder._onDelete;\n\t}\n\n\tgetName(): string {\n\t\tconst { name, columns, foreignColumns } = this.reference();\n\t\tconst columnNames = columns.map((column) => column.name);\n\t\tconst foreignColumnNames = foreignColumns.map((column) => column.name);\n\t\tconst chunks = [\n\t\t\tthis.table[TableName],\n\t\t\t...columnNames,\n\t\t\tforeignColumns[0]!.table[TableName],\n\t\t\t...foreignColumnNames,\n\t\t];\n\t\treturn name ?? `${chunks.join('_')}_fk`;\n\t}\n}\n\ntype ColumnsWithTable<\n\tTTableName extends string,\n\tTColumns extends MySqlColumn[],\n> = { [Key in keyof TColumns]: AnyMySqlColumn<{ tableName: TTableName }> };\n\nexport type GetColumnsTable<TColumns extends MySqlColumn | MySqlColumn[]> = (\n\tTColumns extends MySqlColumn ? TColumns\n\t\t: TColumns extends MySqlColumn[] ? TColumns[number]\n\t\t: never\n) extends AnyMySqlColumn<{ tableName: infer TTableName extends string }> ? TTableName\n\t: never;\n\nexport function foreignKey<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends [AnyMySqlColumn<{ tableName: TTableName }>, ...AnyMySqlColumn<{ tableName: TTableName }>[]],\n>(\n\tconfig: {\n\t\tname?: string;\n\t\tcolumns: TColumns;\n\t\tforeignColumns: ColumnsWithTable<TForeignTableName, TColumns>;\n\t},\n): ForeignKeyBuilder {\n\tfunction mappedConfig() {\n\t\tconst { name, columns, foreignColumns } = config;\n\t\treturn {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tforeignColumns,\n\t\t};\n\t}\n\n\treturn new ForeignKeyBuilder(mappedConfig);\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAanB,MAAM,kBAAkB;AAAA,EAC9B,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA;AAAA,EAEA,YACC,QAKA,SAIC;AACD,SAAK,YAAY,MAAM;AACtB,YAAM,EAAE,MAAM,SAAS,eAAe,IAAI,OAAO;AACjD,aAAO,EAAE,MAAM,SAAS,cAAc,eAAe,CAAC,EAAG,OAAqB,eAAe;AAAA,IAC9F;AACA,QAAI,SAAS;AACZ,WAAK,YAAY,QAAQ;AACzB,WAAK,YAAY,QAAQ;AAAA,IAC1B;AAAA,EACD;AAAA,EAEA,SAAS,QAAkC;AAC1C,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA,EAEA,SAAS,QAAkC;AAC1C,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,OAA+B;AACpC,WAAO,IAAI,WAAW,OAAO,IAAI;AAAA,EAClC;AACD;AAIO,MAAM,WAAW;AAAA,EAOvB,YAAqB,OAAmB,SAA4B;AAA/C;AACpB,SAAK,YAAY,QAAQ;AACzB,SAAK,WAAW,QAAQ;AACxB,SAAK,WAAW,QAAQ;AAAA,EACzB;AAAA,EAVA,QAAiB,UAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EACA;AAAA,EAQT,UAAkB;AACjB,UAAM,EAAE,MAAM,SAAS,eAAe,IAAI,KAAK,UAAU;AACzD,UAAM,cAAc,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI;AACvD,UAAM,qBAAqB,eAAe,IAAI,CAAC,WAAW,OAAO,IAAI;AACrE,UAAM,SAAS;AAAA,MACd,KAAK,MAAM,SAAS;AAAA,MACpB,GAAG;AAAA,MACH,eAAe,CAAC,EAAG,MAAM,SAAS;AAAA,MAClC,GAAG;AAAA,IACJ;AACA,WAAO,QAAQ,GAAG,OAAO,KAAK,GAAG,CAAC;AAAA,EACnC;AACD;AAcO,SAAS,WAKf,QAKoB;AACpB,WAAS,eAAe;AACvB,UAAM,EAAE,MAAM,SAAS,eAAe,IAAI;AAC1C,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAEA,SAAO,IAAI,kBAAkB,YAAY;AAC1C;", "names": []}