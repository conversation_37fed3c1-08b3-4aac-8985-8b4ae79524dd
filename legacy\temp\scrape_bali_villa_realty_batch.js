// Scrape 10 properties from Bali Villa Realty
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { testConnection } = require('./drizzle_client');

async function scrapeBaliVillaRealtyBatch() {
  console.log('🏠 Scraping 10 properties from Bali Villa Realty...\n');
  
  // Test database connection first
  console.log('1. Testing database connection...');
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ Database connection failed. Exiting.');
    return;
  }
  
  // URLs to scrape - I'll use the main listing page multiple times to get different properties
  // In a real scenario, you'd want to first scrape the listing page to get individual property URLs
  const urls = [
    'https://balivillarealty.com/bali-villa-rentals/',
    'https://balivillarealty.com/bali-villa-rentals/?page=2',
    'https://balivillarealty.com/bali-villa-rentals/?page=3',
    'https://balivillarealty.com/bali-villa-rentals/?page=4',
    'https://balivillarealty.com/bali-villa-rentals/?page=5',
    'https://balivillarealty.com/bali-villa-rentals/?page=6',
    'https://balivillarealty.com/bali-villa-rentals/?page=7',
    'https://balivillarealty.com/bali-villa-rentals/?page=8',
    'https://balivillarealty.com/bali-villa-rentals/?page=9',
    'https://balivillarealty.com/bali-villa-rentals/?page=10'
  ];
  
  console.log(`\n2. Scraping ${urls.length} URLs from Bali Villa Realty...`);
  console.log('URLs to scrape:');
  urls.forEach((url, index) => {
    console.log(`   ${index + 1}. ${url}`);
  });
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('bali_villa_realty', urls, {});
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n3. ✅ Scraping completed in ${duration} seconds!`);
    console.log('\n📊 Results Summary:');
    
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}`);
    console.log(`   ❌ Failed: ${failed.length}`);
    console.log(`   📈 Success Rate: ${((successful.length / results.length) * 100).toFixed(1)}%`);
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully scraped properties:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title}`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ Failed properties:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. Error: ${result.error}`);
      });
    }
    
  } catch (error) {
    console.error(`❌ Batch scraping failed:`, error.message);
  }
  
  console.log('\n🎉 Bali Villa Realty batch scraping completed!');
}

// Run the scraping
if (require.main === module) {
  scrapeBaliVillaRealtyBatch().catch(console.error);
}

module.exports = { scrapeBaliVillaRealtyBatch };
