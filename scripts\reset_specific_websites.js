// Reset specific websites for re-crawling
require('dotenv').config();
const { db, websiteConfigs, discoveredUrls, scrapingQueue, closeConnection } = require('../drizzle_client');
const { eq, inArray } = require('drizzle-orm');

async function resetSpecificWebsites() {
  console.log('🔄 Resetting Specific Websites for Re-crawling\n');
  
  const websitesToReset = ['betterplace', 'bali_home_immo', 'villabalisale.com'];
  
  try {
    // Reset timestamps for specific websites
    const result = await db
      .update(websiteConfigs)
      .set({
        last_sitemap_check: null,
        next_crawl_at: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        updated_at: new Date()
      })
      .where(inArray(websiteConfigs.website_id, websitesToReset))
      .returning({ website_id: websiteConfigs.website_id });
    
    console.log('✅ Reset timestamps for websites:');
    result.forEach(row => {
      console.log(`   🌐 ${row.website_id}`);
    });
    
    // Clear discovered URLs for these websites
    const deletedUrls = await db
      .delete(discoveredUrls)
      .where(inArray(discoveredUrls.website_id, websitesToReset))
      .returning({ website_id: discoveredUrls.website_id });
    
    console.log(`\n🗑️  Cleared ${deletedUrls.length} discovered URLs`);
    
    // Clear scraping queue for these websites
    const deletedQueue = await db
      .delete(scrapingQueue)
      .where(inArray(scrapingQueue.website_id, websitesToReset))
      .returning({ website_id: scrapingQueue.website_id });
    
    console.log(`🗑️  Cleared ${deletedQueue.length} queued URLs`);
    
    console.log(`\n📊 Total websites reset: ${result.length}`);
    console.log('✅ Websites ready for re-crawling with new patterns');
    
  } catch (error) {
    console.error('❌ Error resetting websites:', error.message);
    process.exit(1);
  } finally {
    closeConnection();
  }
}

resetSpecificWebsites();
