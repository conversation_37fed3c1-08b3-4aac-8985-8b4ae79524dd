// Direct test: Scrape specific URLs from each website
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testDirectScraping() {
  console.log('🧪 Direct Scraping Test: 2 URLs per website');
  console.log('='.repeat(60));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  // Test URLs for each website
  const testUrls = {
    betterplace: [
      'https://betterplace.cc/buy/properties/BPVL02348',
      'https://betterplace.cc/buy/properties/BPVL02347'
    ],
    bali_home_immo: [
      'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/canggu/4-bedroom-villa-for-sale-freehold-in-canggu-berawa-rf6652',
      'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/canggu/4-bedroom-villa-for-sale-freehold-in-canggu-berawa-rf6651'
    ],
    bali_villa_realty: [
      'https://balivillarealty.com/property/tranquil-3-bedroom-villa-for-yearly-rental-in-canggu/',
      'https://balivillarealty.com/property/tranquil-3-bedroom-villa-for-sale-in-canggu/'
    ],
    villa_bali_sale: [
      'https://www.villabalisale.com/realestate-property/for-sale/villa/all/canggu/villa-for-sale-in-canggu-berawa-bali-vbs001',
      'https://www.villabalisale.com/realestate-property/for-sale/villa/all/canggu/villa-for-sale-in-canggu-bali-vbs002'
    ]
  };
  
  let totalProcessed = 0;
  let totalSuccessful = 0;
  
  for (const [website, urls] of Object.entries(testUrls)) {
    console.log(`\n🌐 Testing ${website.toUpperCase()}`);
    console.log('-'.repeat(40));
    console.log(`📋 URLs to test:`);
    urls.forEach((url, i) => {
      console.log(`   ${i + 1}. ${url.substring(0, 70)}...`);
    });
    
    try {
      console.log(`🔄 Processing ${urls.length} URLs...`);
      const startTime = Date.now();
      
      // Use the existing batch processing function
      const results = await runExtractBatch(website, urls, {
        concurrency: 2,
        timeout: 60000
      });
      
      const duration = ((Date.now() - startTime) / 1000).toFixed(1);
      
      if (results && results.length > 0) {
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        
        console.log(`✅ Completed in ${duration}s:`);
        console.log(`   Successful: ${successful}`);
        console.log(`   Failed: ${failed}`);
        
        totalProcessed += results.length;
        totalSuccessful += successful;
        
        // Show successful results
        results.filter(r => r.success).forEach((result, i) => {
          console.log(`   ${i + 1}. ✅ ${result.title || 'Property'}`);
          if (result.price || result.rent_price) {
            const price = result.price ? `IDR ${result.price.toLocaleString()}` : 
                         result.rent_price ? `IDR ${result.rent_price.toLocaleString()}/month` : '';
            console.log(`      💰 ${price}`);
          }
        });
        
        // Show failed results
        results.filter(r => !r.success).forEach((result, i) => {
          console.log(`   ${i + 1}. ❌ Failed: ${result.error || 'Unknown error'}`);
        });
        
      } else {
        console.log(`⚠️  No results returned (${duration}s)`);
      }
      
    } catch (error) {
      console.error(`❌ Error processing ${website}:`, error.message);
    }
    
    // Wait between websites
    if (website !== Object.keys(testUrls)[Object.keys(testUrls).length - 1]) {
      console.log('⏳ Waiting 15 seconds before next website...');
      await new Promise(resolve => setTimeout(resolve, 15000));
    }
  }
  
  // Final summary
  console.log('\n📊 FINAL SUMMARY');
  console.log('='.repeat(60));
  console.log(`⏰ Completed at: ${new Date().toLocaleTimeString()}`);
  console.log(`📈 Total URLs processed: ${totalProcessed}`);
  console.log(`✅ Total successful: ${totalSuccessful}`);
  console.log(`❌ Total failed: ${totalProcessed - totalSuccessful}`);
  console.log(`📊 Success rate: ${totalProcessed > 0 ? ((totalSuccessful / totalProcessed) * 100).toFixed(1) : 0}%`);
  
  console.log('\n✅ Direct scraping test completed!');
  process.exit(0);
}

testDirectScraping().catch(error => {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
  process.exit(1);
});
