// Debug script om database schema te controleren
require('dotenv').config();
const { db } = require('./drizzle_client');
const { sql } = require('drizzle-orm');

async function debugDatabase() {
  console.log('🔍 Debugging database schema...\n');
  
  try {
    // Check if property table exists
    console.log('1. Checking if property table exists...');
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'property'
      );
    `);
    console.log('Table exists:', tableExists[0]?.exists);
    
    // Get table structure
    console.log('\n2. Getting table structure...');
    const columns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'property'
      ORDER BY ordinal_position;
    `);
    console.table(columns);
    
    // Check constraints
    console.log('\n3. Checking constraints...');
    const constraints = await db.execute(sql`
      SELECT constraint_name, constraint_type
      FROM information_schema.table_constraints
      WHERE table_name = 'property';
    `);
    console.table(constraints);
    
    // Test simple insert
    console.log('\n4. Testing simple insert...');
    const testInsert = await db.execute(sql`
      INSERT INTO property (
        title, category, type, status, address, city, country, source_id, external_id
      ) VALUES (
        'Test Property', 'RESIDENTIAL', 'VILLA', 'AVAILABLE', 'Test Address', 'Test City', 'Indonesia', 'test', 'test123'
      )
      ON CONFLICT (source_id, external_id) DO UPDATE SET
        title = EXCLUDED.title
      RETURNING id;
    `);
    console.log('Test insert result:', testInsert);
    
  } catch (error) {
    console.error('❌ Database debug failed:', error);
  }
}

if (require.main === module) {
  debugDatabase().catch(console.error);
}

module.exports = { debugDatabase };
