# Troubleshooting Guide

## Common Issues

### 1. Website Not Extracting Data

**Symptoms**: Mapper returns null or incomplete data

**Solutions**:
1. **Check extraction patterns**:
   ```bash
   node tools/test_website.js patterns <websiteId> "sample content"
   ```

2. **Verify website configuration**:
   - Check URL patterns match the actual URLs
   - Ensure price patterns match the website's format
   - Validate required fields are realistic

3. **Test with sample URL**:
   ```bash
   node tools/test_website.js website <websiteId> <testUrl>
   ```

### 2. Price Conversion Failures

**Symptoms**: Properties rejected due to invalid prices

**Solutions**:
1. **Check currency patterns**:
   - Ensure patterns capture the full price string
   - Verify currency detection logic
   - Test with actual price examples from the website

2. **Update price range validation**:
   - Adjust `priceRange` in website config
   - Consider different currencies (USD vs IDR)

### 3. Validation Errors

**Symptoms**: Properties fail validation checks

**Solutions**:
1. **Review required fields**:
   - Ensure all required fields can be extracted
   - Consider making some fields optional

2. **Adjust validation ranges**:
   - Update bedroom/bathroom ranges if needed
   - Modify price ranges for different markets

### 4. Low Quality Scores

**Symptoms**: Properties have low quality scores (<70%)

**Solutions**:
1. **Improve extraction patterns**:
   - Add more specific patterns for missing fields
   - Extract additional property details (size, amenities, etc.)

2. **Enhance description extraction**:
   - Use metadata description if available
   - Extract from content if metadata is poor

### 5. Scraping Timeouts

**Symptoms**: Scraping fails with timeout errors

**Solutions**:
1. **Increase timeout**:
   ```javascript
   scraping: {
     timeout: 60000 // Increase to 60 seconds
   }
   ```

2. **Check website accessibility**:
   - Verify URLs are publicly accessible
   - Test with different user agents

## Debugging Tools

### 1. Test Individual Components

```bash
# Test website mapper
node tools/test_website.js website <websiteId> <testUrl>

# Test extraction patterns
node tools/test_website.js patterns <websiteId> "test content"

# Test all websites
node tools/test_website.js all
```

### 2. Check Website Registry

```javascript
const { websiteRegistry } = require('./scrape_worker/website_registry');

// Get all registered websites
console.log(websiteRegistry.getStats());

// Get specific website info
console.log(websiteRegistry.getWebsiteInfo('websiteId'));
```

### 3. Validate Configuration

```javascript
const { getWebsiteConfig } = require('./scrape_worker/website_configs');

// Check website configuration
const config = getWebsiteConfig('websiteId');
console.log(config);
```

## Performance Optimization

### 1. Improve Success Rates

- **Refine extraction patterns**: More specific patterns = better extraction
- **Add fallback patterns**: Multiple patterns for the same field
- **Handle edge cases**: Account for different content formats

### 2. Reduce Processing Time

- **Optimize patterns**: Use efficient regex patterns
- **Minimize API calls**: Batch processing where possible
- **Cache results**: Avoid re-processing same URLs

### 3. Enhance Data Quality

- **Validate extracted data**: Check for reasonable values
- **Sanitize text**: Clean up extracted text
- **Normalize formats**: Consistent data formatting

## Getting Help

1. **Check logs**: Look for error messages and warnings
2. **Test incrementally**: Start with simple patterns and build up
3. **Use debugging tools**: Leverage the built-in testing utilities
4. **Review working examples**: Study existing website configurations

## Best Practices

1. **Start simple**: Begin with basic patterns and add complexity gradually
2. **Test thoroughly**: Always test with multiple URLs from the website
3. **Monitor quality**: Regularly check extraction quality scores
4. **Update patterns**: Websites change - keep patterns current
5. **Document changes**: Record pattern modifications and reasons

Generated on: 2025-08-20T05:29:42.208Z
