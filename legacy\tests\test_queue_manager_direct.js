// Test using QueueManager directly to process URLs
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, properties } = require('./drizzle_client');
const { desc, sql } = require('drizzle-orm');

async function testQueueManagerDirect() {
  console.log('🧪 Testing QueueManager Direct Processing');
  console.log('='.repeat(50));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  const queueManager = new QueueManager();
  
  try {
    // Get initial count
    const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`📊 Initial properties in database: ${initialCount[0].count}`);
    
    console.log('\n🔄 Starting QueueManager processing...');
    console.log('   This will process URLs from the queue automatically');
    console.log('   Processing will stop after a few URLs or timeout');
    
    const startTime = Date.now();
    
    // Process queue with limited batch size
    const result = await queueManager.processQueue(null, 4); // Process max 4 URLs total
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    
    if (result) {
      console.log(`\n✅ QueueManager completed in ${duration}s:`);
      console.log(`   Processed: ${result.processed || 0}`);
      console.log(`   Successful: ${result.successful || 0}`);
      console.log(`   Failed: ${result.failed || 0}`);
      console.log(`   Skipped: ${result.skipped || 0}`);
    } else {
      console.log(`\n⚠️  QueueManager returned no result (${duration}s)`);
    }
    
    // Check final count and show new properties
    const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
    const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
    console.log(`\n🆕 New properties added to database: ${newProperties}`);
    
    if (newProperties > 0) {
      console.log('\n🏠 Latest Properties Added (with Description Check):');
      const latestProps = await db
        .select()
        .from(properties)
        .orderBy(desc(properties.created_at))
        .limit(newProperties);
      
      latestProps.forEach((prop, i) => {
        console.log(`\n   ${i + 1}. [${prop.source_id}] ${prop.title}`);
        console.log(`      🆔 External ID: ${prop.external_id}`);
        if (prop.price) {
          console.log(`      💰 IDR ${prop.price.toLocaleString()}`);
        }
        console.log(`      🏠 ${prop.bedrooms} bed | 🚿 ${prop.bathrooms} bath`);
        console.log(`      📍 ${prop.city}, ${prop.state || 'No state'}`);
        if (prop.ownership_type) {
          console.log(`      🏛️  ${prop.ownership_type}`);
        }
        
        // Check description in database (MAIN TEST!)
        if (prop.description && prop.description.length > 0) {
          console.log(`      ✅ Description in DB: ${prop.description.length} chars`);
          console.log(`      📝 DB Preview: ${prop.description.substring(0, 100)}...`);
        } else {
          console.log(`      ❌ No description in database`);
        }
        
        // Check amenities
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`      ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        } else {
          console.log(`      ❌ No amenities found`);
        }
        
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
      });
      
      // Summary of description functionality
      const propsWithDescription = latestProps.filter(p => p.description && p.description.length > 0);
      console.log(`\n📊 Description Test Results:`);
      console.log(`   Properties with description: ${propsWithDescription.length}/${newProperties}`);
      console.log(`   Description success rate: ${((propsWithDescription.length / newProperties) * 100).toFixed(1)}%`);
      
      if (propsWithDescription.length > 0) {
        console.log(`\n🎉 SUCCESS! Description column is working and being populated!`);
      } else {
        console.log(`\n⚠️  Description column exists but not being populated by mappers`);
      }
      
    } else {
      console.log('\n⚠️  No new properties were added');
      console.log('   This could be due to:');
      console.log('   1. API credit issues');
      console.log('   2. No pending URLs available');
      console.log('   3. All URLs already processed');
      console.log('   4. Validation failures');
    }
    
    console.log('\n✅ QueueManager direct test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testQueueManagerDirect();
