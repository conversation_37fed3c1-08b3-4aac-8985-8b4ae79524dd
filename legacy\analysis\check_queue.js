// Check Queue Status
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function checkQueue() {
  console.log('🔍 Queue Status Check - ' + new Date().toLocaleTimeString());
  
  const pending = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'pending'));
  const processing = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'processing'));
  const failed = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'failed'));
  const completed = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'completed'));
  
  console.log(`📋 Queue Status:`);
  console.log(`   Pending: ${pending.length}`);
  console.log(`   Processing: ${processing.length}`);
  console.log(`   Failed: ${failed.length}`);
  console.log(`   Completed: ${completed.length}`);
  
  if (pending.length > 0) {
    console.log('\n📋 Next 5 pending URLs:');
    pending.slice(0, 5).forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.website_id}: ${item.url.substring(0, 70)}...`);
      console.log(`      Priority: ${item.priority}, Attempts: ${item.attempts}`);
    });
  }
  
  if (processing.length > 0) {
    console.log('\n⚡ Currently processing:');
    processing.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.website_id}: ${item.url.substring(0, 70)}...`);
    });
  }
  
  // Check if scheduler is running
  console.log('\n🔍 Checking for running processes...');
  // This is just informational
}

checkQueue()
  .then(() => {
    console.log('\n✅ Queue check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Error:', error);
    process.exit(1);
  });
