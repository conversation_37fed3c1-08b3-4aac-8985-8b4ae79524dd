{"version": 3, "sources": ["../../src/mysql-core/dialect.ts"], "sourcesContent": ["import { aliasedTable, aliasedTableColumn, mapColumnsInAliasedSQLToAlias, mapColumnsInSQLToAlias } from '~/alias.ts';\nimport { CasingCache } from '~/casing.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { DrizzleError } from '~/errors.ts';\nimport type { MigrationConfig, MigrationMeta } from '~/migrator.ts';\nimport {\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tgetOperators,\n\tgetOrderByOperators,\n\tMany,\n\tnormalizeRelation,\n\tOne,\n\ttype Relation,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { and, eq } from '~/sql/expressions/index.ts';\nimport { Param, SQL, sql, View } from '~/sql/sql.ts';\nimport type { Name, Placeholder, QueryWithTypings, SQLChunk } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { getTableName, getTableUniqueName, Table } from '~/table.ts';\nimport { type Casing, orderSelectedFields, type UpdateSet } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport { MySqlColumn } from './columns/common.ts';\nimport type { MySqlDeleteConfig } from './query-builders/delete.ts';\nimport type { MySqlInsertConfig } from './query-builders/insert.ts';\nimport type {\n\tAnyMySqlSelectQueryBuilder,\n\tMySqlSelectConfig,\n\tMySqlSelectJoinConfig,\n\tSelectedFieldsOrdered,\n} from './query-builders/select.types.ts';\nimport type { MySqlUpdateConfig } from './query-builders/update.ts';\nimport type { MySqlSession } from './session.ts';\nimport { MySqlTable } from './table.ts';\nimport { MySqlViewBase } from './view-base.ts';\n\nexport interface MySqlDialectConfig {\n\tcasing?: Casing;\n}\n\nexport class MySqlDialect {\n\tstatic readonly [entityKind]: string = 'MySqlDialect';\n\n\t/** @internal */\n\treadonly casing: CasingCache;\n\n\tconstructor(config?: MySqlDialectConfig) {\n\t\tthis.casing = new CasingCache(config?.casing);\n\t}\n\n\tasync migrate(\n\t\tmigrations: MigrationMeta[],\n\t\tsession: MySqlSession,\n\t\tconfig: Omit<MigrationConfig, 'migrationsSchema'>,\n\t): Promise<void> {\n\t\tconst migrationsTable = config.migrationsTable ?? '__drizzle_migrations';\n\t\tconst migrationTableCreate = sql`\n\t\t\tcreate table if not exists ${sql.identifier(migrationsTable)} (\n\t\t\t\tid serial primary key,\n\t\t\t\thash text not null,\n\t\t\t\tcreated_at bigint\n\t\t\t)\n\t\t`;\n\t\tawait session.execute(migrationTableCreate);\n\n\t\tconst dbMigrations = await session.all<{ id: number; hash: string; created_at: string }>(\n\t\t\tsql`select id, hash, created_at from ${sql.identifier(migrationsTable)} order by created_at desc limit 1`,\n\t\t);\n\n\t\tconst lastDbMigration = dbMigrations[0];\n\n\t\tawait session.transaction(async (tx) => {\n\t\t\tfor (const migration of migrations) {\n\t\t\t\tif (\n\t\t\t\t\t!lastDbMigration\n\t\t\t\t\t|| Number(lastDbMigration.created_at) < migration.folderMillis\n\t\t\t\t) {\n\t\t\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\t\t\tawait tx.execute(sql.raw(stmt));\n\t\t\t\t\t}\n\t\t\t\t\tawait tx.execute(\n\t\t\t\t\t\tsql`insert into ${\n\t\t\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t\t\t} (\\`hash\\`, \\`created_at\\`) values(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\tescapeName(name: string): string {\n\t\treturn `\\`${name}\\``;\n\t}\n\n\tescapeParam(_num: number): string {\n\t\treturn `?`;\n\t}\n\n\tescapeString(str: string): string {\n\t\treturn `'${str.replace(/'/g, \"''\")}'`;\n\t}\n\n\tprivate buildWithCTE(queries: Subquery[] | undefined): SQL | undefined {\n\t\tif (!queries?.length) return undefined;\n\n\t\tconst withSqlChunks = [sql`with `];\n\t\tfor (const [i, w] of queries.entries()) {\n\t\t\twithSqlChunks.push(sql`${sql.identifier(w._.alias)} as (${w._.sql})`);\n\t\t\tif (i < queries.length - 1) {\n\t\t\t\twithSqlChunks.push(sql`, `);\n\t\t\t}\n\t\t}\n\t\twithSqlChunks.push(sql` `);\n\t\treturn sql.join(withSqlChunks);\n\t}\n\n\tbuildDeleteQuery({ table, where, returning, withList, limit, orderBy }: MySqlDeleteConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst orderBySql = this.buildOrderBy(orderBy);\n\n\t\tconst limitSql = this.buildLimit(limit);\n\n\t\treturn sql`${withSql}delete from ${table}${whereSql}${orderBySql}${limitSql}${returningSql}`;\n\t}\n\n\tbuildUpdateSet(table: MySqlTable, set: UpdateSet): SQL {\n\t\tconst tableColumns = table[Table.Symbol.Columns];\n\n\t\tconst columnNames = Object.keys(tableColumns).filter((colName) =>\n\t\t\tset[colName] !== undefined || tableColumns[colName]?.onUpdateFn !== undefined\n\t\t);\n\n\t\tconst setSize = columnNames.length;\n\t\treturn sql.join(columnNames.flatMap((colName, i) => {\n\t\t\tconst col = tableColumns[colName]!;\n\n\t\t\tconst value = set[colName] ?? sql.param(col.onUpdateFn!(), col);\n\t\t\tconst res = sql`${sql.identifier(this.casing.getColumnCasing(col))} = ${value}`;\n\n\t\t\tif (i < setSize - 1) {\n\t\t\t\treturn [res, sql.raw(', ')];\n\t\t\t}\n\t\t\treturn [res];\n\t\t}));\n\t}\n\n\tbuildUpdateQuery({ table, set, where, returning, withList, limit, orderBy }: MySqlUpdateConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst setSql = this.buildUpdateSet(table, set);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst orderBySql = this.buildOrderBy(orderBy);\n\n\t\tconst limitSql = this.buildLimit(limit);\n\n\t\treturn sql`${withSql}update ${table} set ${setSql}${whereSql}${orderBySql}${limitSql}${returningSql}`;\n\t}\n\n\t/**\n\t * Builds selection SQL with provided fields/expressions\n\t *\n\t * Examples:\n\t *\n\t * `select <selection> from`\n\t *\n\t * `insert ... returning <selection>`\n\t *\n\t * If `isSingleTable` is true, then columns won't be prefixed with table name\n\t */\n\tprivate buildSelection(\n\t\tfields: SelectedFieldsOrdered,\n\t\t{ isSingleTable = false }: { isSingleTable?: boolean } = {},\n\t): SQL {\n\t\tconst columnsLen = fields.length;\n\n\t\tconst chunks = fields\n\t\t\t.flatMap(({ field }, i) => {\n\t\t\t\tconst chunk: SQLChunk[] = [];\n\n\t\t\t\tif (is(field, SQL.Aliased) && field.isSelectionField) {\n\t\t\t\t\tchunk.push(sql.identifier(field.fieldAlias));\n\t\t\t\t} else if (is(field, SQL.Aliased) || is(field, SQL)) {\n\t\t\t\t\tconst query = is(field, SQL.Aliased) ? field.sql : field;\n\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(\n\t\t\t\t\t\t\tnew SQL(\n\t\t\t\t\t\t\t\tquery.queryChunks.map((c) => {\n\t\t\t\t\t\t\t\t\tif (is(c, MySqlColumn)) {\n\t\t\t\t\t\t\t\t\t\treturn sql.identifier(this.casing.getColumnCasing(c));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn c;\n\t\t\t\t\t\t\t\t}),\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(query);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (is(field, SQL.Aliased)) {\n\t\t\t\t\t\tchunk.push(sql` as ${sql.identifier(field.fieldAlias)}`);\n\t\t\t\t\t}\n\t\t\t\t} else if (is(field, Column)) {\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(sql.identifier(this.casing.getColumnCasing(field)));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (i < columnsLen - 1) {\n\t\t\t\t\tchunk.push(sql`, `);\n\t\t\t\t}\n\n\t\t\t\treturn chunk;\n\t\t\t});\n\n\t\treturn sql.join(chunks);\n\t}\n\n\tprivate buildLimit(limit: number | Placeholder | undefined): SQL | undefined {\n\t\treturn typeof limit === 'object' || (typeof limit === 'number' && limit >= 0)\n\t\t\t? sql` limit ${limit}`\n\t\t\t: undefined;\n\t}\n\n\tprivate buildOrderBy(orderBy: (MySqlColumn | SQL | SQL.Aliased)[] | undefined): SQL | undefined {\n\t\treturn orderBy && orderBy.length > 0 ? sql` order by ${sql.join(orderBy, sql`, `)}` : undefined;\n\t}\n\n\tprivate buildIndex({\n\t\tindexes,\n\t\tindexFor,\n\t}: {\n\t\tindexes: string[] | undefined;\n\t\tindexFor: 'USE' | 'FORCE' | 'IGNORE';\n\t}): SQL | undefined {\n\t\treturn indexes && indexes.length > 0\n\t\t\t? sql` ${sql.raw(indexFor)} INDEX (${sql.raw(indexes.join(`, `))})`\n\t\t\t: undefined;\n\t}\n\n\tbuildSelectQuery(\n\t\t{\n\t\t\twithList,\n\t\t\tfields,\n\t\t\tfieldsFlat,\n\t\t\twhere,\n\t\t\thaving,\n\t\t\ttable,\n\t\t\tjoins,\n\t\t\torderBy,\n\t\t\tgroupBy,\n\t\t\tlimit,\n\t\t\toffset,\n\t\t\tlockingClause,\n\t\t\tdistinct,\n\t\t\tsetOperators,\n\t\t\tuseIndex,\n\t\t\tforceIndex,\n\t\t\tignoreIndex,\n\t\t}: MySqlSelectConfig,\n\t): SQL {\n\t\tconst fieldsList = fieldsFlat ?? orderSelectedFields<MySqlColumn>(fields);\n\t\tfor (const f of fieldsList) {\n\t\t\tif (\n\t\t\t\tis(f.field, Column)\n\t\t\t\t&& getTableName(f.field.table)\n\t\t\t\t\t!== (is(table, Subquery)\n\t\t\t\t\t\t? table._.alias\n\t\t\t\t\t\t: is(table, MySqlViewBase)\n\t\t\t\t\t\t? table[ViewBaseConfig].name\n\t\t\t\t\t\t: is(table, SQL)\n\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t: getTableName(table))\n\t\t\t\t&& !((table) =>\n\t\t\t\t\tjoins?.some(({ alias }) =>\n\t\t\t\t\t\talias === (table[Table.Symbol.IsAlias] ? getTableName(table) : table[Table.Symbol.BaseName])\n\t\t\t\t\t))(f.field.table)\n\t\t\t) {\n\t\t\t\tconst tableName = getTableName(f.field.table);\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Your \"${\n\t\t\t\t\t\tf.path.join('->')\n\t\t\t\t\t}\" field references a column \"${tableName}\".\"${f.field.name}\", but the table \"${tableName}\" is not part of the query! Did you forget to join it?`,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst isSingleTable = !joins || joins.length === 0;\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst distinctSql = distinct ? sql` distinct` : undefined;\n\n\t\tconst selection = this.buildSelection(fieldsList, { isSingleTable });\n\n\t\tconst tableSql = (() => {\n\t\t\tif (is(table, Table) && table[Table.Symbol.IsAlias]) {\n\t\t\t\treturn sql`${sql`${sql.identifier(table[Table.Symbol.Schema] ?? '')}.`.if(table[Table.Symbol.Schema])}${\n\t\t\t\t\tsql.identifier(table[Table.Symbol.OriginalName])\n\t\t\t\t} ${sql.identifier(table[Table.Symbol.Name])}`;\n\t\t\t}\n\n\t\t\treturn table;\n\t\t})();\n\n\t\tconst joinsArray: SQL[] = [];\n\n\t\tif (joins) {\n\t\t\tfor (const [index, joinMeta] of joins.entries()) {\n\t\t\t\tif (index === 0) {\n\t\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t\t}\n\t\t\t\tconst table = joinMeta.table;\n\t\t\t\tconst lateralSql = joinMeta.lateral ? sql` lateral` : undefined;\n\t\t\t\tconst onSql = joinMeta.on ? sql` on ${joinMeta.on}` : undefined;\n\n\t\t\t\tif (is(table, MySqlTable)) {\n\t\t\t\t\tconst tableName = table[MySqlTable.Symbol.Name];\n\t\t\t\t\tconst tableSchema = table[MySqlTable.Symbol.Schema];\n\t\t\t\t\tconst origTableName = table[MySqlTable.Symbol.OriginalName];\n\t\t\t\t\tconst alias = tableName === origTableName ? undefined : joinMeta.alias;\n\t\t\t\t\tconst useIndexSql = this.buildIndex({ indexes: joinMeta.useIndex, indexFor: 'USE' });\n\t\t\t\t\tconst forceIndexSql = this.buildIndex({ indexes: joinMeta.forceIndex, indexFor: 'FORCE' });\n\t\t\t\t\tconst ignoreIndexSql = this.buildIndex({ indexes: joinMeta.ignoreIndex, indexFor: 'IGNORE' });\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\t\ttableSchema ? sql`${sql.identifier(tableSchema)}.` : undefined\n\t\t\t\t\t\t}${sql.identifier(origTableName)}${useIndexSql}${forceIndexSql}${ignoreIndexSql}${\n\t\t\t\t\t\t\talias && sql` ${sql.identifier(alias)}`\n\t\t\t\t\t\t}${onSql}`,\n\t\t\t\t\t);\n\t\t\t\t} else if (is(table, View)) {\n\t\t\t\t\tconst viewName = table[ViewBaseConfig].name;\n\t\t\t\t\tconst viewSchema = table[ViewBaseConfig].schema;\n\t\t\t\t\tconst origViewName = table[ViewBaseConfig].originalName;\n\t\t\t\t\tconst alias = viewName === origViewName ? undefined : joinMeta.alias;\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\t\tviewSchema ? sql`${sql.identifier(viewSchema)}.` : undefined\n\t\t\t\t\t\t}${sql.identifier(origViewName)}${alias && sql` ${sql.identifier(alias)}`}${onSql}`,\n\t\t\t\t\t);\n\t\t\t\t} else {\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${table}${onSql}`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tif (index < joins.length - 1) {\n\t\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst joinsSql = sql.join(joinsArray);\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst havingSql = having ? sql` having ${having}` : undefined;\n\n\t\tconst orderBySql = this.buildOrderBy(orderBy);\n\n\t\tconst groupBySql = groupBy && groupBy.length > 0 ? sql` group by ${sql.join(groupBy, sql`, `)}` : undefined;\n\n\t\tconst limitSql = this.buildLimit(limit);\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\tconst useIndexSql = this.buildIndex({ indexes: useIndex, indexFor: 'USE' });\n\n\t\tconst forceIndexSql = this.buildIndex({ indexes: forceIndex, indexFor: 'FORCE' });\n\n\t\tconst ignoreIndexSql = this.buildIndex({ indexes: ignoreIndex, indexFor: 'IGNORE' });\n\n\t\tlet lockingClausesSql;\n\t\tif (lockingClause) {\n\t\t\tconst { config, strength } = lockingClause;\n\t\t\tlockingClausesSql = sql` for ${sql.raw(strength)}`;\n\t\t\tif (config.noWait) {\n\t\t\t\tlockingClausesSql.append(sql` nowait`);\n\t\t\t} else if (config.skipLocked) {\n\t\t\t\tlockingClausesSql.append(sql` skip locked`);\n\t\t\t}\n\t\t}\n\n\t\tconst finalQuery =\n\t\t\tsql`${withSql}select${distinctSql} ${selection} from ${tableSql}${useIndexSql}${forceIndexSql}${ignoreIndexSql}${joinsSql}${whereSql}${groupBySql}${havingSql}${orderBySql}${limitSql}${offsetSql}${lockingClausesSql}`;\n\n\t\tif (setOperators.length > 0) {\n\t\t\treturn this.buildSetOperations(finalQuery, setOperators);\n\t\t}\n\n\t\treturn finalQuery;\n\t}\n\n\tbuildSetOperations(leftSelect: SQL, setOperators: MySqlSelectConfig['setOperators']): SQL {\n\t\tconst [setOperator, ...rest] = setOperators;\n\n\t\tif (!setOperator) {\n\t\t\tthrow new Error('Cannot pass undefined values to any set operator');\n\t\t}\n\n\t\tif (rest.length === 0) {\n\t\t\treturn this.buildSetOperationQuery({ leftSelect, setOperator });\n\t\t}\n\n\t\t// Some recursive magic here\n\t\treturn this.buildSetOperations(\n\t\t\tthis.buildSetOperationQuery({ leftSelect, setOperator }),\n\t\t\trest,\n\t\t);\n\t}\n\n\tbuildSetOperationQuery({\n\t\tleftSelect,\n\t\tsetOperator: { type, isAll, rightSelect, limit, orderBy, offset },\n\t}: { leftSelect: SQL; setOperator: MySqlSelectConfig['setOperators'][number] }): SQL {\n\t\tconst leftChunk = sql`(${leftSelect.getSQL()}) `;\n\t\tconst rightChunk = sql`(${rightSelect.getSQL()})`;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\tconst orderByValues: (SQL<unknown> | Name)[] = [];\n\n\t\t\t// The next bit is necessary because the sql operator replaces ${table.column} with `table`.`column`\n\t\t\t// which is invalid MySql syntax, Table from one of the SELECTs cannot be used in global ORDER clause\n\t\t\tfor (const orderByUnit of orderBy) {\n\t\t\t\tif (is(orderByUnit, MySqlColumn)) {\n\t\t\t\t\torderByValues.push(sql.identifier(this.casing.getColumnCasing(orderByUnit)));\n\t\t\t\t} else if (is(orderByUnit, SQL)) {\n\t\t\t\t\tfor (let i = 0; i < orderByUnit.queryChunks.length; i++) {\n\t\t\t\t\t\tconst chunk = orderByUnit.queryChunks[i];\n\n\t\t\t\t\t\tif (is(chunk, MySqlColumn)) {\n\t\t\t\t\t\t\torderByUnit.queryChunks[i] = sql.identifier(this.casing.getColumnCasing(chunk));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\torderByValues.push(sql`${orderByUnit}`);\n\t\t\t\t} else {\n\t\t\t\t\torderByValues.push(sql`${orderByUnit}`);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\torderBySql = sql` order by ${sql.join(orderByValues, sql`, `)} `;\n\t\t}\n\n\t\tconst limitSql = typeof limit === 'object' || (typeof limit === 'number' && limit >= 0)\n\t\t\t? sql` limit ${limit}`\n\t\t\t: undefined;\n\n\t\tconst operatorChunk = sql.raw(`${type} ${isAll ? 'all ' : ''}`);\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\treturn sql`${leftChunk}${operatorChunk}${rightChunk}${orderBySql}${limitSql}${offsetSql}`;\n\t}\n\n\tbuildInsertQuery(\n\t\t{ table, values: valuesOrSelect, ignore, onConflict, select }: MySqlInsertConfig,\n\t): { sql: SQL; generatedIds: Record<string, unknown>[] } {\n\t\t// const isSingleValue = values.length === 1;\n\t\tconst valuesSqlList: ((SQLChunk | SQL)[] | SQL)[] = [];\n\t\tconst columns: Record<string, MySqlColumn> = table[Table.Symbol.Columns];\n\t\tconst colEntries: [string, MySqlColumn][] = Object.entries(columns).filter(([_, col]) =>\n\t\t\t!col.shouldDisableInsert()\n\t\t);\n\n\t\tconst insertOrder = colEntries.map(([, column]) => sql.identifier(this.casing.getColumnCasing(column)));\n\t\tconst generatedIdsResponse: Record<string, unknown>[] = [];\n\n\t\tif (select) {\n\t\t\tconst select = valuesOrSelect as AnyMySqlSelectQueryBuilder | SQL;\n\n\t\t\tif (is(select, SQL)) {\n\t\t\t\tvaluesSqlList.push(select);\n\t\t\t} else {\n\t\t\t\tvaluesSqlList.push(select.getSQL());\n\t\t\t}\n\t\t} else {\n\t\t\tconst values = valuesOrSelect as Record<string, Param | SQL>[];\n\t\t\tvaluesSqlList.push(sql.raw('values '));\n\n\t\t\tfor (const [valueIndex, value] of values.entries()) {\n\t\t\t\tconst generatedIds: Record<string, unknown> = {};\n\n\t\t\t\tconst valueList: (SQLChunk | SQL)[] = [];\n\t\t\t\tfor (const [fieldName, col] of colEntries) {\n\t\t\t\t\tconst colValue = value[fieldName];\n\t\t\t\t\tif (colValue === undefined || (is(colValue, Param) && colValue.value === undefined)) {\n\t\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\t\tif (col.defaultFn !== undefined) {\n\t\t\t\t\t\t\tconst defaultFnResult = col.defaultFn();\n\t\t\t\t\t\t\tgeneratedIds[fieldName] = defaultFnResult;\n\t\t\t\t\t\t\tconst defaultValue = is(defaultFnResult, SQL) ? defaultFnResult : sql.param(defaultFnResult, col);\n\t\t\t\t\t\t\tvalueList.push(defaultValue);\n\t\t\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\t\t} else if (!col.default && col.onUpdateFn !== undefined) {\n\t\t\t\t\t\t\tconst onUpdateFnResult = col.onUpdateFn();\n\t\t\t\t\t\t\tconst newValue = is(onUpdateFnResult, SQL) ? onUpdateFnResult : sql.param(onUpdateFnResult, col);\n\t\t\t\t\t\t\tvalueList.push(newValue);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tvalueList.push(sql`default`);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (col.defaultFn && is(colValue, Param)) {\n\t\t\t\t\t\t\tgeneratedIds[fieldName] = colValue.value;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvalueList.push(colValue);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tgeneratedIdsResponse.push(generatedIds);\n\t\t\t\tvaluesSqlList.push(valueList);\n\t\t\t\tif (valueIndex < values.length - 1) {\n\t\t\t\t\tvaluesSqlList.push(sql`, `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst valuesSql = sql.join(valuesSqlList);\n\n\t\tconst ignoreSql = ignore ? sql` ignore` : undefined;\n\n\t\tconst onConflictSql = onConflict ? sql` on duplicate key ${onConflict}` : undefined;\n\n\t\treturn {\n\t\t\tsql: sql`insert${ignoreSql} into ${table} ${insertOrder} ${valuesSql}${onConflictSql}`,\n\t\t\tgeneratedIds: generatedIdsResponse,\n\t\t};\n\t}\n\n\tsqlToQuery(sql: SQL, invokeSource?: 'indexes' | undefined): QueryWithTypings {\n\t\treturn sql.toQuery({\n\t\t\tcasing: this.casing,\n\t\t\tescapeName: this.escapeName,\n\t\t\tescapeParam: this.escapeParam,\n\t\t\tescapeString: this.escapeString,\n\t\t\tinvokeSource,\n\t\t});\n\t}\n\n\tbuildRelationalQuery({\n\t\tfullSchema,\n\t\tschema,\n\t\ttableNamesMap,\n\t\ttable,\n\t\ttableConfig,\n\t\tqueryConfig: config,\n\t\ttableAlias,\n\t\tnestedQueryRelation,\n\t\tjoinOn,\n\t}: {\n\t\tfullSchema: Record<string, unknown>;\n\t\tschema: TablesRelationalConfig;\n\t\ttableNamesMap: Record<string, string>;\n\t\ttable: MySqlTable;\n\t\ttableConfig: TableRelationalConfig;\n\t\tqueryConfig: true | DBQueryConfig<'many', true>;\n\t\ttableAlias: string;\n\t\tnestedQueryRelation?: Relation;\n\t\tjoinOn?: SQL;\n\t}): BuildRelationalQueryResult<MySqlTable, MySqlColumn> {\n\t\tlet selection: BuildRelationalQueryResult<MySqlTable, MySqlColumn>['selection'] = [];\n\t\tlet limit, offset, orderBy: MySqlSelectConfig['orderBy'], where;\n\t\tconst joins: MySqlSelectJoinConfig[] = [];\n\n\t\tif (config === true) {\n\t\t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t\t\tselection = selectionEntries.map((\n\t\t\t\t[key, value],\n\t\t\t) => ({\n\t\t\t\tdbKey: value.name,\n\t\t\t\ttsKey: key,\n\t\t\t\tfield: aliasedTableColumn(value as MySqlColumn, tableAlias),\n\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\tisJson: false,\n\t\t\t\tselection: [],\n\t\t\t}));\n\t\t} else {\n\t\t\tconst aliasedColumns = Object.fromEntries(\n\t\t\t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n\t\t\t);\n\n\t\t\tif (config.where) {\n\t\t\t\tconst whereSql = typeof config.where === 'function'\n\t\t\t\t\t? config.where(aliasedColumns, getOperators())\n\t\t\t\t\t: config.where;\n\t\t\t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t\t\t}\n\n\t\t\tconst fieldsSelection: { tsKey: string; value: MySqlColumn | SQL.Aliased }[] = [];\n\t\t\tlet selectedColumns: string[] = [];\n\n\t\t\t// Figure out which columns to select\n\t\t\tif (config.columns) {\n\t\t\t\tlet isIncludeMode = false;\n\n\t\t\t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t\t\t\t\tif (value === undefined) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (field in tableConfig.columns) {\n\t\t\t\t\t\tif (!isIncludeMode && value === true) {\n\t\t\t\t\t\t\tisIncludeMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tselectedColumns.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (selectedColumns.length > 0) {\n\t\t\t\t\tselectedColumns = isIncludeMode\n\t\t\t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t\t\t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Select all columns if selection is not specified\n\t\t\t\tselectedColumns = Object.keys(tableConfig.columns);\n\t\t\t}\n\n\t\t\tfor (const field of selectedColumns) {\n\t\t\t\tconst column = tableConfig.columns[field]! as MySqlColumn;\n\t\t\t\tfieldsSelection.push({ tsKey: field, value: column });\n\t\t\t}\n\n\t\t\tlet selectedRelations: {\n\t\t\t\ttsKey: string;\n\t\t\t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t\t\t\trelation: Relation;\n\t\t\t}[] = [];\n\n\t\t\t// Figure out which relations to select\n\t\t\tif (config.with) {\n\t\t\t\tselectedRelations = Object.entries(config.with)\n\t\t\t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t\t\t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t\t\t}\n\n\t\t\tlet extras;\n\n\t\t\t// Figure out which extras to select\n\t\t\tif (config.extras) {\n\t\t\t\textras = typeof config.extras === 'function'\n\t\t\t\t\t? config.extras(aliasedColumns, { sql })\n\t\t\t\t\t: config.extras;\n\t\t\t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t\t\t\t\tfieldsSelection.push({\n\t\t\t\t\t\ttsKey,\n\t\t\t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Transform `fieldsSelection` into `selection`\n\t\t\t// `fieldsSelection` shouldn't be used after this point\n\t\t\tfor (const { tsKey, value } of fieldsSelection) {\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t\t\t\t\ttsKey,\n\t\t\t\t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\t\tisJson: false,\n\t\t\t\t\tselection: [],\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tlet orderByOrig = typeof config.orderBy === 'function'\n\t\t\t\t? config.orderBy(aliasedColumns, getOrderByOperators())\n\t\t\t\t: config.orderBy ?? [];\n\t\t\tif (!Array.isArray(orderByOrig)) {\n\t\t\t\torderByOrig = [orderByOrig];\n\t\t\t}\n\t\t\torderBy = orderByOrig.map((orderByValue) => {\n\t\t\t\tif (is(orderByValue, Column)) {\n\t\t\t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as MySqlColumn;\n\t\t\t\t}\n\t\t\t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t\t\t});\n\n\t\t\tlimit = config.limit;\n\t\t\toffset = config.offset;\n\n\t\t\t// Process all relations\n\t\t\tfor (\n\t\t\t\tconst {\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tqueryConfig: selectedRelationConfigValue,\n\t\t\t\t\trelation,\n\t\t\t\t} of selectedRelations\n\t\t\t) {\n\t\t\t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t\t\t\tconst relationTableName = getTableUniqueName(relation.referencedTable);\n\t\t\t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t\t\t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t\t\t\tconst joinOn = and(\n\t\t\t\t\t...normalizedRelation.fields.map((field, i) =>\n\t\t\t\t\t\teq(\n\t\t\t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t\t\t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t\t\t\t\t\t)\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t\tconst builtRelation = this.buildRelationalQuery({\n\t\t\t\t\tfullSchema,\n\t\t\t\t\tschema,\n\t\t\t\t\ttableNamesMap,\n\t\t\t\t\ttable: fullSchema[relationTableTsName] as MySqlTable,\n\t\t\t\t\ttableConfig: schema[relationTableTsName]!,\n\t\t\t\t\tqueryConfig: is(relation, One)\n\t\t\t\t\t\t? (selectedRelationConfigValue === true\n\t\t\t\t\t\t\t? { limit: 1 }\n\t\t\t\t\t\t\t: { ...selectedRelationConfigValue, limit: 1 })\n\t\t\t\t\t\t: selectedRelationConfigValue,\n\t\t\t\t\ttableAlias: relationTableAlias,\n\t\t\t\t\tjoinOn,\n\t\t\t\t\tnestedQueryRelation: relation,\n\t\t\t\t});\n\t\t\t\tconst field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier('data')}`.as(selectedRelationTsKey);\n\t\t\t\tjoins.push({\n\t\t\t\t\ton: sql`true`,\n\t\t\t\t\ttable: new Subquery(builtRelation.sql as SQL, {}, relationTableAlias),\n\t\t\t\t\talias: relationTableAlias,\n\t\t\t\t\tjoinType: 'left',\n\t\t\t\t\tlateral: true,\n\t\t\t\t});\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: selectedRelationTsKey,\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tfield,\n\t\t\t\t\trelationTableTsKey: relationTableTsName,\n\t\t\t\t\tisJson: true,\n\t\t\t\t\tselection: builtRelation.selection,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (selection.length === 0) {\n\t\t\tthrow new DrizzleError({ message: `No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")` });\n\t\t}\n\n\t\tlet result;\n\n\t\twhere = and(joinOn, where);\n\n\t\tif (nestedQueryRelation) {\n\t\t\tlet field = sql`json_array(${\n\t\t\t\tsql.join(\n\t\t\t\t\tselection.map(({ field, tsKey, isJson }) =>\n\t\t\t\t\t\tisJson\n\t\t\t\t\t\t\t? sql`${sql.identifier(`${tableAlias}_${tsKey}`)}.${sql.identifier('data')}`\n\t\t\t\t\t\t\t: is(field, SQL.Aliased)\n\t\t\t\t\t\t\t? field.sql\n\t\t\t\t\t\t\t: field\n\t\t\t\t\t),\n\t\t\t\t\tsql`, `,\n\t\t\t\t)\n\t\t\t})`;\n\t\t\tif (is(nestedQueryRelation, Many)) {\n\t\t\t\tfield = sql`coalesce(json_arrayagg(${field}), json_array())`;\n\t\t\t}\n\t\t\tconst nestedSelection = [{\n\t\t\t\tdbKey: 'data',\n\t\t\t\ttsKey: 'data',\n\t\t\t\tfield: field.as('data'),\n\t\t\t\tisJson: true,\n\t\t\t\trelationTableTsKey: tableConfig.tsName,\n\t\t\t\tselection,\n\t\t\t}];\n\n\t\t\tconst needsSubquery = limit !== undefined || offset !== undefined || (orderBy?.length ?? 0) > 0;\n\n\t\t\tif (needsSubquery) {\n\t\t\t\tresult = this.buildSelectQuery({\n\t\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\t\tfields: {},\n\t\t\t\t\tfieldsFlat: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\t\tfield: sql.raw('*'),\n\t\t\t\t\t\t},\n\t\t\t\t\t\t...(((orderBy?.length ?? 0) > 0)\n\t\t\t\t\t\t\t? [{\n\t\t\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\t\t\tfield: sql`row_number() over (order by ${sql.join(orderBy!, sql`, `)})`,\n\t\t\t\t\t\t\t}]\n\t\t\t\t\t\t\t: []),\n\t\t\t\t\t],\n\t\t\t\t\twhere,\n\t\t\t\t\tlimit,\n\t\t\t\t\toffset,\n\t\t\t\t\tsetOperators: [],\n\t\t\t\t});\n\n\t\t\t\twhere = undefined;\n\t\t\t\tlimit = undefined;\n\t\t\t\toffset = undefined;\n\t\t\t\torderBy = undefined;\n\t\t\t} else {\n\t\t\t\tresult = aliasedTable(table, tableAlias);\n\t\t\t}\n\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: is(result, MySqlTable) ? result : new Subquery(result, {}, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: nestedSelection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t} else {\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: selection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t}\n\n\t\treturn {\n\t\t\ttableTsKey: tableConfig.tsName,\n\t\t\tsql: result,\n\t\t\tselection,\n\t\t};\n\t}\n\n\tbuildRelationalQueryWithoutLateralSubqueries({\n\t\tfullSchema,\n\t\tschema,\n\t\ttableNamesMap,\n\t\ttable,\n\t\ttableConfig,\n\t\tqueryConfig: config,\n\t\ttableAlias,\n\t\tnestedQueryRelation,\n\t\tjoinOn,\n\t}: {\n\t\tfullSchema: Record<string, unknown>;\n\t\tschema: TablesRelationalConfig;\n\t\ttableNamesMap: Record<string, string>;\n\t\ttable: MySqlTable;\n\t\ttableConfig: TableRelationalConfig;\n\t\tqueryConfig: true | DBQueryConfig<'many', true>;\n\t\ttableAlias: string;\n\t\tnestedQueryRelation?: Relation;\n\t\tjoinOn?: SQL;\n\t}): BuildRelationalQueryResult<MySqlTable, MySqlColumn> {\n\t\tlet selection: BuildRelationalQueryResult<MySqlTable, MySqlColumn>['selection'] = [];\n\t\tlet limit, offset, orderBy: MySqlSelectConfig['orderBy'] = [], where;\n\n\t\tif (config === true) {\n\t\t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t\t\tselection = selectionEntries.map((\n\t\t\t\t[key, value],\n\t\t\t) => ({\n\t\t\t\tdbKey: value.name,\n\t\t\t\ttsKey: key,\n\t\t\t\tfield: aliasedTableColumn(value as MySqlColumn, tableAlias),\n\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\tisJson: false,\n\t\t\t\tselection: [],\n\t\t\t}));\n\t\t} else {\n\t\t\tconst aliasedColumns = Object.fromEntries(\n\t\t\t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n\t\t\t);\n\n\t\t\tif (config.where) {\n\t\t\t\tconst whereSql = typeof config.where === 'function'\n\t\t\t\t\t? config.where(aliasedColumns, getOperators())\n\t\t\t\t\t: config.where;\n\t\t\t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t\t\t}\n\n\t\t\tconst fieldsSelection: { tsKey: string; value: MySqlColumn | SQL.Aliased }[] = [];\n\t\t\tlet selectedColumns: string[] = [];\n\n\t\t\t// Figure out which columns to select\n\t\t\tif (config.columns) {\n\t\t\t\tlet isIncludeMode = false;\n\n\t\t\t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t\t\t\t\tif (value === undefined) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (field in tableConfig.columns) {\n\t\t\t\t\t\tif (!isIncludeMode && value === true) {\n\t\t\t\t\t\t\tisIncludeMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tselectedColumns.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (selectedColumns.length > 0) {\n\t\t\t\t\tselectedColumns = isIncludeMode\n\t\t\t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t\t\t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Select all columns if selection is not specified\n\t\t\t\tselectedColumns = Object.keys(tableConfig.columns);\n\t\t\t}\n\n\t\t\tfor (const field of selectedColumns) {\n\t\t\t\tconst column = tableConfig.columns[field]! as MySqlColumn;\n\t\t\t\tfieldsSelection.push({ tsKey: field, value: column });\n\t\t\t}\n\n\t\t\tlet selectedRelations: {\n\t\t\t\ttsKey: string;\n\t\t\t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t\t\t\trelation: Relation;\n\t\t\t}[] = [];\n\n\t\t\t// Figure out which relations to select\n\t\t\tif (config.with) {\n\t\t\t\tselectedRelations = Object.entries(config.with)\n\t\t\t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t\t\t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t\t\t}\n\n\t\t\tlet extras;\n\n\t\t\t// Figure out which extras to select\n\t\t\tif (config.extras) {\n\t\t\t\textras = typeof config.extras === 'function'\n\t\t\t\t\t? config.extras(aliasedColumns, { sql })\n\t\t\t\t\t: config.extras;\n\t\t\t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t\t\t\t\tfieldsSelection.push({\n\t\t\t\t\t\ttsKey,\n\t\t\t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Transform `fieldsSelection` into `selection`\n\t\t\t// `fieldsSelection` shouldn't be used after this point\n\t\t\tfor (const { tsKey, value } of fieldsSelection) {\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t\t\t\t\ttsKey,\n\t\t\t\t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\t\tisJson: false,\n\t\t\t\t\tselection: [],\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tlet orderByOrig = typeof config.orderBy === 'function'\n\t\t\t\t? config.orderBy(aliasedColumns, getOrderByOperators())\n\t\t\t\t: config.orderBy ?? [];\n\t\t\tif (!Array.isArray(orderByOrig)) {\n\t\t\t\torderByOrig = [orderByOrig];\n\t\t\t}\n\t\t\torderBy = orderByOrig.map((orderByValue) => {\n\t\t\t\tif (is(orderByValue, Column)) {\n\t\t\t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as MySqlColumn;\n\t\t\t\t}\n\t\t\t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t\t\t});\n\n\t\t\tlimit = config.limit;\n\t\t\toffset = config.offset;\n\n\t\t\t// Process all relations\n\t\t\tfor (\n\t\t\t\tconst {\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tqueryConfig: selectedRelationConfigValue,\n\t\t\t\t\trelation,\n\t\t\t\t} of selectedRelations\n\t\t\t) {\n\t\t\t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t\t\t\tconst relationTableName = getTableUniqueName(relation.referencedTable);\n\t\t\t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t\t\t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t\t\t\tconst joinOn = and(\n\t\t\t\t\t...normalizedRelation.fields.map((field, i) =>\n\t\t\t\t\t\teq(\n\t\t\t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t\t\t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t\t\t\t\t\t)\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t\tconst builtRelation = this.buildRelationalQueryWithoutLateralSubqueries({\n\t\t\t\t\tfullSchema,\n\t\t\t\t\tschema,\n\t\t\t\t\ttableNamesMap,\n\t\t\t\t\ttable: fullSchema[relationTableTsName] as MySqlTable,\n\t\t\t\t\ttableConfig: schema[relationTableTsName]!,\n\t\t\t\t\tqueryConfig: is(relation, One)\n\t\t\t\t\t\t? (selectedRelationConfigValue === true\n\t\t\t\t\t\t\t? { limit: 1 }\n\t\t\t\t\t\t\t: { ...selectedRelationConfigValue, limit: 1 })\n\t\t\t\t\t\t: selectedRelationConfigValue,\n\t\t\t\t\ttableAlias: relationTableAlias,\n\t\t\t\t\tjoinOn,\n\t\t\t\t\tnestedQueryRelation: relation,\n\t\t\t\t});\n\t\t\t\tlet fieldSql = sql`(${builtRelation.sql})`;\n\t\t\t\tif (is(relation, Many)) {\n\t\t\t\t\tfieldSql = sql`coalesce(${fieldSql}, json_array())`;\n\t\t\t\t}\n\t\t\t\tconst field = fieldSql.as(selectedRelationTsKey);\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: selectedRelationTsKey,\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tfield,\n\t\t\t\t\trelationTableTsKey: relationTableTsName,\n\t\t\t\t\tisJson: true,\n\t\t\t\t\tselection: builtRelation.selection,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (selection.length === 0) {\n\t\t\tthrow new DrizzleError({\n\t\t\t\tmessage:\n\t\t\t\t\t`No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\"). You need to have at least one item in \"columns\", \"with\" or \"extras\". If you need to select all columns, omit the \"columns\" key or set it to undefined.`,\n\t\t\t});\n\t\t}\n\n\t\tlet result;\n\n\t\twhere = and(joinOn, where);\n\n\t\tif (nestedQueryRelation) {\n\t\t\tlet field = sql`json_array(${\n\t\t\t\tsql.join(\n\t\t\t\t\tselection.map(({ field }) =>\n\t\t\t\t\t\tis(field, MySqlColumn)\n\t\t\t\t\t\t\t? sql.identifier(this.casing.getColumnCasing(field))\n\t\t\t\t\t\t\t: is(field, SQL.Aliased)\n\t\t\t\t\t\t\t? field.sql\n\t\t\t\t\t\t\t: field\n\t\t\t\t\t),\n\t\t\t\t\tsql`, `,\n\t\t\t\t)\n\t\t\t})`;\n\t\t\tif (is(nestedQueryRelation, Many)) {\n\t\t\t\tfield = sql`json_arrayagg(${field})`;\n\t\t\t}\n\t\t\tconst nestedSelection = [{\n\t\t\t\tdbKey: 'data',\n\t\t\t\ttsKey: 'data',\n\t\t\t\tfield,\n\t\t\t\tisJson: true,\n\t\t\t\trelationTableTsKey: tableConfig.tsName,\n\t\t\t\tselection,\n\t\t\t}];\n\n\t\t\tconst needsSubquery = limit !== undefined || offset !== undefined || orderBy.length > 0;\n\n\t\t\tif (needsSubquery) {\n\t\t\t\tresult = this.buildSelectQuery({\n\t\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\t\tfields: {},\n\t\t\t\t\tfieldsFlat: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\t\tfield: sql.raw('*'),\n\t\t\t\t\t\t},\n\t\t\t\t\t\t...(orderBy.length > 0)\n\t\t\t\t\t\t\t? [{\n\t\t\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\t\t\tfield: sql`row_number() over (order by ${sql.join(orderBy, sql`, `)})`,\n\t\t\t\t\t\t\t}]\n\t\t\t\t\t\t\t: [],\n\t\t\t\t\t],\n\t\t\t\t\twhere,\n\t\t\t\t\tlimit,\n\t\t\t\t\toffset,\n\t\t\t\t\tsetOperators: [],\n\t\t\t\t});\n\n\t\t\t\twhere = undefined;\n\t\t\t\tlimit = undefined;\n\t\t\t\toffset = undefined;\n\t\t\t\torderBy = undefined;\n\t\t\t} else {\n\t\t\t\tresult = aliasedTable(table, tableAlias);\n\t\t\t}\n\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: is(result, MySqlTable) ? result : new Subquery(result, {}, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: nestedSelection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t} else {\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: selection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t}\n\n\t\treturn {\n\t\t\ttableTsKey: tableConfig.tsName,\n\t\t\tsql: result,\n\t\t\tselection,\n\t\t};\n\t}\n}\n"], "mappings": "AAAA,SAAS,cAAc,oBAAoB,+BAA+B,8BAA8B;AACxG,SAAS,mBAAmB;AAC5B,SAAS,cAAc;AACvB,SAAS,YAAY,UAAU;AAC/B,SAAS,oBAAoB;AAE7B;AAAA,EAGC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAIM;AACP,SAAS,KAAK,UAAU;AACxB,SAAS,OAAO,KAAK,KAAK,YAAY;AAEtC,SAAS,gBAAgB;AACzB,SAAS,cAAc,oBAAoB,aAAa;AACxD,SAAsB,2BAA2C;AACjE,SAAS,sBAAsB;AAC/B,SAAS,mBAAmB;AAW5B,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAMvB,MAAM,aAAa;AAAA,EACzB,QAAiB,UAAU,IAAY;AAAA;AAAA,EAG9B;AAAA,EAET,YAAY,QAA6B;AACxC,SAAK,SAAS,IAAI,YAAY,QAAQ,MAAM;AAAA,EAC7C;AAAA,EAEA,MAAM,QACL,YACA,SACA,QACgB;AAChB,UAAM,kBAAkB,OAAO,mBAAmB;AAClD,UAAM,uBAAuB;AAAA,gCACC,IAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7D,UAAM,QAAQ,QAAQ,oBAAoB;AAE1C,UAAM,eAAe,MAAM,QAAQ;AAAA,MAClC,uCAAuC,IAAI,WAAW,eAAe,CAAC;AAAA,IACvE;AAEA,UAAM,kBAAkB,aAAa,CAAC;AAEtC,UAAM,QAAQ,YAAY,OAAO,OAAO;AACvC,iBAAW,aAAa,YAAY;AACnC,YACC,CAAC,mBACE,OAAO,gBAAgB,UAAU,IAAI,UAAU,cACjD;AACD,qBAAW,QAAQ,UAAU,KAAK;AACjC,kBAAM,GAAG,QAAQ,IAAI,IAAI,IAAI,CAAC;AAAA,UAC/B;AACA,gBAAM,GAAG;AAAA,YACR,kBACC,IAAI,WAAW,eAAe,CAC/B,sCAAsC,UAAU,IAAI,KAAK,UAAU,YAAY;AAAA,UAChF;AAAA,QACD;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,WAAW,MAAsB;AAChC,WAAO,KAAK,IAAI;AAAA,EACjB;AAAA,EAEA,YAAY,MAAsB;AACjC,WAAO;AAAA,EACR;AAAA,EAEA,aAAa,KAAqB;AACjC,WAAO,IAAI,IAAI,QAAQ,MAAM,IAAI,CAAC;AAAA,EACnC;AAAA,EAEQ,aAAa,SAAkD;AACtE,QAAI,CAAC,SAAS,OAAQ,QAAO;AAE7B,UAAM,gBAAgB,CAAC,UAAU;AACjC,eAAW,CAAC,GAAG,CAAC,KAAK,QAAQ,QAAQ,GAAG;AACvC,oBAAc,KAAK,MAAM,IAAI,WAAW,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,GAAG,GAAG;AACpE,UAAI,IAAI,QAAQ,SAAS,GAAG;AAC3B,sBAAc,KAAK,OAAO;AAAA,MAC3B;AAAA,IACD;AACA,kBAAc,KAAK,MAAM;AACzB,WAAO,IAAI,KAAK,aAAa;AAAA,EAC9B;AAAA,EAEA,iBAAiB,EAAE,OAAO,OAAO,WAAW,UAAU,OAAO,QAAQ,GAA2B;AAC/F,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,eAAe,YAClB,iBAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,WAAW,QAAQ,aAAa,KAAK,KAAK;AAEhD,UAAM,aAAa,KAAK,aAAa,OAAO;AAE5C,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,WAAO,MAAM,OAAO,eAAe,KAAK,GAAG,QAAQ,GAAG,UAAU,GAAG,QAAQ,GAAG,YAAY;AAAA,EAC3F;AAAA,EAEA,eAAe,OAAmB,KAAqB;AACtD,UAAM,eAAe,MAAM,MAAM,OAAO,OAAO;AAE/C,UAAM,cAAc,OAAO,KAAK,YAAY,EAAE;AAAA,MAAO,CAAC,YACrD,IAAI,OAAO,MAAM,UAAa,aAAa,OAAO,GAAG,eAAe;AAAA,IACrE;AAEA,UAAM,UAAU,YAAY;AAC5B,WAAO,IAAI,KAAK,YAAY,QAAQ,CAAC,SAAS,MAAM;AACnD,YAAM,MAAM,aAAa,OAAO;AAEhC,YAAM,QAAQ,IAAI,OAAO,KAAK,IAAI,MAAM,IAAI,WAAY,GAAG,GAAG;AAC9D,YAAM,MAAM,MAAM,IAAI,WAAW,KAAK,OAAO,gBAAgB,GAAG,CAAC,CAAC,MAAM,KAAK;AAE7E,UAAI,IAAI,UAAU,GAAG;AACpB,eAAO,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,MAC3B;AACA,aAAO,CAAC,GAAG;AAAA,IACZ,CAAC,CAAC;AAAA,EACH;AAAA,EAEA,iBAAiB,EAAE,OAAO,KAAK,OAAO,WAAW,UAAU,OAAO,QAAQ,GAA2B;AACpG,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,SAAS,KAAK,eAAe,OAAO,GAAG;AAE7C,UAAM,eAAe,YAClB,iBAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,WAAW,QAAQ,aAAa,KAAK,KAAK;AAEhD,UAAM,aAAa,KAAK,aAAa,OAAO;AAE5C,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,WAAO,MAAM,OAAO,UAAU,KAAK,QAAQ,MAAM,GAAG,QAAQ,GAAG,UAAU,GAAG,QAAQ,GAAG,YAAY;AAAA,EACpG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaQ,eACP,QACA,EAAE,gBAAgB,MAAM,IAAiC,CAAC,GACpD;AACN,UAAM,aAAa,OAAO;AAE1B,UAAM,SAAS,OACb,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;AAC1B,YAAM,QAAoB,CAAC;AAE3B,UAAI,GAAG,OAAO,IAAI,OAAO,KAAK,MAAM,kBAAkB;AACrD,cAAM,KAAK,IAAI,WAAW,MAAM,UAAU,CAAC;AAAA,MAC5C,WAAW,GAAG,OAAO,IAAI,OAAO,KAAK,GAAG,OAAO,GAAG,GAAG;AACpD,cAAM,QAAQ,GAAG,OAAO,IAAI,OAAO,IAAI,MAAM,MAAM;AAEnD,YAAI,eAAe;AAClB,gBAAM;AAAA,YACL,IAAI;AAAA,cACH,MAAM,YAAY,IAAI,CAAC,MAAM;AAC5B,oBAAI,GAAG,GAAG,WAAW,GAAG;AACvB,yBAAO,IAAI,WAAW,KAAK,OAAO,gBAAgB,CAAC,CAAC;AAAA,gBACrD;AACA,uBAAO;AAAA,cACR,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD,OAAO;AACN,gBAAM,KAAK,KAAK;AAAA,QACjB;AAEA,YAAI,GAAG,OAAO,IAAI,OAAO,GAAG;AAC3B,gBAAM,KAAK,UAAU,IAAI,WAAW,MAAM,UAAU,CAAC,EAAE;AAAA,QACxD;AAAA,MACD,WAAW,GAAG,OAAO,MAAM,GAAG;AAC7B,YAAI,eAAe;AAClB,gBAAM,KAAK,IAAI,WAAW,KAAK,OAAO,gBAAgB,KAAK,CAAC,CAAC;AAAA,QAC9D,OAAO;AACN,gBAAM,KAAK,KAAK;AAAA,QACjB;AAAA,MACD;AAEA,UAAI,IAAI,aAAa,GAAG;AACvB,cAAM,KAAK,OAAO;AAAA,MACnB;AAEA,aAAO;AAAA,IACR,CAAC;AAEF,WAAO,IAAI,KAAK,MAAM;AAAA,EACvB;AAAA,EAEQ,WAAW,OAA0D;AAC5E,WAAO,OAAO,UAAU,YAAa,OAAO,UAAU,YAAY,SAAS,IACxE,aAAa,KAAK,KAClB;AAAA,EACJ;AAAA,EAEQ,aAAa,SAA2E;AAC/F,WAAO,WAAW,QAAQ,SAAS,IAAI,gBAAgB,IAAI,KAAK,SAAS,OAAO,CAAC,KAAK;AAAA,EACvF;AAAA,EAEQ,WAAW;AAAA,IAClB;AAAA,IACA;AAAA,EACD,GAGoB;AACnB,WAAO,WAAW,QAAQ,SAAS,IAChC,OAAO,IAAI,IAAI,QAAQ,CAAC,WAAW,IAAI,IAAI,QAAQ,KAAK,IAAI,CAAC,CAAC,MAC9D;AAAA,EACJ;AAAA,EAEA,iBACC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GACM;AACN,UAAM,aAAa,cAAc,oBAAiC,MAAM;AACxE,eAAW,KAAK,YAAY;AAC3B,UACC,GAAG,EAAE,OAAO,MAAM,KACf,aAAa,EAAE,MAAM,KAAK,OACvB,GAAG,OAAO,QAAQ,IACpB,MAAM,EAAE,QACR,GAAG,OAAO,aAAa,IACvB,MAAM,cAAc,EAAE,OACtB,GAAG,OAAO,GAAG,IACb,SACA,aAAa,KAAK,MACnB,EAAE,CAACA,WACL,OAAO;AAAA,QAAK,CAAC,EAAE,MAAM,MACpB,WAAWA,OAAM,MAAM,OAAO,OAAO,IAAI,aAAaA,MAAK,IAAIA,OAAM,MAAM,OAAO,QAAQ;AAAA,MAC3F,GAAG,EAAE,MAAM,KAAK,GAChB;AACD,cAAM,YAAY,aAAa,EAAE,MAAM,KAAK;AAC5C,cAAM,IAAI;AAAA,UACT,SACC,EAAE,KAAK,KAAK,IAAI,CACjB,gCAAgC,SAAS,MAAM,EAAE,MAAM,IAAI,qBAAqB,SAAS;AAAA,QAC1F;AAAA,MACD;AAAA,IACD;AAEA,UAAM,gBAAgB,CAAC,SAAS,MAAM,WAAW;AAEjD,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,cAAc,WAAW,iBAAiB;AAEhD,UAAM,YAAY,KAAK,eAAe,YAAY,EAAE,cAAc,CAAC;AAEnE,UAAM,YAAY,MAAM;AACvB,UAAI,GAAG,OAAO,KAAK,KAAK,MAAM,MAAM,OAAO,OAAO,GAAG;AACpD,eAAO,MAAM,MAAM,IAAI,WAAW,MAAM,MAAM,OAAO,MAAM,KAAK,EAAE,CAAC,IAAI,GAAG,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GACpG,IAAI,WAAW,MAAM,MAAM,OAAO,YAAY,CAAC,CAChD,IAAI,IAAI,WAAW,MAAM,MAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAC7C;AAEA,aAAO;AAAA,IACR,GAAG;AAEH,UAAM,aAAoB,CAAC;AAE3B,QAAI,OAAO;AACV,iBAAW,CAAC,OAAO,QAAQ,KAAK,MAAM,QAAQ,GAAG;AAChD,YAAI,UAAU,GAAG;AAChB,qBAAW,KAAK,MAAM;AAAA,QACvB;AACA,cAAMA,SAAQ,SAAS;AACvB,cAAM,aAAa,SAAS,UAAU,gBAAgB;AACtD,cAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,EAAE,KAAK;AAEtD,YAAI,GAAGA,QAAO,UAAU,GAAG;AAC1B,gBAAM,YAAYA,OAAM,WAAW,OAAO,IAAI;AAC9C,gBAAM,cAAcA,OAAM,WAAW,OAAO,MAAM;AAClD,gBAAM,gBAAgBA,OAAM,WAAW,OAAO,YAAY;AAC1D,gBAAM,QAAQ,cAAc,gBAAgB,SAAY,SAAS;AACjE,gBAAMC,eAAc,KAAK,WAAW,EAAE,SAAS,SAAS,UAAU,UAAU,MAAM,CAAC;AACnF,gBAAMC,iBAAgB,KAAK,WAAW,EAAE,SAAS,SAAS,YAAY,UAAU,QAAQ,CAAC;AACzF,gBAAMC,kBAAiB,KAAK,WAAW,EAAE,SAAS,SAAS,aAAa,UAAU,SAAS,CAAC;AAC5F,qBAAW;AAAA,YACV,MAAM,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IACjD,cAAc,MAAM,IAAI,WAAW,WAAW,CAAC,MAAM,MACtD,GAAG,IAAI,WAAW,aAAa,CAAC,GAAGF,YAAW,GAAGC,cAAa,GAAGC,eAAc,GAC9E,SAAS,OAAO,IAAI,WAAW,KAAK,CAAC,EACtC,GAAG,KAAK;AAAA,UACT;AAAA,QACD,WAAW,GAAGH,QAAO,IAAI,GAAG;AAC3B,gBAAM,WAAWA,OAAM,cAAc,EAAE;AACvC,gBAAM,aAAaA,OAAM,cAAc,EAAE;AACzC,gBAAM,eAAeA,OAAM,cAAc,EAAE;AAC3C,gBAAM,QAAQ,aAAa,eAAe,SAAY,SAAS;AAC/D,qBAAW;AAAA,YACV,MAAM,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IACjD,aAAa,MAAM,IAAI,WAAW,UAAU,CAAC,MAAM,MACpD,GAAG,IAAI,WAAW,YAAY,CAAC,GAAG,SAAS,OAAO,IAAI,WAAW,KAAK,CAAC,EAAE,GAAG,KAAK;AAAA,UAClF;AAAA,QACD,OAAO;AACN,qBAAW;AAAA,YACV,MAAM,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IAAIA,MAAK,GAAG,KAAK;AAAA,UACpE;AAAA,QACD;AACA,YAAI,QAAQ,MAAM,SAAS,GAAG;AAC7B,qBAAW,KAAK,MAAM;AAAA,QACvB;AAAA,MACD;AAAA,IACD;AAEA,UAAM,WAAW,IAAI,KAAK,UAAU;AAEpC,UAAM,WAAW,QAAQ,aAAa,KAAK,KAAK;AAEhD,UAAM,YAAY,SAAS,cAAc,MAAM,KAAK;AAEpD,UAAM,aAAa,KAAK,aAAa,OAAO;AAE5C,UAAM,aAAa,WAAW,QAAQ,SAAS,IAAI,gBAAgB,IAAI,KAAK,SAAS,OAAO,CAAC,KAAK;AAElG,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,UAAM,YAAY,SAAS,cAAc,MAAM,KAAK;AAEpD,UAAM,cAAc,KAAK,WAAW,EAAE,SAAS,UAAU,UAAU,MAAM,CAAC;AAE1E,UAAM,gBAAgB,KAAK,WAAW,EAAE,SAAS,YAAY,UAAU,QAAQ,CAAC;AAEhF,UAAM,iBAAiB,KAAK,WAAW,EAAE,SAAS,aAAa,UAAU,SAAS,CAAC;AAEnF,QAAI;AACJ,QAAI,eAAe;AAClB,YAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,0BAAoB,WAAW,IAAI,IAAI,QAAQ,CAAC;AAChD,UAAI,OAAO,QAAQ;AAClB,0BAAkB,OAAO,YAAY;AAAA,MACtC,WAAW,OAAO,YAAY;AAC7B,0BAAkB,OAAO,iBAAiB;AAAA,MAC3C;AAAA,IACD;AAEA,UAAM,aACL,MAAM,OAAO,SAAS,WAAW,IAAI,SAAS,SAAS,QAAQ,GAAG,WAAW,GAAG,aAAa,GAAG,cAAc,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS,GAAG,iBAAiB;AAEtN,QAAI,aAAa,SAAS,GAAG;AAC5B,aAAO,KAAK,mBAAmB,YAAY,YAAY;AAAA,IACxD;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,mBAAmB,YAAiB,cAAsD;AACzF,UAAM,CAAC,aAAa,GAAG,IAAI,IAAI;AAE/B,QAAI,CAAC,aAAa;AACjB,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACnE;AAEA,QAAI,KAAK,WAAW,GAAG;AACtB,aAAO,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;AAAA,IAC/D;AAGA,WAAO,KAAK;AAAA,MACX,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;AAAA,MACvD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,uBAAuB;AAAA,IACtB;AAAA,IACA,aAAa,EAAE,MAAM,OAAO,aAAa,OAAO,SAAS,OAAO;AAAA,EACjE,GAAqF;AACpF,UAAM,YAAY,OAAO,WAAW,OAAO,CAAC;AAC5C,UAAM,aAAa,OAAO,YAAY,OAAO,CAAC;AAE9C,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,YAAM,gBAAyC,CAAC;AAIhD,iBAAW,eAAe,SAAS;AAClC,YAAI,GAAG,aAAa,WAAW,GAAG;AACjC,wBAAc,KAAK,IAAI,WAAW,KAAK,OAAO,gBAAgB,WAAW,CAAC,CAAC;AAAA,QAC5E,WAAW,GAAG,aAAa,GAAG,GAAG;AAChC,mBAAS,IAAI,GAAG,IAAI,YAAY,YAAY,QAAQ,KAAK;AACxD,kBAAM,QAAQ,YAAY,YAAY,CAAC;AAEvC,gBAAI,GAAG,OAAO,WAAW,GAAG;AAC3B,0BAAY,YAAY,CAAC,IAAI,IAAI,WAAW,KAAK,OAAO,gBAAgB,KAAK,CAAC;AAAA,YAC/E;AAAA,UACD;AAEA,wBAAc,KAAK,MAAM,WAAW,EAAE;AAAA,QACvC,OAAO;AACN,wBAAc,KAAK,MAAM,WAAW,EAAE;AAAA,QACvC;AAAA,MACD;AAEA,mBAAa,gBAAgB,IAAI,KAAK,eAAe,OAAO,CAAC;AAAA,IAC9D;AAEA,UAAM,WAAW,OAAO,UAAU,YAAa,OAAO,UAAU,YAAY,SAAS,IAClF,aAAa,KAAK,KAClB;AAEH,UAAM,gBAAgB,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,SAAS,EAAE,EAAE;AAE9D,UAAM,YAAY,SAAS,cAAc,MAAM,KAAK;AAEpD,WAAO,MAAM,SAAS,GAAG,aAAa,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS;AAAA,EACxF;AAAA,EAEA,iBACC,EAAE,OAAO,QAAQ,gBAAgB,QAAQ,YAAY,OAAO,GACJ;AAExD,UAAM,gBAA8C,CAAC;AACrD,UAAM,UAAuC,MAAM,MAAM,OAAO,OAAO;AACvE,UAAM,aAAsC,OAAO,QAAQ,OAAO,EAAE;AAAA,MAAO,CAAC,CAAC,GAAG,GAAG,MAClF,CAAC,IAAI,oBAAoB;AAAA,IAC1B;AAEA,UAAM,cAAc,WAAW,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,IAAI,WAAW,KAAK,OAAO,gBAAgB,MAAM,CAAC,CAAC;AACtG,UAAM,uBAAkD,CAAC;AAEzD,QAAI,QAAQ;AACX,YAAMI,UAAS;AAEf,UAAI,GAAGA,SAAQ,GAAG,GAAG;AACpB,sBAAc,KAAKA,OAAM;AAAA,MAC1B,OAAO;AACN,sBAAc,KAAKA,QAAO,OAAO,CAAC;AAAA,MACnC;AAAA,IACD,OAAO;AACN,YAAM,SAAS;AACf,oBAAc,KAAK,IAAI,IAAI,SAAS,CAAC;AAErC,iBAAW,CAAC,YAAY,KAAK,KAAK,OAAO,QAAQ,GAAG;AACnD,cAAM,eAAwC,CAAC;AAE/C,cAAM,YAAgC,CAAC;AACvC,mBAAW,CAAC,WAAW,GAAG,KAAK,YAAY;AAC1C,gBAAM,WAAW,MAAM,SAAS;AAChC,cAAI,aAAa,UAAc,GAAG,UAAU,KAAK,KAAK,SAAS,UAAU,QAAY;AAEpF,gBAAI,IAAI,cAAc,QAAW;AAChC,oBAAM,kBAAkB,IAAI,UAAU;AACtC,2BAAa,SAAS,IAAI;AAC1B,oBAAM,eAAe,GAAG,iBAAiB,GAAG,IAAI,kBAAkB,IAAI,MAAM,iBAAiB,GAAG;AAChG,wBAAU,KAAK,YAAY;AAAA,YAE5B,WAAW,CAAC,IAAI,WAAW,IAAI,eAAe,QAAW;AACxD,oBAAM,mBAAmB,IAAI,WAAW;AACxC,oBAAM,WAAW,GAAG,kBAAkB,GAAG,IAAI,mBAAmB,IAAI,MAAM,kBAAkB,GAAG;AAC/F,wBAAU,KAAK,QAAQ;AAAA,YACxB,OAAO;AACN,wBAAU,KAAK,YAAY;AAAA,YAC5B;AAAA,UACD,OAAO;AACN,gBAAI,IAAI,aAAa,GAAG,UAAU,KAAK,GAAG;AACzC,2BAAa,SAAS,IAAI,SAAS;AAAA,YACpC;AACA,sBAAU,KAAK,QAAQ;AAAA,UACxB;AAAA,QACD;AAEA,6BAAqB,KAAK,YAAY;AACtC,sBAAc,KAAK,SAAS;AAC5B,YAAI,aAAa,OAAO,SAAS,GAAG;AACnC,wBAAc,KAAK,OAAO;AAAA,QAC3B;AAAA,MACD;AAAA,IACD;AAEA,UAAM,YAAY,IAAI,KAAK,aAAa;AAExC,UAAM,YAAY,SAAS,eAAe;AAE1C,UAAM,gBAAgB,aAAa,wBAAwB,UAAU,KAAK;AAE1E,WAAO;AAAA,MACN,KAAK,YAAY,SAAS,SAAS,KAAK,IAAI,WAAW,IAAI,SAAS,GAAG,aAAa;AAAA,MACpF,cAAc;AAAA,IACf;AAAA,EACD;AAAA,EAEA,WAAWC,MAAU,cAAwD;AAC5E,WAAOA,KAAI,QAAQ;AAAA,MAClB,QAAQ,KAAK;AAAA,MACb,YAAY,KAAK;AAAA,MACjB,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA,MACnB;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,qBAAqB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAUwD;AACvD,QAAI,YAA8E,CAAC;AACnF,QAAI,OAAO,QAAQ,SAAuC;AAC1D,UAAM,QAAiC,CAAC;AAExC,QAAI,WAAW,MAAM;AACpB,YAAM,mBAAmB,OAAO,QAAQ,YAAY,OAAO;AAC3D,kBAAY,iBAAiB,IAAI,CAChC,CAAC,KAAK,KAAK,OACN;AAAA,QACL,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,QACP,OAAO,mBAAmB,OAAsB,UAAU;AAAA,QAC1D,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,WAAW,CAAC;AAAA,MACb,EAAE;AAAA,IACH,OAAO;AACN,YAAM,iBAAiB,OAAO;AAAA,QAC7B,OAAO,QAAQ,YAAY,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,mBAAmB,OAAO,UAAU,CAAC,CAAC;AAAA,MACvG;AAEA,UAAI,OAAO,OAAO;AACjB,cAAM,WAAW,OAAO,OAAO,UAAU,aACtC,OAAO,MAAM,gBAAgB,aAAa,CAAC,IAC3C,OAAO;AACV,gBAAQ,YAAY,uBAAuB,UAAU,UAAU;AAAA,MAChE;AAEA,YAAM,kBAAyE,CAAC;AAChF,UAAI,kBAA4B,CAAC;AAGjC,UAAI,OAAO,SAAS;AACnB,YAAI,gBAAgB;AAEpB,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,OAAO,OAAO,GAAG;AAC5D,cAAI,UAAU,QAAW;AACxB;AAAA,UACD;AAEA,cAAI,SAAS,YAAY,SAAS;AACjC,gBAAI,CAAC,iBAAiB,UAAU,MAAM;AACrC,8BAAgB;AAAA,YACjB;AACA,4BAAgB,KAAK,KAAK;AAAA,UAC3B;AAAA,QACD;AAEA,YAAI,gBAAgB,SAAS,GAAG;AAC/B,4BAAkB,gBACf,gBAAgB,OAAO,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM,IAAI,IAC1D,OAAO,KAAK,YAAY,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,SAAS,GAAG,CAAC;AAAA,QACnF;AAAA,MACD,OAAO;AAEN,0BAAkB,OAAO,KAAK,YAAY,OAAO;AAAA,MAClD;AAEA,iBAAW,SAAS,iBAAiB;AACpC,cAAM,SAAS,YAAY,QAAQ,KAAK;AACxC,wBAAgB,KAAK,EAAE,OAAO,OAAO,OAAO,OAAO,CAAC;AAAA,MACrD;AAEA,UAAI,oBAIE,CAAC;AAGP,UAAI,OAAO,MAAM;AAChB,4BAAoB,OAAO,QAAQ,OAAO,IAAI,EAC5C,OAAO,CAAC,UAAoE,CAAC,CAAC,MAAM,CAAC,CAAC,EACtF,IAAI,CAAC,CAAC,OAAO,WAAW,OAAO,EAAE,OAAO,aAAa,UAAU,YAAY,UAAU,KAAK,EAAG,EAAE;AAAA,MAClG;AAEA,UAAI;AAGJ,UAAI,OAAO,QAAQ;AAClB,iBAAS,OAAO,OAAO,WAAW,aAC/B,OAAO,OAAO,gBAAgB,EAAE,IAAI,CAAC,IACrC,OAAO;AACV,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACpD,0BAAgB,KAAK;AAAA,YACpB;AAAA,YACA,OAAO,8BAA8B,OAAO,UAAU;AAAA,UACvD,CAAC;AAAA,QACF;AAAA,MACD;AAIA,iBAAW,EAAE,OAAO,MAAM,KAAK,iBAAiB;AAC/C,kBAAU,KAAK;AAAA,UACd,OAAO,GAAG,OAAO,IAAI,OAAO,IAAI,MAAM,aAAa,YAAY,QAAQ,KAAK,EAAG;AAAA,UAC/E;AAAA,UACA,OAAO,GAAG,OAAO,MAAM,IAAI,mBAAmB,OAAO,UAAU,IAAI;AAAA,UACnE,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,CAAC;AAAA,QACb,CAAC;AAAA,MACF;AAEA,UAAI,cAAc,OAAO,OAAO,YAAY,aACzC,OAAO,QAAQ,gBAAgB,oBAAoB,CAAC,IACpD,OAAO,WAAW,CAAC;AACtB,UAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAChC,sBAAc,CAAC,WAAW;AAAA,MAC3B;AACA,gBAAU,YAAY,IAAI,CAAC,iBAAiB;AAC3C,YAAI,GAAG,cAAc,MAAM,GAAG;AAC7B,iBAAO,mBAAmB,cAAc,UAAU;AAAA,QACnD;AACA,eAAO,uBAAuB,cAAc,UAAU;AAAA,MACvD,CAAC;AAED,cAAQ,OAAO;AACf,eAAS,OAAO;AAGhB,iBACO;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,QACb;AAAA,MACD,KAAK,mBACJ;AACD,cAAM,qBAAqB,kBAAkB,QAAQ,eAAe,QAAQ;AAC5E,cAAM,oBAAoB,mBAAmB,SAAS,eAAe;AACrE,cAAM,sBAAsB,cAAc,iBAAiB;AAC3D,cAAM,qBAAqB,GAAG,UAAU,IAAI,qBAAqB;AACjE,cAAMC,UAAS;AAAA,UACd,GAAG,mBAAmB,OAAO;AAAA,YAAI,CAACC,QAAO,MACxC;AAAA,cACC,mBAAmB,mBAAmB,WAAW,CAAC,GAAI,kBAAkB;AAAA,cACxE,mBAAmBA,QAAO,UAAU;AAAA,YACrC;AAAA,UACD;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,qBAAqB;AAAA,UAC/C;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,WAAW,mBAAmB;AAAA,UACrC,aAAa,OAAO,mBAAmB;AAAA,UACvC,aAAa,GAAG,UAAU,GAAG,IACzB,gCAAgC,OAChC,EAAE,OAAO,EAAE,IACX,EAAE,GAAG,6BAA6B,OAAO,EAAE,IAC5C;AAAA,UACH,YAAY;AAAA,UACZ,QAAAD;AAAA,UACA,qBAAqB;AAAA,QACtB,CAAC;AACD,cAAM,QAAQ,MAAM,IAAI,WAAW,kBAAkB,CAAC,IAAI,IAAI,WAAW,MAAM,CAAC,GAAG,GAAG,qBAAqB;AAC3G,cAAM,KAAK;AAAA,UACV,IAAI;AAAA,UACJ,OAAO,IAAI,SAAS,cAAc,KAAY,CAAC,GAAG,kBAAkB;AAAA,UACpE,OAAO;AAAA,UACP,UAAU;AAAA,UACV,SAAS;AAAA,QACV,CAAC;AACD,kBAAU,KAAK;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,cAAc;AAAA,QAC1B,CAAC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,YAAM,IAAI,aAAa,EAAE,SAAS,iCAAiC,YAAY,MAAM,OAAO,UAAU,KAAK,CAAC;AAAA,IAC7G;AAEA,QAAI;AAEJ,YAAQ,IAAI,QAAQ,KAAK;AAEzB,QAAI,qBAAqB;AACxB,UAAI,QAAQ,iBACX,IAAI;AAAA,QACH,UAAU;AAAA,UAAI,CAAC,EAAE,OAAAC,QAAO,OAAO,OAAO,MACrC,SACG,MAAM,IAAI,WAAW,GAAG,UAAU,IAAI,KAAK,EAAE,CAAC,IAAI,IAAI,WAAW,MAAM,CAAC,KACxE,GAAGA,QAAO,IAAI,OAAO,IACrBA,OAAM,MACNA;AAAA,QACJ;AAAA,QACA;AAAA,MACD,CACD;AACA,UAAI,GAAG,qBAAqB,IAAI,GAAG;AAClC,gBAAQ,6BAA6B,KAAK;AAAA,MAC3C;AACA,YAAM,kBAAkB,CAAC;AAAA,QACxB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO,MAAM,GAAG,MAAM;AAAA,QACtB,QAAQ;AAAA,QACR,oBAAoB,YAAY;AAAA,QAChC;AAAA,MACD,CAAC;AAED,YAAM,gBAAgB,UAAU,UAAa,WAAW,WAAc,SAAS,UAAU,KAAK;AAE9F,UAAI,eAAe;AAClB,iBAAS,KAAK,iBAAiB;AAAA,UAC9B,OAAO,aAAa,OAAO,UAAU;AAAA,UACrC,QAAQ,CAAC;AAAA,UACT,YAAY;AAAA,YACX;AAAA,cACC,MAAM,CAAC;AAAA,cACP,OAAO,IAAI,IAAI,GAAG;AAAA,YACnB;AAAA,YACA,IAAM,SAAS,UAAU,KAAK,IAC3B,CAAC;AAAA,cACF,MAAM,CAAC;AAAA,cACP,OAAO,kCAAkC,IAAI,KAAK,SAAU,OAAO,CAAC;AAAA,YACrE,CAAC,IACC,CAAC;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc,CAAC;AAAA,QAChB,CAAC;AAED,gBAAQ;AACR,gBAAQ;AACR,iBAAS;AACT,kBAAU;AAAA,MACX,OAAO;AACN,iBAAS,aAAa,OAAO,UAAU;AAAA,MACxC;AAEA,eAAS,KAAK,iBAAiB;AAAA,QAC9B,OAAO,GAAG,QAAQ,UAAU,IAAI,SAAS,IAAI,SAAS,QAAQ,CAAC,GAAG,UAAU;AAAA,QAC5E,QAAQ,CAAC;AAAA,QACT,YAAY,gBAAgB,IAAI,CAAC,EAAE,OAAAA,OAAM,OAAO;AAAA,UAC/C,MAAM,CAAC;AAAA,UACP,OAAO,GAAGA,QAAO,MAAM,IAAI,mBAAmBA,QAAO,UAAU,IAAIA;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF,OAAO;AACN,eAAS,KAAK,iBAAiB;AAAA,QAC9B,OAAO,aAAa,OAAO,UAAU;AAAA,QACrC,QAAQ,CAAC;AAAA,QACT,YAAY,UAAU,IAAI,CAAC,EAAE,MAAM,OAAO;AAAA,UACzC,MAAM,CAAC;AAAA,UACP,OAAO,GAAG,OAAO,MAAM,IAAI,mBAAmB,OAAO,UAAU,IAAI;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,WAAO;AAAA,MACN,YAAY,YAAY;AAAA,MACxB,KAAK;AAAA,MACL;AAAA,IACD;AAAA,EACD;AAAA,EAEA,6CAA6C;AAAA,IAC5C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAUwD;AACvD,QAAI,YAA8E,CAAC;AACnF,QAAI,OAAO,QAAQ,UAAwC,CAAC,GAAG;AAE/D,QAAI,WAAW,MAAM;AACpB,YAAM,mBAAmB,OAAO,QAAQ,YAAY,OAAO;AAC3D,kBAAY,iBAAiB,IAAI,CAChC,CAAC,KAAK,KAAK,OACN;AAAA,QACL,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,QACP,OAAO,mBAAmB,OAAsB,UAAU;AAAA,QAC1D,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,WAAW,CAAC;AAAA,MACb,EAAE;AAAA,IACH,OAAO;AACN,YAAM,iBAAiB,OAAO;AAAA,QAC7B,OAAO,QAAQ,YAAY,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,mBAAmB,OAAO,UAAU,CAAC,CAAC;AAAA,MACvG;AAEA,UAAI,OAAO,OAAO;AACjB,cAAM,WAAW,OAAO,OAAO,UAAU,aACtC,OAAO,MAAM,gBAAgB,aAAa,CAAC,IAC3C,OAAO;AACV,gBAAQ,YAAY,uBAAuB,UAAU,UAAU;AAAA,MAChE;AAEA,YAAM,kBAAyE,CAAC;AAChF,UAAI,kBAA4B,CAAC;AAGjC,UAAI,OAAO,SAAS;AACnB,YAAI,gBAAgB;AAEpB,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,OAAO,OAAO,GAAG;AAC5D,cAAI,UAAU,QAAW;AACxB;AAAA,UACD;AAEA,cAAI,SAAS,YAAY,SAAS;AACjC,gBAAI,CAAC,iBAAiB,UAAU,MAAM;AACrC,8BAAgB;AAAA,YACjB;AACA,4BAAgB,KAAK,KAAK;AAAA,UAC3B;AAAA,QACD;AAEA,YAAI,gBAAgB,SAAS,GAAG;AAC/B,4BAAkB,gBACf,gBAAgB,OAAO,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM,IAAI,IAC1D,OAAO,KAAK,YAAY,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,SAAS,GAAG,CAAC;AAAA,QACnF;AAAA,MACD,OAAO;AAEN,0BAAkB,OAAO,KAAK,YAAY,OAAO;AAAA,MAClD;AAEA,iBAAW,SAAS,iBAAiB;AACpC,cAAM,SAAS,YAAY,QAAQ,KAAK;AACxC,wBAAgB,KAAK,EAAE,OAAO,OAAO,OAAO,OAAO,CAAC;AAAA,MACrD;AAEA,UAAI,oBAIE,CAAC;AAGP,UAAI,OAAO,MAAM;AAChB,4BAAoB,OAAO,QAAQ,OAAO,IAAI,EAC5C,OAAO,CAAC,UAAoE,CAAC,CAAC,MAAM,CAAC,CAAC,EACtF,IAAI,CAAC,CAAC,OAAO,WAAW,OAAO,EAAE,OAAO,aAAa,UAAU,YAAY,UAAU,KAAK,EAAG,EAAE;AAAA,MAClG;AAEA,UAAI;AAGJ,UAAI,OAAO,QAAQ;AAClB,iBAAS,OAAO,OAAO,WAAW,aAC/B,OAAO,OAAO,gBAAgB,EAAE,IAAI,CAAC,IACrC,OAAO;AACV,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACpD,0BAAgB,KAAK;AAAA,YACpB;AAAA,YACA,OAAO,8BAA8B,OAAO,UAAU;AAAA,UACvD,CAAC;AAAA,QACF;AAAA,MACD;AAIA,iBAAW,EAAE,OAAO,MAAM,KAAK,iBAAiB;AAC/C,kBAAU,KAAK;AAAA,UACd,OAAO,GAAG,OAAO,IAAI,OAAO,IAAI,MAAM,aAAa,YAAY,QAAQ,KAAK,EAAG;AAAA,UAC/E;AAAA,UACA,OAAO,GAAG,OAAO,MAAM,IAAI,mBAAmB,OAAO,UAAU,IAAI;AAAA,UACnE,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,CAAC;AAAA,QACb,CAAC;AAAA,MACF;AAEA,UAAI,cAAc,OAAO,OAAO,YAAY,aACzC,OAAO,QAAQ,gBAAgB,oBAAoB,CAAC,IACpD,OAAO,WAAW,CAAC;AACtB,UAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAChC,sBAAc,CAAC,WAAW;AAAA,MAC3B;AACA,gBAAU,YAAY,IAAI,CAAC,iBAAiB;AAC3C,YAAI,GAAG,cAAc,MAAM,GAAG;AAC7B,iBAAO,mBAAmB,cAAc,UAAU;AAAA,QACnD;AACA,eAAO,uBAAuB,cAAc,UAAU;AAAA,MACvD,CAAC;AAED,cAAQ,OAAO;AACf,eAAS,OAAO;AAGhB,iBACO;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,QACb;AAAA,MACD,KAAK,mBACJ;AACD,cAAM,qBAAqB,kBAAkB,QAAQ,eAAe,QAAQ;AAC5E,cAAM,oBAAoB,mBAAmB,SAAS,eAAe;AACrE,cAAM,sBAAsB,cAAc,iBAAiB;AAC3D,cAAM,qBAAqB,GAAG,UAAU,IAAI,qBAAqB;AACjE,cAAMD,UAAS;AAAA,UACd,GAAG,mBAAmB,OAAO;AAAA,YAAI,CAACC,QAAO,MACxC;AAAA,cACC,mBAAmB,mBAAmB,WAAW,CAAC,GAAI,kBAAkB;AAAA,cACxE,mBAAmBA,QAAO,UAAU;AAAA,YACrC;AAAA,UACD;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,6CAA6C;AAAA,UACvE;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,WAAW,mBAAmB;AAAA,UACrC,aAAa,OAAO,mBAAmB;AAAA,UACvC,aAAa,GAAG,UAAU,GAAG,IACzB,gCAAgC,OAChC,EAAE,OAAO,EAAE,IACX,EAAE,GAAG,6BAA6B,OAAO,EAAE,IAC5C;AAAA,UACH,YAAY;AAAA,UACZ,QAAAD;AAAA,UACA,qBAAqB;AAAA,QACtB,CAAC;AACD,YAAI,WAAW,OAAO,cAAc,GAAG;AACvC,YAAI,GAAG,UAAU,IAAI,GAAG;AACvB,qBAAW,eAAe,QAAQ;AAAA,QACnC;AACA,cAAM,QAAQ,SAAS,GAAG,qBAAqB;AAC/C,kBAAU,KAAK;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,cAAc;AAAA,QAC1B,CAAC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,YAAM,IAAI,aAAa;AAAA,QACtB,SACC,iCAAiC,YAAY,MAAM,OAAO,UAAU;AAAA,MACtE,CAAC;AAAA,IACF;AAEA,QAAI;AAEJ,YAAQ,IAAI,QAAQ,KAAK;AAEzB,QAAI,qBAAqB;AACxB,UAAI,QAAQ,iBACX,IAAI;AAAA,QACH,UAAU;AAAA,UAAI,CAAC,EAAE,OAAAC,OAAM,MACtB,GAAGA,QAAO,WAAW,IAClB,IAAI,WAAW,KAAK,OAAO,gBAAgBA,MAAK,CAAC,IACjD,GAAGA,QAAO,IAAI,OAAO,IACrBA,OAAM,MACNA;AAAA,QACJ;AAAA,QACA;AAAA,MACD,CACD;AACA,UAAI,GAAG,qBAAqB,IAAI,GAAG;AAClC,gBAAQ,oBAAoB,KAAK;AAAA,MAClC;AACA,YAAM,kBAAkB,CAAC;AAAA,QACxB,OAAO;AAAA,QACP,OAAO;AAAA,QACP;AAAA,QACA,QAAQ;AAAA,QACR,oBAAoB,YAAY;AAAA,QAChC;AAAA,MACD,CAAC;AAED,YAAM,gBAAgB,UAAU,UAAa,WAAW,UAAa,QAAQ,SAAS;AAEtF,UAAI,eAAe;AAClB,iBAAS,KAAK,iBAAiB;AAAA,UAC9B,OAAO,aAAa,OAAO,UAAU;AAAA,UACrC,QAAQ,CAAC;AAAA,UACT,YAAY;AAAA,YACX;AAAA,cACC,MAAM,CAAC;AAAA,cACP,OAAO,IAAI,IAAI,GAAG;AAAA,YACnB;AAAA,YACA,GAAI,QAAQ,SAAS,IAClB,CAAC;AAAA,cACF,MAAM,CAAC;AAAA,cACP,OAAO,kCAAkC,IAAI,KAAK,SAAS,OAAO,CAAC;AAAA,YACpE,CAAC,IACC,CAAC;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc,CAAC;AAAA,QAChB,CAAC;AAED,gBAAQ;AACR,gBAAQ;AACR,iBAAS;AACT,kBAAU;AAAA,MACX,OAAO;AACN,iBAAS,aAAa,OAAO,UAAU;AAAA,MACxC;AAEA,eAAS,KAAK,iBAAiB;AAAA,QAC9B,OAAO,GAAG,QAAQ,UAAU,IAAI,SAAS,IAAI,SAAS,QAAQ,CAAC,GAAG,UAAU;AAAA,QAC5E,QAAQ,CAAC;AAAA,QACT,YAAY,gBAAgB,IAAI,CAAC,EAAE,OAAAA,OAAM,OAAO;AAAA,UAC/C,MAAM,CAAC;AAAA,UACP,OAAO,GAAGA,QAAO,MAAM,IAAI,mBAAmBA,QAAO,UAAU,IAAIA;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF,OAAO;AACN,eAAS,KAAK,iBAAiB;AAAA,QAC9B,OAAO,aAAa,OAAO,UAAU;AAAA,QACrC,QAAQ,CAAC;AAAA,QACT,YAAY,UAAU,IAAI,CAAC,EAAE,MAAM,OAAO;AAAA,UACzC,MAAM,CAAC;AAAA,UACP,OAAO,GAAG,OAAO,MAAM,IAAI,mBAAmB,OAAO,UAAU,IAAI;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,WAAO;AAAA,MACN,YAAY,YAAY;AAAA,MACxB,KAAK;AAAA,MACL;AAAA,IACD;AAAA,EACD;AACD;", "names": ["table", "useIndexSql", "forceIndexSql", "ignoreIndexSql", "select", "sql", "joinOn", "field"]}