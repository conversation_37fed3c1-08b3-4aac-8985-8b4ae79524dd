// Test Bali Coconut Living URL classification with improved patterns
require('dotenv').config();
const { SmartCrawler } = require('../scrape_worker/smart_crawler');
const { db, discoveredUrls, scrapingQueue, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function testBaliCoconutClassification() {
  console.log('🥥 Testing Bali Coconut Living URL Classification\n');
  
  const crawler = new SmartCrawler();
  
  try {
    // Show initial status
    console.log('📊 Initial Status:');
    const initialUrls = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls WHERE website_id = 'bali_coconut_living'`);
    const initialQueue = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue WHERE website_id = 'bali_coconut_living'`);
    console.log(`   🔗 Discovered URLs: ${initialUrls[0]?.count || 0}`);
    console.log(`   📋 Scraping Queue: ${initialQueue[0]?.count || 0}\n`);
    
    // Crawl Bali Coconut Living
    console.log('🕷️  Crawling Bali Coconut Living with improved patterns...');
    const result = await crawler.startWebsiteCrawl('bali_coconut_living');
    
    if (result) {
      console.log('✅ Crawling completed successfully:');
      console.log(`   📊 Type: ${result.type}`);
      console.log(`   🔍 Total discovered: ${result.totalDiscovered || 0}`);
      console.log(`   ✨ New URLs added: ${result.newUrls || 0}`);
      console.log(`   🗺️  Sitemaps processed: ${result.sitemapUrls || 0}`);
    } else {
      console.log('⏭️  Crawling skipped (not due for processing)');
      return;
    }
    
    // Show final status
    console.log('\n📊 Final Status:');
    const finalUrls = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls WHERE website_id = 'bali_coconut_living'`);
    const finalQueue = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue WHERE website_id = 'bali_coconut_living'`);
    console.log(`   🔗 Discovered URLs: ${finalUrls[0]?.count || 0}`);
    console.log(`   📋 Scraping Queue: ${finalQueue[0]?.count || 0}`);
    
    // Detailed classification analysis
    console.log('\n🔍 Detailed Classification Analysis:');
    console.log('=' .repeat(50));
    
    const classificationStats = await db.execute(sql`
      SELECT 
        is_property_page,
        url_type,
        COUNT(*) as count,
        AVG(confidence_score) as avg_confidence
      FROM discovered_urls 
      WHERE website_id = 'bali_coconut_living'
      GROUP BY is_property_page, url_type
      ORDER BY is_property_page DESC, url_type
    `);
    
    console.log('\n📊 Classification Breakdown:');
    classificationStats.forEach(stat => {
      const type = stat.is_property_page ? '🏠 Property' : (stat.url_type === 'listing' ? '📋 Listing' : '📄 Other');
      console.log(`   ${type} (${stat.url_type}): ${stat.count} URLs (avg confidence: ${parseFloat(stat.avg_confidence).toFixed(2)})`);
    });
    
    // Sample URLs for each category
    console.log('\n🔍 Sample URLs by Category:');
    
    // Property URLs
    const propertyUrls = await db.execute(sql`
      SELECT url, confidence_score, classification_reason
      FROM discovered_urls 
      WHERE website_id = 'bali_coconut_living' AND is_property_page = true
      ORDER BY confidence_score DESC
      LIMIT 5
    `);
    
    console.log('\n🏠 Property URLs (Top 5 by confidence):');
    propertyUrls.forEach((url, i) => {
      console.log(`   ${i + 1}. ${url.url}`);
      console.log(`      📊 Confidence: ${url.confidence_score}, Reason: ${url.classification_reason}`);
    });
    
    // Listing URLs
    const listingUrls = await db.execute(sql`
      SELECT url, confidence_score, classification_reason
      FROM discovered_urls 
      WHERE website_id = 'bali_coconut_living' AND is_property_page = false AND url_type = 'listing'
      ORDER BY confidence_score DESC
      LIMIT 5
    `);
    
    console.log('\n📋 Listing URLs (Top 5 by confidence):');
    listingUrls.forEach((url, i) => {
      console.log(`   ${i + 1}. ${url.url}`);
      console.log(`      📊 Confidence: ${url.confidence_score}, Reason: ${url.classification_reason}`);
    });
    
    // Check for problematic URLs that should be listings but are classified as properties
    const problematicUrls = await db.execute(sql`
      SELECT url, confidence_score, classification_reason
      FROM discovered_urls 
      WHERE website_id = 'bali_coconut_living' 
        AND is_property_page = true 
        AND (url LIKE '%/property/%' OR url LIKE '%?page=%' OR url LIKE '%/page/%')
      LIMIT 10
    `);
    
    if (problematicUrls.length > 0) {
      console.log('\n⚠️  Potentially Misclassified URLs (should be listings):');
      problematicUrls.forEach((url, i) => {
        console.log(`   ${i + 1}. ${url.url}`);
        console.log(`      📊 Confidence: ${url.confidence_score}, Reason: ${url.classification_reason}`);
      });
    } else {
      console.log('\n✅ No obviously misclassified URLs found!');
    }
    
    // Summary and evaluation
    const totalDiscovered = parseInt(finalUrls[0]?.count || 0);
    const totalQueued = parseInt(finalQueue[0]?.count || 0);
    const propertyCount = propertyUrls.length > 0 ? parseInt(classificationStats.find(s => s.is_property_page)?.count || 0) : 0;
    
    console.log('\n🎯 SUMMARY:');
    console.log('=' .repeat(50));
    console.log(`   🔗 Total URLs discovered: ${totalDiscovered}`);
    console.log(`   🏠 URLs classified as property: ${propertyCount}`);
    console.log(`   📋 URLs in scraping queue: ${totalQueued}`);
    
    if (totalDiscovered > 0) {
      const propertyRatio = Math.round((propertyCount / totalDiscovered) * 100);
      console.log(`   📈 Property ratio: ${propertyRatio}%`);
      
      if (propertyRatio > 50) {
        console.log('\n⚠️  HIGH PROPERTY RATIO - May indicate over-classification');
        console.log('   🔧 Consider tightening property patterns or adding more exclude patterns');
      } else if (propertyRatio < 10) {
        console.log('\n⚠️  LOW PROPERTY RATIO - May indicate under-classification');
        console.log('   🔧 Consider loosening property patterns or reviewing exclude patterns');
      } else {
        console.log('\n✅ REASONABLE PROPERTY RATIO - Classification looks balanced');
      }
    }
    
    if (totalQueued === propertyCount) {
      console.log('   ✅ All property URLs correctly added to queue');
    } else {
      console.log(`   ⚠️  Queue mismatch: ${totalQueued} queued vs ${propertyCount} properties`);
    }
    
    if (problematicUrls.length === 0) {
      console.log('   ✅ No obviously misclassified URLs detected');
    } else {
      console.log(`   ⚠️  ${problematicUrls.length} potentially misclassified URLs found`);
    }
    
    console.log('\n🎉 Bali Coconut Living classification test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test
testBaliCoconutClassification()
  .then(() => {
    console.log('✅ Classification test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
