require('dotenv').config();
const fetch = require('node-fetch');

// Import the fixed extractCleanDescription function
function extractCleanDescription(markdown) {
  try {
    // Split into lines and clean each line
    const lines = markdown.split('\n').map(line => line.trim());

    // Filter out problematic lines - comprehensive filtering
    const cleanLines = lines.filter(line => {
      // Skip empty or very short lines
      if (line.length < 15) return false;

      const lowerLine = line.toLowerCase();

      // Skip lines with contact/technical content - enhanced filtering
      if (lowerLine.includes('whatsapp') ||
          lowerLine.includes('wa.me') ||
          lowerLine.includes('wp-content') ||
          lowerLine.includes('_next/image') ||
          lowerLine.includes('digitaloceanspaces') ||
          lowerLine.includes('contact us') ||
          lowerLine.includes('phone:') ||
          lowerLine.includes('email:') ||
          lowerLine.includes('call us') ||
          lowerLine.includes('reach out') ||
          lowerLine.includes('get in touch') ||
          lowerLine.includes('schedule') ||
          lowerLine.includes('consultation') ||
          lowerLine.includes('booking') ||
          lowerLine.includes('reserve') ||
          lowerLine.includes('inquiry') ||
          lowerLine.includes('enquiry') ||
          lowerLine.includes('online') ||
          lowerLine.includes('website') ||
          lowerLine.includes('click') ||
          lowerLine.includes('visit') ||
          lowerLine.includes('browse') ||
          lowerLine.includes('download') ||
          lowerLine.includes('subscribe') ||
          lowerLine.includes('newsletter') ||
          lowerLine.includes('follow us') ||
          lowerLine.includes('social media') ||
          lowerLine.includes('facebook') ||
          lowerLine.includes('instagram') ||
          lowerLine.includes('twitter') ||
          lowerLine.includes('linkedin') ||
          lowerLine.includes('youtube') ||
          lowerLine.includes('telegram') ||
          lowerLine.includes('free consultation') ||
          lowerLine.includes('free advice') ||
          lowerLine.includes('no obligation') ||
          lowerLine.includes('terms and conditions') ||
          lowerLine.includes('privacy policy') ||
          lowerLine.includes('cookie policy') ||
          lowerLine.includes('disclaimer') ||
          lowerLine.includes('answer few questions') ||
          lowerLine.includes('we will offer') ||
          lowerLine.includes('real estate solutions') ||
          lowerLine.includes('fill out') ||
          lowerLine.includes('form') ||
          lowerLine.includes('submit') ||
          lowerLine.includes('send us') ||
          lowerLine.includes('contact form') ||
          lowerLine.includes('get quote') ||
          lowerLine.includes('request info') ||
          lowerLine.includes('more information') ||
          lowerLine.includes('call to action') ||
          lowerLine.includes('cta') ||
          lowerLine.includes('button') ||
          lowerLine.includes('link')) {
        return false;
      }

      // Skip lines with markdown/HTML markup
      if (line.includes('[![') ||
          line.includes('](http') ||
          line.includes('<img') ||
          line.includes('<a href') ||
          line.includes('![') ||
          line.startsWith('#') ||
          line.startsWith('*') ||
          line.startsWith('-') ||
          line.startsWith('|') ||
          line.startsWith('>')) {
        return false;
      }

      // Skip lines with currency/pricing info - but only if they're short pricing lines, not descriptive paragraphs
      if (line.length < 100 && (
          lowerLine.includes('usd') ||
          lowerLine.includes('idr') ||
          lowerLine.includes('aud') ||
          lowerLine.includes('eur') ||
          lowerLine.includes('price') ||
          lowerLine.includes('cost') ||
          lowerLine.includes('$') ||
          lowerLine.includes('rp ') ||
          lowerLine.includes('€') ||
          lowerLine.includes('£'))) {
        return false;
      }

      // Skip lines with property specifications - but only if they're short spec lines, not descriptive paragraphs
      if (line.length < 80 && (
          lowerLine.includes('bedroom') ||
          lowerLine.includes('bathroom') ||
          lowerLine.includes('sqm') ||
          lowerLine.includes('m2') ||
          lowerLine.includes('m²') ||
          lowerLine.includes('parking') ||
          lowerLine.includes('year built') ||
          lowerLine.includes('ownership') ||
          lowerLine.includes('built:') ||
          lowerLine.includes('size:'))) {
        return false;
      }
      
      // Always skip very short freehold/leasehold lines as they're usually just labels
      if (line.length < 50 && (lowerLine.includes('leasehold') || lowerLine.includes('freehold'))) {
        return false;
      }

      return true;
    });

    // Find the first substantial paragraph
    for (const line of cleanLines) {
      if (line.length > 30 && line.length < 800) {
        // Clean up any remaining markdown and technical content
        let cleaned = line
          .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
          .replace(/\[.*?\]\(.*?\)/g, '') // Remove links
          .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
          .replace(/\*(.*?)\*/g, '$1') // Remove italic
          .replace(/`(.*?)`/g, '$1') // Remove code
          .replace(/#{1,6}\s*/g, '') // Remove headers
          .replace(/\s+/g, ' ') // Normalize whitespace
          .replace(/[^\w\s.,!?()-]/g, ' ') // Remove special characters except basic punctuation
          .trim();

        if (cleaned.length > 50) {
          // Check if it contains property-related content
          const hasPropertyContent = /\b(villa|house|property|apartment|land|estate|investment|development|location|area|beautiful|stunning|modern|luxury|spacious|bedroom|bathroom|pool|garden|view|beach|rice|field|mountain|valley|river|lake|sea|ocean|bali|canggu|seminyak|ubud|sanur|denpasar|tabanan|gianyar|karangasem|buleleng|jembrana|klungkung|bangli)\b/i.test(cleaned);
          
          // Additional check for land-specific content
          const hasLandContent = /\b(land|plot|are|hectare|sqm|m2|development|investment|opportunity|potential|build|construct|design|project|site|location|zoning|permit|title|certificate|freehold|leasehold)\b/i.test(cleaned);

          // Ensure it's a proper sentence and has property-related content (including land-specific content)
          if ((cleaned.match(/[.!?]$/) || cleaned.length > 100) && (hasPropertyContent || hasLandContent)) {
            return cleaned;
          }
        }
      }
    }

    return null;
  } catch (error) {
    console.log(`   ⚠️  Description extraction failed: ${error.message}`);
    return null;
  }
}

async function testDescriptionFix() {
  try {
    console.log('🧪 TESTING DESCRIPTION FIX...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    
    // Get the raw markdown from Firecrawl
    console.log('1. GETTING RAW MARKDOWN:');
    
    const firecrawlResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: false,
        waitFor: 3000
      })
    });
    
    const firecrawlData = await firecrawlResponse.json();
    
    if (firecrawlData.success) {
      const markdown = firecrawlData.data.markdown;
      console.log(`✅ Got markdown: ${markdown.length} characters`);
      
      // Test the FIXED extractCleanDescription function
      console.log('\n2. TESTING FIXED DESCRIPTION EXTRACTION:');
      const extractedDescription = extractCleanDescription(markdown);
      
      if (extractedDescription) {
        console.log('🎉 SUCCESS! Description extracted:');
        console.log(`Length: ${extractedDescription.length} characters`);
        console.log(`Content: "${extractedDescription}"`);
        
        // Check if it contains the expected content
        const hasExpectedContent = extractedDescription.toLowerCase().includes('imagine stepping into');
        console.log(`\n✅ Contains expected content: ${hasExpectedContent ? 'YES' : 'NO'}`);
        
        if (hasExpectedContent) {
          console.log('🎯 PERFECT! The fix worked - we now extract the full BetterPlace description!');
        }
      } else {
        console.log('❌ Still no description extracted');
        
        // Debug what lines are being filtered
        console.log('\n3. DEBUGGING FILTERED LINES:');
        const lines = markdown.split('\n').map(line => line.trim());
        const longLines = lines.filter(line => line.length > 100 && line.toLowerCase().includes('imagine'));
        
        console.log(`Found ${longLines.length} long lines containing "imagine":`);
        longLines.forEach((line, i) => {
          console.log(`${i + 1}: ${line.substring(0, 150)}...`);
        });
      }
    } else {
      console.log('❌ Failed to get markdown from Firecrawl');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
  
  process.exit(0);
}

testDescriptionFix();
