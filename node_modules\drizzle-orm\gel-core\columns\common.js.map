{"version": 3, "sources": ["../../../src/gel-core/columns/common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBase,\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tHasGenerated,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport { ColumnBuilder } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Simplify, Update } from '~/utils.ts';\n\nimport type { ForeignKey, UpdateDeleteAction } from '~/gel-core/foreign-keys.ts';\nimport { ForeignKeyBuilder } from '~/gel-core/foreign-keys.ts';\nimport type { AnyGelTable, GelTable } from '~/gel-core/table.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport { iife } from '~/tracing-utils.ts';\nimport type { GelIndexOpClass } from '../indexes.ts';\nimport { uniqueKeyName } from '../unique-constraint.ts';\n\nexport interface ReferenceConfig {\n\tref: () => GelColumn;\n\tactions: {\n\t\tonUpdate?: UpdateDeleteAction;\n\t\tonDelete?: UpdateDeleteAction;\n\t};\n}\n\nexport interface GelColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> extends ColumnBuilderBase<T, TTypeConfig & { dialect: 'gel' }> {}\n\nexport abstract class GelColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends ColumnBuilder<T, TRuntimeConfig, TTypeConfig & { dialect: 'gel' }, TExtraConfig>\n\timplements GelColumnBuilderBase<T, TTypeConfig>\n{\n\tprivate foreignKeyConfigs: ReferenceConfig[] = [];\n\n\tstatic override readonly [entityKind]: string = 'GelColumnBuilder';\n\n\tarray<TSize extends number | undefined = undefined>(size?: TSize): GelArrayBuilder<\n\t\t& {\n\t\t\tname: T['name'];\n\t\t\tdataType: 'array';\n\t\t\tcolumnType: 'GelArray';\n\t\t\tdata: T['data'][];\n\t\t\tdriverParam: T['driverParam'][] | string;\n\t\t\tenumValues: T['enumValues'];\n\t\t\tsize: TSize;\n\t\t\tbaseBuilder: T;\n\t\t}\n\t\t& (T extends { notNull: true } ? { notNull: true } : {})\n\t\t& (T extends { hasDefault: true } ? { hasDefault: true } : {}),\n\t\tT\n\t> {\n\t\treturn new GelArrayBuilder(this.config.name, this as GelColumnBuilder<any, any>, size as any);\n\t}\n\n\treferences(\n\t\tref: ReferenceConfig['ref'],\n\t\tactions: ReferenceConfig['actions'] = {},\n\t): this {\n\t\tthis.foreignKeyConfigs.push({ ref, actions });\n\t\treturn this;\n\t}\n\n\tunique(\n\t\tname?: string,\n\t\tconfig?: { nulls: 'distinct' | 'not distinct' },\n\t): this {\n\t\tthis.config.isUnique = true;\n\t\tthis.config.uniqueName = name;\n\t\tthis.config.uniqueType = config?.nulls;\n\t\treturn this;\n\t}\n\n\tgeneratedAlwaysAs(as: SQL | T['data'] | (() => SQL)): HasGenerated<this, {\n\t\ttype: 'always';\n\t}> {\n\t\tthis.config.generated = {\n\t\t\tas,\n\t\t\ttype: 'always',\n\t\t\tmode: 'stored',\n\t\t};\n\t\treturn this as HasGenerated<this, {\n\t\t\ttype: 'always';\n\t\t}>;\n\t}\n\n\t/** @internal */\n\tbuildForeignKeys(column: GelColumn, table: GelTable): ForeignKey[] {\n\t\treturn this.foreignKeyConfigs.map(({ ref, actions }) => {\n\t\t\treturn iife(\n\t\t\t\t(ref, actions) => {\n\t\t\t\t\tconst builder = new ForeignKeyBuilder(() => {\n\t\t\t\t\t\tconst foreignColumn = ref();\n\t\t\t\t\t\treturn { columns: [column], foreignColumns: [foreignColumn] };\n\t\t\t\t\t});\n\t\t\t\t\tif (actions.onUpdate) {\n\t\t\t\t\t\tbuilder.onUpdate(actions.onUpdate);\n\t\t\t\t\t}\n\t\t\t\t\tif (actions.onDelete) {\n\t\t\t\t\t\tbuilder.onDelete(actions.onDelete);\n\t\t\t\t\t}\n\t\t\t\t\treturn builder.build(table);\n\t\t\t\t},\n\t\t\t\tref,\n\t\t\t\tactions,\n\t\t\t);\n\t\t});\n\t}\n\n\t/** @internal */\n\tabstract build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelColumn<MakeColumnConfig<T, TTableName>>;\n\n\t/** @internal */\n\tbuildExtraConfigColumn<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelExtraConfigColumn {\n\t\treturn new GelExtraConfigColumn(table, this.config);\n\t}\n}\n\n// To understand how to use `GelColumn` and `GelColumn`, see `Column` and `AnyColumn` documentation.\nexport abstract class GelColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = {},\n\tTTypeConfig extends object = {},\n> extends Column<T, TRuntimeConfig, TTypeConfig & { dialect: 'gel' }> {\n\tstatic override readonly [entityKind]: string = 'GelColumn';\n\n\tconstructor(\n\t\toverride readonly table: GelTable,\n\t\tconfig: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tif (!config.uniqueName) {\n\t\t\tconfig.uniqueName = uniqueKeyName(table, [config.name]);\n\t\t}\n\t\tsuper(table, config);\n\t}\n}\n\nexport type IndexedExtraConfigType = { order?: 'asc' | 'desc'; nulls?: 'first' | 'last'; opClass?: string };\n\nexport class GelExtraConfigColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n> extends GelColumn<T, IndexedExtraConfigType> {\n\tstatic override readonly [entityKind]: string = 'GelExtraConfigColumn';\n\n\toverride getSQLType(): string {\n\t\treturn this.getSQLType();\n\t}\n\n\tindexConfig: IndexedExtraConfigType = {\n\t\torder: this.config.order ?? 'asc',\n\t\tnulls: this.config.nulls ?? 'last',\n\t\topClass: this.config.opClass,\n\t};\n\tdefaultConfig: IndexedExtraConfigType = {\n\t\torder: 'asc',\n\t\tnulls: 'last',\n\t\topClass: undefined,\n\t};\n\n\tasc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'asc';\n\t\treturn this;\n\t}\n\n\tdesc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'desc';\n\t\treturn this;\n\t}\n\n\tnullsFirst(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'first';\n\t\treturn this;\n\t}\n\n\tnullsLast(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'last';\n\t\treturn this;\n\t}\n\n\t/**\n\t * ### PostgreSQL documentation quote\n\t *\n\t * > An operator class with optional parameters can be specified for each column of an index.\n\t * The operator class identifies the operators to be used by the index for that column.\n\t * For example, a B-tree index on four-byte integers would use the int4_ops class;\n\t * this operator class includes comparison functions for four-byte integers.\n\t * In practice the default operator class for the column's data type is usually sufficient.\n\t * The main point of having operator classes is that for some data types, there could be more than one meaningful ordering.\n\t * For example, we might want to sort a complex-number data type either by absolute value or by real part.\n\t * We could do this by defining two operator classes for the data type and then selecting the proper class when creating an index.\n\t * More information about operator classes check:\n\t *\n\t * ### Useful links\n\t * https://www.postgresql.org/docs/current/sql-createindex.html\n\t *\n\t * https://www.postgresql.org/docs/current/indexes-opclass.html\n\t *\n\t * https://www.postgresql.org/docs/current/xindex.html\n\t *\n\t * ### Additional types\n\t * If you have the `Gel_vector` extension installed in your database, you can use the\n\t * `vector_l2_ops`, `vector_ip_ops`, `vector_cosine_ops`, `vector_l1_ops`, `bit_hamming_ops`, `bit_jaccard_ops`, `halfvec_l2_ops`, `sparsevec_l2_ops` options, which are predefined types.\n\t *\n\t * **You can always specify any string you want in the operator class, in case Drizzle doesn't have it natively in its types**\n\t *\n\t * @param opClass\n\t * @returns\n\t */\n\top(opClass: GelIndexOpClass): Omit<this, 'op'> {\n\t\tthis.indexConfig.opClass = opClass;\n\t\treturn this;\n\t}\n}\n\nexport class IndexedColumn {\n\tstatic readonly [entityKind]: string = 'IndexedColumn';\n\tconstructor(\n\t\tname: string | undefined,\n\t\tkeyAsName: boolean,\n\t\ttype: string,\n\t\tindexConfig: IndexedExtraConfigType,\n\t) {\n\t\tthis.name = name;\n\t\tthis.keyAsName = keyAsName;\n\t\tthis.type = type;\n\t\tthis.indexConfig = indexConfig;\n\t}\n\n\tname: string | undefined;\n\tkeyAsName: boolean;\n\ttype: string;\n\tindexConfig: IndexedExtraConfigType;\n}\n\nexport type AnyGelColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = GelColumn<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport type GelArrayColumnBuilderBaseConfig = ColumnBuilderBaseConfig<'array', 'GelArray'> & {\n\tsize: number | undefined;\n\tbaseBuilder: ColumnBuilderBaseConfig<ColumnDataType, string>;\n};\n\nexport class GelArrayBuilder<\n\tT extends GelArrayColumnBuilderBaseConfig,\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string> | GelArrayColumnBuilderBaseConfig,\n> extends GelColumnBuilder<\n\tT,\n\t{\n\t\tbaseBuilder: TBase extends GelArrayColumnBuilderBaseConfig ? GelArrayBuilder<\n\t\t\t\tTBase,\n\t\t\t\tTBase extends { baseBuilder: infer TBaseBuilder extends ColumnBuilderBaseConfig<any, any> } ? TBaseBuilder\n\t\t\t\t\t: never\n\t\t\t>\n\t\t\t: GelColumnBuilder<TBase, {}, Simplify<Omit<TBase, keyof ColumnBuilderBaseConfig<any, any>>>>;\n\t\tsize: T['size'];\n\t},\n\t{\n\t\tbaseBuilder: TBase extends GelArrayColumnBuilderBaseConfig ? GelArrayBuilder<\n\t\t\t\tTBase,\n\t\t\t\tTBase extends { baseBuilder: infer TBaseBuilder extends ColumnBuilderBaseConfig<any, any> } ? TBaseBuilder\n\t\t\t\t\t: never\n\t\t\t>\n\t\t\t: GelColumnBuilder<TBase, {}, Simplify<Omit<TBase, keyof ColumnBuilderBaseConfig<any, any>>>>;\n\t\tsize: T['size'];\n\t}\n> {\n\tstatic override readonly [entityKind] = 'GelArrayBuilder';\n\n\tconstructor(\n\t\tname: string,\n\t\tbaseBuilder: GelArrayBuilder<T, TBase>['config']['baseBuilder'],\n\t\tsize: T['size'],\n\t) {\n\t\tsuper(name, 'array', 'GelArray');\n\t\tthis.config.baseBuilder = baseBuilder;\n\t\tthis.config.size = size;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelArray<MakeColumnConfig<T, TTableName> & { size: T['size']; baseBuilder: T['baseBuilder'] }, TBase> {\n\t\tconst baseColumn = this.config.baseBuilder.build(table);\n\t\treturn new GelArray<MakeColumnConfig<T, TTableName> & { size: T['size']; baseBuilder: T['baseBuilder'] }, TBase>(\n\t\t\ttable as AnyGelTable<{ name: MakeColumnConfig<T, TTableName>['tableName'] }>,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t\tbaseColumn,\n\t\t);\n\t}\n}\n\nexport class GelArray<\n\tT extends ColumnBaseConfig<'array', 'GelArray'> & {\n\t\tsize: number | undefined;\n\t\tbaseBuilder: ColumnBuilderBaseConfig<ColumnDataType, string>;\n\t},\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n> extends GelColumn<T, {}, { size: T['size']; baseBuilder: T['baseBuilder'] }> {\n\treadonly size: T['size'];\n\n\tstatic override readonly [entityKind]: string = 'GelArray';\n\n\tconstructor(\n\t\ttable: AnyGelTable<{ name: T['tableName'] }>,\n\t\tconfig: GelArrayBuilder<T, TBase>['config'],\n\t\treadonly baseColumn: GelColumn,\n\t\treadonly range?: [number | undefined, number | undefined],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.size = config.size;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `${this.baseColumn.getSQLType()}[${typeof this.size === 'number' ? this.size : ''}]`;\n\t}\n}\n"], "mappings": "AASA,SAAS,qBAAqB;AAE9B,SAAS,cAAc;AACvB,SAAS,kBAAkB;AAI3B,SAAS,yBAAyB;AAGlC,SAAS,YAAY;AAErB,SAAS,qBAAqB;AAevB,MAAe,yBAKZ,cAEV;AAAA,EACS,oBAAuC,CAAC;AAAA,EAEhD,QAA0B,UAAU,IAAY;AAAA,EAEhD,MAAoD,MAclD;AACD,WAAO,IAAI,gBAAgB,KAAK,OAAO,MAAM,MAAoC,IAAW;AAAA,EAC7F;AAAA,EAEA,WACC,KACA,UAAsC,CAAC,GAChC;AACP,SAAK,kBAAkB,KAAK,EAAE,KAAK,QAAQ,CAAC;AAC5C,WAAO;AAAA,EACR;AAAA,EAEA,OACC,MACA,QACO;AACP,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,aAAa,QAAQ;AACjC,WAAO;AAAA,EACR;AAAA,EAEA,kBAAkB,IAEf;AACF,SAAK,OAAO,YAAY;AAAA,MACvB;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,IACP;AACA,WAAO;AAAA,EAGR;AAAA;AAAA,EAGA,iBAAiB,QAAmB,OAA+B;AAClE,WAAO,KAAK,kBAAkB,IAAI,CAAC,EAAE,KAAK,QAAQ,MAAM;AACvD,aAAO;AAAA,QACN,CAACA,MAAKC,aAAY;AACjB,gBAAM,UAAU,IAAI,kBAAkB,MAAM;AAC3C,kBAAM,gBAAgBD,KAAI;AAC1B,mBAAO,EAAE,SAAS,CAAC,MAAM,GAAG,gBAAgB,CAAC,aAAa,EAAE;AAAA,UAC7D,CAAC;AACD,cAAIC,SAAQ,UAAU;AACrB,oBAAQ,SAASA,SAAQ,QAAQ;AAAA,UAClC;AACA,cAAIA,SAAQ,UAAU;AACrB,oBAAQ,SAASA,SAAQ,QAAQ;AAAA,UAClC;AACA,iBAAO,QAAQ,MAAM,KAAK;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA,EAQA,uBACC,OACuB;AACvB,WAAO,IAAI,qBAAqB,OAAO,KAAK,MAAM;AAAA,EACnD;AACD;AAGO,MAAe,kBAIZ,OAA4D;AAAA,EAGrE,YACmB,OAClB,QACC;AACD,QAAI,CAAC,OAAO,YAAY;AACvB,aAAO,aAAa,cAAc,OAAO,CAAC,OAAO,IAAI,CAAC;AAAA,IACvD;AACA,UAAM,OAAO,MAAM;AAND;AAAA,EAOnB;AAAA,EAVA,QAA0B,UAAU,IAAY;AAWjD;AAIO,MAAM,6BAEH,UAAqC;AAAA,EAC9C,QAA0B,UAAU,IAAY;AAAA,EAEvC,aAAqB;AAC7B,WAAO,KAAK,WAAW;AAAA,EACxB;AAAA,EAEA,cAAsC;AAAA,IACrC,OAAO,KAAK,OAAO,SAAS;AAAA,IAC5B,OAAO,KAAK,OAAO,SAAS;AAAA,IAC5B,SAAS,KAAK,OAAO;AAAA,EACtB;AAAA,EACA,gBAAwC;AAAA,IACvC,OAAO;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACV;AAAA,EAEA,MAAkC;AACjC,SAAK,YAAY,QAAQ;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,OAAmC;AAClC,SAAK,YAAY,QAAQ;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,aAAqD;AACpD,SAAK,YAAY,QAAQ;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,YAAoD;AACnD,SAAK,YAAY,QAAQ;AACzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,GAAG,SAA4C;AAC9C,SAAK,YAAY,UAAU;AAC3B,WAAO;AAAA,EACR;AACD;AAEO,MAAM,cAAc;AAAA,EAC1B,QAAiB,UAAU,IAAY;AAAA,EACvC,YACC,MACA,WACA,MACA,aACC;AACD,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,cAAc;AAAA,EACpB;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAWO,MAAM,wBAGH,iBAoBR;AAAA,EACD,QAA0B,UAAU,IAAI;AAAA,EAExC,YACC,MACA,aACA,MACC;AACD,UAAM,MAAM,SAAS,UAAU;AAC/B,SAAK,OAAO,cAAc;AAC1B,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA;AAAA,EAGS,MACR,OACwG;AACxG,UAAM,aAAa,KAAK,OAAO,YAAY,MAAM,KAAK;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,iBAMH,UAAqE;AAAA,EAK9E,YACC,OACA,QACS,YACA,OACR;AACD,UAAM,OAAO,MAAM;AAHV;AACA;AAGT,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA,EAZS;AAAA,EAET,QAA0B,UAAU,IAAY;AAAA,EAYhD,aAAqB;AACpB,WAAO,GAAG,KAAK,WAAW,WAAW,CAAC,IAAI,OAAO,KAAK,SAAS,WAAW,KAAK,OAAO,EAAE;AAAA,EACzF;AACD;", "names": ["ref", "actions"]}