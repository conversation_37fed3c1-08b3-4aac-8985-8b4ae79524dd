// Test Bali Villa Realty status detection directly
require('dotenv').config();

async function testBaliVillaRealtyStatus() {
  console.log('🏠 Testing Bali Villa Realty Status Detection\n');
  
  // Import the mapper
  const { mapBaliVillaRealty } = require('../scrape_worker/mappers');
  
  // Test AVAILABLE property
  const availableProperty = {
    markdown: `
# 3 Bedroom Villa for Sale Leasehold in Bali Tabanan

**Price:** $400,000
**Location:** Tabanan, Bali
**Bedrooms:** 3
**Bathrooms:** 3
**Year Built:** 2024

Beautiful villa available for sale in Tabanan.
    `,
    url: 'https://balivillarealty.com/property/3-bedroom-villa-for-sale-leasehold-in-bali-tabanan/'
  };

  try {
    console.log('Testing AVAILABLE property:');
    console.log('Title: "3 Bedroom Villa for Sale Leasehold in Bali Tabanan"');
    console.log('Description: "Beautiful villa available for sale in Tabanan."');
    
    const result = await mapBaliVillaRealty(availableProperty);
    
    if (result) {
      console.log('\n✅ Mapping successful:');
      console.log(`   Status: ${result.status}`);
      console.log(`   Title: ${result.title}`);
      console.log(`   Price: ${result.price ? `IDR ${result.price.toLocaleString()}` : 'Not found'}`);
      console.log(`   Location: ${result.city}`);
      console.log(`   Bedrooms: ${result.bedrooms}`);
      
      if (result.status === 'AVAILABLE') {
        console.log('\n🎉 Status correctly detected as AVAILABLE!');
      } else {
        console.log(`\n❌ Status incorrectly detected as ${result.status} (expected AVAILABLE)`);
      }
    } else {
      console.log('\n❌ Mapping failed - returned null');
    }
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    console.error(error.stack);
  }
}

testBaliVillaRealtyStatus();
