require('dotenv').config();
const fetch = require('node-fetch');

async function debugSpaKeyword() {
  try {
    console.log('🔍 DEBUGGING SPA KEYWORD...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    
    // Get the raw markdown from Firecrawl
    console.log('1. GETTING RAW MARKDOWN:');
    
    const firecrawlResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const firecrawlData = await firecrawlResponse.json();
    
    if (firecrawlData.success && firecrawlData.data?.markdown) {
      const markdown = firecrawlData.data.markdown;
      console.log('✅ Got markdown:', markdown.length, 'characters');
      
      // Search for "spa" in the markdown
      console.log('\n2. SEARCHING FOR "SPA" KEYWORD:');
      const lowerMarkdown = markdown.toLowerCase();
      
      if (lowerMarkdown.includes('spa')) {
        console.log('⚠️  Found "spa" in markdown!');
        
        // Find all occurrences
        let index = 0;
        let occurrences = 0;
        
        while ((index = lowerMarkdown.indexOf('spa', index)) !== -1) {
          occurrences++;
          const start = Math.max(0, index - 50);
          const end = Math.min(markdown.length, index + 50);
          const context = markdown.substring(start, end);
          
          console.log(`\n   Occurrence ${occurrences} at position ${index}:`);
          console.log(`   Context: ...${context}...`);
          
          // Check if it's in a URL or filename
          const beforeSpa = markdown.substring(Math.max(0, index - 20), index);
          const afterSpa = markdown.substring(index + 3, Math.min(markdown.length, index + 20));
          
          if (beforeSpa.includes('http') || beforeSpa.includes('digitalocean') || beforeSpa.includes('.com')) {
            console.log(`   ❌ This "spa" is in a URL - should be ignored`);
          } else if (beforeSpa.includes('_') || afterSpa.includes('_') || beforeSpa.includes('.') || afterSpa.includes('.')) {
            console.log(`   ❌ This "spa" is in a filename - should be ignored`);
          } else {
            console.log(`   ✅ This "spa" might be a real amenity`);
          }
          
          index += 3; // Move past this occurrence
        }
        
        console.log(`\n   Total occurrences: ${occurrences}`);
        
      } else {
        console.log('✅ No "spa" found in markdown');
      }
      
      // Also check for other common false positives
      console.log('\n3. CHECKING FOR OTHER FALSE POSITIVES:');
      const falsePositives = ['digitaloceanspaces', 'spaces.com', 'display', 'disposal'];
      
      falsePositives.forEach(fp => {
        if (lowerMarkdown.includes(fp)) {
          console.log(`⚠️  Found potential false positive: "${fp}"`);
        }
      });
      
    } else {
      console.log('❌ Failed to get markdown from Firecrawl');
      console.log('Error:', firecrawlData.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

debugSpaKeyword();
