// Check Firecrawl API status directly
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function checkFirecrawlStatus() {
  console.log('🔍 Checking Firecrawl API status directly...\n');

  const keyManager = getKeyManager();
  
  // Bali Home Immo job
  const baliJobId = '04f2a942-230e-4ec5-8bdc-3d09e741457b';
  
  try {
    const currentKey = keyManager.getCurrentKey();
    console.log(`🔑 Using key ${currentKey.index + 1}/${keyManager.keys.length}`);
    
    const response = await fetch(`https://api.firecrawl.dev/v1/crawl/${baliJobId}`, {
      headers: {
        'Authorization': `Bearer ${currentKey.key}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${await response.text()}`);
    }

    const data = await response.json();
    
    console.log('📊 Bali Home Immo Crawl Status:');
    console.log(`   Status: ${data.status}`);
    console.log(`   Total: ${data.total}`);
    console.log(`   Completed: ${data.completed}`);
    console.log(`   Credits used: ${data.creditsUsed}`);
    
    if (data.data && data.data.length > 0) {
      console.log(`\n🔗 Found ${data.data.length} URLs so far:`);
      
      const urlCategories = {
        propertyPages: [],
        listingPages: [],
        otherPages: [],
        noUrl: []
      };
      
      data.data.forEach((item, index) => {
        let url = item.url;
        
        // Extract URL from markdown if missing
        if (!url && item.markdown) {
          const urlMatch = item.markdown.match(/https:\/\/bali-home-immo\.com\/[^\s\)]+/);
          if (urlMatch) {
            url = urlMatch[0];
          }
        }
        
        if (!url) {
          urlCategories.noUrl.push(index);
          return;
        }
        
        // Categorize URLs
        if (url.includes('/realestate-property/for-')) {
          urlCategories.propertyPages.push(url);
        } else if (
          url.includes('/search') ||
          url.includes('/category') ||
          url.includes('/properties') ||
          url.includes('/for-rent') ||
          url.includes('/for-sale')
        ) {
          urlCategories.listingPages.push(url);
        } else {
          urlCategories.otherPages.push(url);
        }
      });
      
      console.log('\n📋 URL Categories:');
      console.log(`   🏠 Property Pages: ${urlCategories.propertyPages.length}`);
      console.log(`   📄 Listing Pages: ${urlCategories.listingPages.length}`);
      console.log(`   🔗 Other Pages: ${urlCategories.otherPages.length}`);
      console.log(`   ❓ No URL: ${urlCategories.noUrl.length}`);
      
      // Show sample property pages
      if (urlCategories.propertyPages.length > 0) {
        console.log('\n🏠 Sample Property Pages:');
        urlCategories.propertyPages.slice(0, 5).forEach(url => {
          console.log(`   • ${url}`);
        });
      }
      
      // Show sample listing pages
      if (urlCategories.listingPages.length > 0) {
        console.log('\n📄 Sample Listing Pages:');
        urlCategories.listingPages.slice(0, 5).forEach(url => {
          console.log(`   • ${url}`);
        });
      }
      
      // Show sample other pages
      if (urlCategories.otherPages.length > 0) {
        console.log('\n🔗 Sample Other Pages:');
        urlCategories.otherPages.slice(0, 5).forEach(url => {
          console.log(`   • ${url}`);
        });
      }
      
      // Check for important pages we wanted to find
      const importantPages = [
        'search',
        'category',
        'properties',
        'for-rent',
        'for-sale'
      ];
      
      console.log('\n🎯 Important Pages Check:');
      importantPages.forEach(page => {
        const found = [...urlCategories.listingPages, ...urlCategories.otherPages]
          .some(url => url.includes(page));
        console.log(`   ${found ? '✅' : '❌'} ${page}`);
      });
      
    } else {
      console.log('\n❌ No crawl data found yet');
    }

  } catch (error) {
    console.error('❌ Error checking Firecrawl status:', error.message);
  } finally {
    process.exit(0);
  }
}

checkFirecrawlStatus();
