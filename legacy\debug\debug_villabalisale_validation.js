// Debug Villa Bali Sale validation issues
require('dotenv').config();
const Firecrawl = require('firecrawl').default;
const { mapVillaBaliSale } = require('./scrape_worker/mappers');
const { validateProperty } = require('./scrape_worker/validate');

async function debugVillaBaliSaleValidation() {
  console.log('🔍 Debugging Villa Bali Sale Validation Issues');
  console.log('='.repeat(60));
  
  const firecrawl = new Firecrawl({ apiKey: process.env.FIRECRAWL_API_KEY });
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/amed/ocean-views-three-bedroom-freehold-villa-in-amed-vl3341';
  
  try {
    console.log(`🔄 Testing URL: ${testUrl}`);
    
    // Step 1: Test Firecrawl extraction
    console.log('\n📡 Step 1: Firecrawl Extraction');
    const result = await firecrawl.scrapeUrl(testUrl, {
      formats: ['json', 'markdown'],
      jsonOptions: {
        prompt: "Extract property details including title, price, bedrooms, bathrooms, location, description, amenities, images, property_id, ownership_type, size information",
        schema: {
          type: "object",
          properties: {
            title: { type: "string" },
            price: { type: "string" },
            location: { type: "string" },
            bedrooms: { type: "integer" },
            bathrooms: { type: "integer" },
            description: { type: "string" },
            amenities: { type: "array", items: { type: "string" } },
            images: { type: "array", items: { type: "string" } },
            propertyID: { type: "string" },
            ownershipType: { type: "string" },
            size: {
              type: "object",
              properties: {
                building_size_sqm: { type: "number" },
                land_size_sqm: { type: "number" }
              }
            }
          }
        }
      },
      onlyMainContent: true,
      timeout: 60000
    });
    
    if (result.success && result.data) {
      console.log('✅ Firecrawl extraction successful');
      console.log('📊 Raw JSON data:');
      console.log(JSON.stringify(result.data.json, null, 2));
      console.log('\n📄 Markdown length:', result.data.markdown?.length || 0);
      console.log('📄 Markdown preview:', result.data.markdown?.substring(0, 200) + '...');
      
      // Step 2: Test mapper
      console.log('\n🗺️  Step 2: Mapper Processing');
      const rawData = {
        url: testUrl,
        json: result.data.json,
        markdown: result.data.markdown,
        html: result.data.html
      };
      
      const mappedData = await mapVillaBaliSale(rawData);
      console.log('✅ Mapper processing successful');
      console.log('📊 Mapped data:');
      console.log(`   Title: ${mappedData.title}`);
      console.log(`   Price: ${mappedData.price}`);
      console.log(`   City: ${mappedData.city}`);
      console.log(`   State: ${mappedData.state}`);
      console.log(`   Bedrooms: ${mappedData.bedrooms}`);
      console.log(`   Bathrooms: ${mappedData.bathrooms}`);
      console.log(`   Description length: ${mappedData.description?.length || 0}`);
      console.log(`   Description: ${mappedData.description?.substring(0, 100)}...`);
      console.log(`   Ownership type: ${mappedData.ownership_type}`);
      console.log(`   Amenities count: ${mappedData.amenities?.raw_amenities?.length || 0}`);
      console.log(`   External ID: ${mappedData.media?.external_id}`);
      
      // Step 3: Test validation
      console.log('\n✅ Step 3: Validation');
      const validation = validateProperty(mappedData);
      if (validation.isValid) {
        console.log('✅ Validation passed - property is valid');
      } else {
        console.log('❌ Validation failed:');
        validation.errors.forEach(error => {
          console.log(`   - ${error}`);
        });
      }
      
      // Step 4: Check specific validation requirements
      console.log('\n🔍 Step 4: Detailed Validation Check');
      console.log('Required fields check:');
      console.log(`   title: ${mappedData.title ? '✅' : '❌'} "${mappedData.title}"`);
      console.log(`   price: ${mappedData.price ? '✅' : '❌'} ${mappedData.price}`);
      console.log(`   city: ${mappedData.city ? '✅' : '❌'} "${mappedData.city}"`);
      console.log(`   state: ${mappedData.state ? '✅' : '❌'} "${mappedData.state}"`);
      console.log(`   country: ${mappedData.country ? '✅' : '❌'} "${mappedData.country}"`);
      console.log(`   bedrooms: ${mappedData.bedrooms ? '✅' : '❌'} ${mappedData.bedrooms}`);
      console.log(`   bathrooms: ${mappedData.bathrooms ? '✅' : '❌'} ${mappedData.bathrooms}`);
      console.log(`   description: ${mappedData.description ? '✅' : '❌'} ${mappedData.description?.length || 0} chars`);
      console.log(`   ownership_type: ${mappedData.ownership_type ? '✅' : '❌'} "${mappedData.ownership_type}"`);
      
      // Check price validation specifically
      if (mappedData.price) {
        console.log(`\n💰 Price validation:`);
        console.log(`   Price value: ${mappedData.price}`);
        console.log(`   Price type: ${typeof mappedData.price}`);
        console.log(`   Is number: ${typeof mappedData.price === 'number'}`);
        console.log(`   Is positive: ${mappedData.price > 0}`);
      }
      
    } else {
      console.log('❌ Firecrawl extraction failed');
      console.log('Error:', result.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

debugVillaBaliSaleValidation();
