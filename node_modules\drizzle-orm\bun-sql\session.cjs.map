{"version": 3, "sources": ["../../src/bun-sql/session.ts"], "sourcesContent": ["/// <reference types=\"bun-types\" />\n\nimport type { SavepointSQL, SQL, TransactionSQL } from 'bun';\nimport { type Cache, NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nexport class BunSQLPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'BunSQLPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: SQL,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params }, cache, queryMetadata, cacheConfig);\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async (span) => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t});\n\n\t\t\tthis.logger.logQuery(this.queryString, params);\n\n\t\t\tconst { fields, queryString: query, client, joinsNotNullableMap, customResultMapper } = this;\n\t\t\tif (!fields && !customResultMapper) {\n\t\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', async () => {\n\t\t\t\t\treturn await this.queryWithCache(query, params, async () => {\n\t\t\t\t\t\treturn await client.unsafe(query, params as any[]);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tconst rows: any[] = await tracer.startActiveSpan('drizzle.driver.execute', async () => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': query,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\n\t\t\t\treturn await this.queryWithCache(query, params, async () => {\n\t\t\t\t\treturn client.unsafe(query, params as any[]).values();\n\t\t\t\t});\n\t\t\t});\n\n\t\t\treturn tracer.startActiveSpan('drizzle.mapResponse', () => {\n\t\t\t\treturn customResultMapper\n\t\t\t\t\t? customResultMapper(rows)\n\t\t\t\t\t: rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t\t\t});\n\t\t});\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async (span) => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t});\n\t\t\tthis.logger.logQuery(this.queryString, params);\n\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', async () => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\t\t\t\treturn await this.queryWithCache(this.queryString, params, async () => {\n\t\t\t\t\treturn await this.client.unsafe(this.queryString, params as any[]);\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface BunSQLSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class BunSQLSession<\n\tTSQL extends SQL,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<BunSQLQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'BunSQLSession';\n\n\tlogger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tpublic client: TSQL,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\t/** @internal */\n\t\treadonly options: BunSQLSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): PgPreparedQuery<T> {\n\t\treturn new BunSQLPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tquery(query: string, params: unknown[]): Promise<any> {\n\t\tthis.logger.logQuery(query, params);\n\t\treturn this.client.unsafe(query, params as any[]).values();\n\t}\n\n\tqueryObjects(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<any> {\n\t\treturn this.client.unsafe(query, params as any[]);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: BunSQLTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T> {\n\t\treturn this.client.begin(async (client) => {\n\t\t\tconst session = new BunSQLSession<TransactionSQL, TFullSchema, TSchema>(\n\t\t\t\tclient,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.options,\n\t\t\t);\n\t\t\tconst tx = new BunSQLTransaction(this.dialect, session, this.schema);\n\t\t\tif (config) {\n\t\t\t\tawait tx.setTransaction(config);\n\t\t\t}\n\t\t\treturn transaction(tx);\n\t\t}) as Promise<T>;\n\t}\n}\n\nexport class BunSQLTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<BunSQLQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'BunSQLTransaction';\n\n\tconstructor(\n\t\tdialect: PgDialect,\n\t\t/** @internal */\n\t\toverride readonly session: BunSQLSession<TransactionSQL | SavepointSQL, TFullSchema, TSchema>,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tnestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema, nestedIndex);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: BunSQLTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\treturn (this.session.client as TransactionSQL).savepoint((client) => {\n\t\t\tconst session = new BunSQLSession<SavepointSQL, TFullSchema, TSchema>(\n\t\t\t\tclient,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.session.options,\n\t\t\t);\n\t\t\tconst tx = new BunSQLTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\t\treturn transaction(tx);\n\t\t}) as Promise<T>;\n\t}\n}\n\nexport interface BunSQLQueryResultHKT extends PgQueryResultHKT {\n\ttype: Assume<this['row'], Record<string, any>[]>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,kBAAsC;AAEtC,oBAA2B;AAE3B,oBAA2B;AAE3B,qBAA8B;AAG9B,qBAA2C;AAE3C,iBAA6C;AAC7C,qBAAuB;AACvB,mBAA0C;AAEnC,MAAM,4BAA2D,+BAAmB;AAAA,EAG1F,YACS,QACA,aACA,QACA,QACR,OACA,eAIA,aACQ,QACA,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,GAAG,OAAO,eAAe,WAAW;AAd7D;AACA;AACA;AACA;AAOA;AACA;AACA;AAAA,EAGT;AAAA,EAlBA,QAA0B,wBAAU,IAAY;AAAA,EAoBhD,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,WAAO,sBAAO,gBAAgB,mBAAmB,OAAO,SAAS;AAChE,YAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAE9D,YAAM,cAAc;AAAA,QACnB,sBAAsB,KAAK;AAAA,QAC3B,wBAAwB,KAAK,UAAU,MAAM;AAAA,MAC9C,CAAC;AAED,WAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAE7C,YAAM,EAAE,QAAQ,aAAa,OAAO,QAAQ,qBAAqB,mBAAmB,IAAI;AACxF,UAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,eAAO,sBAAO,gBAAgB,0BAA0B,YAAY;AACnE,iBAAO,MAAM,KAAK,eAAe,OAAO,QAAQ,YAAY;AAC3D,mBAAO,MAAM,OAAO,OAAO,OAAO,MAAe;AAAA,UAClD,CAAC;AAAA,QACF,CAAC;AAAA,MACF;AAEA,YAAM,OAAc,MAAM,sBAAO,gBAAgB,0BAA0B,YAAY;AACtF,cAAM,cAAc;AAAA,UACnB,sBAAsB;AAAA,UACtB,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AAED,eAAO,MAAM,KAAK,eAAe,OAAO,QAAQ,YAAY;AAC3D,iBAAO,OAAO,OAAO,OAAO,MAAe,EAAE,OAAO;AAAA,QACrD,CAAC;AAAA,MACF,CAAC;AAED,aAAO,sBAAO,gBAAgB,uBAAuB,MAAM;AAC1D,eAAO,qBACJ,mBAAmB,IAAI,IACvB,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,MACnF,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,WAAO,sBAAO,gBAAgB,mBAAmB,OAAO,SAAS;AAChE,YAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,YAAM,cAAc;AAAA,QACnB,sBAAsB,KAAK;AAAA,QAC3B,wBAAwB,KAAK,UAAU,MAAM;AAAA,MAC9C,CAAC;AACD,WAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAC7C,aAAO,sBAAO,gBAAgB,0BAA0B,YAAY;AACnE,cAAM,cAAc;AAAA,UACnB,sBAAsB,KAAK;AAAA,UAC3B,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AACD,eAAO,MAAM,KAAK,eAAe,KAAK,aAAa,QAAQ,YAAY;AACtE,iBAAO,MAAM,KAAK,OAAO,OAAO,KAAK,aAAa,MAAe;AAAA,QAClE,CAAC;AAAA,MACF,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAOO,MAAM,sBAIH,yBAAsD;AAAA,EAM/D,YACQ,QACP,SACQ,QAEC,UAAgC,CAAC,GACzC;AACD,UAAM,OAAO;AANN;AAEC;AAEC;AAGT,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,sBAAU;AAAA,EAC7C;AAAA,EAfA,QAA0B,wBAAU,IAAY;AAAA,EAEhD;AAAA,EACQ;AAAA,EAcR,aACC,OACA,QACA,MACA,uBACA,oBACA,eAIA,aACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,OAAe,QAAiC;AACrD,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,WAAO,KAAK,OAAO,OAAO,OAAO,MAAe,EAAE,OAAO;AAAA,EAC1D;AAAA,EAEA,aACC,OACA,QACe;AACf,WAAO,KAAK,OAAO,OAAO,OAAO,MAAe;AAAA,EACjD;AAAA,EAES,YACR,aACA,QACa;AACb,WAAO,KAAK,OAAO,MAAM,OAAO,WAAW;AAC1C,YAAM,UAAU,IAAI;AAAA,QACnB;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AACA,YAAM,KAAK,IAAI,kBAAkB,KAAK,SAAS,SAAS,KAAK,MAAM;AACnE,UAAI,QAAQ;AACX,cAAM,GAAG,eAAe,MAAM;AAAA,MAC/B;AACA,aAAO,YAAY,EAAE;AAAA,IACtB,CAAC;AAAA,EACF;AACD;AAEO,MAAM,0BAGH,6BAA0D;AAAA,EAGnE,YACC,SAEkB,SAClB,QACA,cAAc,GACb;AACD,UAAM,SAAS,SAAS,QAAQ,WAAW;AAJzB;AAAA,EAKnB;AAAA,EAVA,QAA0B,wBAAU,IAAY;AAAA,EAYvC,YACR,aACa;AACb,WAAQ,KAAK,QAAQ,OAA0B,UAAU,CAAC,WAAW;AACpE,YAAM,UAAU,IAAI;AAAA,QACnB;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,QAAQ;AAAA,MACd;AACA,YAAM,KAAK,IAAI,kBAAwC,KAAK,SAAS,SAAS,KAAK,MAAM;AACzF,aAAO,YAAY,EAAE;AAAA,IACtB,CAAC;AAAA,EACF;AACD;", "names": []}