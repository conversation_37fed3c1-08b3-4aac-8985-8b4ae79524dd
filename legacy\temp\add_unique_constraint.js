// Add unique constraint to existing property table
require('dotenv').config();
const { db } = require('./drizzle_client');
const { sql } = require('drizzle-orm');

async function addUniqueConstraint() {
  console.log('🔧 Adding unique constraint to property table...\n');
  
  try {
    // Check if constraint already exists
    console.log('1. Checking existing constraints...');
    const constraints = await db.execute(sql`
      SELECT constraint_name, constraint_type
      FROM information_schema.table_constraints
      WHERE table_name = 'property' AND constraint_type = 'UNIQUE';
    `);
    console.table(constraints);
    
    // Add unique constraint if it doesn't exist
    const hasUniqueConstraint = constraints.some(c => 
      c.constraint_name.includes('source_id') || 
      c.constraint_name.includes('external_id')
    );
    
    if (!hasUniqueConstraint) {
      console.log('\n2. Adding unique constraint on (source_id, external_id)...');
      await db.execute(sql`
        ALTER TABLE property 
        ADD CONSTRAINT property_source_external_unique 
        UNIQUE (source_id, external_id);
      `);
      console.log('✅ Unique constraint added successfully!');
    } else {
      console.log('\n2. Unique constraint already exists.');
    }
    
    // Verify the constraint was added
    console.log('\n3. Verifying constraints after update...');
    const updatedConstraints = await db.execute(sql`
      SELECT constraint_name, constraint_type
      FROM information_schema.table_constraints
      WHERE table_name = 'property' AND constraint_type = 'UNIQUE';
    `);
    console.table(updatedConstraints);
    
  } catch (error) {
    console.error('❌ Error adding constraint:', error);
  }
}

if (require.main === module) {
  addUniqueConstraint().catch(console.error);
}

module.exports = { addUniqueConstraint };
