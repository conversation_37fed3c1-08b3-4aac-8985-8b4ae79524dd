// Queue Manager - Manages the scraping queue and processes URLs
require('dotenv').config();
const { db, scrapingQueue, discoveredUrls, properties } = require('../drizzle_client');
const { runExtractBatch } = require('./run_batch');
const { eq, and, lte, desc, asc, count } = require('drizzle-orm');

class QueueManager {
  constructor() {
    this.isProcessing = false;
    this.maxConcurrentJobs = parseInt(process.env.SCRAPE_CONCURRENCY) || 10; // Use SCRAPE_CONCURRENCY from .env
    this.processingInterval = 30000; // Check queue every 30 seconds
    console.log(`📋 Queue Manager initialized with ${this.maxConcurrentJobs} concurrent jobs`);
  }

  // Start the queue processor
  start() {
    console.log('🚀 Starting queue processor...');
    this.processQueue();
    
    // Set up periodic processing
    this.intervalId = setInterval(() => {
      if (!this.isProcessing) {
        this.processQueue();
      }
    }, this.processingInterval);
  }

  // Stop the queue processor
  stop() {
    console.log('⏹️  Stopping queue processor...');
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  // Process ALL pending items in the queue
  async processAllQueue() {
    if (this.isProcessing) {
      console.log('⏳ Queue processing already in progress, skipping...');
      return;
    }

    this.isProcessing = true;
    console.log('📋 Processing ALL items in scraping queue...');

    try {
      let totalProcessed = 0;
      let hasMore = true;

      while (hasMore) {
        // Get pending items from queue (ordered by priority and scheduled time)
        const pendingItems = await db.select({
          id: scrapingQueue.id,
          url: scrapingQueue.url,
          website_id: scrapingQueue.website_id,
          priority: scrapingQueue.priority,
          attempts: scrapingQueue.attempts,
          max_attempts: scrapingQueue.max_attempts,
          discovered_url_id: scrapingQueue.discovered_url_id
        })
        .from(scrapingQueue)
        .where(
          and(
            eq(scrapingQueue.status, 'pending'),
            lte(scrapingQueue.scheduled_for, new Date())
          )
        )
        .orderBy(desc(scrapingQueue.priority), asc(scrapingQueue.scheduled_for))
        .limit(this.maxConcurrentJobs);

        if (pendingItems.length === 0) {
          console.log('📭 No more pending items in queue');
          hasMore = false;
          break;
        }

        console.log(`📦 Processing batch ${Math.floor(totalProcessed / this.maxConcurrentJobs) + 1}: ${pendingItems.length} items`);

        // Group by website for batch processing
        const groupedByWebsite = {};
        pendingItems.forEach(item => {
          if (!groupedByWebsite[item.website_id]) {
            groupedByWebsite[item.website_id] = [];
          }
          groupedByWebsite[item.website_id].push(item);
        });

        // Process each website group
        for (const [websiteId, items] of Object.entries(groupedByWebsite)) {
          await this.processWebsiteGroup(websiteId, items);
        }

        totalProcessed += pendingItems.length;
        console.log(`✅ Processed ${totalProcessed} items so far...`);

        // Small delay between batches to respect rate limits
        if (hasMore) {
          console.log('⏳ Waiting 30 seconds before next batch...');
          await new Promise(resolve => setTimeout(resolve, 30000));
        }
      }

      console.log(`🎉 Completed processing all queue items! Total: ${totalProcessed}`);

    } catch (error) {
      console.error('❌ Error processing all queue:', error.message);
    } finally {
      this.isProcessing = false;
    }
  }

  // Process pending items in the queue (single batch)
  async processQueue() {
    if (this.isProcessing) {
      console.log('⏳ Queue processing already in progress, skipping...');
      return;
    }

    this.isProcessing = true;
    console.log('📋 Processing scraping queue...');

    try {
      // Get pending items from queue (ordered by priority and scheduled time)
      const pendingItems = await db.select({
        id: scrapingQueue.id,
        url: scrapingQueue.url,
        website_id: scrapingQueue.website_id,
        priority: scrapingQueue.priority,
        attempts: scrapingQueue.attempts,
        max_attempts: scrapingQueue.max_attempts,
        discovered_url_id: scrapingQueue.discovered_url_id
      })
      .from(scrapingQueue)
      .where(
        and(
          eq(scrapingQueue.status, 'pending'),
          lte(scrapingQueue.scheduled_for, new Date())
        )
      )
      .orderBy(desc(scrapingQueue.priority), asc(scrapingQueue.scheduled_for))
      .limit(this.maxConcurrentJobs);

      if (pendingItems.length === 0) {
        console.log('📭 No pending items in queue');
        return;
      }

      console.log(`📦 Processing ${pendingItems.length} items from queue`);

      // Group by website for batch processing
      const groupedByWebsite = {};
      pendingItems.forEach(item => {
        if (!groupedByWebsite[item.website_id]) {
          groupedByWebsite[item.website_id] = [];
        }
        groupedByWebsite[item.website_id].push(item);
      });

      // Process each website group
      for (const [websiteId, items] of Object.entries(groupedByWebsite)) {
        await this.processWebsiteGroup(websiteId, items);
      }

    } catch (error) {
      console.error('❌ Error processing queue:', error.message);
    } finally {
      this.isProcessing = false;
    }
  }

  // Process a group of URLs from the same website
  async processWebsiteGroup(websiteId, items) {
    console.log(`🌐 Processing ${items.length} URLs for ${websiteId}`);

    // Mark items as processing
    const itemIds = items.map(item => item.id);
    await db.update(scrapingQueue)
      .set({
        status: 'processing',
        started_at: new Date(),
        updated_at: new Date()
      })
      .where(eq(scrapingQueue.id, itemIds[0])); // Update first item as example

    try {
      // Extract URLs for batch processing
      const urls = items.map(item => item.url);

      // Run batch extraction with source URL IDs
      const extractOptions = {
        source_url_ids: items.map(item => item.discovered_url_id) // Pass discovered_url_ids for linking
      };
      const batchResult = await runExtractBatch(websiteId, urls, extractOptions);

      // Extract the actual results array from the batch result
      const results = batchResult.processedResults || [];

      // Process results
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const result = results[i];

        await this.processQueueItemResult(item, result);
      }

      console.log(`✅ Completed processing ${items.length} URLs for ${websiteId}`);

    } catch (error) {
      console.error(`❌ Failed to process ${websiteId} group:`, error.message);

      // Mark all items as failed
      for (const item of items) {
        await this.markItemFailed(item, error.message);
      }
    }
  }



  // Process the result of a single queue item
  async processQueueItemResult(queueItem, scrapingResult) {
    const now = new Date();

    try {
      if (scrapingResult && scrapingResult.ok) {
        // Successful scraping
        console.log(`✅ Successfully scraped: ${queueItem.url}`);

        // Update queue item as completed
        await db.update(scrapingQueue)
          .set({
            status: 'completed',
            completed_at: now,
            updated_at: now
          })
          .where(eq(scrapingQueue.id, queueItem.id));

        // Update discovered URL
        await db.update(discoveredUrls)
          .set({
            last_scraped_at: now,
            scrape_status: 'scraped',
            property_id: scrapingResult.id, // Link to the created property
            updated_at: now
          })
          .where(eq(discoveredUrls.id, queueItem.discovered_url_id));

        // Update property with source URL reference
        if (scrapingResult.id) {
          await db.update(properties)
            .set({
              source_url_id: queueItem.discovered_url_id,
              last_scraped_at: now
            })
            .where(eq(properties.id, scrapingResult.id));
        }

      } else {
        // Failed scraping - provide more detailed error information
        let errorMessage = 'Unknown scraping error';

        if (scrapingResult?.error) {
          errorMessage = scrapingResult.error;
        } else if (scrapingResult?.status) {
          errorMessage = `HTTP ${scrapingResult.status}: ${scrapingResult.statusText || 'Server error'}`;
        } else if (scrapingResult === null) {
          errorMessage = 'No response received from scraper';
        } else if (scrapingResult === undefined) {
          errorMessage = 'Scraper returned undefined result';
        }

        console.log(`❌ Failed to scrape: ${queueItem.url} - ${errorMessage}`);

        await this.markItemFailed(queueItem, errorMessage);
      }

    } catch (error) {
      console.error(`❌ Error processing result for ${queueItem.url}:`, error.message);
      await this.markItemFailed(queueItem, error.message);
    }
  }

  // Mark a queue item as failed
  async markItemFailed(queueItem, errorMessage) {
    const now = new Date();
    const newAttempts = queueItem.attempts + 1;

    if (newAttempts >= queueItem.max_attempts) {
      // Max attempts reached, mark as failed
      await db.update(scrapingQueue)
        .set({
          status: 'failed',
          attempts: newAttempts,
          error_message: errorMessage,
          completed_at: now,
          updated_at: now
        })
        .where(eq(scrapingQueue.id, queueItem.id));

      // Update discovered URL
      await db.update(discoveredUrls)
        .set({
          scrape_status: 'failed',
          scrape_attempts: newAttempts,
          updated_at: now
        })
        .where(eq(discoveredUrls.id, queueItem.discovered_url_id));

    } else {
      // Retry later
      const retryDelay = Math.pow(2, newAttempts) * 60 * 1000; // Exponential backoff
      const nextAttempt = new Date(now.getTime() + retryDelay);

      await db.update(scrapingQueue)
        .set({
          status: 'pending',
          attempts: newAttempts,
          error_message: errorMessage,
          scheduled_for: nextAttempt,
          started_at: null,
          updated_at: now
        })
        .where(eq(scrapingQueue.id, queueItem.id));

      console.log(`🔄 Scheduled retry for ${queueItem.url} in ${Math.round(retryDelay / 60000)} minutes`);
    }
  }

  // Get queue statistics
  async getQueueStats() {
    try {
      const stats = await db.select({
        status: scrapingQueue.status,
        count: count()
      })
      .from(scrapingQueue)
      .groupBy(scrapingQueue.status);

      const result = {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0
      };

      stats.forEach(stat => {
        result[stat.status] = parseInt(stat.count);
        result.total += parseInt(stat.count);
      });

      return result;
    } catch (error) {
      console.error('❌ Error getting queue stats:', error.message);
      return null;
    }
  }

  // Add URL to queue manually
  async addToQueue(url, websiteId, priority = 5) {
    try {
      // Check if URL already exists in discovered_urls
      let discoveredUrl = await db.select().from(discoveredUrls)
        .where(eq(discoveredUrls.url, url));

      if (discoveredUrl.length === 0) {
        // Create discovered URL entry
        discoveredUrl = await db.insert(discoveredUrls).values({
          url: url,
          website_id: websiteId,
          url_type: 'property',
          is_property_page: true,
          confidence_score: 1.0,
          classification_reason: 'Manually added',
          discovered_at: new Date()
        }).returning();
      }

      // Add to queue
      await db.insert(scrapingQueue).values({
        discovered_url_id: discoveredUrl[0].id,
        url: url,
        website_id: websiteId,
        priority: priority,
        scheduled_for: new Date()
      });

      console.log(`➕ Added ${url} to scraping queue`);
      return true;

    } catch (error) {
      console.error(`❌ Failed to add ${url} to queue:`, error.message);
      return false;
    }
  }

  // Clear completed items from queue (cleanup)
  async cleanupQueue(olderThanDays = 7) {
    try {
      const cutoffDate = new Date(Date.now() - (olderThanDays * 24 * 60 * 60 * 1000));
      
      const result = await db.delete(scrapingQueue)
        .where(
          and(
            eq(scrapingQueue.status, 'completed'),
            lte(scrapingQueue.completed_at, cutoffDate)
          )
        );

      console.log(`🧹 Cleaned up queue: removed ${result.rowCount || 0} completed items older than ${olderThanDays} days`);
      return result.rowCount || 0;

    } catch (error) {
      console.error('❌ Error cleaning up queue:', error.message);
      return 0;
    }
  }
}

module.exports = { QueueManager };
