// Test script voor verbeterde scraping met parking en vector embeddings
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { testConnection } = require('./drizzle_client');

async function testEnhancedScraping() {
  console.log('🧪 Testing enhanced scraping with parking and vector embeddings...\n');
  
  // Test database connection first
  console.log('1. Testing database connection...');
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ Database connection failed. Exiting.');
    return;
  }
  
  // Test URLs (one from each source)
  const testUrls = {
    betterplace: ['https://betterplace.cc/buy/properties/BPVL02232'],
    bali_home_immo: ['https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508'],
    bali_villa_realty: ['https://balivillarealty.com/bali-villa-rentals/']
  };
  
  for (const [sourceId, urls] of Object.entries(testUrls)) {
    console.log(`\n2. Testing ${sourceId} scraping...`);
    try {
      const results = await runExtractBatch(sourceId, urls, {});
      console.log(`✅ ${sourceId} results:`, results);
    } catch (error) {
      console.error(`❌ ${sourceId} failed:`, error.message);
    }
  }
  
  console.log('\n🎉 Enhanced scraping test completed!');
}

// Run the test
if (require.main === module) {
  testEnhancedScraping().catch(console.error);
}

module.exports = { testEnhancedScraping };
