// Check recent database entries for missing fields
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc } = require('drizzle-orm');

async function checkRecentDatabase() {
  console.log('🔍 Recent Database Analysis');
  console.log('='.repeat(40));
  
  try {
    // Get the 3 most recent properties
    const recentProperties = await db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(3);
    
    console.log(`📋 Found ${recentProperties.length} recent properties\n`);
    
    recentProperties.forEach((prop, index) => {
      console.log(`${index + 1}. 🏠 ${prop.title}`);
      console.log(`   🌐 Source: ${prop.media?.source_id || 'NULL'}`);
      console.log(`   📅 Created: ${new Date(prop.created_at).toLocaleString()}`);
      console.log('');
      
      // Focus on the problematic fields
      console.log('   🔍 PROBLEMATIC FIELDS:');
      console.log(`   📐 Lot Size (sqft): ${prop.lot_size_sqft || 'NULL'}`);
      console.log(`   📋 Lease Duration Text: ${prop.lease_duration_text || 'NULL'}`);
      console.log(`   📋 Lease Duration Years: ${prop.lease_duration_years || 'NULL'}`);
      
      // Description analysis
      if (prop.description) {
        console.log(`   📝 Description (first 200 chars): "${prop.description.substring(0, 200)}..."`);
        console.log(`   📏 Description Length: ${prop.description.length} chars`);
        
        // Check for problematic content
        const problems = [];
        if (prop.description.includes('Property description not available')) problems.push('Generic placeholder');
        if (prop.description.includes('![')) problems.push('Image markdown');
        if (prop.description.includes('https://')) problems.push('URLs');
        if (prop.description.includes('wp-content')) problems.push('wp-content');
        
        if (problems.length > 0) {
          console.log(`   ⚠️  Description Issues: ${problems.join(', ')}`);
        } else {
          console.log(`   ✅ Description OK`);
        }
      } else {
        console.log(`   📝 Description: NULL`);
      }
      
      // All other fields for completeness
      console.log('\n   📊 ALL FIELDS:');
      console.log(`   💰 Price: ${prop.price || 'NULL'} | Rent: ${prop.rent_price || 'NULL'}`);
      console.log(`   🛏️  Bedrooms: ${prop.bedrooms || 'NULL'}`);
      console.log(`   🚿 Bathrooms: ${prop.bathrooms || 'NULL'}`);
      console.log(`   🚗 Parking: ${prop.parking_spaces || 'NULL'}`);
      console.log(`   📐 Size: ${prop.size_sqft || 'NULL'} sqft`);
      console.log(`   📅 Year Built: ${prop.year_built || 'NULL'}`);
      console.log(`   🏠 Ownership: ${prop.ownership_type || 'NULL'}`);
      console.log(`   📍 Address: ${prop.address || 'NULL'}`);
      console.log(`   🏙️  City: ${prop.city || 'NULL'}`);
      console.log(`   🌍 State: ${prop.state || 'NULL'}`);
      
      console.log('\n' + '─'.repeat(80) + '\n');
    });
    
    // Summary of missing fields
    console.log('📊 MISSING FIELDS SUMMARY:');
    console.log('='.repeat(40));
    
    const fieldStats = {
      lot_size_sqft: 0,
      lease_duration_text: 0,
      lease_duration_years: 0,
      parking_spaces: 0
    };
    
    recentProperties.forEach(prop => {
      if (prop.lot_size_sqft) fieldStats.lot_size_sqft++;
      if (prop.lease_duration_text) fieldStats.lease_duration_text++;
      if (prop.lease_duration_years) fieldStats.lease_duration_years++;
      if (prop.parking_spaces) fieldStats.parking_spaces++;
    });
    
    const total = recentProperties.length;
    Object.entries(fieldStats).forEach(([field, count]) => {
      const percentage = ((count / total) * 100).toFixed(1);
      const status = count === 0 ? '❌' : count === total ? '✅' : '⚠️';
      console.log(`   ${status} ${field}: ${percentage}% (${count}/${total})`);
    });
    
    console.log('\n🔧 ISSUES TO FIX:');
    if (fieldStats.lot_size_sqft === 0) {
      console.log('   ❌ Lot size extraction not working');
    }
    if (fieldStats.lease_duration_text === 0) {
      console.log('   ❌ Lease duration text extraction not working');
    }
    if (fieldStats.lease_duration_years === 0) {
      console.log('   ❌ Lease duration years extraction not working');
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  }
}

// Run check
if (require.main === module) {
  checkRecentDatabase().catch(console.error);
}

module.exports = { checkRecentDatabase };
