// Simple test using QueueManager to process 3 URLs
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, properties } = require('./drizzle_client');
const { desc, sql } = require('drizzle-orm');

async function testQueueManagerSimple() {
  console.log('🧪 Simple Queue Manager Test: 3 URLs');
  console.log('='.repeat(50));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  const queueManager = new QueueManager();
  
  try {
    // Get initial count
    const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`📊 Initial properties in database: ${initialCount[0].count}`);
    
    // Process 3 URLs from any available website
    console.log('\n🔄 Processing 3 URLs from queue...');
    const startTime = Date.now();
    
    // Try different websites until we find one with pending URLs
    const websites = ['bali_home_immo', 'betterplace', 'bali_villa_realty'];
    let processed = false;
    
    for (const website of websites) {
      try {
        console.log(`\n🌐 Trying ${website.toUpperCase()}...`);
        const result = await queueManager.processQueue(website, 3);
        
        if (result && result.processed > 0) {
          const duration = ((Date.now() - startTime) / 1000).toFixed(1);
          
          console.log(`✅ Successfully processed ${result.processed} URLs in ${duration}s`);
          console.log(`   Successful: ${result.successful || 0}`);
          console.log(`   Failed: ${result.failed || 0}`);
          console.log(`   Skipped: ${result.skipped || 0}`);
          
          processed = true;
          break;
        } else {
          console.log(`   ⚠️  No URLs processed for ${website}`);
        }
      } catch (error) {
        console.log(`   ❌ Error with ${website}: ${error.message}`);
      }
    }
    
    if (!processed) {
      console.log('❌ No URLs were processed from any website');
    }
    
    // Check final count and show new properties
    const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
    const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
    console.log(`\n🆕 New properties added to database: ${newProperties}`);
    
    if (newProperties > 0) {
      console.log('\n🏠 Latest Properties Added:');
      const latestProps = await db
        .select({
          title: properties.title,
          source_id: properties.source_id,
          price: properties.price,
          rent_price: properties.rent_price,
          bedrooms: properties.bedrooms,
          city: properties.city,
          amenities: properties.amenities,
          created_at: properties.created_at
        })
        .from(properties)
        .orderBy(desc(properties.created_at))
        .limit(newProperties);
      
      latestProps.forEach((prop, i) => {
        const price = prop.price ? `IDR ${prop.price.toLocaleString()}` : 
                     prop.rent_price ? `IDR ${prop.rent_price.toLocaleString()}/month` : 'Price on request';
        console.log(`\n   ${i + 1}. [${prop.source_id}] ${prop.title.substring(0, 50)}...`);
        console.log(`      💰 ${price}`);
        console.log(`      🏠 ${prop.bedrooms} bed | 📍 ${prop.city}`);
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
        
        // Check amenities
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`      ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        } else {
          console.log(`      ❌ No amenities found`);
        }
      });
    }
    
    console.log('\n✅ Simple queue test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testQueueManagerSimple();
