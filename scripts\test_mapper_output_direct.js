// Test what the mappers actually output
require('dotenv').config();

async function testMapperOutputDirect() {
  console.log('🔍 Testing Mapper Output Directly\n');
  
  try {
    const { mapBetterPlace, mapBaliHomeImmo, mapBaliVillaRealty } = require('../scrape_worker/mappers');
    
    // Test BetterPlace mapper with sample data
    console.log('🌐 Testing BetterPlace Mapper:');
    console.log('-'.repeat(40));
    
    const betterPlaceSample = {
      title: 'Beautiful Villa in Canggu',
      price: 'USD 450,000',
      location: 'Canggu, Bali',
      bedrooms: 3,
      bathrooms: 2,
      description: 'Beautiful villa with pool',
      amenities: ['Pool', 'WiFi', 'Kitchen'],
      images: ['image1.jpg', 'image2.jpg'],
      property_id: 'BP123',
      detail_url: 'https://betterplace.cc/property/123'
    };
    
    const betterPlaceResult = await mapBetterPlace(betterPlaceSample);
    console.log('Result:', JSON.stringify(betterPlaceResult, null, 2));
    
    // Check validation
    const { validateProperty } = require('../scrape_worker/validate');
    const validation = validateProperty(betterPlaceResult);
    console.log('Validation:', validation);
    
    // Test Bali Home Immo mapper
    console.log('\n🌐 Testing Bali Home Immo Mapper:');
    console.log('-'.repeat(40));
    
    const baliHomeImmoSample = {
      title: 'Luxury Villa in Seminyak',
      price: 'IDR 8,500,000,000',
      location: 'Seminyak, Bali',
      bedrooms: 4,
      bathrooms: 3,
      description: 'Luxury villa near beach',
      amenities: ['Pool', 'Garden', 'Security'],
      images: ['image1.jpg', 'image2.jpg', 'image3.jpg'],
      property_id: 'BHI456',
      detail_url: 'https://bali-home-immo.com/property/456'
    };
    
    const baliHomeImmoResult = mapBaliHomeImmo(baliHomeImmoSample);
    console.log('Result:', JSON.stringify(baliHomeImmoResult, null, 2));
    
    const validation2 = validateProperty(baliHomeImmoResult);
    console.log('Validation:', validation2);
    
    // Test Bali Villa Realty mapper
    console.log('\n🌐 Testing Bali Villa Realty Mapper:');
    console.log('-'.repeat(40));
    
    const baliVillaRealtySample = {
      title: '2 Bedroom Townhouse in Batu Bolong',
      price: 'USD 1,200/month',
      location: 'Batu Bolong, Canggu',
      bedrooms: 2,
      bathrooms: 1,
      description: 'Modern townhouse for rent',
      amenities: ['WiFi', 'Kitchen', 'Parking'],
      images: ['image1.jpg'],
      property_id: 'BVR789',
      detail_url: 'https://balivillarealty.com/property/789'
    };
    
    const baliVillaRealtyResult = await mapBaliVillaRealty(baliVillaRealtySample);
    console.log('Result:', JSON.stringify(baliVillaRealtyResult, null, 2));
    
    const validation3 = validateProperty(baliVillaRealtyResult);
    console.log('Validation:', validation3);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testMapperOutputDirect();
