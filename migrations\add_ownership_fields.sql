-- Add ownership type fields to property table
-- Migration: Add ownership_type, lease_duration_years, lease_duration_text

ALTER TABLE property 
ADD COLUMN ownership_type TEXT CHECK (ownership_type IN ('FREEHOLD', 'LEASEHOLD', 'RENT'));

ALTER TABLE property 
ADD COLUMN lease_duration_years INTEGER;

ALTER TABLE property 
ADD COLUMN lease_duration_text TEXT;

-- Create index for fast ownership type filtering
CREATE INDEX idx_property_ownership_type ON property(ownership_type);

-- Create index for lease duration filtering
CREATE INDEX idx_property_lease_duration ON property(lease_duration_years) WHERE ownership_type = 'LEASEHOLD';

-- Add comments for documentation
COMMENT ON COLUMN property.ownership_type IS 'Property ownership type: FREEHOLD, LEASEHOLD, or RENT';
COMMENT ON COLUMN property.lease_duration_years IS 'Lease duration in years (only for LEASEHOLD properties)';
COMMENT ON COLUMN property.lease_duration_text IS 'Original lease duration text for complex terms (e.g., "25+25 years")';
