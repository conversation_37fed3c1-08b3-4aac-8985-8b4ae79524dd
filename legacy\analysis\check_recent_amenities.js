// Check recent properties with amenities
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc, sql } = require('drizzle-orm');

async function checkRecentProperties() {
  try {
    console.log('🔍 Checking recent properties with amenities...');
    
    // Get properties from last 10 minutes
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
    
    const recentProps = await db
      .select({
        id: properties.id,
        title: properties.title,
        source_id: properties.source_id,
        external_id: properties.external_id,
        amenities: properties.amenities,
        created_at: properties.created_at
      })
      .from(properties)
      .where(sql`${properties.created_at} > ${tenMinutesAgo}`)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`\n🆕 Found ${recentProps.length} recent properties:`);
    
    recentProps.forEach((prop, i) => {
      console.log(`\n${i + 1}. [${prop.source_id}] ${prop.title.substring(0, 50)}...`);
      console.log(`   ID: ${prop.id}`);
      console.log(`   External ID: ${prop.external_id}`);
      console.log(`   Created: ${new Date(prop.created_at).toLocaleString()}`);
      
      if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
        console.log(`   ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 5).join(', ')}${prop.amenities.raw_amenities.length > 5 ? '...' : ''}`);
      } else {
        console.log(`   ❌ No amenities found`);
      }
    });
    
    // Count by amenities status
    const withAmenities = recentProps.filter(p => p.amenities && p.amenities.raw_amenities && p.amenities.raw_amenities.length > 0);
    const withoutAmenities = recentProps.filter(p => !p.amenities || !p.amenities.raw_amenities || p.amenities.raw_amenities.length === 0);
    
    console.log(`\n📊 Amenities Summary:`);
    console.log(`   ✅ With amenities: ${withAmenities.length}`);
    console.log(`   ❌ Without amenities: ${withoutAmenities.length}`);
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

checkRecentProperties();
