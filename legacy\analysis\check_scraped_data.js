// Check scraped data in database
require('dotenv').config();
const { db } = require('./drizzle_client');
const { sql } = require('drizzle-orm');

async function checkScrapedData() {
  console.log('🔍 Checking scraped data in database...\n');
  
  try {
    // Get all properties
    console.log('1. All properties in database:');
    const allProperties = await db.execute(sql`
      SELECT id, title, bedrooms, bathrooms, parking_spaces, city, source_id, external_id,
             CASE WHEN vector IS NOT NULL THEN 'YES' ELSE 'NO' END as has_vector
      FROM property
      ORDER BY created_at DESC;
    `);
    console.table(allProperties);
    
    // Get detailed info for the latest property
    if (allProperties.length > 0) {
      console.log('\n2. Detailed info for latest property:');
      const latestProperty = await db.execute(sql`
        SELECT *
        FROM property
        ORDER BY created_at DESC
        LIMIT 1;
      `);
      
      const property = latestProperty[0];
      console.log('Title:', property.title);
      console.log('Location:', property.address);
      console.log('Bedrooms:', property.bedrooms);
      console.log('Bathrooms:', property.bathrooms);
      console.log('Parking Spaces:', property.parking_spaces);
      console.log('Size (sqft):', property.size_sqft);
      console.log('Year Built:', property.year_built);
      console.log('Has Vector:', property.vector ? 'YES' : 'NO');
      console.log('Amenities:', JSON.stringify(property.amenities, null, 2));
      console.log('Media Images Count:', property.media?.image_count || 0);
    }
    
  } catch (error) {
    console.error('❌ Error checking data:', error);
  }
}

if (require.main === module) {
  checkScrapedData().catch(console.error);
}

module.exports = { checkScrapedData };
