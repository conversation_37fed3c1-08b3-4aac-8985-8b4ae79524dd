// Simple database monitoring during scraping
require('dotenv').config();
const { db, properties, closeConnection } = require('../drizzle_client');
const { sql, desc } = require('drizzle-orm');

async function simpleDatabaseMonitor() {
  try {
    console.log('📊 Simple Database Quality Monitor\n');
    
    // Get total count
    const totalResult = await db
      .select({ count: sql`count(*)` })
      .from(properties);
    
    const totalCount = totalResult[0]?.count || 0;
    console.log(`📈 Total Properties: ${totalCount}`);
    
    if (totalCount === 0) {
      console.log('⚠️  No properties in database yet - scraping may still be starting');
      return;
    }
    
    // Get counts by source
    try {
      const sourceCounts = await db
        .select({
          source: properties.source,
          count: sql`count(*)`.as('count')
        })
        .from(properties)
        .groupBy(properties.source)
        .orderBy(sql`count(*) DESC`);
      
      console.log('\n🌐 Properties by Source:');
      sourceCounts.forEach(row => {
        console.log(`   ${row.source || 'Unknown'}: ${row.count}`);
      });
    } catch (error) {
      console.log('⚠️  Could not get source counts:', error.message);
    }
    
    // Get latest 5 properties
    try {
      const latestProperties = await db
        .select({
          id: properties.id,
          title: properties.title,
          source: properties.source,
          price: properties.price,
          rent_price: properties.rent_price,
          bedrooms: properties.bedrooms,
          bathrooms: properties.bathrooms,
          city: properties.city,
          created_at: properties.created_at
        })
        .from(properties)
        .orderBy(desc(properties.created_at))
        .limit(5);
      
      console.log('\n🔍 Latest 5 Properties:');
      console.log('='.repeat(80));
      
      latestProperties.forEach((prop, i) => {
        console.log(`\n${i + 1}. ${prop.title || 'No title'}`);
        console.log(`   🌐 Source: ${prop.source || 'Unknown'}`);
        console.log(`   📅 Created: ${prop.created_at?.toLocaleString() || 'Unknown'}`);
        
        // Price
        if (prop.price) {
          console.log(`   💰 Sale Price: IDR ${prop.price.toLocaleString()}`);
        } else if (prop.rent_price) {
          console.log(`   🏡 Rent Price: IDR ${prop.rent_price.toLocaleString()}/month`);
        } else {
          console.log(`   ⚠️  No Price`);
        }
        
        // Basic details
        console.log(`   🏠 Bedrooms: ${prop.bedrooms || 'N/A'}`);
        console.log(`   🚿 Bathrooms: ${prop.bathrooms || 'N/A'}`);
        console.log(`   📍 City: ${prop.city || 'N/A'}`);
      });
      
    } catch (error) {
      console.log('⚠️  Could not get latest properties:', error.message);
    }
    
    // Check for recent additions (last 5 minutes)
    try {
      const recentResult = await db
        .select({ count: sql`count(*)` })
        .from(properties)
        .where(sql`created_at > NOW() - INTERVAL '5 minutes'`);
      
      const recentCount = recentResult[0]?.count || 0;
      console.log(`\n⏱️  Properties added in last 5 minutes: ${recentCount}`);
      
      if (recentCount > 0) {
        console.log('✅ Scraping is actively adding new properties!');
      } else {
        console.log('⚠️  No recent additions - check if scraping is running');
      }
      
    } catch (error) {
      console.log('⚠️  Could not check recent additions:', error.message);
    }
    
    // Simple quality check
    try {
      const qualityResult = await db
        .select({
          with_price: sql`count(case when price is not null or rent_price is not null then 1 end)`.as('with_price'),
          with_bedrooms: sql`count(case when bedrooms is not null then 1 end)`.as('with_bedrooms'),
          with_location: sql`count(case when city is not null then 1 end)`.as('with_location'),
          total: sql`count(*)`.as('total')
        })
        .from(properties);
      
      const quality = qualityResult[0];
      if (quality && quality.total > 0) {
        console.log('\n📊 Data Quality Summary:');
        console.log(`   💰 Properties with price: ${quality.with_price}/${quality.total} (${((quality.with_price / quality.total) * 100).toFixed(1)}%)`);
        console.log(`   🏠 Properties with bedrooms: ${quality.with_bedrooms}/${quality.total} (${((quality.with_bedrooms / quality.total) * 100).toFixed(1)}%)`);
        console.log(`   📍 Properties with location: ${quality.with_location}/${quality.total} (${((quality.with_location / quality.total) * 100).toFixed(1)}%)`);
        
        const avgQuality = ((quality.with_price + quality.with_bedrooms + quality.with_location) / (quality.total * 3)) * 100;
        console.log(`   📈 Overall Quality Score: ${avgQuality.toFixed(1)}%`);
        
        if (avgQuality >= 80) {
          console.log('   🎉 EXCELLENT data quality!');
        } else if (avgQuality >= 60) {
          console.log('   ✅ GOOD data quality');
        } else {
          console.log('   ⚠️  Data quality needs improvement');
        }
      }
      
    } catch (error) {
      console.log('⚠️  Could not check data quality:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    closeConnection();
  }
}

simpleDatabaseMonitor();
