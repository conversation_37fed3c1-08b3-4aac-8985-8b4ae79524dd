// Simple BetterPlace test
require('dotenv').config();
const Firecrawl = require('firecrawl').default;

async function simpleBetterPlaceTest() {
  console.log('🧪 Simple BetterPlace Test');
  
  const firecrawl = new Firecrawl({ apiKey: process.env.FIRECRAWL_API_KEY });
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02348';
  
  try {
    console.log(`🔄 Testing URL: ${testUrl}`);
    
    const result = await firecrawl.scrapeUrl(testUrl, {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 30000
    });
    
    console.log('📊 Result:');
    console.log('Success:', result.success);
    console.log('Data keys:', Object.keys(result.data || {}));
    console.log('Markdown length:', result.data?.markdown?.length || 0);
    
    if (result.data?.markdown) {
      console.log('First 500 chars:', result.data.markdown.substring(0, 500));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

simpleBetterPlaceTest().catch(console.error);
