// Test Generic Mapper fix for Bali Coconut Living
require('dotenv').config();
const { GenericMapper } = require('../scrape_worker/generic_mapper');
const { websiteConfigs } = require('../scrape_worker/website_configs');

async function testGenericMapperFix() {
  console.log('🥥 Testing Generic Mapper Fix for Bali Coconut Living\n');
  
  // Sample content from the Villa Jangmi page
  const sampleContent = `
    # A NICE 2BR VILLA IN PETITENGET FOR YEARLY AND SALE

    ## Villa Jangmi Detail
    * ID: V010-4221
    * Type: Villa
    * Location: Petitenget
    * Bedroom(s): 2
    * Bathroom(s): 2
    * Swimming Pool: Yes
    * Furniture: Fully Furnished
    * Living Room: Close
    * Land Size: 155 m2
    * Building Size: 150 m2
    * No. of Floor: 1

    ## Villa Jangmi
    Petitenget
    Villa | V010-4221

    **Sale & Rent Type**
    Freehold and Yearly

    IDR 5.000.000.000
    IDR 380.000.000

    Add to Wishlist Export Pdf
  `;

  const testUrl = 'https://balicoconutliving.com/bali-villa-yearly-rental/Petitenget/4569-V010-4221/Villa-Jangmi';
  
  try {
    // Get Bali Coconut Living config
    const config = websiteConfigs.bali_coconut_living;
    
    if (!config) {
      console.error('❌ Bali Coconut Living config not found');
      return;
    }
    
    console.log('📋 Using Bali Coconut Living config');
    
    // Create mapper
    const mapper = new GenericMapper(config);
    
    // Test full mapping
    console.log('🗺️  Testing full mapping...');
    const mapped = await mapper.mapProperty({
      markdown: sampleContent,
      url: testUrl,
      metadata: { title: 'A NICE 2BR VILLA IN PETITENGET FOR YEARLY AND SALE' }
    });
    
    if (mapped) {
      console.log('\n✅ Full mapping successful:');
      console.log(`   Title: ${mapped.title}`);
      console.log(`   Location: ${mapped.city}`);
      console.log(`   Bedrooms: ${mapped.bedrooms}`);
      console.log(`   Bathrooms: ${mapped.bathrooms}`);
      console.log(`   Sale Price: ${mapped.price ? `IDR ${mapped.price.toLocaleString()}` : 'Not found'}`);
      console.log(`   Rent Price: ${mapped.rent_price ? `IDR ${mapped.rent_price.toLocaleString()}` : 'Not found'}`);
      console.log(`   Ownership: ${mapped.ownership_type}`);
      console.log(`   Land Size: ${mapped.lot_size_sqft ? `${mapped.lot_size_sqft} sqft` : 'Not found'}`);
      console.log(`   Building Size: ${mapped.size_sqft ? `${mapped.size_sqft} sqft` : 'Not found'}`);
      
      // Check if it passes validation
      const validation = mapper.validateProperty(mapped);
      console.log(`\n🔍 Validation: ${validation.isValid ? '✅ PASS' : '❌ FAIL'}`);
      if (!validation.isValid) {
        console.log(`   Errors: ${validation.errors.join(', ')}`);
      }
    } else {
      console.log('❌ Full mapping failed - returned null');
    }
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    console.error(error.stack);
  }
}

testGenericMapperFix();
