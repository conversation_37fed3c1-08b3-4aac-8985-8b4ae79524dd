// Check exchange rates in database
require('dotenv').config();
const { db, exchangeRates } = require('./drizzle_client');
const { desc } = require('drizzle-orm');

async function checkExchangeRates() {
  try {
    console.log('🔍 Checking exchange rates in database...\n');
    
    // Get all exchange rates
    const rates = await db.select().from(exchangeRates).orderBy(desc(exchangeRates.date)).limit(20);
    
    if (rates.length === 0) {
      console.log('❌ No exchange rates found in database');
      console.log('💡 The exchange_rates table exists but is empty');
      return;
    }
    
    console.log(`✅ Found ${rates.length} exchange rate entries:`);
    console.log('\n📊 Recent Exchange Rates:');
    console.log('Date'.padEnd(12) + 'From'.padEnd(6) + 'To'.padEnd(6) + 'Rate'.padEnd(12) + 'Created');
    console.log('-'.repeat(60));
    
    rates.forEach(rate => {
      const date = new Date(rate.date).toISOString().split('T')[0];
      const created = new Date(rate.created_at).toISOString().split('T')[0];
      console.log(
        date.padEnd(12) + 
        rate.from_currency.padEnd(6) + 
        rate.to_currency.padEnd(6) + 
        rate.rate.padEnd(12) + 
        created
      );
    });
    
    // Check for today's rates
    const today = new Date().toISOString().split('T')[0];
    const todayRates = rates.filter(rate => 
      new Date(rate.date).toISOString().split('T')[0] === today
    );
    
    console.log(`\n📅 Today's rates (${today}): ${todayRates.length} entries`);
    
    if (todayRates.length > 0) {
      console.log('✅ Current exchange rates available for today');
      todayRates.forEach(rate => {
        console.log(`   ${rate.from_currency} → ${rate.to_currency}: ${rate.rate}`);
      });
    } else {
      console.log('⚠️  No exchange rates for today - using fallback rates');
    }
    
  } catch (error) {
    console.error('❌ Error checking exchange rates:', error.message);
  } finally {
    process.exit(0);
  }
}

if (require.main === module) {
  checkExchangeRates();
}

module.exports = { checkExchangeRates };
