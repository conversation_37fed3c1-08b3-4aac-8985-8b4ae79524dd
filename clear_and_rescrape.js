require('dotenv').config();
const { Pool } = require('pg');

async function clearAndRescrape() {
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    console.log('🗑️  CLEARING PROPERTY TABLE...');
    
    // Clear the property table
    const deleteResult = await pool.query('DELETE FROM property');
    console.log(`✅ Deleted ${deleteResult.rowCount} properties from database`);
    
    // Verify table is empty
    const countResult = await pool.query('SELECT COUNT(*) as count FROM property');
    console.log(`📊 Properties remaining: ${countResult.rows[0].count}`);
    
    if (countResult.rows[0].count === '0') {
      console.log('✅ Property table is now empty');
      console.log('\n🚀 Ready to start fresh scraping with new CSS-based extraction!');
      console.log('💡 Run: node scripts/start_queue_processor.js');
    } else {
      console.log('❌ Table not fully cleared');
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

clearAndRescrape();
