// Test complete SOLD property handling
require('dotenv').config();

async function testCompleteSoldHandling() {
  console.log('🏠 Testing Complete SOLD Property Handling\n');
  
  // Import the mapper
  const { mapBaliVillaRealty } = require('../scrape_worker/mappers');
  
  // Test cases
  const testCases = [
    {
      name: 'SOLD Property',
      property: {
        markdown: `
# Kerobokan 4 Bedroom Private Pool Villa (SOLD)

**Price:** $242,038
**Location:** Kerobokan, Bali
**Bedrooms:** 4
**Bathrooms:** 3
**Year Built:** 2025

This beautiful villa has been sold and is no longer available.
        `,
        url: 'https://balivillarealty.com/property/kerobokan-4-bedroom-private-pool-villa/'
      },
      expectedStatus: 'SOLD'
    },
    {
      name: 'AVAILABLE Property',
      property: {
        markdown: `
# 3 Bedroom Villa for Sale Leasehold in Bali Tabanan

**Price:** $400,000
**Location:** Tabanan, Bali
**Bedrooms:** 3
**Bathrooms:** 3
**Year Built:** 2024

Beautiful villa available for sale in Tabanan.
        `,
        url: 'https://balivillarealty.com/property/3-bedroom-villa-for-sale-leasehold-in-bali-tabanan/'
      },
      expectedStatus: 'AVAILABLE'
    },
    {
      name: 'SOLD Property (different format)',
      property: {
        markdown: `
# Off-Plan 3 Bedroom Villa for Sale Leasehold in Bali Seseh (SOLD)

**Price:** $350,000
**Location:** Seseh, Bali
**Bedrooms:** 3
**Bathrooms:** 2

Modern villa in Seseh area.
        `,
        url: 'https://balivillarealty.com/property/off-plan-3-bedroom-villa-for-sale-leasehold-in-bali-seseh/'
      },
      expectedStatus: 'SOLD'
    }
  ];

  let passed = 0;
  let failed = 0;

  for (const testCase of testCases) {
    console.log(`🔍 Testing: ${testCase.name}`);
    
    try {
      const result = await mapBaliVillaRealty(testCase.property);
      
      if (result) {
        console.log(`   ✅ Mapped successfully`);
        console.log(`   📊 Status: ${result.status} (expected: ${testCase.expectedStatus})`);
        console.log(`   💰 Price: ${result.price ? `IDR ${result.price.toLocaleString()}` : 'Not found'}`);
        console.log(`   🏠 Bedrooms: ${result.bedrooms}`);
        console.log(`   📍 Location: ${result.city}`);
        
        if (result.status === testCase.expectedStatus) {
          console.log(`   ✅ Status correctly detected!`);
          passed++;
        } else {
          console.log(`   ❌ Status incorrectly detected (expected ${testCase.expectedStatus})`);
          failed++;
        }
      } else {
        console.log(`   ❌ Mapping failed - returned null`);
        failed++;
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      failed++;
    }
    
    console.log('');
  }

  console.log('📊 Summary:');
  console.log(`   ✅ Passed: ${passed}/${testCases.length}`);
  console.log(`   ❌ Failed: ${failed}/${testCases.length}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / testCases.length) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All status detection tests passed!');
    console.log('✅ SOLD properties are correctly detected');
    console.log('✅ AVAILABLE properties remain available');
    console.log('✅ Ready for production use!');
  } else {
    console.log('\n⚠️  Some tests failed - needs investigation');
  }
}

testCompleteSoldHandling();
