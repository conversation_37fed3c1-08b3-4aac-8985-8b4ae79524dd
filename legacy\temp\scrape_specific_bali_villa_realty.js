// Scrape specific Bali Villa Realty properties with different URLs
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { testConnection } = require('./drizzle_client');

async function scrapeSpecificBaliVillaRealty() {
  console.log('🏠 Scraping specific Bali Villa Realty properties...\n');
  
  // Test database connection first
  console.log('1. Testing database connection...');
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ Database connection failed. Exiting.');
    return;
  }
  
  // Try different approaches to get more properties
  const urls = [
    // Main listing page
    'https://balivillarealty.com/bali-villa-rentals/',
    
    // Try different search parameters
    'https://balivillarealty.com/bali-villa-rentals/?location=canggu',
    'https://balivillarealty.com/bali-villa-rentals/?location=seminyak',
    'https://balivillarealty.com/bali-villa-rentals/?location=ubud',
    'https://balivillarealty.com/bali-villa-rentals/?location=kerobokan',
    
    // Try different property types
    'https://balivillarealty.com/bali-villa-rentals/?bedrooms=2',
    'https://balivillarealty.com/bali-villa-rentals/?bedrooms=3',
    'https://balivillarealty.com/bali-villa-rentals/?bedrooms=4',
    
    // Try different rental types
    'https://balivillarealty.com/bali-villa-rentals/?rental_type=monthly',
    'https://balivillarealty.com/bali-villa-rentals/?rental_type=yearly'
  ];
  
  console.log(`\n2. Scraping ${urls.length} different URLs from Bali Villa Realty...`);
  console.log('URLs to scrape:');
  urls.forEach((url, index) => {
    console.log(`   ${index + 1}. ${url}`);
  });
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('bali_villa_realty', urls, {});
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n3. ✅ Scraping completed in ${duration} seconds!`);
    console.log('\n📊 Results Summary:');
    
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}`);
    console.log(`   ❌ Failed: ${failed.length}`);
    console.log(`   📈 Success Rate: ${((successful.length / results.length) * 100).toFixed(1)}%`);
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully scraped properties:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title}`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ Failed properties:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. Error: ${result.error}`);
      });
    }
    
  } catch (error) {
    console.error(`❌ Batch scraping failed:`, error.message);
  }
  
  console.log('\n🎉 Specific Bali Villa Realty scraping completed!');
}

// Run the scraping
if (require.main === module) {
  scrapeSpecificBaliVillaRealty().catch(console.error);
}

module.exports = { scrapeSpecificBaliVillaRealty };
