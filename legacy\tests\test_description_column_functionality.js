// Test description column functionality and queue status
require('dotenv').config();
const { db, properties, scrapingQueue } = require('./drizzle_client');
const { desc, sql, eq, and } = require('drizzle-orm');

async function testDescriptionColumnFunctionality() {
  console.log('🧪 Testing Description Column Functionality & Queue Status');
  console.log('='.repeat(70));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  try {
    // Test 1: Verify description column exists and works
    console.log('\n📋 Test 1: Description Column Verification');
    console.log('='.repeat(50));
    
    // Check column exists
    const columnCheck = await db.execute(sql`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'property' 
      AND column_name = 'description'
    `);
    
    if (columnCheck.length > 0) {
      console.log('✅ Description column exists:');
      console.log(`   Column: ${columnCheck[0].column_name}`);
      console.log(`   Type: ${columnCheck[0].data_type}`);
      console.log(`   Nullable: ${columnCheck[0].is_nullable}`);
    } else {
      console.log('❌ Description column not found!');
      return;
    }
    
    // Test insert with description
    console.log('\n🧪 Testing description insert...');
    const testDescription = 'This is a test description for the property to verify the column works correctly.';
    
    const insertResult = await db.execute(sql`
      INSERT INTO property (
        title, category, type, status, address, city, country, description,
        price, bedrooms, bathrooms, source_id, external_id, source_url
      ) VALUES (
        'Test Property with Description', 'RESIDENTIAL', 'VILLA', 'AVAILABLE', 
        'Test Address', 'Test City', 'Indonesia', ${testDescription},
        1000000, 2, 2, 'test_source', 'TEST_DESC_001', 'https://test.com'
      )
      RETURNING id, title, description
    `);
    
    if (insertResult.length > 0) {
      console.log('✅ Description insert successful:');
      console.log(`   ID: ${insertResult[0].id}`);
      console.log(`   Title: ${insertResult[0].title}`);
      console.log(`   Description length: ${insertResult[0].description?.length || 0}`);
      console.log(`   Description: ${insertResult[0].description?.substring(0, 80)}...`);
      
      // Clean up test record
      await db.execute(sql`DELETE FROM property WHERE external_id = 'TEST_DESC_001'`);
      console.log('   ✅ Test record cleaned up');
    }
    
    // Test 2: Check current properties with/without descriptions
    console.log('\n📊 Test 2: Current Properties Description Status');
    console.log('='.repeat(50));
    
    const totalProperties = await db.select({ count: sql`count(*)` }).from(properties);
    const withDescription = await db.execute(sql`
      SELECT COUNT(*) as count FROM property WHERE description IS NOT NULL AND description != ''
    `);
    const withoutDescription = await db.execute(sql`
      SELECT COUNT(*) as count FROM property WHERE description IS NULL OR description = ''
    `);
    
    console.log(`📈 Property Description Statistics:`);
    console.log(`   Total properties: ${totalProperties[0].count}`);
    console.log(`   With description: ${withDescription[0].count}`);
    console.log(`   Without description: ${withoutDescription[0].count}`);
    console.log(`   Description coverage: ${((withDescription[0].count / totalProperties[0].count) * 100).toFixed(1)}%`);
    
    // Test 3: Check queue status for each website
    console.log('\n📋 Test 3: Queue Status by Website');
    console.log('='.repeat(50));
    
    const websites = [
      { id: 'betterplace', name: 'BetterPlace' },
      { id: 'bali_home_immo.com', name: 'Bali Home Immo' },
      { id: 'bali-villa-realty.com', name: 'Bali Villa Realty' },
      { id: 'villabalisale.com', name: 'Villa Bali Sale' }
    ];
    
    console.log('🌐 Queue Status by Website:');
    let totalPendingUrls = 0;
    
    for (const website of websites) {
      const queueStats = await db.execute(sql`
        SELECT 
          status,
          COUNT(*) as count
        FROM scraping_queue 
        WHERE website_id = ${website.id}
        GROUP BY status
      `);
      
      const pendingCount = queueStats.find(s => s.status === 'pending')?.count || 0;
      const processedCount = queueStats.find(s => s.status === 'processed')?.count || 0;
      const failedCount = queueStats.find(s => s.status === 'failed')?.count || 0;
      const totalCount = pendingCount + processedCount + failedCount;
      
      console.log(`\n   ${website.name} (${website.id}):`);
      console.log(`      Pending: ${pendingCount}`);
      console.log(`      Processed: ${processedCount}`);
      console.log(`      Failed: ${failedCount}`);
      console.log(`      Total: ${totalCount}`);
      
      totalPendingUrls += pendingCount;
      
      if (pendingCount > 0) {
        // Show sample URLs
        const sampleUrls = await db
          .select({ url: scrapingQueue.url })
          .from(scrapingQueue)
          .where(and(
            eq(scrapingQueue.website_id, website.id),
            eq(scrapingQueue.status, 'pending')
          ))
          .limit(2);
        
        console.log(`      Sample URLs:`);
        sampleUrls.forEach((row, i) => {
          console.log(`         ${i + 1}. ${row.url.substring(0, 70)}...`);
        });
      }
    }
    
    console.log(`\n📊 Total pending URLs across all websites: ${totalPendingUrls}`);
    
    // Test 4: Check latest properties by source
    console.log('\n🏠 Test 4: Latest Properties by Source (with Description Check)');
    console.log('='.repeat(50));
    
    for (const website of websites) {
      const sourceId = website.id === 'villabalisale.com' ? 'villa_bali_sale' : 
                      website.id === 'bali_home_immo.com' ? 'bali_home_immo' :
                      website.id === 'bali-villa-realty.com' ? 'bali_villa_realty' :
                      website.id;
      
      const latestProps = await db
        .select({
          id: properties.id,
          title: properties.title,
          external_id: properties.external_id,
          price: properties.price,
          bedrooms: properties.bedrooms,
          city: properties.city,
          state: properties.state,
          description: properties.description,
          amenities: properties.amenities,
          created_at: properties.created_at
        })
        .from(properties)
        .where(eq(properties.source_id, sourceId))
        .orderBy(desc(properties.created_at))
        .limit(2);
      
      if (latestProps.length > 0) {
        console.log(`\n   ${website.name} (${latestProps.length} latest properties):`);
        latestProps.forEach((prop, i) => {
          console.log(`\n      ${i + 1}. [${prop.external_id}] ${prop.title}`);
          if (prop.price) {
            console.log(`         💰 IDR ${prop.price.toLocaleString()}`);
          }
          console.log(`         🏠 ${prop.bedrooms} bed | 📍 ${prop.city}, ${prop.state || 'No state'}`);
          
          // Check description
          if (prop.description && prop.description.length > 0) {
            console.log(`         ✅ Description: ${prop.description.length} chars`);
            console.log(`         📝 Preview: ${prop.description.substring(0, 60)}...`);
          } else {
            console.log(`         ❌ No description`);
          }
          
          // Check amenities
          if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
            console.log(`         🎯 Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 2).join(', ')}${prop.amenities.raw_amenities.length > 2 ? '...' : ''}`);
          } else {
            console.log(`         ⚠️  No amenities`);
          }
          
          console.log(`         ⏰ ${new Date(prop.created_at).toLocaleString()}`);
        });
      } else {
        console.log(`\n   ${website.name}: No properties found`);
      }
    }
    
    // Test 5: Summary and recommendations
    console.log('\n📈 Test 5: Summary & Recommendations');
    console.log('='.repeat(50));
    
    console.log('✅ Description Column Status: OPERATIONAL');
    console.log(`📊 Current Description Coverage: ${((withDescription[0].count / totalProperties[0].count) * 100).toFixed(1)}%`);
    console.log(`🔄 URLs Available for Processing: ${totalPendingUrls}`);
    
    if (totalPendingUrls > 0) {
      console.log('\n🎯 Recommendations:');
      console.log('   1. ✅ Description column is ready for use');
      console.log('   2. 🔑 Firecrawl API credits needed for scraping');
      console.log('   3. 📊 Once credits available, can process URLs to test description extraction');
      console.log(`   4. 🎯 ${totalPendingUrls} URLs ready for processing across all websites`);
    } else {
      console.log('\n⚠️  No pending URLs available for testing');
      console.log('   Consider running sitemap discovery to find new URLs');
    }
    
    console.log('\n✅ Description column functionality test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testDescriptionColumnFunctionality();
