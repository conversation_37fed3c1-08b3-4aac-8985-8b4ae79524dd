// Analyze Bali Home Immo markdown for price patterns
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function analyzeBaliHomeImmoMarkdown() {
  console.log('🔍 Analyzing Bali Home Immo Markdown for Price Patterns');
  console.log('='.repeat(50));
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();
  
  const testUrl = 'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/4-bedroom-family-villa-for-rent-in-buduk-canggu-residential-side-rf6558';
  
  try {
    console.log(`📡 Scraping: ${testUrl}`);
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.markdown) {
      const markdown = result.data.markdown;
      console.log(`📝 Markdown length: ${markdown.length} chars`);
      
      // Look for price-related content
      console.log('\n💰 Searching for price patterns...');
      
      // Search for various price patterns
      const pricePatterns = [
        /price.*?(\d+)/gi,
        /(\d+).*?price/gi,
        /rent.*?(\d+)/gi,
        /(\d+).*?rent/gi,
        /IDR.*?(\d+)/gi,
        /(\d+).*?IDR/gi,
        /USD.*?(\d+)/gi,
        /(\d+).*?USD/gi,
        /Rp.*?(\d+)/gi,
        /(\d+).*?Rp/gi,
        /\$.*?(\d+)/gi,
        /(\d+).*?\$/gi,
        /month.*?(\d+)/gi,
        /(\d+).*?month/gi,
        /monthly.*?(\d+)/gi,
        /(\d+).*?monthly/gi
      ];
      
      pricePatterns.forEach((pattern, i) => {
        const matches = [...markdown.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\nPattern ${i+1} (${pattern.source}):`);
          matches.slice(0, 5).forEach(match => {
            const start = Math.max(0, match.index - 50);
            const end = Math.min(markdown.length, match.index + match[0].length + 50);
            const context = markdown.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      // Look for specific rental terms
      console.log('\n🏠 Looking for rental-specific terms...');
      
      const rentalPatterns = [
        /for\s*rent/gi,
        /rental/gi,
        /monthly/gi,
        /per\s*month/gi,
        /\/\s*month/gi
      ];
      
      rentalPatterns.forEach(pattern => {
        const matches = [...markdown.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\n${pattern.source} found ${matches.length} times:`);
          matches.slice(0, 3).forEach(match => {
            const start = Math.max(0, match.index - 50);
            const end = Math.min(markdown.length, match.index + match[0].length + 50);
            const context = markdown.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      // Show a sample of the markdown
      console.log('\n📄 Markdown Sample (first 1000 chars):');
      console.log(markdown.substring(0, 1000));
      
    } else {
      console.log('❌ Failed to get markdown');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

analyzeBaliHomeImmoMarkdown().then(() => process.exit(0));
