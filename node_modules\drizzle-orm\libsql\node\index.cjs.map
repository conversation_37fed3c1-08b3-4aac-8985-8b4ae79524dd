{"version": 3, "sources": ["../../../src/libsql/node/index.ts"], "sourcesContent": ["import { type Client, type Config, createClient } from '@libsql/client/node';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport { construct, type LibSQLDatabase } from '../driver-core.ts';\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends Client = Client,\n>(\n\t...params: [\n\t\tTClient | string,\n\t] | [\n\t\tTClient | string,\n\t\tDrizzleConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t& DrizzleConfig<TSchema>\n\t\t\t& ({\n\t\t\t\tconnection: string | Config;\n\t\t\t} | {\n\t\t\t\tclient: TClient;\n\t\t\t})\n\t\t),\n\t]\n): LibSQLDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst instance = createClient({\n\t\t\turl: params[0],\n\t\t});\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as\n\t\t\t& { connection?: Config; client?: TClient }\n\t\t\t& DrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tconst instance = typeof connection === 'string' ? createClient({ url: connection }) : createClient(connection!);\n\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): LibSQLDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuD;AACvD,mBAA6C;AAC7C,yBAA+C;AAExC,SAAS,WAIZ,QAiBF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,eAAW,0BAAa;AAAA,MAC7B,KAAK,OAAO,CAAC;AAAA,IACd,CAAC;AAED,eAAO,8BAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,UAAI,uBAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAIzD,QAAI,OAAQ,YAAO,8BAAU,QAAQ,aAAa;AAElD,UAAM,WAAW,OAAO,eAAe,eAAW,0BAAa,EAAE,KAAK,WAAW,CAAC,QAAI,0BAAa,UAAW;AAE9G,eAAO,8BAAU,UAAU,aAAa;AAAA,EACzC;AAEA,aAAO,8BAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AACvF;AAAA,CAEO,CAAUA,aAAV;AACC,WAAS,KACf,QAGC;AACD,eAAO,8BAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["drizzle"]}