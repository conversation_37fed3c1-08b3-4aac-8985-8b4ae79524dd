// Direct check of scraping_queue table for villa_bali_sale
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq, sql } = require('drizzle-orm');

async function checkScrapingQueueDirect() {
  try {
    console.log('🔍 Direct check of scraping_queue table...');
    
    // First check all website_ids in the queue
    console.log('\n📊 All website_ids in scraping_queue:');
    const allWebsites = await db
      .select({ 
        website_id: scrapingQueue.website_id,
        count: sql`count(*)`.as('count')
      })
      .from(scrapingQueue)
      .groupBy(scrapingQueue.website_id);
    
    allWebsites.forEach(row => {
      console.log(`   ${row.website_id}: ${row.count} URLs`);
    });
    
    // Check specifically for villa_bali_sale (exact match)
    console.log('\n🏠 Checking villa_bali_sale URLs...');
    const villaBaliSaleUrls = await db
      .select({
        url: scrapingQueue.url,
        id: scrapingQueue.id,
        status: scrapingQueue.status,
        attempts: scrapingQueue.attempts,
        website_id: scrapingQueue.website_id,
        created_at: scrapingQueue.created_at
      })
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'villa_bali_sale'))
      .limit(10);
    
    console.log(`Found ${villaBaliSaleUrls.length} villa_bali_sale URLs`);
    
    if (villaBaliSaleUrls.length > 0) {
      villaBaliSaleUrls.forEach((url, i) => {
        console.log(`\n${i + 1}. [${url.status}] ${url.url}`);
        console.log(`   ID: ${url.id}`);
        console.log(`   Website ID: ${url.website_id}`);
        console.log(`   Attempts: ${url.attempts}`);
        console.log(`   Created: ${new Date(url.created_at).toLocaleString()}`);
      });
    }
    
    // Also check for any similar website_ids
    console.log('\n🔍 Checking for similar website_ids...');
    const similarWebsites = await db
      .select({ 
        website_id: scrapingQueue.website_id,
        count: sql`count(*)`.as('count')
      })
      .from(scrapingQueue)
      .where(sql`website_id LIKE '%villa%' OR website_id LIKE '%bali%'`)
      .groupBy(scrapingQueue.website_id);
    
    if (similarWebsites.length > 0) {
      console.log('Similar website_ids found:');
      similarWebsites.forEach(row => {
        console.log(`   ${row.website_id}: ${row.count} URLs`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

checkScrapingQueueDirect();
