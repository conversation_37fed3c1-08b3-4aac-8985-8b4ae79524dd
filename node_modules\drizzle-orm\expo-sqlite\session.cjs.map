{"version": 3, "sources": ["../../src/expo-sqlite/session.ts"], "sourcesContent": ["import type { SQLiteDatabase, SQLiteRunResult, SQLiteStatement } from 'expo-sqlite';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport type { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport { SQLiteTransaction } from '~/sqlite-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/sqlite-core/query-builders/select.types.ts';\nimport {\n\ttype PreparedQueryConfig as PreparedQueryConfigBase,\n\ttype SQLiteExecuteMethod,\n\tSQLitePreparedQuery,\n\tSQLiteSession,\n\ttype SQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport interface ExpoSQLiteSessionOptions {\n\tlogger?: Logger;\n}\n\ntype PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\n\nexport class ExpoSQLiteSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteSession<'sync', SQLiteRunResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'ExpoSQLiteSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: SQLiteDatabase,\n\t\tdialect: SQLiteSyncDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: ExpoSQLiteSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => unknown,\n\t): ExpoSQLitePreparedQuery<T> {\n\t\tconst stmt = this.client.prepareSync(query.sql);\n\t\treturn new ExpoSQLitePreparedQuery(\n\t\t\tstmt,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\texecuteMethod,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: ExpoSQLiteTransaction<TFullSchema, TSchema>) => T,\n\t\tconfig: SQLiteTransactionConfig = {},\n\t): T {\n\t\tconst tx = new ExpoSQLiteTransaction('sync', this.dialect, this, this.schema);\n\t\tthis.run(sql.raw(`begin${config?.behavior ? ' ' + config.behavior : ''}`));\n\t\ttry {\n\t\t\tconst result = transaction(tx);\n\t\t\tthis.run(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tthis.run(sql`rollback`);\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class ExpoSQLiteTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteTransaction<'sync', SQLiteRunResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'ExpoSQLiteTransaction';\n\n\toverride transaction<T>(transaction: (tx: ExpoSQLiteTransaction<TFullSchema, TSchema>) => T): T {\n\t\tconst savepointName = `sp${this.nestedIndex}`;\n\t\tconst tx = new ExpoSQLiteTransaction('sync', this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tthis.session.run(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = transaction(tx);\n\t\t\tthis.session.run(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tthis.session.run(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class ExpoSQLitePreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends SQLitePreparedQuery<\n\t{ type: 'sync'; run: SQLiteRunResult; all: T['all']; get: T['get']; values: T['values']; execute: T['execute'] }\n> {\n\tstatic override readonly [entityKind]: string = 'ExpoSQLitePreparedQuery';\n\n\tconstructor(\n\t\tprivate stmt: SQLiteStatement,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => unknown,\n\t) {\n\t\tsuper('sync', executeMethod, query);\n\t}\n\n\trun(placeholderValues?: Record<string, unknown>): SQLiteRunResult {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\tconst { changes, lastInsertRowId } = this.stmt.executeSync(params as any[]);\n\t\treturn {\n\t\t\tchanges,\n\t\t\tlastInsertRowId,\n\t\t};\n\t}\n\n\tall(placeholderValues?: Record<string, unknown>): T['all'] {\n\t\tconst { fields, joinsNotNullableMap, query, logger, stmt, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\t\tlogger.logQuery(query.sql, params);\n\t\t\treturn stmt.executeSync(params as any[]).getAllSync();\n\t\t}\n\n\t\tconst rows = this.values(placeholderValues) as unknown[][];\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows) as T['all'];\n\t\t}\n\t\treturn rows.map((row) => mapResultRow(fields!, row, joinsNotNullableMap));\n\t}\n\n\tget(placeholderValues?: Record<string, unknown>): T['get'] {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\tconst { fields, stmt, joinsNotNullableMap, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn stmt.executeSync(params as any[]).getFirstSync();\n\t\t}\n\n\t\tconst rows = this.values(placeholderValues) as unknown[][];\n\t\tconst row = rows[0];\n\n\t\tif (!row) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows) as T['get'];\n\t\t}\n\n\t\treturn mapResultRow(fields!, row, joinsNotNullableMap);\n\t}\n\n\tvalues(placeholderValues?: Record<string, unknown>): T['values'] {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.stmt.executeForRawResultSync(params as any[]).getAllSync();\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAE3B,oBAA2B;AAE3B,iBAAkD;AAElD,yBAAkC;AAElC,qBAMO;AACP,mBAA6B;AAQtB,MAAM,0BAGH,6BAA6D;AAAA,EAKtE,YACS,QACR,SACQ,QACR,UAAoC,CAAC,GACpC;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAZA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EAYR,aACC,OACA,QACA,eACA,uBACA,oBAC6B;AAC7B,UAAM,OAAO,KAAK,OAAO,YAAY,MAAM,GAAG;AAC9C,WAAO,IAAI;AAAA,MACV;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,YACR,aACA,SAAkC,CAAC,GAC/B;AACJ,UAAM,KAAK,IAAI,sBAAsB,QAAQ,KAAK,SAAS,MAAM,KAAK,MAAM;AAC5E,SAAK,IAAI,eAAI,IAAI,QAAQ,QAAQ,WAAW,MAAM,OAAO,WAAW,EAAE,EAAE,CAAC;AACzE,QAAI;AACH,YAAM,SAAS,YAAY,EAAE;AAC7B,WAAK,IAAI,sBAAW;AACpB,aAAO;AAAA,IACR,SAAS,KAAK;AACb,WAAK,IAAI,wBAAa;AACtB,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,8BAGH,qCAAiE;AAAA,EAC1E,QAA0B,wBAAU,IAAY;AAAA,EAEvC,YAAe,aAAwE;AAC/F,UAAM,gBAAgB,KAAK,KAAK,WAAW;AAC3C,UAAM,KAAK,IAAI,sBAAsB,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AAC1G,SAAK,QAAQ,IAAI,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,YAAY,EAAE;AAC7B,WAAK,QAAQ,IAAI,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,WAAK,QAAQ,IAAI,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,gCAAqF,mCAEhG;AAAA,EAGD,YACS,MACR,OACQ,QACA,QACR,eACQ,wBACA,oBACP;AACD,UAAM,QAAQ,eAAe,KAAK;AAR1B;AAEA;AACA;AAEA;AACA;AAAA,EAGT;AAAA,EAZA,QAA0B,wBAAU,IAAY;AAAA,EAchD,IAAI,mBAA8D;AACjE,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,UAAM,EAAE,SAAS,gBAAgB,IAAI,KAAK,KAAK,YAAY,MAAe;AAC1E,WAAO;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,IAAI,mBAAuD;AAC1D,UAAM,EAAE,QAAQ,qBAAqB,OAAO,QAAQ,MAAM,mBAAmB,IAAI;AACjF,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,aAAS,6BAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AACjC,aAAO,KAAK,YAAY,MAAe,EAAE,WAAW;AAAA,IACrD;AAEA,UAAM,OAAO,KAAK,OAAO,iBAAiB;AAC1C,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AACA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAAa,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACzE;AAAA,EAEA,IAAI,mBAAuD;AAC1D,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,UAAM,EAAE,QAAQ,MAAM,qBAAqB,mBAAmB,IAAI;AAClE,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,KAAK,YAAY,MAAe,EAAE,aAAa;AAAA,IACvD;AAEA,UAAM,OAAO,KAAK,OAAO,iBAAiB;AAC1C,UAAM,MAAM,KAAK,CAAC;AAElB,QAAI,CAAC,KAAK;AACT,aAAO;AAAA,IACR;AAEA,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,eAAO,2BAAa,QAAS,KAAK,mBAAmB;AAAA,EACtD;AAAA,EAEA,OAAO,mBAA0D;AAChE,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,KAAK,wBAAwB,MAAe,EAAE,WAAW;AAAA,EACtE;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}