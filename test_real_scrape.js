require('dotenv').config();
const { scrapeAsMarkdown_BrightData } = require('./scrape_worker/scraper_bright_data');

async function testRealScrape() {
  console.log('🧪 TESTING REAL SCRAPE WITH BPVL01072...');
  
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL01072';
  
  try {
    console.log('📥 Scraping real data...');
    const markdown = await scrapeAsMarkdown_BrightData(testUrl);
    
    console.log(`📄 Got markdown: ${markdown.length} characters`);
    
    // Test the CSS patterns
    const bedroomPattern = /bedrooms\.7a6788f7\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
    const bathroomPattern = /bathrooms\.45d31171\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
    
    const bedroomMatch = markdown.match(bedroomPattern);
    const bathroomMatch = markdown.match(bathroomPattern);
    
    console.log('\n🔍 EXTRACTION RESULTS:');
    console.log(`Bedrooms: ${bedroomMatch ? bedroomMatch[1] : 'Not found'}`);
    console.log(`Bathrooms: ${bathroomMatch ? bathroomMatch[1] : 'Not found'}`);
    
    // Also test if BPVL01072 is in the content
    const hasPropertyId = markdown.includes('BPVL01072');
    console.log(`Property ID found: ${hasPropertyId ? '✅' : '❌'}`);
    
    if (bedroomMatch) {
      console.log(`✅ Bedroom extraction successful!`);
    }
    if (bathroomMatch) {
      console.log(`✅ Bathroom extraction successful!`);
    }
    
  } catch (error) {
    console.error('❌ ERROR:', error.message);
  }
}

testRealScrape();
