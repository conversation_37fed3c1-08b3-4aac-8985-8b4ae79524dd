// Test the smart currency service
require('dotenv').config();
const { getCurrencyService } = require('./scrape_worker/currency_service');

async function testCurrencyService() {
  console.log('🧪 Testing Smart Currency Service...\n');
  
  const currencyService = getCurrencyService();
  
  // Test 1: Database rate lookup
  console.log('1. Testing database rate lookup...');
  try {
    const usdRate = await currencyService.getExchangeRate('USD', 'IDR');
    console.log(`   USD→IDR rate: ${usdRate}`);
    
    const eurRate = await currencyService.getExchangeRate('EUR', 'IDR');
    console.log(`   EUR→IDR rate: ${eurRate}`);
  } catch (error) {
    console.error(`   ❌ Error: ${error.message}`);
  }
  
  // Test 2: Price parsing
  console.log('\n2. Testing price parsing...');
  const testPrices = [
    '$1,500',
    'USD 2500',
    '€1,200.50',
    'IDR 25,000,000',
    'Rp 15.000.000',
    '1500 USD',
    'Price on request'
  ];
  
  testPrices.forEach(price => {
    const parsed = currencyService.parsePrice(price);
    console.log(`   "${price}" → ${parsed.amount} ${parsed.currency}`);
  });
  
  // Test 3: Currency conversion
  console.log('\n3. Testing currency conversion...');
  const testConversions = [
    { amount: 1000, from: 'USD' },
    { amount: 1500, from: 'EUR' },
    { amount: 25000000, from: 'IDR' }
  ];
  
  for (const test of testConversions) {
    try {
      const converted = await currencyService.convertCurrency(test.amount, test.from, 'IDR');
      console.log(`   ${test.amount} ${test.from} → ${converted.toFixed(2)} IDR`);
    } catch (error) {
      console.error(`   ❌ Conversion error: ${error.message}`);
    }
  }
  
  // Test 4: Full price conversion
  console.log('\n4. Testing full price conversion...');
  const testFullPrices = [
    '$2,500',
    'USD 1,800',
    '€2,200',
    'IDR 35,000,000',
    'Rp 25.000.000'
  ];
  
  for (const price of testFullPrices) {
    try {
      const idrPrice = await currencyService.convertPriceToIDR(price);
      console.log(`   "${price}" → ${idrPrice ? idrPrice.toFixed(2) : 'null'} IDR`);
    } catch (error) {
      console.error(`   ❌ Price conversion error: ${error.message}`);
    }
  }
  
  // Test 5: Cache performance
  console.log('\n5. Testing cache performance...');
  const start = Date.now();
  
  // First call (should hit database)
  await currencyService.getExchangeRate('USD', 'IDR');
  const firstCall = Date.now() - start;
  
  const start2 = Date.now();
  // Second call (should hit cache)
  await currencyService.getExchangeRate('USD', 'IDR');
  const secondCall = Date.now() - start2;
  
  console.log(`   First call (DB): ${firstCall}ms`);
  console.log(`   Second call (cache): ${secondCall}ms`);
  console.log(`   Cache speedup: ${(firstCall / secondCall).toFixed(1)}x faster`);
  
  // Test 6: Cache stats
  console.log('\n6. Cache statistics...');
  const stats = currencyService.getCacheStats();
  console.log(`   Cache size: ${stats.cacheSize} entries`);
  console.log(`   Last update: ${stats.lastUpdate}`);
  console.log('   Cached rates:');
  stats.cachedRates.forEach(rate => {
    console.log(`     ${rate.pair}: ${rate.rate} (${rate.source})`);
  });
  
  // Test 7: Compare with ENV fallback
  console.log('\n7. Comparing database vs ENV rates...');
  const envUsdRate = Number(process.env.USD_TO_IDR || 15800);
  const dbUsdRate = await currencyService.getExchangeRate('USD', 'IDR');
  
  console.log(`   ENV USD rate: ${envUsdRate}`);
  console.log(`   Database USD rate: ${dbUsdRate}`);
  console.log(`   Difference: ${(dbUsdRate - envUsdRate).toFixed(2)} IDR`);
  console.log(`   Database is ${((dbUsdRate / envUsdRate - 1) * 100).toFixed(2)}% ${dbUsdRate > envUsdRate ? 'higher' : 'lower'}`);
  
  // Example conversion difference
  const testAmount = 1000; // $1000
  const envConversion = testAmount * envUsdRate;
  const dbConversion = testAmount * dbUsdRate;
  
  console.log(`\n   Example: $${testAmount} conversion:`);
  console.log(`     ENV method: ${envConversion.toLocaleString()} IDR`);
  console.log(`     Database method: ${dbConversion.toLocaleString()} IDR`);
  console.log(`     Difference: ${(dbConversion - envConversion).toLocaleString()} IDR`);
  
  console.log('\n🎯 Currency service test completed!');
  process.exit(0);
}

if (require.main === module) {
  testCurrencyService().catch(console.error);
}

module.exports = { testCurrencyService };
