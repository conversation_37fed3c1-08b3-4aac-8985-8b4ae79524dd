{"version": 3, "sources": ["../../src/gel-core/schema.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport { SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport type { gelSequence } from './sequence.ts';\nimport { gelSequenceWithSchema } from './sequence.ts';\nimport { type GelTableFn, gelTableWithSchema } from './table.ts';\n// import type { gelMaterializedView, gelView } from './view.ts';\n// import { gelMaterializedViewWithSchema, gelViewWithSchema } from './view.ts';\n\nexport class GelSchema<TName extends string = string> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'GelSchema';\n\tconstructor(\n\t\tpublic readonly schemaName: TName,\n\t) {}\n\n\ttable: GelTableFn<TName> = ((name, columns, extraConfig) => {\n\t\treturn gelTableWithSchema(name, columns, extraConfig, this.schemaName);\n\t});\n\n\t// view = ((name, columns) => {\n\t// \treturn gelViewWithSchema(name, columns, this.schemaName);\n\t// }) as typeof gelView;\n\n\t// materializedView = ((name, columns) => {\n\t// \treturn gelMaterializedViewWithSchema(name, columns, this.schemaName);\n\t// }) as typeof gelMaterializedView;\n\n\t// enum: typeof gelEnum = ((name, values) => {\n\t// \treturn gelEnumWithSchema(name, values, this.schemaName);\n\t// });\n\n\tsequence: typeof gelSequence = ((name, options) => {\n\t\treturn gelSequenceWithSchema(name, options, this.schemaName);\n\t});\n\n\tgetSQL(): SQL {\n\t\treturn new SQL([sql.identifier(this.schemaName)]);\n\t}\n\n\tshouldOmitSQLParens(): boolean {\n\t\treturn true;\n\t}\n}\n\nexport function isGelSchema(obj: unknown): obj is GelSchema {\n\treturn is(obj, GelSchema);\n}\n\nexport function gelSchema<T extends string>(name: T) {\n\tif (name === 'public') {\n\t\tthrow new Error(\n\t\t\t`You can't specify 'public' as schema name. Postgres is using public schema by default. If you want to use 'public' schema, just use GelTable() instead of creating a schema`,\n\t\t);\n\t}\n\n\treturn new GelSchema(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA+B;AAC/B,iBAA0C;AAE1C,sBAAsC;AACtC,mBAAoD;AAI7C,MAAM,UAA+D;AAAA,EAE3E,YACiB,YACf;AADe;AAAA,EACd;AAAA,EAHH,QAAiB,wBAAU,IAAY;AAAA,EAKvC,QAA4B,CAAC,MAAM,SAAS,gBAAgB;AAC3D,eAAO,iCAAmB,MAAM,SAAS,aAAa,KAAK,UAAU;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,WAAgC,CAAC,MAAM,YAAY;AAClD,eAAO,uCAAsB,MAAM,SAAS,KAAK,UAAU;AAAA,EAC5D;AAAA,EAEA,SAAc;AACb,WAAO,IAAI,eAAI,CAAC,eAAI,WAAW,KAAK,UAAU,CAAC,CAAC;AAAA,EACjD;AAAA,EAEA,sBAA+B;AAC9B,WAAO;AAAA,EACR;AACD;AAEO,SAAS,YAAY,KAAgC;AAC3D,aAAO,kBAAG,KAAK,SAAS;AACzB;AAEO,SAAS,UAA4B,MAAS;AACpD,MAAI,SAAS,UAAU;AACtB,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,SAAO,IAAI,UAAU,IAAI;AAC1B;", "names": []}