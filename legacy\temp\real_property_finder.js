// Real Property Finder - Discovers actual property URLs from listing pages
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

class RealPropertyFinder {
  constructor() {
    this.keyManager = getKeyManager();
  }

  // Scrape a listing page to find individual property URLs
  async findPropertiesOnListingPage(listingUrl) {
    console.log(`🔍 Analyzing listing page: ${listingUrl}`);
    
    try {
      const currentKey = this.keyManager.getCurrentKey();
      
      const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentKey.key}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          url: listingUrl,
          formats: ['markdown', 'html'],
          onlyMainContent: false,
          includeTags: ['a', 'div', 'span'],
          excludeTags: ['script', 'style', 'nav', 'footer', 'header']
        })
      });

      if (!response.ok) {
        if (response.status === 429) {
          this.keyManager.rotateKey();
          throw new Error('Rate limit hit, key rotated');
        }
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const data = await response.json();
      
      if (!data.success || !data.data) {
        throw new Error('No data returned from listing page');
      }

      // Extract property URLs from the HTML
      const html = data.data.html || '';
      const markdown = data.data.markdown || '';
      
      const propertyUrls = this.extractPropertyUrls(html, listingUrl);
      
      console.log(`✅ Found ${propertyUrls.length} property URLs on listing page`);
      
      return {
        listingUrl,
        propertyUrls,
        totalFound: propertyUrls.length,
        html: html.substring(0, 1000) + '...', // Sample for debugging
        markdown: markdown.substring(0, 500) + '...'
      };

    } catch (error) {
      console.log(`❌ Error analyzing listing page: ${error.message}`);
      return {
        listingUrl,
        propertyUrls: [],
        totalFound: 0,
        error: error.message
      };
    }
  }

  // Extract individual property URLs from listing page HTML
  extractPropertyUrls(html, baseUrl) {
    const propertyUrls = new Set();
    
    // Bali Home Immo patterns
    if (baseUrl.includes('bali-home-immo.com')) {
      // Look for property links in the HTML
      const patterns = [
        // Direct property links
        /href="([^"]*\/realestate-property\/[^"]*\/[^"]*\/[^"]*\/[^"]*\/[^"\/]+)"[^>]*>/gi,
        // Property detail links
        /href="([^"]*\/property\/[^"]+)"[^>]*>/gi,
        // Villa specific links
        /href="([^"]*\/villa\/[^"]+)"[^>]*>/gi,
        // Links with property IDs
        /href="([^"]*\/\d+[^"]*)"[^>]*>/gi
      ];

      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let url = match[1];
          
          // Make absolute URL
          if (url.startsWith('/')) {
            url = 'https://bali-home-immo.com' + url;
          }
          
          // Filter out obvious listing pages
          if (!this.isListingPage(url) && this.isValidPropertyUrl(url)) {
            propertyUrls.add(url);
          }
        }
      });
    }

    // BetterPlace patterns
    if (baseUrl.includes('betterplace.cc')) {
      const patterns = [
        // BPVL property codes
        /href="([^"]*\/properties\/BPVL\d+)"[^>]*>/gi,
        // Property detail pages
        /href="([^"]*\/buy\/properties\/[^"\/]+)"[^>]*>/gi
      ];

      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let url = match[1];
          
          if (url.startsWith('/')) {
            url = 'https://betterplace.cc' + url;
          }
          
          if (!this.isListingPage(url) && this.isValidPropertyUrl(url)) {
            propertyUrls.add(url);
          }
        }
      });
    }

    return Array.from(propertyUrls);
  }

  // Check if URL is a listing page (should be skipped)
  isListingPage(url) {
    const listingIndicators = [
      /\/search/i,
      /\/results/i,
      /\/properties$/i,
      /\/listings$/i,
      /\/page\/\d+/i,
      /\?page=/i,
      /\/for-rent\/?$/i,
      /\/for-sale\/?$/i,
      /\/villa\/?$/i,
      /\/apartment\/?$/i,
      /\/monthly\/?$/i,
      /\/yearly\/?$/i,
      /\/freehold\/?$/i,
      /\/leasehold\/?$/i,
      /\/other\/?$/i,
      // Location-only endings (these are listing pages)
      /\/canggu$/i,
      /\/seminyak$/i,
      /\/ubud$/i,
      /\/jimbaran$/i,
      /\/uluwatu$/i,
      /\/kerobokan$/i,
      /\/sanur$/i,
      /\/denpasar$/i,
      /\/nusa_dua$/i,
      /\/berawa$/i,
      /\/petitenget$/i,
      /\/bingin$/i
    ];

    return listingIndicators.some(pattern => pattern.test(url));
  }

  // Check if URL looks like a valid individual property
  isValidPropertyUrl(url) {
    const validIndicators = [
      /\/BPVL\d+$/i, // BetterPlace property codes
      /\/property\/\d+/i, // Property with ID
      /\/villa\/[^\/]+-\d+/i, // Villa with name and ID
      /\/apartment\/[^\/]+-\d+/i, // Apartment with name and ID
      /\/\d{4,}/i, // Contains 4+ digit ID
      /\/[^\/]*-villa-[^\/]*$/i, // Villa in name
      /\/[^\/]*-apartment-[^\/]*$/i // Apartment in name
    ];

    return validIndicators.some(pattern => pattern.test(url));
  }

  // Analyze multiple listing pages to find real properties
  async findRealProperties(listingUrls) {
    console.log(`🚀 Analyzing ${listingUrls.length} listing pages for real properties...`);
    
    const results = [];
    let totalPropertiesFound = 0;

    for (const listingUrl of listingUrls) {
      console.log(`\n📋 Processing: ${listingUrl}`);
      
      const result = await this.findPropertiesOnListingPage(listingUrl);
      results.push(result);
      totalPropertiesFound += result.totalFound;
      
      // Show sample URLs found
      if (result.propertyUrls.length > 0) {
        console.log(`   Sample URLs found:`);
        result.propertyUrls.slice(0, 3).forEach((url, index) => {
          console.log(`     ${index + 1}. ${url}`);
        });
      }
      
      // Wait between requests to avoid rate limits
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    console.log(`\n🎯 SUMMARY:`);
    console.log(`   Listing pages analyzed: ${listingUrls.length}`);
    console.log(`   Total property URLs found: ${totalPropertiesFound}`);
    console.log(`   Average per listing: ${(totalPropertiesFound / listingUrls.length).toFixed(1)}`);

    return {
      results,
      totalPropertiesFound,
      allPropertyUrls: results.flatMap(r => r.propertyUrls)
    };
  }

  // Test with known listing pages
  async testWithKnownListings() {
    const testListings = [
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu_berawa',
      'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/seminyak_center',
      'https://betterplace.cc/buy/properties'
    ];

    return await this.findRealProperties(testListings);
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const finder = new RealPropertyFinder();

  try {
    switch (command) {
      case 'test':
        await finder.testWithKnownListings();
        break;

      case 'analyze':
        const url = args[1];
        if (!url) {
          console.log('Usage: node real_property_finder.js analyze <listing_url>');
          process.exit(1);
        }
        await finder.findPropertiesOnListingPage(url);
        break;

      default:
        console.log('Real Property Finder');
        console.log('');
        console.log('Commands:');
        console.log('  test           - Test with known listing pages');
        console.log('  analyze <url>  - Analyze specific listing page');
        console.log('');
        console.log('Examples:');
        console.log('  node real_property_finder.js test');
        console.log('  node real_property_finder.js analyze "https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu_berawa"');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }

  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = { RealPropertyFinder };
