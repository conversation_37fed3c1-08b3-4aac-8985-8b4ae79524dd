{"version": 3, "sources": ["../../src/gel-core/primary-keys.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { AnyGelColumn, GelColumn } from './columns/index.ts';\nimport { GelTable } from './table.ts';\n\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumn extends AnyGelColumn<{ tableName: TTableName }>,\n\tTColumns extends AnyGelColumn<{ tableName: TTableName }>[],\n>(config: { name?: string; columns: [TColumn, ...TColumns] }): PrimaryKeyBuilder;\n/**\n * @deprecated: Please use primaryKey({ columns: [] }) instead of this function\n * @param columns\n */\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumns extends AnyGelColumn<{ tableName: TTableName }>[],\n>(...columns: TColumns): PrimaryKeyBuilder;\nexport function primaryKey(...config: any) {\n\tif (config[0].columns) {\n\t\treturn new PrimaryKeyBuilder(config[0].columns, config[0].name);\n\t}\n\treturn new PrimaryKeyBuilder(config);\n}\n\nexport class PrimaryKeyBuilder {\n\tstatic readonly [entityKind]: string = 'GelPrimaryKeyBuilder';\n\n\t/** @internal */\n\tcolumns: GelColumn[];\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tcolumns: GelColumn[],\n\t\tname?: string,\n\t) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\t/** @internal */\n\tbuild(table: GelTable): PrimaryKey {\n\t\treturn new PrimaryKey(table, this.columns, this.name);\n\t}\n}\n\nexport class PrimaryKey {\n\tstatic readonly [entityKind]: string = 'GelPrimaryKey';\n\n\treadonly columns: AnyGelColumn<{}>[];\n\treadonly name?: string;\n\n\tconstructor(readonly table: GelTable, columns: AnyGelColumn<{}>[], name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\tgetName(): string {\n\t\treturn this.name ?? `${this.table[GelTable.Symbol.Name]}_${this.columns.map((column) => column.name).join('_')}_pk`;\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAE3B,SAAS,gBAAgB;AAelB,SAAS,cAAc,QAAa;AAC1C,MAAI,OAAO,CAAC,EAAE,SAAS;AACtB,WAAO,IAAI,kBAAkB,OAAO,CAAC,EAAE,SAAS,OAAO,CAAC,EAAE,IAAI;AAAA,EAC/D;AACA,SAAO,IAAI,kBAAkB,MAAM;AACpC;AAEO,MAAM,kBAAkB;AAAA,EAC9B,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAGA;AAAA,EAEA,YACC,SACA,MACC;AACD,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACb;AAAA;AAAA,EAGA,MAAM,OAA6B;AAClC,WAAO,IAAI,WAAW,OAAO,KAAK,SAAS,KAAK,IAAI;AAAA,EACrD;AACD;AAEO,MAAM,WAAW;AAAA,EAMvB,YAAqB,OAAiB,SAA6B,MAAe;AAA7D;AACpB,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACb;AAAA,EARA,QAAiB,UAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EAOT,UAAkB;AACjB,WAAO,KAAK,QAAQ,GAAG,KAAK,MAAM,SAAS,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,EAAE,KAAK,GAAG,CAAC;AAAA,EAC/G;AACD;", "names": []}