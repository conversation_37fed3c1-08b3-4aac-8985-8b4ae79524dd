// Test currency parsing improvements
require('dotenv').config();

const { getCurrencyService } = require('./scrape_worker/currency_service');

async function testCurrencyParsing() {
  console.log('🧪 Testing improved currency parsing...');
  console.log('='.repeat(50));

  const currencyService = getCurrencyService();
  
  const testPrices = [
    '5.7 million',
    '45.375 million',
    '1.963 million',
    'USD 350,000',
    '$450,000',
    'IDR 5,700,000,000',
    'IDR 5.7 billion',  // Test explicit IDR billion
    '6.105',
    '5.197'
  ];

  for (const priceString of testPrices) {
    console.log(`\n💰 Testing: "${priceString}"`);
    
    try {
      const parsed = currencyService.parsePrice(priceString);
      console.log(`   Parsed: ${parsed.amount} ${parsed.currency}`);
      
      const converted = await currencyService.convertPriceToIDR(priceString);
      console.log(`   Converted to IDR: ${converted}`);
      
      // Check if it passes validation
      const isValid = converted && converted >= 50_000_000 && converted <= 1_000_000_000_000;
      console.log(`   Validation: ${isValid ? '✅ PASS' : '❌ FAIL'} (range: 50M-1T IDR)`);
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  console.log('\n🎯 Currency parsing test completed!');
}

// Run if called directly
if (require.main === module) {
  testCurrencyParsing()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testCurrencyParsing };
