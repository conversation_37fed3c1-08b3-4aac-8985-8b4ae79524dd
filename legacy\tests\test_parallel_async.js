// Test parallel async scraping with multiple URLs
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testParallelAsync() {
  console.log('⚡ Testing parallel async scraping...\n');
  
  // Test with multiple URLs from same site to see parallel processing
  const testUrls = [
    'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/',
    'https://balivillarealty.com/property/cozy-villa-3-bedrooms-for-rental-in-umalas-bali/',
    'https://balivillarealty.com/property/charming-2-bedrooms-villa-for-rental-in-babakan-canggu-bali/',
    'https://balivillarealty.com/property/cozy-villa-2-bedrooms-for-rental-in-canggu-bali/',
    'https://balivillarealty.com/property/moa-villa-canggu-by-ilot-property-bali/'
  ];
  
  console.log(`🔄 Testing parallel processing with ${testUrls.length} URLs...`);
  console.log('URLs to process:');
  testUrls.forEach((url, index) => {
    console.log(`   ${index + 1}. ${url.split('/').pop()}`);
  });
  
  try {
    const startTime = Date.now();
    console.log(`\n⏰ Start time: ${new Date(startTime).toLocaleTimeString()}`);
    
    const results = await runExtractBatch('bali_villa_realty', testUrls, {});
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n⏰ End time: ${new Date(endTime).toLocaleTimeString()}`);
    console.log(`⚡ Total duration: ${duration} seconds`);
    console.log(`📊 Average per URL: ${(duration / testUrls.length).toFixed(2)} seconds`);
    
    console.log('\n📊 Results Summary:');
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}/${testUrls.length}`);
    console.log(`   ❌ Failed: ${failed.length}/${testUrls.length}`);
    console.log(`   📈 Success Rate: ${((successful.length / testUrls.length) * 100).toFixed(1)}%`);
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully processed properties:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title.substring(0, 60)}...`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ Failed properties:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. Error: ${result.error}`);
      });
    }
    
    // Performance analysis
    console.log('\n📈 Performance Analysis:');
    if (duration < testUrls.length * 30) { // Less than 30 seconds per URL
      console.log('   ✅ PARALLEL PROCESSING WORKING - Much faster than sequential!');
      console.log(`   🚀 Expected sequential time: ~${testUrls.length * 60}s`);
      console.log(`   ⚡ Actual parallel time: ${duration}s`);
      console.log(`   📊 Speed improvement: ${((testUrls.length * 60) / duration).toFixed(1)}x faster`);
    } else {
      console.log('   ⚠️  Processing seems sequential - may need optimization');
    }
    
  } catch (error) {
    console.error(`❌ Test failed: ${error.message}`);
  }
  
  console.log('\n🎯 Parallel async test completed!');
}

if (require.main === module) {
  testParallelAsync().catch(console.error);
}

module.exports = { testParallelAsync };
