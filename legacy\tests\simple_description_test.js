// Simple test for description extraction
const mockMarkdown = `
# Modern 3 Bedroom Villa with Rooftop in Tumbak Bayuh

![Modern Villa](https://betterplace.cc/_next/image?url=https%3A%2F%2Fbetterplace.sgp1.cdn.digitaloceanspaces.com%2FCACHE%2Fimages%2Fprocessed%2F626340cf-6f6b-4cd...)

**Price:** USD 450,000

**Location:** Tumbak Bayuh, Canggu, Bali

**Property Details:**
- 3 bedrooms
- 2 bathrooms  
- Building size: 180 sqm
- Land size: 250 sqm
- Year built: 2022
- Parking: 2 cars
- Ownership: 25-year leasehold

**Description:**
This stunning modern villa features contemporary design with high-quality finishes throughout. Located in the peaceful area of Tumbak Bayuh, just minutes from Canggu's famous beaches and vibrant dining scene. Perfect for families or investors looking for a premium property.

**Amenities:**
- Swimming pool
- Garden
- Air conditioning
- Fully furnished

Contact us via WhatsApp: https://wa.me/628123456789
Online booking available at: https://betterplace.cc/booking
`;

// Test the extractCleanDescription function
function extractCleanDescription(markdown) {
  try {
    // Split into lines and clean each line
    const lines = markdown.split('\n').map(line => line.trim());
    
    // Filter out problematic lines
    const cleanLines = lines.filter(line => {
      // Skip empty lines
      if (line.length < 20) return false;
      
      // Skip lines with technical content
      if (line.includes('WhatsApp') || 
          line.includes('wa.me') || 
          line.includes('wp-content') ||
          line.includes('_next/image') ||
          line.includes('digitaloceanspaces') ||
          line.includes('[![') ||
          line.includes('](http') ||
          line.includes('Online') ||
          line.includes('Contact') ||
          line.includes('Phone:') ||
          line.includes('Email:') ||
          line.includes('USD') ||
          line.includes('IDR') ||
          line.includes('AUD') ||
          line.includes('EUR') ||
          line.includes('Free Consultation') ||
          line.includes('Schedule') ||
          line.startsWith('#') ||
          line.startsWith('*') ||
          line.startsWith('-') ||
          line.startsWith('|') ||
          line.includes('bedroom') ||
          line.includes('bathroom') ||
          line.includes('sqm') ||
          line.includes('parking') ||
          line.includes('year built') ||
          line.includes('ownership')) {
        return false;
      }
      
      // Skip lines that are mostly URLs or technical markup
      if (line.includes('http') && line.length < 100) return false;
      
      return true;
    });
    
    // Find the first substantial paragraph
    for (const line of cleanLines) {
      if (line.length > 50 && line.length < 500) {
        // Clean up any remaining markdown
        let cleaned = line
          .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
          .replace(/\[.*?\]\(.*?\)/g, '') // Remove links
          .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
          .replace(/\*(.*?)\*/g, '$1') // Remove italic
          .replace(/`(.*?)`/g, '$1') // Remove code
          .replace(/#{1,6}\s*/g, '') // Remove headers
          .trim();
        
        if (cleaned.length > 30) {
          return cleaned;
        }
      }
    }
    
    return null;
  } catch (error) {
    console.log(`   ⚠️  Description extraction failed: ${error.message}`);
    return null;
  }
}

console.log('🧪 Testing Description Extraction');
console.log('='.repeat(40));

const result = extractCleanDescription(mockMarkdown);
console.log('📝 Extracted Description:');
console.log(`"${result}"`);

console.log('\n🔍 Analysis:');
console.log(`Length: ${result?.length || 0} characters`);
console.log(`Contains WhatsApp: ${result?.includes('WhatsApp') ? 'YES ❌' : 'NO ✅'}`);
console.log(`Contains URLs: ${result?.includes('http') ? 'YES ❌' : 'NO ✅'}`);
console.log(`Contains technical markup: ${result?.includes('![') || result?.includes('[![') ? 'YES ❌' : 'NO ✅'}`);

console.log('\n✅ Description extraction test complete!');
