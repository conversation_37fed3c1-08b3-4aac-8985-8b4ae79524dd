// Direct test of mapper functions to isolate the issue
require('dotenv').config();
const { mapBaliVillaRealty, mapBetterPlace, mapBaliHomeImmo } = require('./scrape_worker/mappers');

async function directMapperTest() {
  console.log('🧪 Direct Mapper Test');
  console.log('='.repeat(40));
  
  // Test with sample markdown that should contain bedroom/bathroom info
  const testCases = [
    {
      name: 'Bali Villa Realty',
      mapper: mapBaliVillaRealty,
      sampleData: {
        url: 'https://balivillarealty.com/property/cozy-1-bedroom-villa-in-prime-petitenget-location-exceptional-investment-opportunity/',
        markdown: `
# Cozy 1-Bedroom Villa in Prime Petitenget Location – Exceptional Investment Opportunity

**Property Details:**
- 1 bedroom
- 2 bathrooms  
- 100 sqm building size
- $323,000 USD
- Leasehold ownership
- Built in 2024

This stunning 1-bedroom villa offers exceptional investment potential in the heart of Seminyak's prestigious Petitenget area.
        `,
        json: null,
        html: null
      },
      expectedBedrooms: 1,
      expectedBathrooms: 2
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      mapper: mapBetter<PERSON>lace,
      sampleData: {
        url: 'https://betterplace.cc/buy/properties/BPVL02209',
        markdown: `
# Exclusive 2-Bedroom Villa Loft in Umalas

**Specifications:**
- 2 bedrooms
- 3 bathrooms
- 150 sqm
- USD 305,000
- Freehold
- Year built: 2025

Modern villa with smart home integration.
        `,
        json: null,
        html: null
      },
      expectedBedrooms: 2,
      expectedBathrooms: 3
    },
    {
      name: 'Bali Home Immo',
      mapper: mapBaliHomeImmo,
      sampleData: {
        url: 'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/1-bedroom-apartment-for-yearly-rental-in-berawa-bhi1022',
        markdown: `
# 1 bedroom apartment for yearly rental in Berawa - BHI1022

**Property Information:**
- 1 bedroom
- 1 bathroom
- 50 sqm
- IDR 15,000,000/month
- Leasehold

Perfect location in Berawa, close to the beach.
        `,
        json: null,
        html: null
      },
      expectedBedrooms: 1,
      expectedBathrooms: 1
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n🏠 Testing ${testCase.name}`);
    console.log('─'.repeat(40));
    
    try {
      console.log('🔄 Running mapper...');
      const result = await testCase.mapper(testCase.sampleData);
      
      console.log('\n📊 MAPPER OUTPUT:');
      console.log(`   Title: "${result.title}"`);
      console.log(`   Bedrooms: ${result.bedrooms} (expected: ${testCase.expectedBedrooms})`);
      console.log(`   Bathrooms: ${result.bathrooms} (expected: ${testCase.expectedBathrooms})`);
      console.log(`   Size: ${result.size_sqft} sqft`);
      console.log(`   Price: ${result.price || result.rent_price}`);
      console.log(`   Year: ${result.year_built}`);
      console.log(`   Ownership: ${result.ownership_type}`);
      console.log(`   Address: ${result.address}`);
      console.log(`   City: ${result.city}`);
      
      // Validation
      const bedroomMatch = result.bedrooms === testCase.expectedBedrooms;
      const bathroomMatch = result.bathrooms === testCase.expectedBathrooms;
      
      console.log('\n🎯 VALIDATION:');
      console.log(`   Bedrooms: ${bedroomMatch ? '✅' : '❌'} ${result.bedrooms} vs ${testCase.expectedBedrooms}`);
      console.log(`   Bathrooms: ${bathroomMatch ? '✅' : '❌'} ${result.bathrooms} vs ${testCase.expectedBathrooms}`);
      
      if (bedroomMatch && bathroomMatch) {
        console.log('   🎉 MAPPER WORKING CORRECTLY!');
      } else {
        console.log('   🚨 MAPPER EXTRACTION FAILED!');
        
        // Debug: show what patterns might be missing
        console.log('\n🔍 DEBUG INFO:');
        console.log('   Sample markdown contains:');
        if (testCase.sampleData.markdown.includes('bedroom')) {
          console.log('   ✅ Contains "bedroom"');
        }
        if (testCase.sampleData.markdown.includes('bathroom')) {
          console.log('   ✅ Contains "bathroom"');
        }
        
        // Show the actual regex patterns being used
        console.log('   📝 Need to check regex patterns in mapper');
      }
      
      // Show description quality
      if (result.description) {
        console.log(`\n📝 Description: "${result.description.substring(0, 100)}..."`);
      }
      
    } catch (error) {
      console.log(`❌ Mapper failed: ${error.message}`);
      console.log(error.stack);
    }
  }
  
  console.log('\n🔧 DIAGNOSIS:');
  console.log('If mappers fail with clear bedroom/bathroom data,');
  console.log('the regex patterns in scrape_worker/mappers.js need fixing.');
  console.log('\nIf mappers work but batch runner fails,');
  console.log('the issue is in the data flow between mapper and batch runner.');
}

// Run test
if (require.main === module) {
  directMapperTest().catch(console.error);
}

module.exports = { directMapperTest };
