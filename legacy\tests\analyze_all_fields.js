// Analyze all fields in recent properties to identify issues
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc } = require('drizzle-orm');

async function analyzeAllFields() {
  console.log('🔍 Analyzing All Fields in Recent Properties');
  console.log('='.repeat(50));
  
  try {
    const recentProperties = await db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`📋 Found ${recentProperties.length} recent properties:`);
    
    recentProperties.forEach((prop, index) => {
      if (prop.media?.source_id && ['betterplace', 'bali_villa_realty', 'bali_home_immo'].includes(prop.media.source_id)) {
        console.log(`\n${index + 1}. ${prop.title || 'UNTITLED'}`);
        console.log(`   Source: ${prop.media?.source_id}`);
        console.log(`   External ID: ${prop.media?.external_id || 'NULL'}`);
        console.log(`   Source URL: ${prop.media?.source_url || 'NULL'}`);
        
        // Basic fields
        console.log(`   Price: ${prop.price || 'NULL'}`);
        console.log(`   Rent Price: ${prop.rent_price || 'NULL'}`);
        console.log(`   Bedrooms: ${prop.bedrooms || 'NULL'}`);
        console.log(`   Bathrooms: ${prop.bathrooms || 'NULL'}`);
        console.log(`   Parking: ${prop.parking_spaces || 'NULL'}`);
        
        // Size fields
        console.log(`   Size (sqft): ${prop.size_sqft || 'NULL'}`);
        console.log(`   Lot Size (sqft): ${prop.lot_size_sqft || 'NULL'}`);
        
        // Date/ownership fields
        console.log(`   Year Built: ${prop.year_built || 'NULL'}`);
        console.log(`   Ownership Type: ${prop.ownership_type || 'NULL'}`);
        console.log(`   Lease Duration Years: ${prop.lease_duration_years || 'NULL'}`);
        console.log(`   Lease Duration Text: ${prop.lease_duration_text || 'NULL'}`);
        
        // Location fields
        console.log(`   Address: ${prop.address || 'NULL'}`);
        console.log(`   City: ${prop.city || 'NULL'}`);
        console.log(`   State: ${prop.state || 'NULL'}`);
        console.log(`   Country: ${prop.country || 'NULL'}`);
        
        // Description field - check for problematic content
        if (prop.description) {
          const desc = prop.description.substring(0, 200);
          console.log(`   Description: "${desc}${prop.description.length > 200 ? '...' : ''}"`);
          
          // Check for problematic patterns
          const problematicPatterns = [
            'WhatsApp',
            'https://',
            'http://',
            'wa.me',
            'wp-content',
            '|',
            'Online',
            'Contact',
            'Phone',
            'Email'
          ];
          
          const foundProblems = problematicPatterns.filter(pattern => 
            prop.description.toLowerCase().includes(pattern.toLowerCase())
          );
          
          if (foundProblems.length > 0) {
            console.log(`   ⚠️  Description Issues: ${foundProblems.join(', ')}`);
          }
        } else {
          console.log(`   Description: NULL`);
        }
        
        // Amenities
        if (prop.amenities) {
          const amenitiesStr = JSON.stringify(prop.amenities).substring(0, 100);
          console.log(`   Amenities: ${amenitiesStr}${JSON.stringify(prop.amenities).length > 100 ? '...' : ''}`);
        } else {
          console.log(`   Amenities: NULL`);
        }
        
        // Media info
        console.log(`   Images: ${prop.media?.image_count || 0} images`);
        console.log(`   Created: ${new Date(prop.created_at).toLocaleString()}`);
      }
    });
    
    console.log('\n🔍 Common Issues Found:');
    console.log('1. Description contains contact info (WhatsApp, URLs)');
    console.log('2. Description contains technical markup (|, wp-content)');
    console.log('3. Some lease duration fields are NULL');
    console.log('4. Some ownership types are NULL');
    
    console.log('\n🔧 Fixes Needed:');
    console.log('1. Improve description extraction to filter out contact info');
    console.log('2. Better markdown parsing to avoid technical content');
    console.log('3. Enhanced ownership type detection');
    console.log('4. Better lease duration extraction');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    console.error(error.stack);
  }
}

analyzeAllFields().catch(console.error);
