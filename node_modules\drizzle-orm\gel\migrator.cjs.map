{"version": 3, "sources": ["../../src/gel/migrator.ts"], "sourcesContent": ["// import type { MigrationConfig } from '~/migrator.ts';\n// import { readMigrationFiles } from '~/migrator.ts';\n// import type { GelJsDatabase } from './driver.ts';\n\n// not supported\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nasync function migrate<TSchema extends Record<string, unknown>>(\n\t// db: GelJsDatabase<TSchema>,\n\t// config: MigrationConfig,\n) {\n\treturn {};\n\t// const migrations = readMigrationFiles(config);\n\t// await db.dialect.migrate(migrations, db.session, config);\n}\n"], "mappings": ";AAMA,eAAe,UAGb;AACD,SAAO,CAAC;AAGT;", "names": []}