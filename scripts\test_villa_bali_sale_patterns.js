// Test Villa Bali Sale URL patterns
require('dotenv').config();

function testVillaBaliSalePatterns() {
  console.log('🏠 Testing Villa Bali Sale URL Patterns\n');
  
  // Real URLs from the scraped content
  const testUrls = [
    // Rental properties
    'https://www.villabalisale.com/realestate-property/for-rent/villa/annually/canggu/elegance-three-bedroom-villa-with-enclosed-living-room-in-berawa-yrr3439',
    'https://www.villabalisale.com/realestate-property/for-rent/villa/annually/umalas/exquisite-three-bedroom-villa-with-enclosed-living-room-in-umalas-yrr3464',
    'https://www.villabalisale.com/realestate-property/for-rent/villa/annually/canggu/simplicity-unfrunished-two-bedrooms-enclosed-villa-in-canggu-yrc5218',
    
    // Sale properties
    'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold/umalas/great-quality-and-design-four-bedroom-leasehold-property-in-umalas-vl3309',
    'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold/ungasan/stylish-one-bedroom-ocean-views-property-for-lease-in-ungasan',
    'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold/umalas/new-upcoming-modern-three-bedroom-villa-in-umalas-bumbak',
    
    // Listing pages (should be excluded)
    'https://www.villabalisale.com/realestate-property/for-rent/villa/annually',
    'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold',
    'https://www.villabalisale.com/realestate-property/for-rent/villa/all/canggu',
    'https://www.villabalisale.com/realestate-property'
  ];
  
  // Updated patterns
  const propertyPatterns = [
    /\/realestate-property\/for-sale\/villa\/(freehold|leasehold)\/[^\/]+\/[^\/]+-[a-z0-9]+\/?$/,  // Sale properties: /for-sale/villa/leasehold/location/property-name-code
    /\/realestate-property\/for-rent\/villa\/(annually|monthly|daily|weekly)\/[^\/]+\/[^\/]+-[a-z0-9]+\/?$/,  // Rental properties: /for-rent/villa/annually/location/property-name-code
    /\/realestate-property\/for-sale\/villa\/(freehold|leasehold)\/[^\/]+\/[^\/]+\/?$/,  // Sale properties (fallback)
    /\/realestate-property\/for-rent\/villa\/(annually|monthly|daily|weekly)\/[^\/]+\/[^\/]+\/?$/,  // Rental properties (fallback)
    /\/unique-villas\/[^\/]+\/?$/  // Unique villas
  ];
  
  const excludePatterns = [
    /\/blog\//,
    /\/news\//,
    /\/search/,
    /\/filter/,
    /\?page=/,
    /\/page\//,
    /\/fr\//,  // Exclude all French URLs
    /\/id\//,  // Exclude all Indonesian URLs
    /\/realestate-property\/for-rent\/villa\/annually\/?$/,  // Listing pages
    /\/realestate-property\/for-rent\/villa\/daily\/?$/,
    /\/realestate-property\/for-rent\/villa\/weekly\/?$/,
    /\/realestate-property\/for-rent\/villa\/monthly\/?$/,
    /\/realestate-property\/for-sale\/villa\/freehold\/?$/,
    /\/realestate-property\/for-sale\/villa\/leasehold\/?$/,
    /\/realestate-property\/for-sale\/villa\/all\//,  // Location listing pages
    /\/realestate-property\/for-rent\/villa\/all\//,
    /\/realestate-property\/?$/  // Main listing page
  ];
  
  console.log('📊 Testing URL patterns:\n');
  
  let propertyCount = 0;
  let excludedCount = 0;
  
  testUrls.forEach((url, i) => {
    console.log(`${i + 1}. ${url}`);
    
    // Check if excluded
    const isExcluded = excludePatterns.some(pattern => pattern.test(url));
    if (isExcluded) {
      console.log('   ❌ EXCLUDED (listing page)');
      excludedCount++;
      return;
    }
    
    // Check if matches property patterns
    const isProperty = propertyPatterns.some(pattern => pattern.test(url));
    if (isProperty) {
      console.log('   ✅ PROPERTY PAGE');
      propertyCount++;
    } else {
      console.log('   ⚠️  NO MATCH');
    }
    
    console.log('');
  });
  
  console.log('📈 Results:');
  console.log(`   ✅ Property pages: ${propertyCount}`);
  console.log(`   ❌ Excluded pages: ${excludedCount}`);
  console.log(`   ⚠️  No match: ${testUrls.length - propertyCount - excludedCount}`);
  
  const expectedPropertyCount = 6; // 3 rental + 3 sale
  const expectedExcludedCount = 4; // 4 listing pages
  
  if (propertyCount === expectedPropertyCount && excludedCount === expectedExcludedCount) {
    console.log('\n🎉 All patterns working correctly!');
    console.log('✅ Property URLs are correctly identified');
    console.log('✅ Listing URLs are correctly excluded');
  } else {
    console.log('\n⚠️  Pattern matching needs adjustment');
    console.log(`Expected: ${expectedPropertyCount} properties, ${expectedExcludedCount} excluded`);
    console.log(`Got: ${propertyCount} properties, ${excludedCount} excluded`);
  }
}

testVillaBaliSalePatterns();
