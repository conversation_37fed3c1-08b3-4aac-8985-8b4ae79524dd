// Scrape 1 URL from each website using the scraping_queue table
require('dotenv').config();
const { db, scrapingQueue, properties } = require('./drizzle_client');
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { eq, and, desc, sql } = require('drizzle-orm');

async function scrapeFromQueue() {
  console.log('🎯 Scraping from Queue - 1 URL per Website');
  console.log('='.repeat(50));
  
  try {
    // Step 1: Get 1 URL from each website from the queue
    console.log('📋 Step 1: Fetching URLs from scraping_queue...\n');
    
    // Use dynamic website registry instead of hardcoded list
    const { websiteRegistry } = require('./scrape_worker/website_registry');
    const websites = websiteRegistry.getActiveWebsites();
    const selectedUrls = {};
    
    for (const website of websites) {
      console.log(`🔍 Looking for ${website} URLs...`);
      
      // Get unprocessed URLs for this website
      const urls = await db
        .select({
          id: scrapingQueue.id,
          url: scrapingQueue.url,
          website_id: scrapingQueue.website_id,
          status: scrapingQueue.status,
          priority: scrapingQueue.priority,
          created_at: scrapingQueue.created_at
        })
        .from(scrapingQueue)
        .where(
          and(
            eq(scrapingQueue.website_id, website),
            eq(scrapingQueue.status, 'pending')
          )
        )
        .orderBy(desc(scrapingQueue.priority), scrapingQueue.created_at)
        .limit(5); // Get top 5 to have options
      
      if (urls.length > 0) {
        // Select the first URL
        const selectedUrl = urls[0];
        selectedUrls[website] = selectedUrl;
        console.log(`   ✅ Selected: ${selectedUrl.url}`);
        console.log(`   📊 Priority: ${selectedUrl.priority}, Status: ${selectedUrl.status}`);
        console.log(`   📅 Created: ${new Date(selectedUrl.created_at).toLocaleString()}`);
        
        // Mark as processing
        await db
          .update(scrapingQueue)
          .set({ 
            status: 'processing',
            updated_at: new Date()
          })
          .where(eq(scrapingQueue.id, selectedUrl.id));
        
        console.log(`   🔄 Marked as processing`);
      } else {
        console.log(`   ❌ No pending URLs found for ${website}`);
      }
      
      console.log('');
    }
    
    // Step 2: Scrape the selected URLs
    console.log('🔄 Step 2: Scraping selected URLs...\n');
    
    const scrapingResults = {};
    let totalSuccessful = 0;
    
    for (const [website, urlData] of Object.entries(selectedUrls)) {
      console.log(`📡 Scraping ${website}...`);
      console.log(`   URL: ${urlData.url}`);
      
      try {
        const startTime = Date.now();
        const results = await runExtractBatch(website, [urlData.url], {});
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(1);

        // Handle new return structure
        let successful = [];
        if (results && results.processedResults) {
          successful = results.processedResults.filter(r => r && r.ok && r.data && r.data.title) || [];
        } else if (Array.isArray(results)) {
          // Fallback for old format
          successful = results.filter(r => r && r.title) || [];
        }
        
        if (successful.length > 0) {
          const result = successful[0];
          // Get property data from the correct location
          const prop = result.data || result;

          console.log(`   ✅ Success (${duration}s): ${prop.title}`);
          console.log(`   🛏️  Bedrooms: ${prop.bedrooms || 'NULL'}`);
          console.log(`   🚿 Bathrooms: ${prop.bathrooms || 'NULL'}`);
          console.log(`   📐 Size: ${prop.size_sqft || 'NULL'} sqft`);
          console.log(`   📅 Year: ${prop.year_built || 'NULL'}`);
          console.log(`   🏠 Ownership: ${prop.ownership_type || 'NULL'}`);
          console.log(`   💰 Price: ${prop.price || prop.rent_price || 'NULL'}`);

          // Check description quality
          if (prop.description) {
            const hasProblems = prop.description.includes('WhatsApp') ||
                               prop.description.includes('https://') ||
                               prop.description.includes('wp-content');
            console.log(`   📝 Description: ${hasProblems ? '❌ Has issues' : '✅ Clean'}`);
          }

          // Debug: Check source_url
          console.log(`   🔗 Source URL: ${prop.source_url || 'NULL ❌'}`);

          // Mark as completed in queue
          await db
            .update(scrapingQueue)
            .set({ 
              status: 'completed',
              updated_at: new Date()
            })
            .where(eq(scrapingQueue.id, urlData.id));
          
          scrapingResults[website] = { success: true, data: prop, duration };
          totalSuccessful++;
        } else {
          console.log(`   ❌ Failed: No data extracted`);
          
          // Mark as failed in queue
          await db
            .update(scrapingQueue)
            .set({ 
              status: 'failed',
              updated_at: new Date()
            })
            .where(eq(scrapingQueue.id, urlData.id));
          
          scrapingResults[website] = { success: false, data: null, duration };
        }
        
      } catch (error) {
        console.log(`   ❌ Failed: ${error.message}`);
        
        // Mark as failed in queue
        await db
          .update(scrapingQueue)
          .set({ 
            status: 'failed',
            updated_at: new Date()
          })
          .where(eq(scrapingQueue.id, urlData.id));
        
        scrapingResults[website] = { success: false, error: error.message };
      }
      
      console.log('');
    }
    
    // Step 3: Check database for the newly scraped properties
    console.log('🔄 Step 3: Checking database for new properties...\n');
    
    // Wait a moment for database to update
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const recentProperties = await db
      .select({
        id: properties.id,
        title: properties.title,
        price: properties.price,
        rent_price: properties.rent_price,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        size_sqft: properties.size_sqft,
        year_built: properties.year_built,
        ownership_type: properties.ownership_type,
        description: properties.description,
        media: properties.media,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(5);
    
    console.log(`📋 Found ${recentProperties.length} recent properties:`);
    
    recentProperties.forEach((prop, index) => {
      const sourceId = prop.media?.source_id;
      if (sourceId && websites.includes(sourceId)) {
        console.log(`\n${index + 1}. ${prop.title || 'UNTITLED'}`);
        console.log(`   🌐 Source: ${sourceId}`);
        console.log(`   💰 Price: ${prop.price || prop.rent_price || 'NULL'}`);
        console.log(`   🛏️  Bedrooms: ${prop.bedrooms || 'NULL'}`);
        console.log(`   🚿 Bathrooms: ${prop.bathrooms || 'NULL'}`);
        console.log(`   📐 Size: ${prop.size_sqft || 'NULL'} sqft`);
        console.log(`   📅 Year: ${prop.year_built || 'NULL'}`);
        console.log(`   🏠 Ownership: ${prop.ownership_type || 'NULL'}`);
        console.log(`   🗓️  Created: ${new Date(prop.created_at).toLocaleString()}`);
        
        // Check description quality
        if (prop.description) {
          const desc = prop.description.substring(0, 100);
          console.log(`   📝 Description: "${desc}..."`);
          
          const hasProblems = prop.description.includes('WhatsApp') || 
                             prop.description.includes('https://') ||
                             prop.description.includes('wp-content');
          console.log(`   🔍 Quality: ${hasProblems ? '❌ Has technical content' : '✅ Clean'}`);
        }
      }
    });
    
    // Step 4: Summary
    console.log('\n📊 SCRAPING SUMMARY');
    console.log('='.repeat(50));
    
    const totalAttempted = Object.keys(selectedUrls).length;
    console.log(`🎯 URLs attempted: ${totalAttempted}`);
    console.log(`✅ Successful scrapes: ${totalSuccessful}`);
    console.log(`❌ Failed scrapes: ${totalAttempted - totalSuccessful}`);
    console.log(`📈 Success rate: ${((totalSuccessful / totalAttempted) * 100).toFixed(1)}%`);
    
    console.log('\n🌐 Website Results:');
    Object.entries(scrapingResults).forEach(([website, result]) => {
      const status = result.success ? '✅' : '❌';
      const duration = result.duration ? ` (${result.duration}s)` : '';
      console.log(`   ${status} ${website}${duration}`);
      if (result.data) {
        console.log(`      Title: ${result.data.title}`);
        console.log(`      Fields: ${result.data.bedrooms || 'N/A'} bed, ${result.data.bathrooms || 'N/A'} bath`);
      }
      if (result.error) {
        console.log(`      Error: ${result.error}`);
      }
    });
    
    // Step 5: Queue status
    console.log('\n📋 Queue Status Update:');
    for (const website of websites) {
      const queueStats = await db
        .select({
          status: scrapingQueue.status,
          count: sql`count(*)`.as('count')
        })
        .from(scrapingQueue)
        .where(eq(scrapingQueue.website_id, website))
        .groupBy(scrapingQueue.status);
      
      console.log(`   ${website}:`);
      queueStats.forEach(stat => {
        console.log(`      ${stat.status}: ${stat.count}`);
      });
    }
    
    console.log('\n🎉 Queue scraping completed!');
    console.log('💰 Cost-effective markdown-only scraping maintained');
    console.log('📊 High data quality with optimized field extraction');
    
    return {
      success: totalSuccessful > 0,
      attempted: totalAttempted,
      successful: totalSuccessful,
      results: scrapingResults
    };
    
  } catch (error) {
    console.error('❌ Queue scraping failed:', error.message);
    console.error(error.stack);
    return { success: false, error: error.message };
  }
}

// Run the scraping
if (require.main === module) {
  scrapeFromQueue()
    .then(result => {
      if (result.success) {
        console.log('\n🎉 Queue scraping completed successfully!');
        process.exit(0);
      } else {
        console.log('\n❌ Queue scraping failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Scraping error:', error.message);
      process.exit(1);
    });
}

module.exports = { scrapeFromQueue };
