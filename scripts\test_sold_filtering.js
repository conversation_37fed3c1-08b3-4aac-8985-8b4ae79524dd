// Test SOLD property filtering
require('dotenv').config();

async function testSoldFiltering() {
  console.log('🚫 Testing SOLD Property Filtering\n');
  
  // Import the mapper
  const { mapBaliVillaRealty } = require('../scrape_worker/mappers');
  
  // Test SOLD property (should be filtered out)
  const soldProperty = {
    markdown: `
# Kerobokan 4 Bedroom Private Pool Villa (SOLD)

**Price:** $242,038
**Location:** Kerobokan, Bali
**Bedrooms:** 4
**Bathrooms:** 3
**Year Built:** 2025

This beautiful villa has been sold and is no longer available.
    `,
    url: 'https://balivillarealty.com/property/kerobokan-4-bedroom-private-pool-villa/'
  };

  // Test AVAILABLE property (should pass through)
  const availableProperty = {
    markdown: `
# 3 Bedroom Villa for Sale Leasehold in Bali Tabanan

**Price:** $400,000
**Location:** Tabanan, Bali
**Bedrooms:** 3
**Bathrooms:** 3
**Year Built:** 2024

Beautiful villa available for sale in Tabanan.
    `,
    url: 'https://balivillarealty.com/property/3-bedroom-villa-for-sale-leasehold-in-bali-tabanan/'
  };

  try {
    console.log('1. Testing SOLD property (should be filtered out):');
    console.log('   Title: "Kerobokan 4 Bedroom Private Pool Villa (SOLD)"');
    
    const soldResult = await mapBaliVillaRealty(soldProperty);
    
    if (soldResult === null) {
      console.log('   ✅ SOLD property correctly filtered out (returned null)');
    } else {
      console.log('   ❌ SOLD property was NOT filtered out');
      console.log(`   📊 Status: ${soldResult.status}`);
    }

    console.log('\n2. Testing AVAILABLE property (should pass through):');
    console.log('   Title: "3 Bedroom Villa for Sale Leasehold in Bali Tabanan"');
    
    const availableResult = await mapBaliVillaRealty(availableProperty);
    
    if (availableResult) {
      console.log('   ✅ AVAILABLE property correctly passed through');
      console.log(`   📊 Status: ${availableResult.status}`);
      console.log(`   💰 Price: ${availableResult.price ? `IDR ${availableResult.price.toLocaleString()}` : 'Not found'}`);
    } else {
      console.log('   ❌ AVAILABLE property was incorrectly filtered out');
    }

    console.log('\n📊 Summary:');
    if (soldResult === null && availableResult !== null) {
      console.log('   ✅ SOLD filtering is working correctly!');
      console.log('   ✅ SOLD properties will be skipped during scraping');
      console.log('   ✅ Database will stay clean of unavailable properties');
    } else {
      console.log('   ❌ SOLD filtering needs adjustment');
    }
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    console.error(error.stack);
  }
}

testSoldFiltering();
