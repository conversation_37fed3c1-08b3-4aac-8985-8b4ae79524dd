// Test Bali Coconut Living with detailed debugging
require('dotenv').config();
const { runBatch } = require('../scrape_worker/run_batch');

async function testBaliCoconutDebug() {
  console.log('🥥 Testing Bali Coconut Living with Debug\n');
  
  const testUrls = [
    'https://balicoconutliving.com/bali-villa-yearly-rental/Petitenget/4569-V010-4221/Villa-Jangmi'
  ];
  
  try {
    console.log(`🔍 Testing ${testUrls.length} URL(s):`);
    testUrls.forEach((url, i) => {
      console.log(`   ${i + 1}. ${url}`);
    });
    console.log('');
    
    // Run batch processing
    const results = await runBatch(testUrls, 'bali_coconut_living', {
      batchSize: 1,
      delayBetweenBatches: 0,
      delayBetweenRequests: 0
    });
    
    console.log('\n📊 Results:');
    console.log(`   Total processed: ${results.length}`);
    
    results.forEach((result, i) => {
      console.log(`\n   ${i + 1}. ${testUrls[i]}`);
      if (result.success) {
        console.log('      ✅ Success');
        console.log(`      Title: ${result.property.title}`);
        console.log(`      Sale Price: ${result.property.price ? `IDR ${result.property.price.toLocaleString()}` : 'Not found'}`);
        console.log(`      Rent Price: ${result.property.rent_price ? `IDR ${result.property.rent_price.toLocaleString()}` : 'Not found'}`);
        console.log(`      Ownership: ${result.property.ownership_type}`);
      } else {
        console.log('      ❌ Failed');
        console.log(`      Error: ${result.error}`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    console.error(error.stack);
  }
}

testBaliCoconutDebug();
