// Update Website Configs - Update website_configs table with latest configurations
require('dotenv').config();
const { db, websiteConfigs, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function updateWebsiteConfigs() {
  console.log('⚙️  Updating Website Configurations\n');
  
  try {
    // Clear existing configs
    await db.delete(websiteConfigs);
    console.log('🗑️  Removed old website configurations');
    
    // Insert updated website configurations
    const websiteConfigsData = [
      {
        website_id: 'betterplace',
        name: 'BetterPlace',
        base_url: 'https://betterplace.cc',
        sitemap_enabled: true,
        sitemap_urls: '["https://betterplace.cc/sitemap_index.xml"]',
        sitemap_filters: '{"include": ["real-estate", "buy", "rent"], "exclude": ["blog", "pages", "search"]}',
        property_url_patterns: {
          property_patterns: [
            '/buy/properties/[^/]+/?$',   // All buy properties with ID
            '/rent/properties/[^/]+/?$'   // All rent properties with ID
          ],
          exclude_patterns: [
            '/blog/',
            '/news/',
            '/search',
            '/filter',
            '\\?page=',
            '/page/',
            '/buy/?$',
            '/rent/?$',
            '/buy/properties/?$',
            '/rent/properties/?$'
          ],
          listing_patterns: [
            '/buy/?$',
            '/rent/?$',
            '/buy/properties/?$',
            '/rent/properties/?$'
          ],
          keywords: ['bedroom', 'bathroom', 'villa', 'apartment', 'price', 'sqm']
        },
        crawl_frequency_hours: 24,
        sitemap_check_frequency_hours: 6,
        is_active: true
      },
      {
        website_id: 'bali_home_immo',
        name: 'Bali Home Immo',
        base_url: 'https://bali-home-immo.com',
        sitemap_enabled: true,
        sitemap_urls: '["https://bali-home-immo.com/sitemap.xml"]',
        sitemap_filters: '{"include": ["realestate-property"], "exclude": ["blog", "pages", "search", "category"]}',
        property_url_patterns: {
          property_patterns: [
            '/realestate-property/for-rent/[^/]+/(monthly|yearly)/[^/]+/[^/]+/?$',  // Rental properties with period/location/name
            '/realestate-property/for-sale/[^/]+/(freehold|leasehold)/[^/]+/[^/]+/?$'  // Sale properties with ownership/location/name
          ],
          exclude_patterns: [
            '/blog/',
            '/news/',
            '/search',
            '/filter',
            '\\?page=',
            '/page/',
            '/realestate-property/?$',
            '/realestate-property/[^/]+/?$',  // Category pages
            '/realestate-property/[^/]+/[^/]+/?$'  // Type/category pages without location/name
          ],
          listing_patterns: [
            '/realestate-property/?$',
            '/realestate-property/[^/]+/?$',  // Category pages
            '/realestate-property/[^/]+/[^/]+/?$'  // Type/category pages
          ],
          keywords: ['bedroom', 'villa', 'apartment', 'sale', 'rent']
        },
        crawl_frequency_hours: 24,
        sitemap_check_frequency_hours: 6,
        is_active: true
      },
      {
        website_id: 'bali_villa_realty',
        name: 'Bali Villa Realty',
        base_url: 'https://balivillarealty.com',
        sitemap_enabled: true,
        sitemap_urls: '["https://balivillarealty.com/property-sitemap.xml", "https://balivillarealty.com/property-sitemap2.xml"]',
        sitemap_filters: '{"include": ["property"], "exclude": ["blog", "pages", "search", "category"]}',
        property_url_patterns: {
          property_patterns: [
            '/property/[^/]+-villa-[^/]+/?$',  // Villa property pages
            '/property/[^/]+-bedroom[^/]+/?$',  // Bedroom-based property pages
            '/property/[^/]+-for-sale-[^/]+/?$',  // Sale property pages
            '/property/[^/]+-for-rental-[^/]+/?$'  // Rental property pages
          ],
          exclude_patterns: [
            '/blog/',
            '/news/',
            '/search',
            '/filter',
            '\\?page=',
            '/page/',
            '/property/?$'
          ],
          listing_patterns: [
            '/property/?$'
          ],
          keywords: ['bedroom', 'villa', 'sale', 'rent', 'leasehold', 'freehold']
        },
        crawl_frequency_hours: 24,
        sitemap_check_frequency_hours: 6,
        is_active: true
      },
      {
        website_id: 'villabalisale.com',
        name: 'Villa Bali Sale',
        base_url: 'https://www.villabalisale.com',
        sitemap_enabled: true,
        sitemap_urls: '["https://www.villabalisale.com/sitemap_property.xml"]',
        sitemap_filters: '{"include": ["realestate-property", "unique-villas"], "exclude": ["blog", "pages", "search"]}',
        property_url_patterns: {
          property_patterns: [
            '/realestate-property/for-sale/villa/(freehold|leasehold)/[^/]+/[^/]+-[a-z0-9]+/?$',  // Sale properties: /for-sale/villa/leasehold/location/property-name-code
            '/realestate-property/for-rent/villa/(annually|monthly|daily|weekly)/[^/]+/[^/]+-[a-z0-9]+/?$',  // Rental properties: /for-rent/villa/annually/location/property-name-code
            '/realestate-property/for-sale/villa/(freehold|leasehold)/[^/]+/[^/]+/?$',  // Sale properties (fallback)
            '/realestate-property/for-rent/villa/(annually|monthly|daily|weekly)/[^/]+/[^/]+/?$',  // Rental properties (fallback)
            '/unique-villas/[^/]+/?$'  // Unique villas
          ],
          exclude_patterns: [
            '/blog/',
            '/news/',
            '/search',
            '/filter',
            '\\?page=',
            '/page/',
            '/fr/',  // Exclude all French URLs
            '/id/',  // Exclude all Indonesian URLs
            '/realestate-property/for-rent/villa/annually/?$',  // Listing pages
            '/realestate-property/for-rent/villa/daily/?$',
            '/realestate-property/for-rent/villa/weekly/?$',
            '/realestate-property/for-rent/villa/monthly/?$',
            '/realestate-property/for-sale/villa/freehold/?$',
            '/realestate-property/for-sale/villa/leasehold/?$'
          ],
          listing_patterns: [
            '/realestate-property/?$',
            '/realestate-property/for-rent/?$',
            '/realestate-property/for-sale/?$',
            '/realestate-property/for-rent/villa/?$',
            '/realestate-property/for-sale/villa/?$',
            '/realestate-property/for-rent/villa/annually/?$',
            '/realestate-property/for-rent/villa/daily/?$',
            '/realestate-property/for-rent/villa/weekly/?$',
            '/realestate-property/for-rent/villa/monthly/?$',
            '/realestate-property/for-sale/villa/freehold/?$',
            '/realestate-property/for-sale/villa/leasehold/?$',
            '/fr/',  // All French URLs are listings
            '/id/'   // All Indonesian URLs are listings
          ],
          keywords: ['bedroom', 'villa', 'sale', 'leasehold', 'freehold', 'rent']
        },
        crawl_frequency_hours: 24,
        sitemap_check_frequency_hours: 6,
        is_active: true
      },
      {
        website_id: 'bali_coconut_living',
        name: 'Bali Coconut Living',
        base_url: 'https://balicoconutliving.com',
        sitemap_enabled: true,
        sitemap_urls: '["https://balicoconutliving.com/sitemap.xml"]',
        sitemap_filters: '{"include": ["bali-villa-", "property"], "exclude": ["blog", "news", "about", "contact", "services"]}',
        property_url_patterns: {
          property_patterns: [
            '/bali-villa-sale-freehold/[^/]+/[^/]+/[^/]+/?$',  // /bali-villa-sale-freehold/Canggu/6273-V005-5801/Villa-Akili-Padonan-2-
            '/bali-villa-sale-leasehold/[^/]+/[^/]+/[^/]+/?$',  // /bali-villa-sale-leasehold/Pererenan/5894-V009-5449/Villa-Calma
            '/bali-villa-yearly-rental/[^/]+/[^/]+/[^/]+/?$',  // /bali-villa-yearly-rental/Canggu/4950-V005-4542/Villa-Ivana-A15
            '/bali-villa-monthly-rental/[^/]+/[^/]+/[^/]+/?$',  // /bali-villa-monthly-rental/Padonan/4929-V008-4524/Villa-Alelia
            '/bali-land-sale-freehold/[^/]+/[^/]+/[^/]+/?$',
            '/bali-land-sale-leasehold/[^/]+/[^/]+/[^/]+/?$'
          ],
          exclude_patterns: [
            '/blog/',
            '/news/',
            '/search',
            '/filter',
            '\\?page=',
            '/page/',
            '/property/?$',
            '/property/[^/]+/?$'  // Category/location pages like /property/Seminyak/
          ],
          listing_patterns: [
            '/property/?$',
            '/property/[^/]+/?$',  // Category/location pages
            '/bali-villa-sale-freehold/?$',
            '/bali-villa-sale-leasehold/?$',
            '/bali-villa-yearly-rental/?$',
            '/bali-villa-monthly-rental/?$'
          ],
          keywords: ['bedroom', 'bathroom', 'villa', 'land', 'sale', 'rental']
        },
        crawl_frequency_hours: 24,
        sitemap_check_frequency_hours: 6,
        is_active: true
      }
    ];
    
    // Insert new configurations
    for (const config of websiteConfigsData) {
      await db.insert(websiteConfigs).values(config);
      console.log(`✅ Added configuration for ${config.name}`);
    }
    
    // Verify configurations
    console.log('\n🔍 Verifying website configurations...');
    const configsCount = await db.execute(sql`SELECT COUNT(*) as count FROM website_configs`);
    console.log(`   ⚙️  Website Configs: ${configsCount[0]?.count || 0} records`);
    
    // Show configurations
    console.log('\n📋 Updated Website Configurations:');
    const configs = await db.select().from(websiteConfigs);
    configs.forEach((config, i) => {
      console.log(`   ${i + 1}. ${config.name} (${config.website_id})`);
      console.log(`      🌐 Base URL: ${config.base_url}`);
      console.log(`      🗺️  Sitemap: ${config.sitemap_enabled ? 'Enabled' : 'Disabled'}`);
      console.log(`      ⏰ Crawl Frequency: ${config.crawl_frequency_hours}h`);
      console.log(`      ✅ Active: ${config.is_active}`);
    });
    
    console.log('\n🎉 Website configurations updated successfully!');
    
  } catch (error) {
    console.error('❌ Error updating website configs:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the update
updateWebsiteConfigs()
  .then(() => {
    console.log('✅ Website configs updated successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Update failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
