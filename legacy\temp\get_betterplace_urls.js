// Get BetterPlace URLs from queue
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq, and } = require('drizzle-orm');

async function getBetterPlaceUrls() {
  try {
    console.log('🏠 Getting BetterPlace URLs from queue...');
    
    const urls = await db
      .select({ url: scrapingQueue.url, id: scrapingQueue.id })
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, 'betterplace'),
        eq(scrapingQueue.status, 'pending')
      ))
      .limit(5);
    
    console.log(`Found ${urls.length} BetterPlace URLs:`);
    urls.forEach((row, i) => {
      console.log(`${i + 1}. ${row.url}`);
      console.log(`   ID: ${row.id}`);
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

getBetterPlaceUrls();
