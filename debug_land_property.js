// Debug land property scraping
require('dotenv').config();

async function debugLandProperty() {
  const testUrl = 'https://betterplace.cc/buy/properties/BPLF02065';
  const apiKey = process.env.FIRECRAWL_API_KEY;
  
  try {
    console.log(`🔍 Testing land property: ${testUrl}`);
    
    // Start scrape job
    const response = await fetch('https://api.firecrawl.dev/v1/batch/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        urls: [testUrl],
        formats: ['markdown'],
        onlyMainContent: true,
        timeout: 45000,
        ignoreInvalidURLs: true,
        blockAds: true,
        proxy: 'auto',
        waitFor: 2000,
        removeBase64Images: true
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Scrape start failed: HTTP ${response.status} - ${errorText}`);
      return;
    }

    const result = await response.json();
    
    if (!result.success || !result.id) {
      console.error(`❌ Scrape start failed: ${JSON.stringify(result)}`);
      return;
    }

    const jobId = result.id;
    console.log(`📋 Started job: ${jobId}`);
    
    // Poll for completion
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      console.log(`⏳ Polling attempt ${attempts + 1}/${maxAttempts}...`);
      
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      const pollResponse = await fetch(`https://api.firecrawl.dev/v1/batch/scrape/${jobId}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });

      if (!pollResponse.ok) {
        console.error(`❌ Poll failed: HTTP ${pollResponse.status}`);
        attempts++;
        continue;
      }

      const pollResult = await pollResponse.json();
      
      if (pollResult.status === 'completed') {
        console.log(`✅ Job completed!`);
        
        if (pollResult.data && pollResult.data.length > 0) {
          const scraped = pollResult.data[0];
          console.log(`📄 Scraped data:`, {
            success: scraped.success,
            url: scraped.url,
            hasMarkdown: !!scraped.markdown,
            markdownLength: scraped.markdown?.length || 0,
            error: scraped.error
          });
          
          if (scraped.markdown) {
            console.log(`\n📝 Markdown content (first 1000 chars):`);
            console.log(scraped.markdown.substring(0, 1000));
            console.log('...\n');
            
            // Test the mapper
            const { mapBetterPlace } = require('./scrape_worker/mappers');
            console.log(`🔄 Testing mapper...`);
            
            const mapped = await mapBetterPlace({
              markdown: scraped.markdown,
              url: testUrl
            });
            
            if (mapped) {
              console.log(`✅ Mapping successful:`, {
                title: mapped.title,
                price: mapped.price,
                location: mapped.location,
                property_type: mapped.property_type,
                bedrooms: mapped.bedrooms,
                bathrooms: mapped.bathrooms
              });
            } else {
              console.log(`❌ Mapping failed - returned null`);
            }
          }
        }
        break;
        
      } else if (pollResult.status === 'failed') {
        console.error(`❌ Job failed: ${pollResult.error || 'Unknown error'}`);
        break;
      } else {
        console.log(`🔄 Job status: ${pollResult.status}`);
      }
      
      attempts++;
    }
    
    if (attempts >= maxAttempts) {
      console.error(`❌ Job timed out after ${maxAttempts} attempts`);
    }
    
  } catch (error) {
    console.error(`❌ Debug failed:`, error.message);
  }
}

debugLandProperty();
