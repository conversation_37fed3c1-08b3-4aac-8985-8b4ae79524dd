// Add Bali Coconut Living URLs to Queue
require('dotenv').config();

const { db, scrapingQueue } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

// Sample property URLs from the sitemap
const propertyUrls = [
  'https://balicoconutliving.com/bali-villa-sale-freehold/Canggu/6273-V005-5801/Villa-Akili-Padonan-2-',
  'https://balicoconutliving.com/bali-villa-sale-freehold/Canggu/6268-V005-5796/Villa-Kimi-Berawa-B',
  'https://balicoconutliving.com/bali-villa-sale-freehold/Canggu/6267-V005-5795/Villa-Kimi-Berawa-A',
  'https://balicoconutliving.com/bali-villa-sale-freehold/Pererenan/6241-V009-5770/Villa-Jangmi-3',
  'https://balicoconutliving.com/bali-villa-sale-leasehold/Pererenan/5894-V009-5449/Villa-Calma',
  'https://balicoconutliving.com/bali-villa-sale-leasehold/Gianyar/5491-V021-5064/Casa-De-Gracia-2-Ubud',
  'https://balicoconutliving.com/bali-villa-sale-leasehold/Canggu/5223-V005-4802/Forest-Bloom-Villa-2',
  'https://balicoconutliving.com/bali-villa-sale-leasehold/Canggu/5217-V019-4796/Forest-Bloom-Villa-1',
  'https://balicoconutliving.com/bali-villa-yearly-rental/Canggu/5589-V005-5156/Villa-Leah',
  'https://balicoconutliving.com/bali-villa-yearly-rental/Canggu/4950-V005-4542/Villa-Ivana-A15',
  'https://balicoconutliving.com/bali-villa-yearly-rental/Uluwatu/6271-V014-5799/Villa-Merayu-Pecatu-19',
  'https://balicoconutliving.com/bali-villa-yearly-rental/Canggu/6262-V005-5790/Villa-Merbau-Babakan',
  'https://balicoconutliving.com/bali-villa-monthly-rental/Canggu/2449-V005-2385/Joglo-Bintang-2',
  'https://balicoconutliving.com/bali-villa-sale-leasehold/Umalas/5433-V001-5007/Villa-Indires',
  'https://balicoconutliving.com/bali-villa-sale-freehold/Canggu/5478-V005-5051/Villa-Devara-Berawa',
  'https://balicoconutliving.com/bali-villa-yearly-rental/Petitenget/4569-V010-4221/Villa-Jangmi',
  'https://balicoconutliving.com/bali-land-sale-freehold/Jimbaran/5612-L013-0422/30-Are-Land-For-Sale-',
  'https://balicoconutliving.com/bali-land-sale-freehold/Nusa-Dua/3153-L003-0144/17-Are,-Land-for-Sale',
  'https://balicoconutliving.com/bali-land-sale-leasehold/Mengwi/5553-L015-0416/15-Are-Land-For-Sale-',
  'https://balicoconutliving.com/bali-land-sale-leasehold/Sanur/5299-L012-0413/566-Sqm-Land-For-Sale-',
  'https://balicoconutliving.com/bali-land-sale-freehold/Canggu/5069-L005-0403/10-Are-Land-For-Sale',
  'https://balicoconutliving.com/bali-land-sale-freehold/Seminyak/4374-L011-0322/10,25-Are-for-sale,-Land',
  'https://balicoconutliving.com/bali-land-sale-freehold/Uluwatu/3886-L014-0263/20-Are,-Land-for',
  'https://balicoconutliving.com/bali-villa-monthly-rental/Seminyak/3173-V011-3022/Villa-Santo-Domingo-2',
  'https://balicoconutliving.com/bali-villa-monthly-rental/Seminyak/2911-V011-2790/Villa-Laura-2',
  'https://balicoconutliving.com/bali-villa-sale-freehold/Uluwatu/5259-V014-4836/Villa-Ronja',
  'https://balicoconutliving.com/bali-villa-yearly-rental/Uluwatu/4723-V014-4342/Joglo-Enam',
  'https://balicoconutliving.com/bali-villa-sale-freehold/Nusa-Dua/3533-V003-3350/Villa-Miriam',
  'https://balicoconutliving.com/bali-villa-sale-freehold/Uluwatu/4665-V014-4291/Villa-Bukit-Bali',
  'https://balicoconutliving.com/bali-villa-sale-leasehold/Uluwatu/5232-V014-4811/Villa-Sachio'
];

async function addUrlsToQueue() {
  console.log('🚀 Adding Bali Coconut Living URLs to Queue');
  console.log('='.repeat(50));

  let addedCount = 0;
  let skippedCount = 0;

  for (const url of propertyUrls) {
    try {
      // Check if URL already exists in queue
      const existing = await db
        .select()
        .from(scrapingQueue)
        .where(eq(scrapingQueue.url, url))
        .limit(1);

      if (existing.length > 0) {
        console.log(`⚠️  Skipped (exists): ${url}`);
        skippedCount++;
        continue;
      }

      // Add to queue
      await db.insert(scrapingQueue).values({
        url: url,
        website_id: 'bali_coconut_living',
        priority: 8,
        status: 'pending',
        created_at: new Date(),
        metadata: JSON.stringify({
          source: 'manual_sitemap',
          added_by: 'add_bali_coconut_urls_script'
        })
      });

      console.log(`✅ Added: ${url}`);
      addedCount++;

    } catch (error) {
      console.error(`❌ Error adding URL ${url}:`, error.message);
      skippedCount++;
    }
  }

  console.log(`\n📊 Summary:`);
  console.log(`   Added: ${addedCount} URLs`);
  console.log(`   Skipped: ${skippedCount} URLs`);
  console.log(`   Total processed: ${propertyUrls.length} URLs`);

  // Show queue status
  const queueStatus = await db
    .select()
    .from(scrapingQueue)
    .where(eq(scrapingQueue.website_id, 'bali_coconut_living'));

  console.log(`\n📋 Queue Status for Bali Coconut Living:`);
  console.log(`   Total URLs in queue: ${queueStatus.length}`);

  const statusCounts = {};
  queueStatus.forEach(item => {
    statusCounts[item.status] = (statusCounts[item.status] || 0) + 1;
  });

  Object.entries(statusCounts).forEach(([status, count]) => {
    console.log(`   ${status}: ${count}`);
  });
}

// Run if called directly
if (require.main === module) {
  addUrlsToQueue()
    .then(() => {
      console.log('\n🎉 URLs added to queue successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Failed to add URLs:', error.message);
      process.exit(1);
    });
}

module.exports = { addUrlsToQueue };
