// Monitor Bali Villa Realty crawl and process URLs when ready
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');
const { SmartCrawler } = require('./scrape_worker/smart_crawler');
const { QueueManager } = require('./scrape_worker/queue_manager');

async function monitorAndProcessBaliVillaRealty() {
  console.log('🔍 Monitoring Bali Villa Realty crawl...\n');

  const keyManager = getKeyManager();
  const crawler = new SmartCrawler();
  const qm = new QueueManager();
  
  // Bali Villa Realty job ID
  const jobId = '1ea85a53-2fc2-4a85-9441-aa3a6260bca0';
  const firecrawlJobId = '36b3eaa3-d15d-40b5-ac3a-62a191d34d53';
  
  let attempts = 0;
  const maxAttempts = 30; // 15 minutes max
  
  while (attempts < maxAttempts) {
    try {
      console.log(`📊 Attempt ${attempts + 1}/${maxAttempts}: Checking crawl status...`);
      
      const currentKey = keyManager.getCurrentKey();
      const response = await fetch(`https://api.firecrawl.dev/v1/crawl/${firecrawlJobId}`, {
        headers: {
          'Authorization': `Bearer ${currentKey.key}`
        }
      });

      if (!response.ok) {
        if (response.status === 429 || response.status === 403) {
          console.log('⚠️  Rate limit hit, rotating key...');
          keyManager.rotateKey();
          await new Promise(resolve => setTimeout(resolve, 5000));
          continue;
        }
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const data = await response.json();
      
      console.log(`   Status: ${data.status}`);
      console.log(`   Progress: ${data.completed}/${data.total}`);
      console.log(`   Credits used: ${data.creditsUsed}`);
      
      if (data.status === 'completed') {
        console.log('\n🎉 Bali Villa Realty crawl completed!');
        console.log(`📊 Final stats: ${data.completed}/${data.total} URLs processed`);
        
        // Process the crawl results
        console.log('\n🔄 Processing crawl results...');
        await crawler.checkCrawlStatus(jobId);
        
        // Check how many URLs were added to queue
        const { db, scrapingQueue } = require('./drizzle_client');
        const { eq } = require('drizzle-orm');
        
        const queueItems = await db.select().from(scrapingQueue)
          .where(eq(scrapingQueue.website_id, 'bali_villa_realty'));
        
        console.log(`📋 URLs added to queue: ${queueItems.length}`);
        
        if (queueItems.length > 0) {
          console.log('\n🚀 Starting to scrape Bali Villa Realty URLs...');
          
          // Process up to 30 URLs in batches
          const urlsToProcess = Math.min(30, queueItems.length);
          const batches = Math.ceil(urlsToProcess / 10);
          
          for (let batch = 1; batch <= batches; batch++) {
            console.log(`\n📦 Batch ${batch}/${batches}:`);
            
            await qm.processQueue('bali_villa_realty', 10);
            
            console.log(`✅ Batch ${batch} completed`);
            
            // Wait between batches
            if (batch < batches) {
              console.log('⏳ Waiting 60 seconds before next batch...');
              await new Promise(resolve => setTimeout(resolve, 60000));
            }
          }
          
          // Final summary
          console.log('\n📊 Final Summary:');
          const { properties } = require('./drizzle_client');
          const baliVillaProps = await db.select().from(properties)
            .where(eq(properties.source_id, 'bali_villa_realty'));
          
          console.log(`✅ Bali Villa Realty properties scraped: ${baliVillaProps.length}`);
          
          // Show sample properties
          if (baliVillaProps.length > 0) {
            console.log('\n🏠 Sample Bali Villa Realty properties:');
            baliVillaProps.slice(0, 5).forEach((prop, index) => {
              console.log(`   ${index + 1}. ${prop.title}`);
              console.log(`      Category: ${prop.category}/${prop.type}`);
              console.log(`      Rent: ${prop.rent_price ? (prop.rent_price/1000000).toFixed(1) + 'M' : 'N/A'} IDR`);
            });
          }
          
        } else {
          console.log('⚠️  No URLs were added to queue. Check crawl results.');
        }
        
        break;
        
      } else if (data.status === 'failed') {
        console.log('\n❌ Bali Villa Realty crawl failed');
        break;
        
      } else {
        // Still running, wait and try again
        console.log(`   Still ${data.status}... waiting 30 seconds`);
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
      
    } catch (error) {
      console.log(`⚠️  Error checking status: ${error.message}`);
      
      // Try rotating key on error
      keyManager.rotateKey();
      await new Promise(resolve => setTimeout(resolve, 10000));
    }
    
    attempts++;
  }
  
  if (attempts >= maxAttempts) {
    console.log('\n⏰ Timeout waiting for crawl to complete');
  }
}

// Run the monitoring
monitorAndProcessBaliVillaRealty()
  .then(() => {
    console.log('\n✅ Monitoring and processing completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Monitoring failed:', error);
    process.exit(1);
  });
