// Test single Bali Coconut Living property with detailed debugging
require('dotenv').config();
const { PropertyScraper } = require('../scrape_worker/property_scraper');
const { FirecrawlKeyManager } = require('../scrape_worker/firecrawl_key_manager');

async function testSingleProperty() {
  console.log('🥥 Testing Single Bali Coconut Living Property\n');
  
  const testUrl = 'https://balicoconutliving.com/bali-villa-yearly-rental/Petitenget/4569-V010-4221/Villa-Jangmi';
  
  try {
    // Initialize components
    const keyManager = new FirecrawlKeyManager();
    const scraper = new PropertyScraper(keyManager);
    
    console.log(`🔍 Testing URL: ${testUrl}`);
    console.log('📊 Expected: Sale price 5B IDR + Rental price 380M IDR\n');
    
    // Scrape the property
    const result = await scraper.scrapeProperty(testUrl, 'bali_coconut_living');
    
    if (result.success) {
      console.log('\n✅ Scraping successful!');
      console.log('📊 Result:');
      console.log(`   Title: ${result.property.title}`);
      console.log(`   Sale Price: ${result.property.price ? `IDR ${result.property.price.toLocaleString()}` : 'Not found'}`);
      console.log(`   Rent Price: ${result.property.rent_price ? `IDR ${result.property.rent_price.toLocaleString()}` : 'Not found'}`);
      console.log(`   Location: ${result.property.city}`);
      console.log(`   Bedrooms: ${result.property.bedrooms}`);
      console.log(`   Bathrooms: ${result.property.bathrooms}`);
      console.log(`   Ownership: ${result.property.ownership_type}`);
      console.log(`   Land Size: ${result.property.lot_size_sqft ? `${result.property.lot_size_sqft} sqft` : 'Not found'}`);
      console.log(`   Building Size: ${result.property.size_sqft ? `${result.property.size_sqft} sqft` : 'Not found'}`);
    } else {
      console.log('\n❌ Scraping failed:');
      console.log(`   Error: ${result.error}`);
      console.log(`   Details: ${result.details || 'No details'}`);
    }
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    console.error(error.stack);
  }
}

testSingleProperty();
