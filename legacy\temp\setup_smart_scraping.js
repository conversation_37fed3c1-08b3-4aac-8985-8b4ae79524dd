// Setup Smart Scraping System - Create tables and initial configuration
require('dotenv').config();
const { db, websiteConfigs } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function setupSmartScraping() {
  console.log('🔧 Setting up Smart Scraping System...\n');

  try {
    // Create tables using raw SQL (since we don't have migrations set up)
    console.log('1. Creating database tables...');
    
    await db.execute(`
      -- Crawl Jobs Table
      CREATE TABLE IF NOT EXISTS crawl_jobs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          website_id VARCHAR(50) NOT NULL,
          base_url VARCHAR(500) NOT NULL,
          firecrawl_job_id VARCHAR(100),
          status VARCHAR(20) NOT NULL DEFAULT 'pending',
          total_urls INTEGER DEFAULT 0,
          processed_urls INTEGER DEFAULT 0,
          property_urls_found INTEGER DEFAULT 0,
          started_at TIMESTAMP,
          completed_at TIMESTAMP,
          error_message TEXT,
          crawl_options JSONB,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    await db.execute(`
      -- Discovered URLs Table
      CREATE TABLE IF NOT EXISTS discovered_urls (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          crawl_job_id UUID REFERENCES crawl_jobs(id) ON DELETE CASCADE,
          url VARCHAR(1000) NOT NULL UNIQUE,
          website_id VARCHAR(50) NOT NULL,
          url_type VARCHAR(20) NOT NULL,
          is_property_page BOOLEAN DEFAULT FALSE,
          confidence_score DECIMAL(3,2),
          classification_reason TEXT,
          discovered_at TIMESTAMP DEFAULT NOW(),
          last_scraped_at TIMESTAMP,
          scrape_attempts INTEGER DEFAULT 0,
          scrape_status VARCHAR(20) DEFAULT 'pending',
          property_id UUID REFERENCES property(id),
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    await db.execute(`
      -- Scraping Queue Table
      CREATE TABLE IF NOT EXISTS scraping_queue (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          discovered_url_id UUID REFERENCES discovered_urls(id) ON DELETE CASCADE,
          url VARCHAR(1000) NOT NULL,
          website_id VARCHAR(50) NOT NULL,
          priority INTEGER DEFAULT 5,
          scheduled_for TIMESTAMP DEFAULT NOW(),
          attempts INTEGER DEFAULT 0,
          max_attempts INTEGER DEFAULT 3,
          status VARCHAR(20) DEFAULT 'pending',
          assigned_to VARCHAR(100),
          started_at TIMESTAMP,
          completed_at TIMESTAMP,
          error_message TEXT,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    await db.execute(`
      -- Website Configurations Table
      CREATE TABLE IF NOT EXISTS website_configs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          website_id VARCHAR(50) NOT NULL UNIQUE,
          name VARCHAR(100) NOT NULL,
          base_url VARCHAR(500) NOT NULL,
          crawl_patterns JSONB,
          property_url_patterns JSONB,
          crawl_frequency_hours INTEGER DEFAULT 24,
          max_pages INTEGER DEFAULT 1000,
          is_active BOOLEAN DEFAULT TRUE,
          last_crawl_at TIMESTAMP,
          next_crawl_at TIMESTAMP,
          crawl_options JSONB,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log('✅ Database tables created successfully');

    // Create indexes
    console.log('2. Creating database indexes...');
    
    await db.execute(`
      CREATE INDEX IF NOT EXISTS idx_discovered_urls_website_type ON discovered_urls(website_id, url_type);
      CREATE INDEX IF NOT EXISTS idx_discovered_urls_last_scraped ON discovered_urls(last_scraped_at);
      CREATE INDEX IF NOT EXISTS idx_discovered_urls_property_page ON discovered_urls(is_property_page);
      CREATE INDEX IF NOT EXISTS idx_scraping_queue_status_priority ON scraping_queue(status, priority DESC);
      CREATE INDEX IF NOT EXISTS idx_scraping_queue_scheduled ON scraping_queue(scheduled_for);
      CREATE INDEX IF NOT EXISTS idx_crawl_jobs_status ON crawl_jobs(status);
      CREATE INDEX IF NOT EXISTS idx_website_configs_next_crawl ON website_configs(next_crawl_at);
    `);

    console.log('✅ Database indexes created successfully');

    // Add columns to existing property table
    console.log('3. Updating property table...');

    try {
      await db.execute(`
        ALTER TABLE property ADD COLUMN IF NOT EXISTS source_url_id UUID REFERENCES discovered_urls(id);
        ALTER TABLE property ADD COLUMN IF NOT EXISTS last_scraped_at TIMESTAMP DEFAULT NOW();
      `);
      console.log('✅ Property table updated successfully');
    } catch (error) {
      console.log('⚠️  Property table update skipped (columns may already exist)');
    }

    // Insert website configurations
    console.log('4. Setting up website configurations...');
    
    const websiteConfigsData = [
      {
        website_id: 'betterplace',
        name: 'BetterPlace',
        base_url: 'https://betterplace.cc',
        crawl_patterns: {
          include: [
            '/buy/properties/*',    // Individual property pages
            '/rental/properties/*', // Rental property pages
            '/buy-results*',        // Buy listing pages
            '/rental-results*',     // Rental listing pages
            '/buy*',               // Buy section
            '/rental*',            // Rental section
            '/properties*'         // General properties pages
          ],
          exclude: [
            '/api/*',              // API endpoints
            '/_next/*',            // Next.js assets
            '/static/*',           // Static assets
            '/images/*',           // Image assets
            '/css/*',              // CSS files
            '/js/*',               // JavaScript files
            '*.jpg',               // Image files
            '*.png',               // Image files
            '*.webp',              // Image files
            '*.svg'                // SVG files
          ]
        },
        property_url_patterns: {
          patterns: [
            '/buy/properties/BP[A-Z0-9]+$',     // Buy property pages
            '/rental/properties/BP[A-Z0-9]+$'   // Rental property pages
          ],
          keywords: ['bedroom', 'bathroom', 'villa', 'price', 'sqm', 'USD', 'rent', 'lease']
        },
        listing_url_patterns: {
          patterns: [
            '/buy-results',
            '/rental-results',
            '/buy$',
            '/rental$',
            '/properties'
          ],
          keywords: ['results', 'listings', 'properties', 'search', 'filter']
        },
        crawl_options: {
          formats: ['markdown'],
          onlyMainContent: true,
          excludeTags: ['img', 'script', 'style', 'link']
        },
        crawl_frequency_hours: 24,
        max_pages: 200
      },
      {
        website_id: 'bali_villa_realty',
        name: 'Bali Villa Realty',
        base_url: 'https://balivillarealty.com',
        crawl_patterns: {
          include: ['/property/*'],
          exclude: ['/search*', '/category*', '/tag*', '/author*']
        },
        property_url_patterns: {
          patterns: ['/property/[^/]+/$'],
          keywords: ['bedroom', 'bathroom', 'rental', 'sale', 'villa', 'USD']
        },
        crawl_options: {
          formats: ['markdown'],
          onlyMainContent: true
        },
        crawl_frequency_hours: 24,
        max_pages: 500
      },
      {
        website_id: 'bali_home_immo',
        name: 'Bali Home Immo',
        base_url: 'https://bali-home-immo.com',
        crawl_patterns: {
          include: ['/realestate-property/*'],
          exclude: ['/search*', '/category*', '/wp-admin*', '/wp-content*']
        },
        property_url_patterns: {
          patterns: ['/realestate-property/for-(rent|sale)/'],
          keywords: ['bedroom', 'bathroom', 'villa', 'rent', 'monthly', 'USD']
        },
        crawl_options: {
          formats: ['markdown'],
          onlyMainContent: true
        },
        crawl_frequency_hours: 24,
        max_pages: 500
      }
    ];

    for (const config of websiteConfigsData) {
      try {
        // Check if config already exists
        const existing = await db.select()
          .from(websiteConfigs)
          .where(eq(websiteConfigs.website_id, config.website_id));

        if (existing.length === 0) {
          // Insert new config
          await db.insert(websiteConfigs).values({
            ...config,
            next_crawl_at: new Date(Date.now() + Math.random() * 60 * 60 * 1000) // Random delay up to 1 hour
          });
          console.log(`✅ Added configuration for ${config.name}`);
        } else {
          console.log(`⏭️  Configuration for ${config.name} already exists`);
        }
      } catch (error) {
        console.error(`❌ Failed to add configuration for ${config.name}:`, error.message);
      }
    }

    console.log('\n🎉 Smart Scraping System setup completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Start the system: node smart_scraping_system.js start');
    console.log('2. Check status: node smart_scraping_system.js status');
    console.log('3. Manual crawl: node smart_scraping_system.js crawl <website_id>');
    
    console.log('\n🌐 Configured Websites:');
    websiteConfigsData.forEach(config => {
      console.log(`   • ${config.name} (${config.website_id})`);
      console.log(`     URL: ${config.base_url}`);
      console.log(`     Frequency: Every ${config.crawl_frequency_hours} hours`);
      console.log(`     Max pages: ${config.max_pages}`);
    });

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

if (require.main === module) {
  setupSmartScraping();
}

module.exports = { setupSmartScraping };
