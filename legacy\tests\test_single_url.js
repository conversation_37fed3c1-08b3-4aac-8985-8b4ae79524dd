// Test single URL to debug data flow
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testSingleUrl() {
  console.log('🧪 Test Single URL');
  console.log('='.repeat(30));
  
  try {
    // Use a simple URL that should work
    const testUrl = 'https://balivillarealty.com/property/cozy-1-bedroom-villa-in-prime-petitenget-location-exceptional-investment-opportunity/';
    
    console.log(`🔄 Testing: ${testUrl}`);
    console.log('Expected: 1 bedroom from title');
    
    const results = await runExtractBatch('bali_villa_realty', [testUrl], {});
    
    console.log('\n📊 Results:');
    console.log('Type:', typeof results);
    console.log('Keys:', Object.keys(results || {}));
    
    if (results && results.extractedData) {
      console.log('\n✅ ExtractedData found:');
      results.extractedData.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.title}`);
        console.log(`      Bedrooms: ${item.bedrooms}`);
        console.log(`      Bathrooms: ${item.bathrooms}`);
      });
    }
    
    if (results && results.processedResults) {
      console.log('\n📋 ProcessedResults found:');
      results.processedResults.forEach((item, index) => {
        console.log(`   ${index + 1}. OK: ${item.ok}, Title: ${item.title}`);
        if (item.data) {
          console.log(`      Data Bedrooms: ${item.data.bedrooms}`);
          console.log(`      Data Bathrooms: ${item.data.bathrooms}`);
        }
      });
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run test
if (require.main === module) {
  testSingleUrl().catch(console.error);
}

module.exports = { testSingleUrl };
