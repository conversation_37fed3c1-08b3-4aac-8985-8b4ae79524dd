// Test async scraping with improved rate limiting
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { testConnection } = require('./drizzle_client');

async function testAsyncScraping() {
  console.log('🧪 Testing improved async scraping...\n');
  
  // Test database connection first
  console.log('1. Testing database connection...');
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ Database connection failed. Exiting.');
    return;
  }
  
  // Test with just 3 URLs to avoid rate limits
  const urls = [
    'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/',
    'https://balivillarealty.com/property/cozy-villa-3-bedrooms-for-rental-in-umalas-bali/',
    'https://balivillarealty.com/property/charming-2-bedrooms-villa-for-rental-in-babakan-canggu-bali/'
  ];
  
  console.log(`\n2. Testing async scraping with ${urls.length} URLs...`);
  console.log('URLs to test:');
  urls.forEach((url, index) => {
    console.log(`   ${index + 1}. ${url}`);
  });
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('bali_villa_realty', urls, {});
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n3. ✅ Async scraping completed in ${duration} seconds!`);
    console.log('\n📊 Results Summary:');
    
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}`);
    console.log(`   ❌ Failed: ${failed.length}`);
    console.log(`   📈 Success Rate: ${((successful.length / results.length) * 100).toFixed(1)}%`);
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully scraped properties:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title}`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ Failed properties:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. Error: ${result.error}`);
      });
    }
    
  } catch (error) {
    console.error(`❌ Async scraping test failed:`, error.message);
  }
  
  console.log('\n🎉 Async scraping test completed!');
}

// Run the test
if (require.main === module) {
  testAsyncScraping().catch(console.error);
}

module.exports = { testAsyncScraping };
