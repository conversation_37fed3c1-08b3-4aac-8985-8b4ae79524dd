// Check BetterPlace properties amenities
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function checkBetterPlaceAmenities() {
  try {
    console.log('🏠 Checking BetterPlace Properties...');
    
    // Find the BetterPlace property from the recent list
    const betterPlaceProps = await db
      .select()
      .from(properties)
      .where(eq(properties.source_id, 'betterplace'))
      .limit(5);
    
    console.log(`Found ${betterPlaceProps.length} BetterPlace Properties:`);
    betterPlaceProps.forEach((prop, i) => {
      console.log(`\n${i + 1}. ${prop.title}`);
      console.log(`   ID: ${prop.id}`);
      console.log(`   External ID: ${prop.external_id}`);
      console.log(`   Price: IDR ${prop.price?.toLocaleString()}`);
      console.log(`   Bedrooms: ${prop.bedrooms}`);
      console.log(`   City: ${prop.city}`);
      console.log(`   Description length: ${prop.description?.length || 0}`);
      
      if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
        console.log(`   ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.join(', ')}`);
      } else {
        console.log(`   ❌ No amenities found`);
      }
      
      console.log(`   Created: ${new Date(prop.created_at).toLocaleString()}`);
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

checkBetterPlaceAmenities();
