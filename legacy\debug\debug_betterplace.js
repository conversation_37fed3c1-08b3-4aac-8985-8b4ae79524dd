// Debug BetterPlace async scraping issue
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function debugBetterPlace() {
  console.log('🔍 Debugging BetterPlace async scraping...\n');
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();
  
  const url = 'https://betterplace.cc/buy/properties/BPVL02232';
  
  console.log(`🔑 Using key: ${currentKey.maskedKey}`);
  console.log(`🌐 Testing URL: ${url}\n`);
  
  try {
    // Test the raw Firecrawl API call
    console.log('1. Testing raw Firecrawl crawl API...');
    
    const crawlResponse = await fetch('https://api.firecrawl.dev/v1/crawl', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${currentKey.key}`
      },
      body: JSON.stringify({
        url: url,
        limit: 1,
        scrapeOptions: {
          formats: ['json'],
          jsonOptions: {
            prompt: "Extract property details: title, price, location, bedrooms, bathrooms, description, images (array), property_id, detail_url, property_type, status, size: {land_size_sqm, building_size_sqm}, amenities (array), year_built, parking (parking information like '2 car garage', 'open parking', 'covered parking')"
          },
          onlyMainContent: true,
          timeout: 60000
        }
      })
    });
    
    console.log(`   Response status: ${crawlResponse.status}`);
    
    if (!crawlResponse.ok) {
      const errorText = await crawlResponse.text();
      console.log(`   ❌ Error response: ${errorText}`);
      return;
    }
    
    const crawlData = await crawlResponse.json();
    console.log(`   ✅ Crawl started successfully`);
    console.log(`   Job ID: ${crawlData.id}`);
    console.log(`   Success: ${crawlData.success}`);
    
    if (!crawlData.success || !crawlData.id) {
      console.log(`   ❌ Failed to start crawl: ${JSON.stringify(crawlData)}`);
      return;
    }
    
    const jobId = crawlData.id;
    
    // Poll for completion
    console.log('\n2. Polling for completion...');
    let attempts = 0;
    const maxAttempts = 15;
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 5000));
      attempts++;
      
      console.log(`   Attempt ${attempts}/${maxAttempts}...`);
      
      const statusResponse = await fetch(`https://api.firecrawl.dev/v1/crawl/${jobId}`, {
        headers: {
          'Authorization': `Bearer ${currentKey.key}`
        }
      });
      
      if (!statusResponse.ok) {
        console.log(`   ⚠️  Status check failed: ${statusResponse.status}`);
        continue;
      }
      
      const statusData = await statusResponse.json();
      console.log(`   Status: ${statusData.status}`);
      console.log(`   Progress: ${statusData.completed || 0}/${statusData.total || 1}`);
      
      if (statusData.status === 'completed') {
        console.log('\n3. ✅ Crawl completed! Analyzing data...');
        
        console.log(`   Total results: ${statusData.data?.length || 0}`);
        
        if (statusData.data && statusData.data.length > 0) {
          const result = statusData.data[0];
          console.log(`   Has JSON data: ${!!result.json}`);
          console.log(`   Has markdown: ${!!result.markdown}`);
          console.log(`   Has HTML: ${!!result.html}`);
          
          if (result.json) {
            console.log('\n📋 Extracted JSON data:');
            console.log(JSON.stringify(result.json, null, 2));
          } else {
            console.log('\n❌ No JSON data extracted');
            
            if (result.markdown) {
              console.log('\n📄 Available markdown (first 500 chars):');
              console.log(result.markdown.substring(0, 500) + '...');
            }
          }
        } else {
          console.log('   ❌ No data returned');
        }
        
        return;
        
      } else if (statusData.status === 'failed') {
        console.log(`   ❌ Crawl failed: ${statusData.error || 'Unknown error'}`);
        return;
      }
    }
    
    console.log('\n⏰ Polling timeout reached');
    
  } catch (error) {
    console.error(`❌ Debug failed: ${error.message}`);
  }
}

if (require.main === module) {
  debugBetterPlace().catch(console.error);
}

module.exports = { debugBetterPlace };
