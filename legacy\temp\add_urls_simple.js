// Simple URL Adder
require('dotenv').config();

async function addUrls() {
  console.log('🚀 Adding URLs to queue...');
  
  try {
    const { db, scrapingQueue } = require('./drizzle_client');
    
    // BetterPlace URLs (sequential property IDs)
    const betterplaceUrls = [];
    for (let i = 2330; i <= 2350; i++) {
      betterplaceUrls.push(`https://betterplace.cc/buy/properties/BPVL${i.toString().padStart(5, '0')}`);
    }
    
    // Bali Home Immo URLs (different locations)
    const locations = [
      'canggu_berawa', 'seminyak_petitenget', 'ubud_monkey_forest', 
      'uluwatu_bingin', 'jimbaran_four_seasons', 'kerobokan_umalas',
      'canggu_echo_beach', 'seminyak_oberoi', 'ubud_campuhan', 'uluwatu_dreamland',
      'canggu_batu_bolong', 'seminyak_kayu_aya', 'ubud_bisma', 'uluwatu_pecatu',
      'jimbaran_kedonganan', 'kerobokan_seminyak', 'canggu_pererenan', 'seminyak_laksmana',
      'ubud_penestanan', 'uluwatu_suluban'
    ];
    
    const baliHomeUrls = [];
    locations.forEach(location => {
      baliHomeUrls.push(`https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/${location}`);
      baliHomeUrls.push(`https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/${location}`);
    });
    
    let totalAdded = 0;
    
    // Add BetterPlace URLs
    console.log('\n📦 Adding BetterPlace URLs...');
    for (const url of betterplaceUrls) {
      try {
        await db.insert(scrapingQueue).values({
          website_id: 'betterplace',
          url: url,
          priority: 7,
          status: 'pending',
          attempts: 0,
          created_at: new Date(),
          updated_at: new Date()
        });
        totalAdded++;
        console.log(`✅ ${totalAdded}: ${url}`);
      } catch (error) {
        if (!error.message.includes('duplicate')) {
          console.log(`❌ Error: ${error.message}`);
        }
      }
    }
    
    // Add Bali Home Immo URLs
    console.log('\n🏠 Adding Bali Home Immo URLs...');
    for (const url of baliHomeUrls.slice(0, 20)) { // Limit to 20
      try {
        await db.insert(scrapingQueue).values({
          website_id: 'bali_home_immo',
          url: url,
          priority: 7,
          status: 'pending',
          attempts: 0,
          created_at: new Date(),
          updated_at: new Date()
        });
        totalAdded++;
        console.log(`✅ ${totalAdded}: ${url.substring(0, 80)}...`);
      } catch (error) {
        if (!error.message.includes('duplicate')) {
          console.log(`❌ Error: ${error.message}`);
        }
      }
    }
    
    console.log(`\n🎯 TOTAL ADDED: ${totalAdded} URLs`);
    
    // Check queue status
    const { eq } = require('drizzle-orm');
    const pending = await db.select().from(scrapingQueue).where(eq(scrapingQueue.status, 'pending'));
    console.log(`📋 Total pending URLs in queue: ${pending.length}`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

addUrls().then(() => {
  console.log('✅ URL addition completed');
  process.exit(0);
}).catch(console.error);
