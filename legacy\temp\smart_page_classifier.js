// Smart Page Classifier - Detects listing vs property pages
require('dotenv').config();

class SmartPageClassifier {
  constructor() {
    this.listingPageIndicators = {
      // URL patterns that indicate listing pages
      urlPatterns: [
        /\/search/i,
        /\/results/i,
        /\/properties$/i,
        /\/listings/i,
        /\/page\/\d+/i,
        /\/category\//i,
        /\/filter/i,
        /\/browse/i,
        /\?page=/i,
        /\?search=/i,
        /\/for-rent\/?$/i,
        /\/for-sale\/?$/i,
        /\/villa\/?$/i,
        /\/apartment\/?$/i,
        /\/monthly\/?$/i,
        /\/yearly\/?$/i,
        /\/freehold\/?$/i,
        /\/leasehold\/?$/i,
        /\/other\/?$/i
      ],
      
      // Content patterns that indicate listing pages
      contentPatterns: [
        /properties found/i,
        /results found/i,
        /showing \d+ of \d+/i,
        /page \d+ of \d+/i,
        /next page/i,
        /previous page/i,
        /load more/i,
        /view all properties/i,
        /filter by/i,
        /sort by/i,
        /price range/i,
        /property type/i,
        /location filter/i
      ],
      
      // HTML structure patterns
      structurePatterns: [
        /<div[^>]*class="[^"]*property-list/i,
        /<div[^>]*class="[^"]*search-results/i,
        /<div[^>]*class="[^"]*listing-grid/i,
        /<div[^>]*class="[^"]*property-grid/i,
        /<ul[^>]*class="[^"]*property-list/i,
        /data-property-id/i,
        /property-card/i,
        /listing-item/i
      ]
    };

    this.propertyPageIndicators = {
      // URL patterns for individual properties
      urlPatterns: [
        /\/property\/[^\/]+$/i,
        /\/villa\/[^\/]+$/i,
        /\/apartment\/[^\/]+$/i,
        /\/BPVL\d+$/i,
        /\/\d+$/i,
        /\/[^\/]+-\d+$/i,
        /\/monthly\/[^\/]+$/i,
        /\/yearly\/[^\/]+$/i,
        /\/freehold\/[^\/]+$/i,
        /\/leasehold\/[^\/]+$/i,
        /\/other\/monthly\/[^\/]+$/i,
        /\/other\/yearly\/[^\/]+$/i
      ],
      
      // Content that indicates single property
      contentPatterns: [
        /bedrooms?/i,
        /bathrooms?/i,
        /square meters?/i,
        /sqm/i,
        /price.*idr/i,
        /monthly rent/i,
        /for sale/i,
        /contact agent/i,
        /book viewing/i,
        /property details/i,
        /amenities/i,
        /description/i,
        /location map/i
      ],
      
      // Structure for single property
      structurePatterns: [
        /<div[^>]*class="[^"]*property-detail/i,
        /<div[^>]*class="[^"]*single-property/i,
        /<div[^>]*class="[^"]*property-info/i,
        /property-gallery/i,
        /property-description/i,
        /contact-form/i
      ]
    };
  }

  // Classify page type based on URL
  classifyByUrl(url) {
    // Check for listing page patterns
    for (const pattern of this.listingPageIndicators.urlPatterns) {
      if (pattern.test(url)) {
        return {
          type: 'listing',
          confidence: 0.8,
          reason: `URL matches listing pattern: ${pattern}`
        };
      }
    }

    // Check for property page patterns
    for (const pattern of this.propertyPageIndicators.urlPatterns) {
      if (pattern.test(url)) {
        return {
          type: 'property',
          confidence: 0.7,
          reason: `URL matches property pattern: ${pattern}`
        };
      }
    }

    return {
      type: 'unknown',
      confidence: 0.1,
      reason: 'URL does not match known patterns'
    };
  }

  // Classify page type based on content
  classifyByContent(content, title = '') {
    let listingScore = 0;
    let propertyScore = 0;
    const reasons = [];

    // Check content for listing indicators
    for (const pattern of this.listingPageIndicators.contentPatterns) {
      if (pattern.test(content) || pattern.test(title)) {
        listingScore += 1;
        reasons.push(`Listing indicator: ${pattern}`);
      }
    }

    // Check content for property indicators
    for (const pattern of this.propertyPageIndicators.contentPatterns) {
      if (pattern.test(content) || pattern.test(title)) {
        propertyScore += 1;
        reasons.push(`Property indicator: ${pattern}`);
      }
    }

    // Check HTML structure
    for (const pattern of this.listingPageIndicators.structurePatterns) {
      if (pattern.test(content)) {
        listingScore += 2; // Structure patterns are more reliable
        reasons.push(`Listing structure: ${pattern}`);
      }
    }

    for (const pattern of this.propertyPageIndicators.structurePatterns) {
      if (pattern.test(content)) {
        propertyScore += 2;
        reasons.push(`Property structure: ${pattern}`);
      }
    }

    // Determine result
    if (listingScore > propertyScore && listingScore > 2) {
      return {
        type: 'listing',
        confidence: Math.min(0.9, listingScore / 10),
        reason: reasons.join(', '),
        scores: { listing: listingScore, property: propertyScore }
      };
    } else if (propertyScore > listingScore && propertyScore > 1) {
      return {
        type: 'property',
        confidence: Math.min(0.9, propertyScore / 10),
        reason: reasons.join(', '),
        scores: { listing: listingScore, property: propertyScore }
      };
    }

    return {
      type: 'unknown',
      confidence: 0.2,
      reason: 'Insufficient indicators',
      scores: { listing: listingScore, property: propertyScore }
    };
  }

  // Combined classification
  classifyPage(url, content = '', title = '') {
    const urlClassification = this.classifyByUrl(url);
    const contentClassification = this.classifyByContent(content, title);

    // Combine results with weighted scoring
    let finalType = 'unknown';
    let finalConfidence = 0;
    let finalReason = '';

    if (urlClassification.type === contentClassification.type) {
      // Both agree
      finalType = urlClassification.type;
      finalConfidence = Math.min(0.95, (urlClassification.confidence + contentClassification.confidence) / 2 + 0.2);
      finalReason = `URL and content agree: ${urlClassification.type}`;
    } else if (urlClassification.confidence > 0.7) {
      // URL is very confident
      finalType = urlClassification.type;
      finalConfidence = urlClassification.confidence;
      finalReason = `URL classification confident: ${urlClassification.reason}`;
    } else if (contentClassification.confidence > 0.6) {
      // Content is confident
      finalType = contentClassification.type;
      finalConfidence = contentClassification.confidence;
      finalReason = `Content classification confident: ${contentClassification.reason}`;
    } else {
      // Use the higher confidence one
      if (urlClassification.confidence >= contentClassification.confidence) {
        finalType = urlClassification.type;
        finalConfidence = urlClassification.confidence;
        finalReason = urlClassification.reason;
      } else {
        finalType = contentClassification.type;
        finalConfidence = contentClassification.confidence;
        finalReason = contentClassification.reason;
      }
    }

    return {
      type: finalType,
      confidence: finalConfidence,
      reason: finalReason,
      details: {
        url: urlClassification,
        content: contentClassification
      }
    };
  }

  // Check if page should be skipped
  shouldSkipPage(url, content = '', title = '') {
    const classification = this.classifyPage(url, content, title);
    
    // Skip if it's a listing page with high confidence
    if (classification.type === 'listing' && classification.confidence > 0.6) {
      return {
        skip: true,
        reason: `Listing page detected: ${classification.reason}`,
        confidence: classification.confidence
      };
    }

    // Skip if unknown with very low confidence (likely error page)
    if (classification.type === 'unknown' && classification.confidence < 0.1) {
      return {
        skip: true,
        reason: 'Unknown page type with very low confidence',
        confidence: classification.confidence
      };
    }

    return {
      skip: false,
      reason: `Property page detected: ${classification.reason}`,
      confidence: classification.confidence
    };
  }

  // Test URLs for classification
  testUrls(urls) {
    console.log('🧪 Testing URL classification...\n');
    
    urls.forEach((url, index) => {
      const classification = this.classifyPage(url);
      const shouldSkip = this.shouldSkipPage(url);
      
      console.log(`${index + 1}. ${url}`);
      console.log(`   Type: ${classification.type} (${(classification.confidence * 100).toFixed(1)}%)`);
      console.log(`   Skip: ${shouldSkip.skip ? '✅ YES' : '❌ NO'}`);
      console.log(`   Reason: ${classification.reason}`);
      console.log('');
    });
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const classifier = new SmartPageClassifier();

  if (command === 'test') {
    const testUrls = [
      // Listing pages (should be skipped)
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/',
      'https://betterplace.cc/buy/properties',
      'https://betterplace.cc/buy/properties?page=2',
      'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/',
      
      // Property pages (should be processed)
      'https://betterplace.cc/buy/properties/BPVL02320',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu_berawa',
      'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/seminyak_center',
      
      // Ambiguous
      'https://bali-home-immo.com/realestate-property/for-rent/other/monthly/ubud_center'
    ];
    
    classifier.testUrls(testUrls);
  } else {
    console.log('Smart Page Classifier');
    console.log('');
    console.log('Commands:');
    console.log('  test - Test classification on sample URLs');
    console.log('');
    console.log('Example: node smart_page_classifier.js test');
  }
}

if (require.main === module) {
  main();
}

module.exports = { SmartPageClassifier };
