// Test URLs directly from the queue using QueueManager
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, properties } = require('./drizzle_client');
const { desc, sql } = require('drizzle-orm');

async function testQueueUrls() {
  console.log('🧪 Testing Queue URLs: 3 URLs per website');
  console.log('='.repeat(60));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  const queueManager = new QueueManager();
  const websites = ['betterplace', 'bali_villa_realty'];
  
  // Get initial count
  const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
  console.log(`📊 Initial properties in database: ${initialCount[0].count}`);
  
  let totalProcessed = 0;
  let totalSuccessful = 0;
  const results = {};
  
  for (const website of websites) {
    console.log(`\n🌐 Testing ${website.toUpperCase()}`);
    console.log('-'.repeat(40));
    
    try {
      console.log(`🔄 Processing 3 URLs from queue...`);
      const startTime = Date.now();
      
      // Process exactly 3 URLs using queue manager
      const result = await queueManager.processQueue(website, 3);
      
      const duration = ((Date.now() - startTime) / 1000).toFixed(1);
      
      if (result && result.processed > 0) {
        console.log(`✅ Completed in ${duration}s:`);
        console.log(`   Processed: ${result.processed}`);
        console.log(`   Successful: ${result.successful || 0}`);
        console.log(`   Failed: ${result.failed || 0}`);
        console.log(`   Skipped: ${result.skipped || 0}`);
        
        totalProcessed += result.processed;
        totalSuccessful += (result.successful || 0);
        
        results[website] = {
          processed: result.processed,
          successful: result.successful || 0,
          failed: result.failed || 0,
          duration: duration
        };
        
        // Show newly created properties with amenities check
        await showRecentProperties(website);
        
      } else {
        console.log(`⚠️  No URLs were processed (${duration}s)`);
        results[website] = {
          processed: 0,
          successful: 0,
          failed: 0,
          duration: duration
        };
      }
      
    } catch (error) {
      console.error(`❌ Error processing ${website}:`, error.message);
      results[website] = {
        processed: 0,
        successful: 0,
        failed: 0,
        error: error.message
      };
    }
    
    // Wait between websites
    if (website !== websites[websites.length - 1]) {
      console.log('⏳ Waiting 10 seconds before next website...');
      await new Promise(resolve => setTimeout(resolve, 10000));
    }
  }
  
  // Final summary
  console.log('\n📊 FINAL SUMMARY');
  console.log('='.repeat(60));
  console.log(`⏰ Completed at: ${new Date().toLocaleTimeString()}`);
  console.log(`📈 Total URLs processed: ${totalProcessed}`);
  console.log(`✅ Total successful: ${totalSuccessful}`);
  console.log(`❌ Total failed: ${totalProcessed - totalSuccessful}`);
  
  if (totalProcessed > 0) {
    console.log(`📊 Success rate: ${((totalSuccessful / totalProcessed) * 100).toFixed(1)}%`);
  }
  
  // Website breakdown
  console.log('\n📋 Results by Website:');
  Object.entries(results).forEach(([website, result]) => {
    console.log(`   ${website.toUpperCase()}:`);
    console.log(`     Processed: ${result.processed}`);
    console.log(`     Successful: ${result.successful}`);
    console.log(`     Failed: ${result.failed}`);
    console.log(`     Duration: ${result.duration}s`);
    if (result.error) {
      console.log(`     Error: ${result.error}`);
    }
  });
  
  // Check final count
  const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
  const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
  console.log(`\n🆕 New properties added to database: ${newProperties}`);
  
  console.log('\n✅ Queue URL test completed!');
  process.exit(0);
}

async function showRecentProperties(website) {
  try {
    // Get the most recent properties for this website (last 2 minutes)
    const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000);
    
    const recentProps = await db
      .select({
        title: properties.title,
        price: properties.price,
        rent_price: properties.rent_price,
        bedrooms: properties.bedrooms,
        city: properties.city,
        amenities: properties.amenities,
        created_at: properties.created_at
      })
      .from(properties)
      .where(sql`${properties.source_id} = ${website} AND ${properties.created_at} > ${twoMinutesAgo}`)
      .orderBy(desc(properties.created_at))
      .limit(5);
    
    if (recentProps.length > 0) {
      console.log(`   🏠 New properties created (${recentProps.length}):`);
      recentProps.forEach((prop, i) => {
        const price = prop.price ? `IDR ${prop.price.toLocaleString()}` : 
                     prop.rent_price ? `IDR ${prop.rent_price.toLocaleString()}/month` : 'Price on request';
        console.log(`      ${i + 1}. ${prop.title.substring(0, 45)}...`);
        console.log(`         💰 ${price} | 🏠 ${prop.bedrooms} bed | 📍 ${prop.city}`);
        
        // Check amenities
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`         🎯 Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        } else {
          console.log(`         ⚠️  No amenities found`);
        }
      });
    } else {
      console.log(`   ℹ️  No new properties found for ${website}`);
    }
  } catch (error) {
    console.log(`   ⚠️  Could not fetch new properties: ${error.message}`);
  }
}

testQueueUrls().catch(error => {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
  process.exit(1);
});
