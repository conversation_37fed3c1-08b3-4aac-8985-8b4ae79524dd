// Cleanup Listing Pages from Database
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { like, or, eq } = require('drizzle-orm');

class ListingPageCleanup {
  constructor() {
    this.listingPatterns = [
      // Bali Home Immo listing patterns
      '/for-rent/villa/monthly/canggu_berawa',
      '/for-rent/villa/monthly/seminyak_center',
      '/for-rent/villa/monthly/ubud_center',
      '/for-rent/villa/yearly/seminyak_center',
      '/for-sale/villa/freehold/seminyak_center',
      '/for-sale/villa/freehold/seminyak_petitenget',
      '/for-sale/villa/freehold/uluwatu_bingin',
      '/for-sale/villa/leasehold/uluwatu_dreamland',
      '/for-rent/other/monthly/jimbaran_center',
      '/for-rent/other/yearly/jimbaran_beach',
      '/for-rent/villa/monthly/jimbaran_hills',
      '/for-rent/villa/yearly/kerobokan_center',
      '/for-sale/villa/freehold/kerobokan_north',
      '/for-sale/villa/leasehold/denpasar_center',
      '/for-rent/other/monthly/sanur_center',
      '/for-rent/other/yearly/nusa_dua',
      
      // Generic location-only patterns
      '/monthly/',
      '/yearly/',
      '/freehold/',
      '/leasehold/',
      '/other/',
      
      // Patterns that end with location only
      'canggu_berawa',
      'seminyak_center',
      'seminyak_petitenget',
      'ubud_center',
      'uluwatu_bingin',
      'uluwatu_dreamland',
      'jimbaran_center',
      'jimbaran_beach',
      'jimbaran_hills',
      'kerobokan_center',
      'kerobokan_north',
      'denpasar_center',
      'sanur_center',
      'nusa_dua'
    ];
  }

  // Check if a URL is a listing page
  isListingPage(url) {
    // Check for obvious listing patterns
    const listingIndicators = [
      // Ends with location only (no property name/ID)
      /\/[a-z_]+$/i,
      // Ends with rental type only
      /\/monthly\/?$/i,
      /\/yearly\/?$/i,
      /\/freehold\/?$/i,
      /\/leasehold\/?$/i,
      /\/other\/?$/i,
      // Contains only category paths without specific property
      /\/for-(rent|sale)\/[^\/]+\/[^\/]+\/[^\/]+\/?$/i
    ];

    return listingIndicators.some(pattern => pattern.test(url));
  }

  // Find listing pages in database
  async findListingPages() {
    console.log('🔍 Scanning database for listing pages...');
    
    const allProperties = await db.select().from(properties);
    console.log(`📊 Total properties in database: ${allProperties.length}`);
    
    const listingPages = [];
    const validProperties = [];
    
    allProperties.forEach(prop => {
      if (this.isListingPage(prop.source_url)) {
        listingPages.push(prop);
      } else {
        validProperties.push(prop);
      }
    });
    
    console.log(`❌ Listing pages found: ${listingPages.length}`);
    console.log(`✅ Valid properties: ${validProperties.length}`);
    
    return { listingPages, validProperties };
  }

  // Show listing pages for review
  async reviewListingPages() {
    const { listingPages, validProperties } = await this.findListingPages();
    
    console.log('\n❌ LISTING PAGES TO REMOVE:');
    listingPages.forEach((prop, index) => {
      console.log(`${index + 1}. ${prop.title.substring(0, 60)}...`);
      console.log(`   URL: ${prop.source_url}`);
      console.log(`   Source: ${prop.source_id}, Created: ${prop.created_at.toISOString().split('T')[0]}`);
      console.log('');
    });
    
    console.log('\n✅ VALID PROPERTIES (sample):');
    validProperties.slice(0, 5).forEach((prop, index) => {
      console.log(`${index + 1}. ${prop.title.substring(0, 60)}...`);
      console.log(`   URL: ${prop.source_url}`);
      console.log('');
    });
    
    return { listingPages, validProperties };
  }

  // Remove listing pages from database
  async removeListingPages() {
    const { listingPages, validProperties } = await this.findListingPages();

    if (listingPages.length === 0) {
      console.log('✅ No listing pages found to remove');
      return { removed: 0, remaining: validProperties.length };
    }

    console.log(`\n🗑️  Removing ${listingPages.length} listing pages...`);

    let removed = 0;
    for (const listingPage of listingPages) {
      try {
        await db.delete(properties).where(eq(properties.id, listingPage.id));
        removed++;
        console.log(`✅ Removed: ${listingPage.title.substring(0, 50)}...`);
      } catch (error) {
        console.log(`❌ Error removing ${listingPage.id}: ${error.message}`);
      }
    }
    
    console.log(`\n🎯 CLEANUP RESULTS:`);
    console.log(`   Removed: ${removed} listing pages`);
    console.log(`   Remaining: ${validProperties.length} valid properties`);
    
    return { removed, remaining: validProperties.length };
  }

  // Clean queue of listing page URLs
  async cleanQueue() {
    console.log('\n🔍 Cleaning queue of listing page URLs...');
    
    const { scrapingQueue } = require('./drizzle_client');
    const { eq } = require('drizzle-orm');
    
    const queueItems = await db.select().from(scrapingQueue);
    console.log(`📊 Total queue items: ${queueItems.length}`);
    
    const listingQueueItems = [];
    const validQueueItems = [];
    
    queueItems.forEach(item => {
      if (this.isListingPage(item.url)) {
        listingQueueItems.push(item);
      } else {
        validQueueItems.push(item);
      }
    });
    
    console.log(`❌ Listing URLs in queue: ${listingQueueItems.length}`);
    console.log(`✅ Valid URLs in queue: ${validQueueItems.length}`);
    
    // Remove listing URLs from queue
    let removedFromQueue = 0;
    for (const listingItem of listingQueueItems) {
      try {
        await db.delete(scrapingQueue).where(eq(scrapingQueue.id, listingItem.id));
        removedFromQueue++;
      } catch (error) {
        console.log(`❌ Error removing queue item: ${error.message}`);
      }
    }
    
    console.log(`🗑️  Removed ${removedFromQueue} listing URLs from queue`);
    
    return { removedFromQueue, validQueueItems: validQueueItems.length };
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const cleanup = new ListingPageCleanup();

  try {
    switch (command) {
      case 'review':
        await cleanup.reviewListingPages();
        break;

      case 'remove':
        await cleanup.removeListingPages();
        break;

      case 'clean-queue':
        await cleanup.cleanQueue();
        break;

      case 'full-cleanup':
        console.log('🚀 Starting full cleanup...');
        await cleanup.removeListingPages();
        await cleanup.cleanQueue();
        console.log('✅ Full cleanup completed');
        break;

      default:
        console.log('Listing Page Cleanup Tool');
        console.log('');
        console.log('Commands:');
        console.log('  review       - Review listing pages without removing');
        console.log('  remove       - Remove listing pages from database');
        console.log('  clean-queue  - Remove listing URLs from queue');
        console.log('  full-cleanup - Remove from both database and queue');
        console.log('');
        console.log('Examples:');
        console.log('  node cleanup_listing_pages.js review');
        console.log('  node cleanup_listing_pages.js full-cleanup');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }

  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = { ListingPageCleanup };
