// Add description column to property table
require('dotenv').config();
const { db } = require('./drizzle_client');
const { sql } = require('drizzle-orm');

async function addDescriptionColumn() {
  console.log('🔧 Adding description column to property table...');
  
  try {
    // Add description column if it doesn't exist
    await db.execute(sql`
      ALTER TABLE property 
      ADD COLUMN IF NOT EXISTS description TEXT
    `);
    
    console.log('✅ Description column added successfully');
    
    // Verify the column was added
    const result = await db.execute(sql`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'property' 
      AND column_name = 'description'
    `);
    
    if (result.length > 0) {
      console.log('✅ Verification successful:');
      console.log(`   Column: ${result[0].column_name}`);
      console.log(`   Type: ${result[0].data_type}`);
      console.log(`   Nullable: ${result[0].is_nullable}`);
    } else {
      console.log('❌ Column verification failed');
    }
    
    // Test insert to make sure it works
    console.log('\n🧪 Testing description column...');
    const testResult = await db.execute(sql`
      SELECT COUNT(*) as count FROM property WHERE description IS NOT NULL
    `);
    
    console.log(`📊 Properties with description: ${testResult[0].count}`);
    
  } catch (error) {
    console.error('❌ Error adding description column:', error.message);
  } finally {
    process.exit(0);
  }
}

addDescriptionColumn();
