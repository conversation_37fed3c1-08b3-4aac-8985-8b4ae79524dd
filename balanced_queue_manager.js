// Balanced Queue Manager - Select equal numbers of URLs from each website
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq, and, sql } = require('drizzle-orm');
const { EfficientBatchScraper } = require('./efficient_batch_scraper');

class BalancedQueueManager {
  constructor() {
    this.websites = ['betterplace', 'bali_home_immo', 'bali_villa_realty', 'villabalisale'];
  }

  async analyzeQueue() {
    console.log('🔍 Analyzing Queue Status by Website');
    console.log('='.repeat(60));

    const analysis = {};
    let totalPending = 0;

    for (const website of this.websites) {
      const counts = await db
        .select({
          pending: sql`COUNT(CASE WHEN status = 'pending' THEN 1 END)`,
          processed: sql`COUNT(CASE WHEN status = 'processed' THEN 1 END)`,
          failed: sql`COUNT(CASE WHEN status = 'failed' THEN 1 END)`,
          archived: sql`COUNT(CASE WHEN status = 'archived' THEN 1 END)`,
          total: sql`COUNT(*)`
        })
        .from(scrapingQueue)
        .where(eq(scrapingQueue.website_id, website));

      const stats = counts[0];
      analysis[website] = {
        pending: parseInt(stats.pending),
        processed: parseInt(stats.processed),
        failed: parseInt(stats.failed),
        archived: parseInt(stats.archived),
        total: parseInt(stats.total)
      };

      totalPending += analysis[website].pending;

      console.log(`📊 ${website.toUpperCase()}:`);
      console.log(`   Pending: ${analysis[website].pending}`);
      console.log(`   Processed: ${analysis[website].processed}`);
      console.log(`   Failed: ${analysis[website].failed}`);
      console.log(`   Archived: ${analysis[website].archived}`);
      console.log(`   Total: ${analysis[website].total}`);
      console.log('');
    }

    console.log(`📈 SUMMARY:`);
    console.log(`   Total pending URLs: ${totalPending}`);
    console.log(`   Available websites: ${this.websites.length}`);
    console.log(`   Average per website: ${Math.floor(totalPending / this.websites.length)}`);

    return analysis;
  }

  async selectBalancedUrls(urlsPerWebsite = 5) {
    console.log(`\n🎯 Selecting ${urlsPerWebsite} URLs per website`);
    console.log('='.repeat(50));

    const selectedUrls = [];
    const results = {
      totalRequested: urlsPerWebsite * this.websites.length,
      totalSelected: 0,
      byWebsite: {}
    };

    for (const website of this.websites) {
      console.log(`\n📦 Selecting URLs from ${website}...`);

      const urls = await db
        .select({
          id: scrapingQueue.id,
          url: scrapingQueue.url,
          website_id: scrapingQueue.website_id,
          priority: scrapingQueue.priority,
          attempts: scrapingQueue.attempts
        })
        .from(scrapingQueue)
        .where(
          and(
            eq(scrapingQueue.website_id, website),
            eq(scrapingQueue.status, 'pending')
          )
        )
        .orderBy(scrapingQueue.priority, scrapingQueue.created_at)
        .limit(urlsPerWebsite);

      results.byWebsite[website] = {
        requested: urlsPerWebsite,
        found: urls.length,
        urls: urls
      };

      console.log(`   Found: ${urls.length}/${urlsPerWebsite} URLs`);

      if (urls.length > 0) {
        selectedUrls.push(...urls);
        results.totalSelected += urls.length;

        // Show sample URLs
        console.log('   Sample URLs:');
        urls.slice(0, 3).forEach((url, i) => {
          console.log(`   ${i + 1}. ${url.url.substring(0, 70)}...`);
        });

        if (urls.length < urlsPerWebsite) {
          console.log(`   ⚠️  Only ${urls.length} pending URLs available (requested ${urlsPerWebsite})`);
        }
      } else {
        console.log(`   ❌ No pending URLs found for ${website}`);
      }
    }

    console.log(`\n📊 SELECTION SUMMARY:`);
    console.log(`   Total requested: ${results.totalRequested} URLs`);
    console.log(`   Total selected: ${results.totalSelected} URLs`);
    console.log(`   Success rate: ${((results.totalSelected / results.totalRequested) * 100).toFixed(1)}%`);

    return { selectedUrls, results };
  }

  async processBalancedBatch(urlsPerWebsite = 5, useEfficientScraper = true) {
    console.log('🚀 Processing Balanced Batch');
    console.log('='.repeat(50));
    console.log(`   URLs per website: ${urlsPerWebsite}`);
    console.log(`   Using efficient scraper: ${useEfficientScraper ? 'Yes' : 'No'}`);
    console.log('');

    try {
      // Analyze current queue
      await this.analyzeQueue();

      // Select balanced URLs
      const { selectedUrls, results } = await this.selectBalancedUrls(urlsPerWebsite);

      if (selectedUrls.length === 0) {
        console.log('❌ No URLs selected for processing');
        return { processed: 0, successful: 0, failed: 0 };
      }

      console.log(`\n🔄 Processing ${selectedUrls.length} selected URLs...`);

      if (useEfficientScraper) {
        // Use the efficient batch scraper
        const scraper = new EfficientBatchScraper();
        
        // Calculate optimal batch size (all URLs in one batch if possible)
        const batchSize = Math.min(selectedUrls.length, 25);
        const maxBatches = Math.ceil(selectedUrls.length / batchSize);
        
        console.log(`   Batch configuration: ${batchSize} URLs per batch, ${maxBatches} batches`);
        
        const scrapingResults = await scraper.processQueueEfficiently(batchSize, maxBatches, selectedUrls);
        
        return {
          processed: scrapingResults.totalProcessed,
          successful: scrapingResults.totalSuccessful,
          failed: scrapingResults.totalFailed,
          byWebsite: results.byWebsite,
          method: 'efficient_batch_scraper'
        };
      } else {
        // Use traditional queue manager (for comparison)
        const { QueueManager } = require('./scrape_worker/queue_manager');
        const queueManager = new QueueManager();
        
        let totalProcessed = 0;
        let totalSuccessful = 0;
        let totalFailed = 0;

        for (const website of this.websites) {
          const websiteUrls = results.byWebsite[website];
          if (websiteUrls.found > 0) {
            console.log(`\n🔄 Processing ${websiteUrls.found} ${website} URLs...`);
            
            const result = await queueManager.processQueue(website, websiteUrls.found);
            
            totalProcessed += result.processed || 0;
            totalSuccessful += result.successful || 0;
            totalFailed += result.failed || 0;
            
            console.log(`   ✅ ${website}: ${result.processed || 0} processed`);
          }
        }

        return {
          processed: totalProcessed,
          successful: totalSuccessful,
          failed: totalFailed,
          byWebsite: results.byWebsite,
          method: 'traditional_queue_manager'
        };
      }

    } catch (error) {
      console.error('❌ Balanced batch processing failed:', error.message);
      throw error;
    }
  }

  async showRecommendations() {
    console.log('\n💡 RECOMMENDATIONS');
    console.log('='.repeat(50));

    const analysis = await this.analyzeQueue();
    
    // Find websites with low pending counts
    const lowPendingWebsites = this.websites.filter(website => 
      analysis[website].pending < 10
    );

    if (lowPendingWebsites.length > 0) {
      console.log('⚠️  Websites with low pending URLs:');
      lowPendingWebsites.forEach(website => {
        console.log(`   ${website}: ${analysis[website].pending} pending URLs`);
      });
      console.log('\n💡 Consider running URL discovery for these websites:');
      console.log('   node scrape_worker/sitemap_parser.js');
      console.log('   node scrape_worker/listing_page_crawler.js');
    }

    // Find optimal batch size
    const minPending = Math.min(...this.websites.map(w => analysis[w].pending));
    const maxBalancedBatch = Math.floor(minPending);
    
    console.log(`\n🎯 Optimal balanced batch sizes:`);
    console.log(`   Maximum balanced: ${maxBalancedBatch} URLs per website`);
    console.log(`   Recommended small: ${Math.min(5, maxBalancedBatch)} URLs per website`);
    console.log(`   Recommended medium: ${Math.min(10, maxBalancedBatch)} URLs per website`);
    console.log(`   Recommended large: ${Math.min(25, maxBalancedBatch)} URLs per website`);
  }
}

// CLI Interface
async function main() {
  const manager = new BalancedQueueManager();
  
  const command = process.argv[2];
  const urlsPerWebsite = parseInt(process.argv[3]) || 5;
  
  try {
    switch (command) {
      case 'analyze':
        await manager.analyzeQueue();
        await manager.showRecommendations();
        break;
        
      case 'select':
        const { selectedUrls, results } = await manager.selectBalancedUrls(urlsPerWebsite);
        console.log(`\n✅ Selected ${selectedUrls.length} URLs total`);
        break;
        
      case 'process':
        const useEfficient = process.argv[4] !== 'traditional';
        const result = await manager.processBalancedBatch(urlsPerWebsite, useEfficient);
        console.log(`\n🎉 Processing completed!`);
        console.log(`   Method: ${result.method}`);
        console.log(`   Processed: ${result.processed}`);
        console.log(`   Successful: ${result.successful}`);
        console.log(`   Failed: ${result.failed}`);
        console.log(`   Success rate: ${result.processed > 0 ? ((result.successful / result.processed) * 100).toFixed(1) : 0}%`);
        break;
        
      default:
        console.log('🎯 Balanced Queue Manager');
        console.log('');
        console.log('Commands:');
        console.log('  analyze                    - Analyze current queue status');
        console.log('  select [count]             - Select balanced URLs (default: 5 per website)');
        console.log('  process [count] [method]   - Process balanced batch (default: 5 per website, efficient)');
        console.log('');
        console.log('Examples:');
        console.log('  node balanced_queue_manager.js analyze');
        console.log('  node balanced_queue_manager.js select 10');
        console.log('  node balanced_queue_manager.js process 5');
        console.log('  node balanced_queue_manager.js process 10 traditional');
        console.log('');
        console.log('Methods:');
        console.log('  efficient (default) - Use EfficientBatchScraper (96% less credits)');
        console.log('  traditional        - Use traditional QueueManager');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    process.exit(0);
  }
}

if (require.main === module) {
  main();
}

module.exports = { BalancedQueueManager };
