// Debug crawl data structure
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function debugCrawlData() {
  console.log('🔍 Debugging crawl data structure...\n');

  const keyManager = getKeyManager();
  
  // Use a completed Firecrawl job ID from our database
  const firecrawlJobId = 'b01d84cd-dc57-4542-9c5a-b76e844eac9f';
  
  try {
    const currentKey = keyManager.getCurrentKey();
    console.log(`🔑 Using key ${currentKey.index + 1}/${keyManager.keys.length}`);
    
    const response = await fetch(`https://api.firecrawl.dev/v1/crawl/${firecrawlJobId}`, {
      headers: {
        'Authorization': `Bearer ${currentKey.key}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${await response.text()}`);
    }

    const data = await response.json();
    
    console.log('📊 Crawl Status Response:');
    console.log(`   Status: ${data.status}`);
    console.log(`   Total: ${data.total}`);
    console.log(`   Completed: ${data.completed}`);
    console.log(`   Credits used: ${data.creditsUsed}`);
    
    if (data.data && data.data.length > 0) {
      console.log(`\n📋 Sample crawl data (first 3 items):`);
      
      for (let i = 0; i < Math.min(3, data.data.length); i++) {
        const item = data.data[i];
        console.log(`\n   Item ${i + 1}:`);
        console.log(`     URL: ${item.url || 'UNDEFINED'}`);
        console.log(`     Has markdown: ${!!item.markdown}`);
        console.log(`     Has html: ${!!item.html}`);
        console.log(`     Markdown length: ${item.markdown ? item.markdown.length : 0}`);
        
        if (item.url) {
          // Test URL classification
          const isPropertyUrl = /\/buy\/properties\/BPVL\d+$/.test(item.url);
          console.log(`     Matches property pattern: ${isPropertyUrl}`);
          
          if (item.markdown) {
            const hasKeywords = ['bedroom', 'bathroom', 'villa', 'price'].some(keyword => 
              item.markdown.toLowerCase().includes(keyword.toLowerCase())
            );
            console.log(`     Has property keywords: ${hasKeywords}`);
          }
        }
        
        // Show first 200 chars of markdown
        if (item.markdown) {
          console.log(`     Markdown preview: ${item.markdown.substring(0, 200)}...`);
        }
      }
      
      // Count URLs that match property patterns
      let propertyUrlCount = 0;
      let urlsWithKeywords = 0;
      
      data.data.forEach(item => {
        if (item.url && /\/buy\/properties\/BPVL\d+$/.test(item.url)) {
          propertyUrlCount++;
        }
        
        if (item.markdown && ['bedroom', 'bathroom', 'villa', 'price'].some(keyword => 
          item.markdown.toLowerCase().includes(keyword.toLowerCase())
        )) {
          urlsWithKeywords++;
        }
      });
      
      console.log(`\n📈 Analysis:`);
      console.log(`   Total URLs: ${data.data.length}`);
      console.log(`   URLs matching property pattern: ${propertyUrlCount}`);
      console.log(`   URLs with property keywords: ${urlsWithKeywords}`);
      
      // Show some actual URLs
      console.log(`\n🔗 Sample URLs:`);
      data.data.slice(0, 10).forEach((item, index) => {
        if (item.url) {
          console.log(`   ${index + 1}. ${item.url}`);
        }
      });
      
    } else {
      console.log('\n❌ No crawl data found');
    }

  } catch (error) {
    console.error('❌ Error debugging crawl data:', error.message);
  } finally {
    process.exit(0);
  }
}

debugCrawlData();
