// Monitor scraping results for all websites
require('dotenv').config();
const { db, properties, scrapingQueue, closeConnection } = require('../drizzle_client');
const { sql, desc, eq } = require('drizzle-orm');

async function monitorAllWebsites() {
  try {
    console.log('🌐 Monitoring All Websites - Real Estate Scraping\n');
    console.log('='.repeat(80));
    
    // Get total counts
    const totalProperties = await db
      .select({ count: sql`count(*)` })
      .from(properties);
    
    const totalQueue = await db
      .select({ count: sql`count(*)` })
      .from(scrapingQueue);
    
    console.log(`📊 OVERALL STATUS:`);
    console.log(`   📈 Total Properties in Database: ${totalProperties[0].count}`);
    console.log(`   🔄 Remaining URLs in Queue: ${totalQueue[0].count}`);
    console.log(`   📉 Progress: ${((14835 - totalQueue[0].count) / 14835 * 100).toFixed(1)}% complete`);
    
    // Get properties by source
    const sourceStats = await db
      .select({
        source: properties.source,
        count: sql`count(*)`.as('count'),
        avg_price: sql`avg(case when price > 0 then price end)`.as('avg_price'),
        avg_rent: sql`avg(case when rent_price > 0 then rent_price end)`.as('avg_rent'),
        with_bedrooms: sql`count(case when bedrooms is not null then 1 end)`.as('with_bedrooms'),
        with_location: sql`count(case when city is not null then 1 end)`.as('with_location')
      })
      .from(properties)
      .groupBy(properties.source)
      .orderBy(sql`count(*) DESC`);
    
    console.log('\n🌐 RESULTS BY WEBSITE:');
    console.log('='.repeat(80));
    
    for (const stats of sourceStats) {
      const websiteName = getWebsiteName(stats.source);
      console.log(`\n🏠 ${websiteName.toUpperCase()}`);
      console.log(`   📊 Properties: ${stats.count}`);
      
      // Price analysis
      if (stats.avg_price) {
        console.log(`   💰 Avg Sale Price: IDR ${Math.round(stats.avg_price).toLocaleString()}`);
      }
      if (stats.avg_rent) {
        console.log(`   🏡 Avg Rent Price: IDR ${Math.round(stats.avg_rent).toLocaleString()}/month`);
      }
      
      // Quality metrics
      const bedroomRate = ((stats.with_bedrooms / stats.count) * 100).toFixed(1);
      const locationRate = ((stats.with_location / stats.count) * 100).toFixed(1);
      
      console.log(`   🏠 Bedrooms Data: ${stats.with_bedrooms}/${stats.count} (${bedroomRate}%)`);
      console.log(`   📍 Location Data: ${stats.with_location}/${stats.count} (${locationRate}%)`);
      
      // Quality assessment
      const avgQuality = (parseFloat(bedroomRate) + parseFloat(locationRate)) / 2;
      const qualityStatus = avgQuality >= 80 ? '🎉 EXCELLENT' : avgQuality >= 60 ? '✅ GOOD' : '⚠️ NEEDS WORK';
      console.log(`   📈 Data Quality: ${avgQuality.toFixed(1)}% ${qualityStatus}`);
    }
    
    // Get remaining queue by website
    const queueStats = await db
      .select({
        website_id: scrapingQueue.website_id,
        count: sql`count(*)`.as('count')
      })
      .from(scrapingQueue)
      .groupBy(scrapingQueue.website_id)
      .orderBy(sql`count(*) DESC`);
    
    console.log('\n🔄 REMAINING QUEUE BY WEBSITE:');
    console.log('='.repeat(50));
    
    queueStats.forEach(queue => {
      const websiteName = getWebsiteName(queue.website_id);
      const progress = ((getOriginalCount(queue.website_id) - queue.count) / getOriginalCount(queue.website_id) * 100).toFixed(1);
      console.log(`   ${websiteName}: ${queue.count} URLs remaining (${progress}% done)`);
    });
    
    // Get latest properties from each website
    console.log('\n🔍 LATEST PROPERTIES BY WEBSITE:');
    console.log('='.repeat(80));
    
    for (const stats of sourceStats) {
      const latestProps = await db
        .select({
          title: properties.title,
          price: properties.price,
          rent_price: properties.rent_price,
          bedrooms: properties.bedrooms,
          city: properties.city,
          created_at: properties.created_at
        })
        .from(properties)
        .where(eq(properties.source, stats.source))
        .orderBy(desc(properties.created_at))
        .limit(3);
      
      const websiteName = getWebsiteName(stats.source);
      console.log(`\n🏠 ${websiteName.toUpperCase()} - Latest 3 Properties:`);
      
      latestProps.forEach((prop, i) => {
        console.log(`   ${i + 1}. ${prop.title?.substring(0, 60)}...`);
        
        if (prop.price) {
          console.log(`      💰 Sale: IDR ${prop.price.toLocaleString()}`);
        } else if (prop.rent_price) {
          console.log(`      🏡 Rent: IDR ${prop.rent_price.toLocaleString()}/month`);
        }
        
        console.log(`      🏠 ${prop.bedrooms || 'N/A'} bed | 📍 ${prop.city || 'N/A'} | ⏰ ${prop.created_at?.toLocaleString()}`);
      });
    }
    
    // Recent activity check
    const recentActivity = await db
      .select({
        source: properties.source,
        count: sql`count(*)`.as('count')
      })
      .from(properties)
      .where(sql`created_at > NOW() - INTERVAL '10 minutes'`)
      .groupBy(properties.source);
    
    console.log('\n⏱️ RECENT ACTIVITY (Last 10 minutes):');
    console.log('='.repeat(50));
    
    if (recentActivity.length > 0) {
      recentActivity.forEach(activity => {
        const websiteName = getWebsiteName(activity.source);
        console.log(`   ✅ ${websiteName}: +${activity.count} new properties`);
      });
      console.log('\n🚀 Scraping is actively running!');
    } else {
      console.log('   ⚠️ No recent activity - check if scraping is still running');
    }
    
    // Overall assessment
    console.log('\n🎯 OVERALL ASSESSMENT:');
    console.log('='.repeat(50));
    
    const totalProcessed = 14835 - totalQueue[0].count;
    const processingRate = totalProcessed / 10; // assuming 10 minutes elapsed
    const estimatedCompletion = totalQueue[0].count / processingRate;
    
    console.log(`   📊 Processed: ${totalProcessed}/14,835 URLs`);
    console.log(`   ⚡ Rate: ~${processingRate.toFixed(1)} URLs/minute`);
    console.log(`   ⏱️ ETA: ~${estimatedCompletion.toFixed(0)} minutes remaining`);
    
    if (totalProperties[0].count >= 100) {
      console.log('   🎉 EXCELLENT: Database is filling up nicely!');
    } else if (totalProperties[0].count >= 50) {
      console.log('   ✅ GOOD: Steady progress being made');
    } else {
      console.log('   ⚠️ SLOW: Check if all websites are processing correctly');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    closeConnection();
  }
}

function getWebsiteName(source) {
  const names = {
    'bali_villa_realty': 'Bali Villa Realty',
    'betterplace': 'BetterPlace',
    'bali_home_immo': 'Bali Home Immo',
    'villabalisale': 'Villa Bali Sale',
    'villabalisale.com': 'Villa Bali Sale',
    'bali_coconut_living': 'Bali Coconut Living'
  };
  return names[source] || source;
}

function getOriginalCount(websiteId) {
  const counts = {
    'bali_home_immo': 8373,
    'villabalisale.com': 2867,
    'betterplace': 2382,
    'bali_villa_realty': 1149,
    'bali_coconut_living': 64
  };
  return counts[websiteId] || 1000;
}

monitorAllWebsites();
