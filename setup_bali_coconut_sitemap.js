// Setup Bali Coconut Living for Sitemap Crawling
require('dotenv').config();

const { db, websiteConfigs } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function setupBaliCoconutSitemap() {
  console.log('🚀 Setting up Bali Coconut Living for Sitemap Crawling');
  console.log('='.repeat(60));

  try {
    // Check if website already exists
    const existing = await db
      .select()
      .from(websiteConfigs)
      .where(eq(websiteConfigs.website_id, 'bali_coconut_living'))
      .limit(1);

    if (existing.length > 0) {
      console.log('⚠️  Website already exists, updating configuration...');
      
      // Update existing configuration
      await db
        .update(websiteConfigs)
        .set({
          name: 'Bali Coconut Living',
          base_url: 'https://balicoconutliving.com',
          sitemap_urls: JSON.stringify(['https://balicoconutliving.com/sitemap.xml']),
          sitemap_enabled: true,
          sitemap_check_frequency_hours: 6,
          property_url_patterns: JSON.stringify({
            sale: ['/bali-villa-sale-freehold/', '/bali-villa-sale-leasehold/', '/bali-land-sale-'],
            rent: ['/bali-villa-yearly-rental/', '/bali-villa-monthly-rental/'],
            listing: ['/bali-villa-', '/property/']
          }),
          sitemap_filters: JSON.stringify({
            include_patterns: [
              '/bali-villa-sale-',
              '/bali-villa-rental-',
              '/bali-villa-yearly-rental/',
              '/bali-villa-monthly-rental/',
              '/bali-land-sale-'
            ],
            exclude_patterns: [
              '/blog/',
              '/news/',
              '/about/',
              '/contact/',
              '/services/'
            ]
          }),
          crawl_frequency_hours: 24,
          max_pages_per_crawl: 100,
          is_active: true,
          updated_at: new Date()
        })
        .where(eq(websiteConfigs.website_id, 'bali_coconut_living'));

      console.log('✅ Updated existing website configuration');

    } else {
      console.log('➕ Adding new website configuration...');
      
      // Insert new configuration
      await db.insert(websiteConfigs).values({
        website_id: 'bali_coconut_living',
        name: 'Bali Coconut Living',
        base_url: 'https://balicoconutliving.com',
        sitemap_urls: JSON.stringify(['https://balicoconutliving.com/sitemap.xml']),
        sitemap_enabled: true,
        sitemap_check_frequency_hours: 6,
        property_url_patterns: JSON.stringify({
          sale: ['/bali-villa-sale-freehold/', '/bali-villa-sale-leasehold/', '/bali-land-sale-'],
          rent: ['/bali-villa-yearly-rental/', '/bali-villa-monthly-rental/'],
          listing: ['/bali-villa-', '/property/']
        }),
        sitemap_filters: JSON.stringify({
          include_patterns: [
            '/bali-villa-sale-',
            '/bali-villa-rental-',
            '/bali-villa-yearly-rental/',
            '/bali-villa-monthly-rental/',
            '/bali-land-sale-'
          ],
          exclude_patterns: [
            '/blog/',
            '/news/',
            '/about/',
            '/contact/',
            '/services/'
          ]
        }),
        crawl_patterns: JSON.stringify({
          property_page_selectors: [
            'h1',
            '.property-title',
            '.villa-title'
          ],
          listing_page_selectors: [
            '.property-list',
            '.villa-grid'
          ]
        }),
        crawl_frequency_hours: 24,
        max_pages_per_crawl: 100,
        is_active: true,
        last_crawl_at: null,
        next_crawl_at: new Date(),
        last_sitemap_check: null,
        created_at: new Date(),
        updated_at: new Date()
      });

      console.log('✅ Added new website configuration');
    }

    // Verify configuration
    const config = await db
      .select()
      .from(websiteConfigs)
      .where(eq(websiteConfigs.website_id, 'bali_coconut_living'))
      .limit(1);

    if (config.length > 0) {
      const cfg = config[0];
      console.log('\n📋 Configuration Summary:');
      console.log(`   Website ID: ${cfg.website_id}`);
      console.log(`   Name: ${cfg.name}`);
      console.log(`   Base URL: ${cfg.base_url}`);
      console.log(`   Sitemap Enabled: ${cfg.sitemap_enabled}`);
      console.log(`   Sitemap URLs: ${cfg.sitemap_urls}`);
      console.log(`   Check Frequency: ${cfg.sitemap_check_frequency_hours} hours`);
      console.log(`   Active: ${cfg.is_active}`);
      console.log(`   Last Sitemap Check: ${cfg.last_sitemap_check || 'Never'}`);
      console.log(`   Next Crawl: ${cfg.next_crawl_at}`);

      console.log('\n🎯 URL Patterns:');
      try {
        const patterns = typeof cfg.property_url_patterns === 'string'
          ? JSON.parse(cfg.property_url_patterns)
          : cfg.property_url_patterns || {};
        Object.entries(patterns).forEach(([type, urls]) => {
          console.log(`   ${type}: ${Array.isArray(urls) ? urls.join(', ') : urls}`);
        });
      } catch (e) {
        console.log(`   Error parsing patterns: ${e.message}`);
      }

      console.log('\n🔍 Sitemap Filters:');
      try {
        const filters = typeof cfg.sitemap_filters === 'string'
          ? JSON.parse(cfg.sitemap_filters)
          : cfg.sitemap_filters || {};
        if (filters.include_patterns) {
          console.log(`   Include: ${filters.include_patterns.join(', ')}`);
        }
        if (filters.exclude_patterns) {
          console.log(`   Exclude: ${filters.exclude_patterns.join(', ')}`);
        }
      } catch (e) {
        console.log(`   Error parsing filters: ${e.message}`);
      }
    }

    console.log('\n🎉 Bali Coconut Living is now configured for automatic sitemap crawling!');
    console.log('\n📅 Cronjob Schedule:');
    console.log('   - Sitemap check: Every 6 hours');
    console.log('   - Daily crawl: 02:00 UTC (via /api/cron/crawl-websites)');
    console.log('   - Queue processing: Every 30 minutes (via /api/cron/process-queue)');

  } catch (error) {
    console.error('❌ Error setting up sitemap configuration:', error.message);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  setupBaliCoconutSitemap()
    .then(() => {
      console.log('\n✅ Setup completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Setup failed:', error.message);
      process.exit(1);
    });
}

module.exports = { setupBaliCoconutSitemap };
