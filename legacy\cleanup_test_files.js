// Cleanup script to move test, debug, and analysis files to legacy folder
const fs = require('fs');
const path = require('path');

const testFiles = [
  // Analysis files
  'analyze_bali_home_immo_markdown.js',
  'analyze_betterplace_markdown.js',
  'analyze_markdown.js',
  'analyze_markdown_content.js',
  'analyze_property_table.js',
  'analyze_scraped_properties_detailed.js',
  'analyze_villa_bali_sale_issues.js',
  'analyze_villa_bali_sale_markdown.js',
  
  // Debug files
  'debug_bali_home_immo.js',
  'debug_bali_villa_realty.js',
  'debug_betterplace_bathroom.js',
  'debug_betterplace_bedrooms_bug.js',
  'debug_land_size.js',
  'debug_mapper_extraction.js',
  'debug_villa_bali_sale_data.js',
  'debug_villa_bali_sale_leasehold_years.js',
  'debug_villa_bali_sale_lot_size.js',
  'debug_villa_bali_sale_missing_fields.js',
  'debug_villa_bali_sale_size.js',
  
  // Test files
  'test_betterplace_bedroom_fix.js',
  'test_fixed_scraping.js',
  'test_queue_manager_direct.js',
  'test_queue_manager_simple.js',
  'test_queue_manager_villabalisale.js',
  'test_queue_processing.js',
  'test_queue_urls.js',
  'test_real_villabalisale_urls.js',
  'test_single_betterplace.js',
  'test_single_url.js',
  'test_url_extraction.js',
  'test_villa_bali_sale.js',
  'test_villa_bali_sale_enhanced.js',
  'test_villa_bali_sale_leasehold.js',
  'test_villabalisale_fixes.js',
  'test_villabalisale_mapper_fixes.js',
  'test_villabalisale_queue.js',
  'test_villabalisale_scraping.js',
  
  // Check files
  'check_latest_property.js',
  'check_property.js',
  'check_recent_database.js',
  'check_specific_property.js',
  'check_villa_bali_sale_queue.js',
  
  // Investigation files
  'investigate_database_issues.js',
  'investigate_specific_properties.js',
  
  // Direct test files
  'direct_mapper_test.js',
  'direct_url_crawler.js',
  'quick_mapper_test.js',
  'quick_test_all_sites.js',
  
  // Verification files
  'verify_property_specs.js',
  
  // Find files
  'find_successful_bali_home_immo.js'
];

function moveFilesToLegacy() {
  console.log('🧹 Cleaning up test, debug, and analysis files');
  console.log('='.repeat(50));
  
  // Ensure legacy directory exists
  const legacyDir = path.join(__dirname, 'legacy');
  if (!fs.existsSync(legacyDir)) {
    fs.mkdirSync(legacyDir, { recursive: true });
  }
  
  // Create subdirectories in legacy
  const subdirs = ['tests', 'debug', 'analysis'];
  subdirs.forEach(subdir => {
    const subdirPath = path.join(legacyDir, subdir);
    if (!fs.existsSync(subdirPath)) {
      fs.mkdirSync(subdirPath, { recursive: true });
    }
  });
  
  let movedCount = 0;
  let notFoundCount = 0;
  
  testFiles.forEach(filename => {
    const sourcePath = path.join(__dirname, filename);
    
    if (fs.existsSync(sourcePath)) {
      // Determine target subdirectory
      let targetSubdir = 'tests';
      if (filename.startsWith('debug_')) {
        targetSubdir = 'debug';
      } else if (filename.startsWith('analyze_') || filename.startsWith('investigate_') || filename.startsWith('verify_') || filename.startsWith('check_') || filename.startsWith('find_')) {
        targetSubdir = 'analysis';
      }
      
      const targetPath = path.join(legacyDir, targetSubdir, filename);
      
      try {
        fs.renameSync(sourcePath, targetPath);
        console.log(`✅ Moved: ${filename} → legacy/${targetSubdir}/`);
        movedCount++;
      } catch (error) {
        console.log(`❌ Failed to move ${filename}: ${error.message}`);
      }
    } else {
      console.log(`⚠️  Not found: ${filename}`);
      notFoundCount++;
    }
  });
  
  console.log('\n📊 Summary:');
  console.log(`✅ Files moved: ${movedCount}`);
  console.log(`⚠️  Files not found: ${notFoundCount}`);
  console.log(`📁 Total files processed: ${testFiles.length}`);
  
  console.log('\n📂 Legacy directory structure:');
  console.log('legacy/');
  console.log('├── tests/     - Test files');
  console.log('├── debug/     - Debug files');
  console.log('└── analysis/  - Analysis and check files');
  
  console.log('\n🎉 Cleanup completed!');
}

moveFilesToLegacy();
