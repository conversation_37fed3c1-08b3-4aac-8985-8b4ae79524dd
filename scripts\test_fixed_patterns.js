// Test fixed URL patterns for Bali Coconut Living and Bali Villa Realty
require('dotenv').config();
const { SmartCrawler } = require('../scrape_worker/smart_crawler');
const { db, discoveredUrls, scrapingQueue, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function testFixedPatterns() {
  console.log('🔧 Testing Fixed URL Patterns for Bali Coconut Living and Bali Villa Realty\n');
  
  const websites = [
    { id: 'bali_coconut_living', name: 'Bali Coconut Living' },
    { id: 'bali_villa_realty', name: 'Bali Villa Realty' }
  ];
  
  const crawler = new SmartCrawler();
  
  try {
    for (const website of websites) {
      console.log(`${'='.repeat(70)}`);
      console.log(`🌐 Testing ${website.name} (${website.id})`);
      console.log(`${'='.repeat(70)}`);
      
      // Show initial status
      const initialUrls = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls WHERE website_id = ${website.id}`);
      const initialQueue = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue WHERE website_id = ${website.id}`);
      console.log(`📊 Initial Status:`);
      console.log(`   🔗 Discovered URLs: ${initialUrls[0]?.count || 0}`);
      console.log(`   📋 Scraping Queue: ${initialQueue[0]?.count || 0}`);
      
      // Crawl website
      console.log(`\n🕷️  Crawling ${website.name} with fixed patterns...`);
      const result = await crawler.startWebsiteCrawl(website.id);
      
      if (result) {
        console.log('✅ Crawling completed successfully:');
        console.log(`   📊 Type: ${result.type}`);
        console.log(`   🔍 Total discovered: ${result.totalDiscovered || 0}`);
        console.log(`   ✨ New URLs added: ${result.newUrls || 0}`);
        console.log(`   🗺️  Sitemaps processed: ${result.sitemapUrls || 0}`);
      } else {
        console.log('⏭️  Crawling skipped (not due for processing)');
        continue;
      }
      
      // Show final status
      const finalUrls = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls WHERE website_id = ${website.id}`);
      const finalQueue = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue WHERE website_id = ${website.id}`);
      console.log(`\n📊 Final Status:`);
      console.log(`   🔗 Discovered URLs: ${finalUrls[0]?.count || 0}`);
      console.log(`   📋 Scraping Queue: ${finalQueue[0]?.count || 0}`);
      
      // Classification analysis
      console.log('\n🔍 Classification Analysis:');
      const classificationStats = await db.execute(sql`
        SELECT 
          is_property_page,
          url_type,
          COUNT(*) as count,
          AVG(confidence_score) as avg_confidence
        FROM discovered_urls 
        WHERE website_id = ${website.id}
        GROUP BY is_property_page, url_type
        ORDER BY is_property_page DESC, url_type
      `);
      
      classificationStats.forEach(stat => {
        const type = stat.is_property_page ? '🏠 Property' : (stat.url_type === 'listing' ? '📋 Listing' : '📄 Other');
        console.log(`   ${type}: ${stat.count} URLs (avg confidence: ${parseFloat(stat.avg_confidence).toFixed(2)})`);
      });
      
      // Sample property URLs
      const propertyUrls = await db.execute(sql`
        SELECT url, confidence_score, classification_reason
        FROM discovered_urls 
        WHERE website_id = ${website.id} AND is_property_page = true
        ORDER BY confidence_score DESC
        LIMIT 5
      `);
      
      if (propertyUrls.length > 0) {
        console.log('\n🏠 Sample Property URLs:');
        propertyUrls.forEach((url, i) => {
          console.log(`   ${i + 1}. ${url.url}`);
          console.log(`      📊 Confidence: ${url.confidence_score}, Reason: ${url.classification_reason}`);
        });
      } else {
        console.log('\n❌ No property URLs found!');
      }
      
      // Sample listing URLs
      const listingUrls = await db.execute(sql`
        SELECT url, confidence_score, classification_reason
        FROM discovered_urls 
        WHERE website_id = ${website.id} AND is_property_page = false AND url_type = 'listing'
        ORDER BY confidence_score DESC
        LIMIT 3
      `);
      
      if (listingUrls.length > 0) {
        console.log('\n📋 Sample Listing URLs:');
        listingUrls.forEach((url, i) => {
          console.log(`   ${i + 1}. ${url.url}`);
          console.log(`      📊 Confidence: ${url.confidence_score}, Reason: ${url.classification_reason}`);
        });
      }
      
      // Evaluation
      const totalDiscovered = parseInt(finalUrls[0]?.count || 0);
      const totalQueued = parseInt(finalQueue[0]?.count || 0);
      const propertyCount = propertyUrls.length;
      
      console.log('\n🎯 Evaluation:');
      if (totalDiscovered > 0) {
        const propertyRatio = Math.round((propertyCount / totalDiscovered) * 100);
        console.log(`   📈 Property ratio: ${propertyRatio}%`);
        
        if (website.id === 'bali_coconut_living') {
          if (propertyRatio >= 10 && propertyRatio <= 30) {
            console.log('   ✅ Good property ratio for Bali Coconut Living');
          } else if (propertyRatio < 10) {
            console.log('   ⚠️  Low property ratio - patterns may be too strict');
          } else {
            console.log('   ⚠️  High property ratio - patterns may be too loose');
          }
        } else if (website.id === 'bali_villa_realty') {
          if (propertyRatio >= 70 && propertyRatio <= 95) {
            console.log('   ✅ Good property ratio for Bali Villa Realty');
          } else if (propertyRatio < 70) {
            console.log('   ⚠️  Low property ratio - patterns may be too strict');
          } else {
            console.log('   ⚠️  High property ratio - patterns may be too loose');
          }
        }
      }
      
      if (totalQueued === propertyCount) {
        console.log('   ✅ All property URLs correctly added to queue');
      } else {
        console.log(`   ⚠️  Queue mismatch: ${totalQueued} queued vs ${propertyCount} properties`);
      }
      
      console.log('\n');
    }
    
    // Overall summary
    console.log(`${'='.repeat(70)}`);
    console.log('🏆 OVERALL RESULTS');
    console.log(`${'='.repeat(70)}`);
    
    const totalUrls = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls`);
    const totalQueue = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue`);
    
    console.log(`\n📊 Total Results:`);
    console.log(`   🔗 Total URLs discovered: ${totalUrls[0]?.count || 0}`);
    console.log(`   📋 Total URLs in queue: ${totalQueue[0]?.count || 0}`);
    
    // Check for expected property URL patterns
    console.log('\n🔍 Pattern Validation:');
    
    // Bali Coconut Living expected patterns
    const coconutPatterns = await db.execute(sql`
      SELECT COUNT(*) as count
      FROM discovered_urls 
      WHERE website_id = 'bali_coconut_living' 
        AND is_property_page = true 
        AND (url LIKE '%/bali-villa-sale-freehold/%/%/%' 
             OR url LIKE '%/bali-villa-yearly-rental/%/%/%'
             OR url LIKE '%/bali-villa-monthly-rental/%/%/%'
             OR url LIKE '%/bali-villa-sale-leasehold/%/%/%')
    `);
    
    console.log(`   🥥 Bali Coconut Living: ${coconutPatterns[0]?.count || 0} URLs match expected patterns`);
    
    // Bali Villa Realty expected patterns
    const villaRealtyPatterns = await db.execute(sql`
      SELECT COUNT(*) as count
      FROM discovered_urls 
      WHERE website_id = 'bali_villa_realty' 
        AND is_property_page = true 
        AND (url LIKE '%/property/%-villa-%' 
             OR url LIKE '%/property/%-bedroom%'
             OR url LIKE '%/property/%-for-sale-%'
             OR url LIKE '%/property/%-for-rental-%')
    `);
    
    console.log(`   🏠 Bali Villa Realty: ${villaRealtyPatterns[0]?.count || 0} URLs match expected patterns`);
    
    if ((coconutPatterns[0]?.count || 0) > 0 && (villaRealtyPatterns[0]?.count || 0) > 0) {
      console.log('\n🎉 SUCCESS! Both websites now have properly classified property URLs!');
      console.log('✅ Ready to proceed with full crawling of all 5 websites');
    } else {
      console.log('\n⚠️  Some patterns still need adjustment');
      if ((coconutPatterns[0]?.count || 0) === 0) {
        console.log('❌ Bali Coconut Living patterns not working');
      }
      if ((villaRealtyPatterns[0]?.count || 0) === 0) {
        console.log('❌ Bali Villa Realty patterns not working');
      }
    }
    
    console.log('\n🎉 Pattern testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test
testFixedPatterns()
  .then(() => {
    console.log('✅ Pattern testing completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
