// Test Villa Bali Sale leasehold duration extraction
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testVillaBaliSaleLeasehold() {
  console.log('🔍 Testing Villa Bali Sale Leasehold Duration Extraction');
  console.log('='.repeat(60));
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold/amed/mediteranian-style-off-plan-villa-in-amed-for-sale';
  
  console.log(`📡 Testing URL: ${testUrl}`);
  console.log('');
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('villabalisale', [testUrl], {});
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);
    
    console.log(`⏱️ Duration: ${duration}s`);
    
    if (results && results.processedResults && results.processedResults.length > 0) {
      const result = results.processedResults[0];
      if (result && result.ok && result.data) {
        const prop = result.data;
        
        console.log('\n📊 Extracted Data:');
        console.log(`Title: "${prop.title}"`);
        console.log(`Bedrooms: ${prop.bedrooms}`);
        console.log(`Bathrooms: ${prop.bathrooms}`);
        console.log(`Building Size: ${prop.size_sqft} sqft`);
        console.log(`Lot Size: ${prop.lot_size_sqft} sqft`);
        console.log(`Year Built: ${prop.year_built}`);
        console.log(`Ownership: ${prop.ownership_type}`);
        console.log(`Price: ${prop.price || prop.rent_price}`);
        console.log(`Location: ${prop.city}, ${prop.state}`);
        
        // Focus on leasehold duration
        console.log('\n🏛️ LEASEHOLD DURATION:');
        console.log(`Lease Duration (years): ${prop.lease_duration_years} ${prop.lease_duration_years ? '✅ EXTRACTED!' : '❌ MISSING!'}`);
        console.log(`Lease Duration (text): ${prop.lease_duration_text} ${prop.lease_duration_text ? '✅ EXTRACTED!' : '❌ MISSING!'}`);
        
        if (prop.lease_duration_years === 27) {
          console.log('\n🎉 SUCCESS! Correctly extracted 27 years from "Leasehold / 27 years"');
        } else if (prop.lease_duration_years) {
          console.log(`\n⚠️ PARTIAL SUCCESS: Extracted ${prop.lease_duration_years} years (expected 27)`);
        } else {
          console.log('\n❌ FAILED: No leasehold duration extracted');
        }
        
        // Check amenities
        console.log('\n🏖️ Amenities:');
        if (prop.amenities && prop.amenities.raw_amenities) {
          const amenities = prop.amenities.raw_amenities;
          console.log(`Count: ${amenities.length}`);
          amenities.slice(0, 3).forEach(amenity => {
            console.log(`   - ${amenity}`);
          });
        }
        
        // Check images
        console.log('\n📸 Images:');
        if (prop.media && prop.media.images) {
          console.log(`Count: ${prop.media.images.length}`);
        }
        
        if (prop.description) {
          console.log(`\nDescription: ${prop.description.substring(0, 100)}...`);
        }
        
        console.log('\n✅ Villa Bali Sale leasehold test complete!');
        
        // Summary of all features
        console.log('\n📋 All Features Summary:');
        console.log(`✅ Title: ${prop.title ? 'EXTRACTED' : 'MISSING'}`);
        console.log(`✅ Bedrooms: ${prop.bedrooms ? 'EXTRACTED' : 'MISSING'}`);
        console.log(`✅ Bathrooms: ${prop.bathrooms ? 'EXTRACTED' : 'MISSING'}`);
        console.log(`✅ Building Size: ${prop.size_sqft ? 'EXTRACTED' : 'MISSING'}`);
        console.log(`✅ Lot Size: ${prop.lot_size_sqft ? 'EXTRACTED' : 'MISSING'}`);
        console.log(`✅ Year Built: ${prop.year_built ? 'EXTRACTED' : 'MISSING'}`);
        console.log(`✅ Ownership: ${prop.ownership_type ? 'EXTRACTED' : 'MISSING'}`);
        console.log(`✅ Lease Duration: ${prop.lease_duration_years ? 'EXTRACTED' : 'MISSING'} ← NEW!`);
        console.log(`✅ Amenities: ${prop.amenities?.raw_amenities?.length || 0} items`);
        console.log(`✅ Images: ${prop.media?.images?.length || 0} photos`);
        console.log(`✅ External ID: ${prop.media?.external_id ? 'EXTRACTED' : 'MISSING'}`);
        console.log(`✅ Price: ${prop.price || prop.rent_price ? 'EXTRACTED' : 'MISSING'}`);
        
      } else {
        console.log('❌ No valid data extracted');
        console.log('Result:', JSON.stringify(result, null, 2));
      }
    } else {
      console.log('❌ No results returned');
      console.log('Results:', JSON.stringify(results, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testVillaBaliSaleLeasehold().then(() => process.exit(0));
