// Smart Scraping System - Main orchestrator
require('dotenv').config();
const { SmartCrawler } = require('./scrape_worker/smart_crawler');
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, websiteConfigs, crawlJobs, scrapingQueue } = require('./drizzle_client');
const { eq, and, lte } = require('drizzle-orm');

class SmartScrapingSystem {
  constructor() {
    this.crawler = new SmartCrawler();
    this.queueManager = new QueueManager();
    this.isRunning = false;
    console.log('🤖 Smart Scraping System initialized');
  }

  // Start the complete system
  async start() {
    console.log('🚀 Starting Smart Scraping System...\n');
    
    this.isRunning = true;
    
    // Start queue manager
    this.queueManager.start();
    
    // Start periodic crawling
    this.startPeriodicCrawling();
    
    console.log('✅ Smart Scraping System is running');
    console.log('   📋 Queue Manager: Active');
    console.log('   🕷️  Periodic Crawler: Active');
    console.log('   ⏰ Checking for new crawls every 10 minutes\n');
  }

  // Stop the system
  async stop() {
    console.log('⏹️  Stopping Smart Scraping System...');
    
    this.isRunning = false;
    this.queueManager.stop();
    
    if (this.crawlIntervalId) {
      clearInterval(this.crawlIntervalId);
      this.crawlIntervalId = null;
    }
    
    console.log('✅ Smart Scraping System stopped');
  }

  // Start periodic crawling
  startPeriodicCrawling() {
    // Check for websites that need crawling every 10 minutes
    this.crawlIntervalId = setInterval(async () => {
      if (this.isRunning) {
        await this.checkAndStartCrawls();
      }
    }, 10 * 60 * 1000); // 10 minutes

    // Also check immediately
    setTimeout(() => this.checkAndStartCrawls(), 5000);
  }

  // Check which websites need crawling and start them
  async checkAndStartCrawls() {
    try {
      console.log('🔍 Checking for websites that need crawling...');
      
      const now = new Date();
      const websitesToCrawl = await db.select()
        .from(websiteConfigs)
        .where(
          and(
            eq(websiteConfigs.is_active, true),
            lte(websiteConfigs.next_crawl_at, now)
          )
        );

      if (websitesToCrawl.length === 0) {
        console.log('📭 No websites need crawling at this time');
        return;
      }

      console.log(`🕷️  Found ${websitesToCrawl.length} websites that need crawling`);

      for (const website of websitesToCrawl) {
        try {
          console.log(`\n🌐 Starting crawl for ${website.name} (${website.website_id})`);
          const result = await this.crawler.startWebsiteCrawl(website.website_id);
          
          if (result) {
            console.log(`✅ Started crawl job ${result.jobId} for ${website.name}`);
            
            // Monitor this crawl job
            this.monitorCrawlJob(result.jobId);
          }
          
        } catch (error) {
          console.error(`❌ Failed to start crawl for ${website.website_id}:`, error.message);
        }
      }

    } catch (error) {
      console.error('❌ Error checking for crawls:', error.message);
    }
  }

  // Monitor a crawl job until completion
  async monitorCrawlJob(jobId) {
    const checkInterval = 30000; // Check every 30 seconds
    let attempts = 0;
    const maxAttempts = 120; // Max 1 hour of monitoring

    const monitor = async () => {
      try {
        attempts++;
        const status = await this.crawler.checkCrawlStatus(jobId);
        
        console.log(`📊 Crawl ${jobId}: ${status.status} (${status.completed}/${status.total})`);

        if (status.status === 'completed') {
          console.log(`✅ Crawl ${jobId} completed successfully`);
          return;
        } else if (status.status === 'failed') {
          console.log(`❌ Crawl ${jobId} failed`);
          return;
        } else if (attempts >= maxAttempts) {
          console.log(`⏰ Monitoring timeout for crawl ${jobId}`);
          return;
        }

        // Continue monitoring
        setTimeout(monitor, checkInterval);

      } catch (error) {
        console.error(`❌ Error monitoring crawl ${jobId}:`, error.message);
      }
    };

    // Start monitoring after a short delay
    setTimeout(monitor, checkInterval);
  }

  // Get system status
  async getSystemStatus() {
    try {
      // Get queue stats
      const queueStats = await this.queueManager.getQueueStats();
      
      // Get active crawl jobs
      const activeCrawls = await db.select({
        id: crawlJobs.id,
        website_id: crawlJobs.website_id,
        status: crawlJobs.status,
        total_urls: crawlJobs.total_urls,
        processed_urls: crawlJobs.processed_urls,
        property_urls_found: crawlJobs.property_urls_found,
        started_at: crawlJobs.started_at
      })
      .from(crawlJobs)
      .where(eq(crawlJobs.status, 'running'))
      .orderBy(crawlJobs.started_at);

      // Get website configs
      const websites = await db.select({
        website_id: websiteConfigs.website_id,
        name: websiteConfigs.name,
        is_active: websiteConfigs.is_active,
        last_crawl_at: websiteConfigs.last_crawl_at,
        next_crawl_at: websiteConfigs.next_crawl_at
      })
      .from(websiteConfigs);

      return {
        system: {
          isRunning: this.isRunning,
          queueManagerActive: this.queueManager.intervalId !== null
        },
        queue: queueStats,
        activeCrawls: activeCrawls,
        websites: websites
      };

    } catch (error) {
      console.error('❌ Error getting system status:', error.message);
      return null;
    }
  }

  // Print system status
  async printStatus() {
    const status = await this.getSystemStatus();
    if (!status) return;

    console.log('\n📊 SMART SCRAPING SYSTEM STATUS');
    console.log('='.repeat(50));
    
    console.log(`🤖 System Running: ${status.system.isRunning ? '✅' : '❌'}`);
    console.log(`📋 Queue Manager: ${status.system.queueManagerActive ? '✅' : '❌'}`);
    
    if (status.queue) {
      console.log('\n📋 Scraping Queue:');
      console.log(`   Total: ${status.queue.total}`);
      console.log(`   Pending: ${status.queue.pending}`);
      console.log(`   Processing: ${status.queue.processing}`);
      console.log(`   Completed: ${status.queue.completed}`);
      console.log(`   Failed: ${status.queue.failed}`);
    }

    if (status.activeCrawls.length > 0) {
      console.log('\n🕷️  Active Crawls:');
      status.activeCrawls.forEach(crawl => {
        const progress = crawl.total_urls > 0 ? 
          `${crawl.processed_urls}/${crawl.total_urls}` : 'Starting...';
        console.log(`   ${crawl.website_id}: ${crawl.status} (${progress}) - ${crawl.property_urls_found} properties found`);
      });
    } else {
      console.log('\n🕷️  No active crawls');
    }

    console.log('\n🌐 Websites:');
    status.websites.forEach(website => {
      const active = website.is_active ? '✅' : '❌';
      const lastCrawl = website.last_crawl_at ? 
        new Date(website.last_crawl_at).toLocaleString() : 'Never';
      const nextCrawl = website.next_crawl_at ? 
        new Date(website.next_crawl_at).toLocaleString() : 'Not scheduled';
      
      console.log(`   ${active} ${website.name}`);
      console.log(`      Last: ${lastCrawl}`);
      console.log(`      Next: ${nextCrawl}`);
    });
  }

  // Add URLs manually to the queue
  async addUrlsToQueue(urls, websiteId) {
    console.log(`➕ Adding ${urls.length} URLs to queue for ${websiteId}`);
    
    let added = 0;
    for (const url of urls) {
      const success = await this.queueManager.addToQueue(url, websiteId);
      if (success) added++;
    }
    
    console.log(`✅ Added ${added}/${urls.length} URLs to queue`);
    return added;
  }

  // Cleanup old data
  async cleanup() {
    console.log('🧹 Running system cleanup...');
    
    try {
      // Cleanup completed queue items older than 7 days
      const cleanedQueue = await this.queueManager.cleanupQueue(7);
      
      // Could add more cleanup tasks here
      
      console.log(`✅ Cleanup completed: ${cleanedQueue} queue items removed`);
      
    } catch (error) {
      console.error('❌ Cleanup failed:', error.message);
    }
  }
}

// CLI interface
async function main() {
  const system = new SmartScrapingSystem();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'start':
      await system.start();
      
      // Keep running and show status every 5 minutes
      setInterval(async () => {
        await system.printStatus();
      }, 5 * 60 * 1000);
      
      // Handle graceful shutdown
      process.on('SIGINT', async () => {
        console.log('\n🛑 Received shutdown signal...');
        await system.stop();
        process.exit(0);
      });
      
      break;
      
    case 'status':
      await system.printStatus();
      process.exit(0);
      break;
      
    case 'cleanup':
      await system.cleanup();
      process.exit(0);
      break;
      
    case 'crawl':
      const websiteId = process.argv[3];
      if (!websiteId) {
        console.log('Usage: node smart_scraping_system.js crawl <website_id>');
        process.exit(1);
      }
      
      try {
        const result = await system.crawler.startWebsiteCrawl(websiteId);
        if (result) {
          console.log(`✅ Started crawl job ${result.jobId} for ${websiteId}`);
          system.monitorCrawlJob(result.jobId);
        }
      } catch (error) {
        console.error(`❌ Failed to start crawl: ${error.message}`);
        process.exit(1);
      }
      break;
      
    default:
      console.log('Smart Scraping System');
      console.log('Usage:');
      console.log('  node smart_scraping_system.js start    - Start the system');
      console.log('  node smart_scraping_system.js status   - Show system status');
      console.log('  node smart_scraping_system.js cleanup  - Run cleanup');
      console.log('  node smart_scraping_system.js crawl <website_id> - Start crawl for specific website');
      process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { SmartScrapingSystem };
