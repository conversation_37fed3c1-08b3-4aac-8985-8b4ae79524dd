// Test Villa Bali Sale property scraping with new URLs
require('dotenv').config();
const { db, scrapingQueue, properties, closeConnection } = require('../drizzle_client');
const { eq, sql } = require('drizzle-orm');
const { mapVillaBaliSale } = require('../scrape_worker/mappers');
const Firecrawl = require('firecrawl').default;

async function testVillaBaliSaleScraping() {
  try {
    console.log('🧪 Testing Villa Bali Sale Property Scraping\n');
    
    // Get 5 random URLs from the queue
    const testUrls = await db
      .select()
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'villabalisale.com'))
      .limit(5);
    
    if (testUrls.length === 0) {
      console.log('❌ No Villa Bali Sale URLs found in queue');
      return;
    }
    
    console.log(`🔍 Testing ${testUrls.length} Villa Bali Sale properties:`);
    testUrls.forEach((urlRecord, i) => {
      console.log(`   ${i + 1}. ${urlRecord.url}`);
    });
    
    const firecrawl = new Firecrawl({ apiKey: process.env.FIRECRAWL_API_KEY });
    
    let successCount = 0;
    let failCount = 0;
    const results = [];
    
    for (let i = 0; i < testUrls.length; i++) {
      const urlRecord = testUrls[i];
      console.log(`\n🔄 Testing ${i + 1}/${testUrls.length}: ${urlRecord.url}`);
      
      try {
        // Step 1: Scrape with Firecrawl
        console.log('   📡 Scraping with Firecrawl...');
        const scrapeResult = await firecrawl.scrape({
          url: urlRecord.url,
          formats: ['markdown'],
          onlyMainContent: true,
          timeout: 30000
        });
        
        if (!scrapeResult.success) {
          throw new Error(`Firecrawl failed: ${scrapeResult.error}`);
        }
        
        console.log(`   ✅ Scraped successfully (${scrapeResult.data.markdown?.length || 0} chars)`);
        
        // Step 2: Map with Villa Bali Sale mapper
        console.log('   🗺️  Mapping with Villa Bali Sale mapper...');
        const mappedData = await mapVillaBaliSale({
          markdown: scrapeResult.data.markdown,
          url: urlRecord.url
        });
        
        if (!mappedData) {
          throw new Error('Mapper returned null (possibly SOLD property)');
        }
        
        console.log('   ✅ Mapped successfully');
        console.log(`   📊 Title: ${mappedData.title}`);
        console.log(`   💰 Price: ${mappedData.price ? `IDR ${mappedData.price.toLocaleString()}` : 'Not found'}`);
        console.log(`   🏠 Bedrooms: ${mappedData.bedrooms || 'Not found'}`);
        console.log(`   🛁 Bathrooms: ${mappedData.bathrooms || 'Not found'}`);
        console.log(`   📍 Location: ${mappedData.city || 'Not found'}`);
        console.log(`   📋 Status: ${mappedData.status}`);
        console.log(`   🏷️  Category: ${mappedData.category}`);
        console.log(`   🏗️  Type: ${mappedData.type}`);
        
        // Check if it's a sale or rental
        const isSale = mappedData.price && !mappedData.rent_price;
        const isRental = mappedData.rent_price && !mappedData.price;
        console.log(`   💼 Property Type: ${isSale ? 'SALE' : isRental ? 'RENTAL' : 'UNKNOWN'}`);
        
        if (mappedData.ownership_type) {
          console.log(`   🏛️  Ownership: ${mappedData.ownership_type}`);
        }
        
        if (mappedData.lease_duration_years) {
          console.log(`   ⏰ Lease Duration: ${mappedData.lease_duration_years} years`);
        }
        
        successCount++;
        results.push({
          url: urlRecord.url,
          success: true,
          data: mappedData
        });
        
      } catch (error) {
        console.log(`   ❌ Failed: ${error.message}`);
        failCount++;
        results.push({
          url: urlRecord.url,
          success: false,
          error: error.message
        });
      }
    }
    
    // Summary
    console.log('\n📊 Test Results Summary:');
    console.log(`   ✅ Successful: ${successCount}/${testUrls.length}`);
    console.log(`   ❌ Failed: ${failCount}/${testUrls.length}`);
    console.log(`   📈 Success Rate: ${((successCount / testUrls.length) * 100).toFixed(1)}%`);
    
    // Analyze successful results
    const successfulResults = results.filter(r => r.success);
    if (successfulResults.length > 0) {
      console.log('\n🔍 Analysis of Successful Results:');
      
      const withPrice = successfulResults.filter(r => r.data.price || r.data.rent_price).length;
      const withBedrooms = successfulResults.filter(r => r.data.bedrooms).length;
      const withLocation = successfulResults.filter(r => r.data.city).length;
      const saleProperties = successfulResults.filter(r => r.data.price && !r.data.rent_price).length;
      const rentalProperties = successfulResults.filter(r => r.data.rent_price && !r.data.price).length;
      
      console.log(`   💰 Properties with price: ${withPrice}/${successfulResults.length}`);
      console.log(`   🏠 Properties with bedrooms: ${withBedrooms}/${successfulResults.length}`);
      console.log(`   📍 Properties with location: ${withLocation}/${successfulResults.length}`);
      console.log(`   🏠 Sale properties: ${saleProperties}`);
      console.log(`   🏡 Rental properties: ${rentalProperties}`);
    }
    
    // Overall assessment
    console.log('\n🎯 Overall Assessment:');
    if (successCount >= 4) {
      console.log('🎉 EXCELLENT! Villa Bali Sale scraping is working perfectly!');
      console.log('✅ Ready for full-scale property scraping');
      console.log(`🚀 ${2867} properties are ready to be processed`);
    } else if (successCount >= 2) {
      console.log('✅ GOOD! Villa Bali Sale scraping is mostly working');
      console.log('⚠️  Some minor issues may need attention');
    } else {
      console.log('⚠️  NEEDS ATTENTION! Villa Bali Sale scraping has issues');
      console.log('🔧 Review failed cases and adjust mapper if needed');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    closeConnection();
  }
}

testVillaBaliSaleScraping();
