// Simple amenities check
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc } = require('drizzle-orm');

async function checkAmenities() {
  try {
    console.log('🏠 Checking amenities in latest properties...');
    
    const latest = await db
      .select({
        id: properties.id,
        title: properties.title,
        source_id: properties.source_id,
        amenities: properties.amenities
      })
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(5);
    
    console.log(`\n🆕 Latest 5 Properties Amenities Check:`);
    latest.forEach((prop, i) => {
      console.log(`\n${i + 1}. [${prop.source_id}] ${prop.title.substring(0, 40)}...`);
      console.log(`   ID: ${prop.id}`);
      
      if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
        console.log(`   ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
      } else {
        console.log(`   ❌ No amenities found`);
      }
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

checkAmenities();
