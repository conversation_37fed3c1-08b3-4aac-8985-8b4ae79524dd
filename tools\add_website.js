// Website Addition Tool
// Interactive tool to add new websites to the scraping system

require('dotenv').config();
const readline = require('readline');
const { websiteRegistry } = require('../scrape_worker/website_registry');
const { runExtractBatch } = require('../scrape_worker/run_batch');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise(resolve => rl.question(prompt, resolve));
}

class WebsiteAdditionWizard {
  constructor() {
    this.websiteConfig = {};
  }

  async run() {
    console.log('🚀 Website Addition Wizard');
    console.log('='.repeat(50));
    console.log('This tool will help you add a new website to the scraping system.\n');

    try {
      await this.collectBasicInfo();
      await this.collectScrapingConfig();
      await this.collectExtractionPatterns();
      await this.collectValidationRules();
      await this.testConfiguration();
      await this.finalizeRegistration();
    } catch (error) {
      console.error('❌ Error:', error.message);
    } finally {
      rl.close();
    }
  }

  async collectBasicInfo() {
    console.log('📋 Step 1: Basic Information');
    console.log('-'.repeat(30));

    this.websiteConfig.id = await question('Website ID (lowercase, underscore separated): ');
    this.websiteConfig.name = await question('Website Name: ');
    this.websiteConfig.domain = await question('Domain (e.g., example.com): ');
    this.websiteConfig.baseUrl = await question('Base URL (e.g., https://example.com): ');

    console.log('\n✅ Basic information collected\n');
  }

  async collectScrapingConfig() {
    console.log('🔧 Step 2: Scraping Configuration');
    console.log('-'.repeat(30));

    const timeout = await question('Scraping timeout in seconds (default: 30): ');
    
    this.websiteConfig.scraping = {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: parseInt(timeout) * 1000 || 30000
    };

    console.log('\n✅ Scraping configuration set\n');
  }

  async collectExtractionPatterns() {
    console.log('🎯 Step 3: Data Extraction Patterns');
    console.log('-'.repeat(30));

    const currency = await question('Primary currency (USD/IDR/EUR/mixed): ');
    this.websiteConfig.extraction = { currency };

    // URL patterns
    console.log('\nURL Patterns (press Enter to skip):');
    const salePattern = await question('Sale URL pattern (e.g., /for-sale/): ');
    const rentPattern = await question('Rent URL pattern (e.g., /for-rent/): ');
    const listingPattern = await question('General listing pattern (e.g., /property/): ');

    this.websiteConfig.urlPatterns = {
      sale: salePattern ? [salePattern] : [],
      rent: rentPattern ? [rentPattern] : [],
      listing: listingPattern ? [listingPattern] : []
    };

    // Price patterns
    console.log('\nPrice extraction patterns (regex):');
    const pricePattern1 = await question('Price pattern 1 (e.g., /\\$\\s*([\\d,]+)/i): ');
    const pricePattern2 = await question('Price pattern 2 (optional): ');

    this.websiteConfig.extraction.pricePatterns = [];
    if (pricePattern1) {
      try {
        this.websiteConfig.extraction.pricePatterns.push(new RegExp(pricePattern1, 'i'));
      } catch (e) {
        console.log('⚠️  Invalid regex, using default pattern');
        this.websiteConfig.extraction.pricePatterns.push(/\$\s*([\d,]+)/i);
      }
    }

    // Skip phrases
    const skipPhrase = await question('Skip phrase for no-price properties (e.g., "price on request"): ');
    if (skipPhrase) {
      this.websiteConfig.extraction.skipPhrases = [skipPhrase];
    }

    console.log('\n✅ Extraction patterns configured\n');
  }

  async collectValidationRules() {
    console.log('✅ Step 4: Validation Rules');
    console.log('-'.repeat(30));

    const requiredFields = await question('Required fields (comma-separated, e.g., title,price,bedrooms): ');
    const minPrice = await question('Minimum price: ');
    const maxPrice = await question('Maximum price: ');
    const skipOnMissingPrice = await question('Skip properties without price? (y/n): ');

    this.websiteConfig.validation = {
      requiredFields: requiredFields ? requiredFields.split(',').map(f => f.trim()) : ['title'],
      priceRange: {
        min: parseInt(minPrice) || 1000,
        max: parseInt(maxPrice) || 100000000
      },
      bedroomRange: { min: 1, max: 15 },
      skipOnMissingPrice: skipOnMissingPrice.toLowerCase() === 'y'
    };

    this.websiteConfig.queue = {
      priority: 5,
      batchSize: 1,
      retryAttempts: 3
    };

    console.log('\n✅ Validation rules set\n');
  }

  async testConfiguration() {
    console.log('🧪 Step 5: Test Configuration');
    console.log('-'.repeat(30));

    const testUrl = await question('Test URL (full property URL): ');
    if (!testUrl) {
      console.log('⚠️  Skipping test - no URL provided\n');
      return;
    }

    try {
      // Register the website temporarily
      const registration = websiteRegistry.addNewWebsite(this.websiteConfig, {
        type: 'generic',
        active: true
      });

      console.log('🔄 Testing website configuration...');

      // Test the scraping and mapping
      const results = await runExtractBatch(this.websiteConfig.id, [testUrl], {});
      
      if (results && results.processedResults && results.processedResults.length > 0) {
        const result = results.processedResults[0];
        
        if (result.ok && result.data) {
          console.log('✅ Test successful!');
          console.log('📊 Extracted data:');
          console.log(`   Title: "${result.data.title}"`);
          console.log(`   Bedrooms: ${result.data.bedrooms}`);
          console.log(`   Bathrooms: ${result.data.bathrooms}`);
          console.log(`   Price: ${result.data.price || result.data.rent_price || 'N/A'}`);
          
          // Test validation
          const testResult = await websiteRegistry.testWebsiteMapper(this.websiteConfig.id, {
            markdown: 'test content',
            url: testUrl
          });
          
          if (testResult.validation) {
            console.log(`   Quality Score: ${testResult.validation.score}%`);
            if (testResult.validation.warnings.length > 0) {
              console.log('   Warnings:', testResult.validation.warnings.join(', '));
            }
          }
        } else {
          console.log('❌ Test failed - no data extracted');
          console.log('   Error:', result.error || 'Unknown error');
        }
      } else {
        console.log('❌ Test failed - no results returned');
      }

    } catch (error) {
      console.log('❌ Test failed:', error.message);
    }

    console.log('');
  }

  async finalizeRegistration() {
    console.log('🎯 Step 6: Finalize Registration');
    console.log('-'.repeat(30));

    console.log('Configuration Summary:');
    console.log(`   ID: ${this.websiteConfig.id}`);
    console.log(`   Name: ${this.websiteConfig.name}`);
    console.log(`   Domain: ${this.websiteConfig.domain}`);
    console.log(`   Currency: ${this.websiteConfig.extraction.currency}`);
    console.log(`   Required Fields: ${this.websiteConfig.validation.requiredFields.join(', ')}`);

    const confirm = await question('\nRegister this website? (y/n): ');
    
    if (confirm.toLowerCase() === 'y') {
      try {
        // The website is already registered from the test step
        console.log('✅ Website successfully registered!');
        console.log(`\n📋 Next steps:`);
        console.log(`1. Add URLs to the scraping queue for ${this.websiteConfig.id}`);
        console.log(`2. Run: node scrape_from_queue.js`);
        console.log(`3. Monitor results and adjust patterns if needed`);
        console.log(`\n💡 To modify patterns, edit scrape_worker/website_configs.js`);
      } catch (error) {
        console.log('❌ Registration failed:', error.message);
      }
    } else {
      console.log('❌ Registration cancelled');
    }
  }
}

// Run the wizard
if (require.main === module) {
  const wizard = new WebsiteAdditionWizard();
  wizard.run();
}

module.exports = { WebsiteAdditionWizard };
