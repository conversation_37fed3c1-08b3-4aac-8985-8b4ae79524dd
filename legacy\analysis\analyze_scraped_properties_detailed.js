// Analyze the 3 recently scraped properties in detail - field by field
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc, sql } = require('drizzle-orm');

async function analyzeScrapedPropertiesDetailed() {
  console.log('🔍 Analyzing Recently Scraped Properties - Field by Field');
  console.log('='.repeat(70));
  
  try {
    // Get the 3 most recent properties (from our scraping session)
    const recentProperties = await db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(3);
    
    console.log(`📋 Found ${recentProperties.length} recent properties\n`);
    
    recentProperties.forEach((prop, index) => {
      const source = prop.media?.source_id || 'unknown';
      const sourceUrl = prop.source_url || 'N/A';
      
      console.log(`${index + 1}. 🌐 ${source.toUpperCase()}`);
      console.log('='.repeat(50));
      
      // Basic Info
      console.log('📋 BASIC INFORMATION:');
      console.log(`   ID: ${prop.id}`);
      console.log(`   Title: "${prop.title}"`);
      console.log(`   Category: ${prop.category}`);
      console.log(`   Type: ${prop.type}`);
      console.log(`   Status: ${prop.status}`);
      console.log(`   Source URL: ${sourceUrl.substring(0, 80)}...`);
      
      // Location Info
      console.log('\n📍 LOCATION:');
      console.log(`   Address: "${prop.address}"`);
      console.log(`   City: "${prop.city}"`);
      console.log(`   State: "${prop.state}"`);
      console.log(`   Country: "${prop.country}"`);
      
      // Property Details
      console.log('\n🏠 PROPERTY DETAILS:');
      console.log(`   Bedrooms: ${prop.bedrooms || 'NULL'}`);
      console.log(`   Bathrooms: ${prop.bathrooms || 'NULL'}`);
      console.log(`   Parking Spaces: ${prop.parking_spaces || 'NULL'}`);
      console.log(`   Building Size: ${prop.size_sqft || 'NULL'} sqft`);
      console.log(`   Lot Size: ${prop.lot_size_sqft || 'NULL'} sqft`);
      console.log(`   Year Built: ${prop.year_built || 'NULL'}`);
      
      // Convert sizes to sqm for verification
      if (prop.size_sqft) {
        const buildingSqm = (parseFloat(prop.size_sqft) / 10.764).toFixed(1);
        console.log(`   Building Size (sqm): ${buildingSqm} sqm`);
      }
      if (prop.lot_size_sqft) {
        const lotSqm = (parseFloat(prop.lot_size_sqft) / 10.764).toFixed(1);
        console.log(`   Lot Size (sqm): ${lotSqm} sqm`);
      }
      
      // Ownership & Lease
      console.log('\n🏛️ OWNERSHIP:');
      console.log(`   Ownership Type: ${prop.ownership_type || 'NULL'}`);
      console.log(`   Lease Duration (years): ${prop.lease_duration_years || 'NULL'}`);
      console.log(`   Lease Duration (text): ${prop.lease_duration_text || 'NULL'}`);
      
      // Pricing
      console.log('\n💰 PRICING:');
      const salePrice = prop.price ? parseFloat(prop.price) : null;
      const rentPrice = prop.rent_price ? parseFloat(prop.rent_price) : null;
      
      if (salePrice) {
        const priceUSD = (salePrice / 16000).toFixed(0);
        console.log(`   Sale Price: ${salePrice.toLocaleString()} IDR (~$${priceUSD})`);
      } else {
        console.log(`   Sale Price: NULL`);
      }
      
      if (rentPrice) {
        const rentUSD = (rentPrice / 16000).toFixed(0);
        console.log(`   Rent Price: ${rentPrice.toLocaleString()} IDR (~$${rentUSD})`);
      } else {
        console.log(`   Rent Price: NULL`);
      }
      
      // Description
      console.log('\n📝 DESCRIPTION:');
      if (prop.description) {
        const desc = prop.description.substring(0, 200);
        console.log(`   "${desc}${prop.description.length > 200 ? '...' : ''}"`);
        console.log(`   Length: ${prop.description.length} characters`);
      } else {
        console.log(`   NULL`);
      }
      
      // Amenities
      console.log('\n🏖️ AMENITIES:');
      if (prop.amenities && prop.amenities.raw_amenities) {
        const amenities = prop.amenities.raw_amenities;
        if (amenities.length > 0) {
          console.log(`   Count: ${amenities.length}`);
          amenities.slice(0, 5).forEach(amenity => {
            console.log(`   - ${amenity}`);
          });
          if (amenities.length > 5) {
            console.log(`   ... and ${amenities.length - 5} more`);
          }
        } else {
          console.log(`   Empty array`);
        }
      } else {
        console.log(`   NULL or invalid format`);
      }
      
      // Media
      console.log('\n📸 MEDIA:');
      if (prop.media) {
        console.log(`   Source ID: ${prop.media.source_id || 'NULL'}`);
        console.log(`   External ID: ${prop.media.external_id || 'NULL'}`);
        console.log(`   Image Count: ${prop.media.image_count || 0}`);
        if (prop.media.images && prop.media.images.length > 0) {
          console.log(`   Sample Image: ${prop.media.images[0].substring(0, 60)}...`);
        }
      } else {
        console.log(`   NULL`);
      }
      
      // Timestamps
      console.log('\n⏰ TIMESTAMPS:');
      console.log(`   Created: ${new Date(prop.created_at).toLocaleString()}`);
      console.log(`   Updated: ${new Date(prop.updated_at).toLocaleString()}`);
      console.log(`   Last Scraped: ${prop.last_scraped_at ? new Date(prop.last_scraped_at).toLocaleString() : 'NULL'}`);
      
      // Vector
      console.log('\n🔍 VECTOR:');
      if (prop.vector && Array.isArray(prop.vector)) {
        console.log(`   Dimensions: ${prop.vector.length}`);
        console.log(`   Sample values: [${prop.vector.slice(0, 5).map(v => v.toFixed(3)).join(', ')}...]`);
      } else {
        console.log(`   NULL or invalid format`);
      }
      
      // Data Quality Assessment
      console.log('\n✅ DATA QUALITY ASSESSMENT:');
      const qualityChecks = {
        'Has Title': !!prop.title && prop.title !== 'Villa Bali Sale Property',
        'Has Bedrooms': !!prop.bedrooms,
        'Has Bathrooms': !!prop.bathrooms,
        'Has Building Size': !!prop.size_sqft,
        'Has Price': !!(prop.price || prop.rent_price),
        'Has Description': !!prop.description && prop.description.length > 50,
        'Has Ownership': !!prop.ownership_type,
        'Has Location': !!prop.city && !!prop.state
      };
      
      Object.entries(qualityChecks).forEach(([check, passed]) => {
        console.log(`   ${passed ? '✅' : '❌'} ${check}`);
      });
      
      const passedChecks = Object.values(qualityChecks).filter(Boolean).length;
      const totalChecks = Object.keys(qualityChecks).length;
      const qualityScore = ((passedChecks / totalChecks) * 100).toFixed(1);
      
      console.log(`\n📊 Quality Score: ${qualityScore}% (${passedChecks}/${totalChecks})`);
      
      console.log('\n' + '='.repeat(70) + '\n');
    });
    
    // Summary comparison
    console.log('📊 COMPARISON SUMMARY');
    console.log('='.repeat(50));
    
    const sources = [...new Set(recentProperties.map(p => p.media?.source_id).filter(Boolean))];
    
    sources.forEach(source => {
      const sourceProps = recentProperties.filter(p => p.media?.source_id === source);
      console.log(`\n🌐 ${source.toUpperCase()}:`);
      
      sourceProps.forEach(prop => {
        console.log(`   "${prop.title}"`);
        console.log(`   📐 ${prop.bedrooms || 0}bed/${prop.bathrooms || 0}bath, ${prop.size_sqft || 0} sqft`);
        console.log(`   💰 ${prop.price ? (parseFloat(prop.price) / 1000000).toFixed(1) + 'M IDR' : 'No price'}`);
        console.log(`   🏠 ${prop.ownership_type || 'Unknown ownership'}`);
      });
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

analyzeScrapedPropertiesDetailed().then(() => process.exit(0));
