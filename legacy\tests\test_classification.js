// Test the new property classification system for all websites
const { mapBetterPlace, mapBaliHomeImmo, mapBaliVillaRealty } = require('./scrape_worker/mappers');

console.log('🧪 Testing smart property classification for ALL websites...\n');

// Test cases
const testCases = [
  {
    title: 'Shop & office for rent monthly in East Uluwatu - Bali',
    description: 'Commercial space for business',
    expected: { category: 'COMMERCIAL', type: 'OFFICE' }
  },
  {
    title: 'COZY 2 BEDROOMS VILLA FOR YEARLY AND MONTHLY RENTAL IN BALI PANDAWA-KUTUH',
    description: 'Beautiful villa with garden',
    expected: { category: 'RESIDENTIAL', type: 'VILLA' }
  },
  {
    title: 'Modern Apartment in Seminyak',
    description: 'Luxury apartment with pool',
    expected: { category: 'RESIDENTIAL', type: 'APARTMENT' }
  },
  {
    title: 'Retail Shop in Canggu',
    description: 'Perfect for retail business',
    expected: { category: 'COMMERCIAL', type: 'RETAIL' }
  },
  {
    title: 'Land for Sale in Ubud',
    description: 'Prime land plot for development',
    expected: { category: 'LAND', type: 'LAND' }
  }
];

console.log('🔍 Testing classification logic for all websites:\n');

const websites = [
  { name: 'BetterPlace', mapper: mapBetterPlace, priceField: 'price' },
  { name: 'Bali Home Immo', mapper: mapBaliHomeImmo, priceField: 'price' },
  { name: 'Bali Villa Realty', mapper: mapBaliVillaRealty, priceField: 'price' }
];

async function runTests() {
  for (const website of websites) {
    console.log(`📊 Testing ${website.name}:`);

    for (const [index, testCase] of testCases.entries()) {
      const mockRaw = {
        title: testCase.title,
        description: testCase.description,
        [website.priceField]: website.name === 'BetterPlace' ? '$50,000' : 'Rp 50,000,000',
        location: 'Bali, Indonesia'
      };

      const mapped = await website.mapper(mockRaw);

      const correct = mapped.category === testCase.expected.category &&
                      mapped.type === testCase.expected.type;

      console.log(`   ${index + 1}. ${testCase.title}`);
      console.log(`      Expected: ${testCase.expected.category}/${testCase.expected.type}`);
      console.log(`      Got:      ${mapped.category}/${mapped.type}`);
      console.log(`      Result:   ${correct ? '✅ CORRECT' : '❌ INCORRECT'}`);
    }
    console.log('');
  }

  console.log('🎯 Classification test completed for all websites!');
  process.exit(0);
}

runTests().catch(console.error);
