// Check existing BetterPlace URLs in database
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq, desc } = require('drizzle-orm');

async function checkBetterPlaceUrls() {
  console.log('🔍 Checking existing BetterPlace URLs in database...');
  
  try {
    // First, let's see all properties to understand the structure
    const allProperties = await db
      .select({
        id: properties.id,
        title: properties.title,
        external_id: properties.external_id,
        media: properties.media,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(5);

    console.log('📋 Sample properties structure:');
    allProperties.forEach((prop, index) => {
      console.log(`${index + 1}. ${prop.title}`);
      console.log(`   Media: ${JSON.stringify(prop.media, null, 2)}`);
    });

    // Now search for BetterPlace properties
    const betterPlaceProperties = allProperties.filter(prop =>
      prop.media?.source_id === 'betterplace' ||
      prop.media?.source_url?.includes('betterplace')
    );
    
    console.log(`📊 Found ${betterPlaceProperties.length} BetterPlace properties`);
    
    betterPlaceProperties.forEach((prop, index) => {
      console.log(`\n${index + 1}. ${prop.title}`);
      console.log(`   ID: ${prop.id}`);
      console.log(`   External ID: ${prop.external_id}`);
      console.log(`   Source URL: ${prop.source_url?.source_url || 'N/A'}`);
      console.log(`   Created: ${prop.created_at}`);
    });
    
    // Try to find a working URL
    if (betterPlaceProperties.length > 0) {
      const testUrl = betterPlaceProperties[0].media?.source_url || 'https://betterplace.cc/buy/properties/BPVL02270';
      if (testUrl) {
        console.log(`\n🧪 Testing most recent URL: ${testUrl}`);
        
        const Firecrawl = require('firecrawl').default;
        const firecrawl = new Firecrawl({ apiKey: process.env.FIRECRAWL_API_KEY });
        
        const result = await firecrawl.scrapeUrl(testUrl, {
          formats: ['markdown'],
          onlyMainContent: true,
          timeout: 30000
        });
        
        console.log('📊 Test result:');
        console.log('Success:', result.success);
        console.log('Markdown length:', result.data?.markdown?.length || 0);
        
        if (result.data?.markdown && result.data.markdown.length > 0) {
          console.log('✅ URL is working!');
          console.log('First 300 chars:', result.data.markdown.substring(0, 300));
        } else {
          console.log('❌ URL not working or no content');
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkBetterPlaceUrls().catch(console.error);
