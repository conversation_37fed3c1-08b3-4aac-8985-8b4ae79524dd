// Crawl Bali Coconut Living Sitemap and Add to Queue
require('dotenv').config();

const { SitemapParser } = require('./scrape_worker/sitemap_parser');
const { getWebsiteConfig } = require('./scrape_worker/website_configs');
const { db, scraping_queue } = require('./drizzle_client');

async function crawlBaliCoconutSitemap() {
  console.log('🚀 Crawling Bali Coconut Living Sitemap');
  console.log('='.repeat(50));

  try {
    // Get website configuration
    const config = getWebsiteConfig('bali_coconut_living');
    if (!config) {
      throw new Error('Website configuration not found for bali_coconut_living');
    }

    console.log(`📋 Website: ${config.name}`);
    console.log(`🌐 Domain: ${config.domain}`);
    console.log(`🗺️  Sitemap: https://balicoconutliving.com/sitemap.xml`);

    // Initialize sitemap parser
    const parser = new SitemapParser();

    // Parse sitemap
    const urls = await parser.parseSitemap('https://balicoconutliving.com/sitemap.xml', config);

    console.log(`\n📊 Sitemap Results:`);
    console.log(`   Total URLs found: ${urls.length}`);

    if (urls.length === 0) {
      console.log('⚠️  No URLs found in sitemap');
      return;
    }

    // Filter for property URLs
    const propertyUrls = urls.filter(urlObj => {
      const url = urlObj.url;
      return (
        url.includes('/bali-villa-sale-') ||
        url.includes('/bali-villa-rental-') ||
        url.includes('/property/') ||
        url.includes('/bali-land-sale-') ||
        url.includes('/villa-for-')
      );
    });

    console.log(`   Property URLs: ${propertyUrls.length}`);

    if (propertyUrls.length === 0) {
      console.log('⚠️  No property URLs found');
      return;
    }

    // Show sample URLs
    console.log(`\n📋 Sample URLs:`);
    propertyUrls.slice(0, 5).forEach((urlObj, i) => {
      console.log(`   ${i + 1}. ${urlObj.url}`);
    });

    if (propertyUrls.length > 5) {
      console.log(`   ... and ${propertyUrls.length - 5} more`);
    }

    // Add URLs to scraping queue
    console.log(`\n📥 Adding URLs to scraping queue...`);

    let addedCount = 0;
    let skippedCount = 0;

    for (const urlObj of propertyUrls) {
      try {
        // Check if URL already exists in queue
        const existing = await db
          .select()
          .from(scraping_queue)
          .where(scraping_queue.url.eq(urlObj.url))
          .limit(1);

        if (existing.length > 0) {
          skippedCount++;
          continue;
        }

        // Add to queue
        await db.insert(scraping_queue).values({
          url: urlObj.url,
          website_id: 'bali_coconut_living',
          priority: config.queue.priority || 5,
          status: 'pending',
          created_at: new Date(),
          metadata: JSON.stringify({
            source: 'sitemap',
            lastmod: urlObj.lastmod,
            priority: urlObj.priority,
            changefreq: urlObj.changefreq
          })
        });

        addedCount++;

        // Progress indicator
        if (addedCount % 10 === 0) {
          console.log(`   Added ${addedCount} URLs...`);
        }

      } catch (error) {
        console.error(`❌ Error adding URL ${urlObj.url}:`, error.message);
        skippedCount++;
      }
    }

    console.log(`\n✅ Queue Update Complete:`);
    console.log(`   Added: ${addedCount} URLs`);
    console.log(`   Skipped (already exists): ${skippedCount} URLs`);
    console.log(`   Total processed: ${propertyUrls.length} URLs`);

    // Show queue status
    const queueStatus = await db
      .select()
      .from(scraping_queue)
      .where(scraping_queue.website_id.eq('bali_coconut_living'));

    console.log(`\n📊 Queue Status for Bali Coconut Living:`);
    console.log(`   Total URLs in queue: ${queueStatus.length}`);

    const statusCounts = {};
    queueStatus.forEach(item => {
      statusCounts[item.status] = (statusCounts[item.status] || 0) + 1;
    });

    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`   ${status}: ${count}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  crawlBaliCoconutSitemap()
    .then(() => {
      console.log('\n🎉 Sitemap crawling completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Sitemap crawling failed:', error.message);
      process.exit(1);
    });
}

module.exports = { crawlBaliCoconutSitemap };
