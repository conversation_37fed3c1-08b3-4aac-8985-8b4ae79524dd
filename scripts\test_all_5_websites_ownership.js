// Test ownership fields for all 5 websites - 5 URLs each from queue
require('dotenv').config();
const { db, scrapingQueue, properties, closeConnection } = require('../drizzle_client');
const { eq, desc, inArray } = require('drizzle-orm');
const { runExtractBatch } = require('../scrape_worker/run_batch');

async function testAll5WebsitesOwnership() {
  console.log('🚀 Testing Ownership Fields for All 5 Websites (5 URLs each from queue)\n');
  
  const websites = [
    { 
      id: 'villabalisale.com', 
      name: 'Villa Bali Sale', 
      mapper: 'Villa Bali Sale',
      expectedOwnership: ['FREEHOLD', 'LEASEHOLD']
    },
    { 
      id: 'betterplace', 
      name: 'BetterPlace', 
      mapper: 'BetterPlace',
      expectedOwnership: ['FREEHOLD', 'LEASEHOLD'] 
    },
    { 
      id: 'bali_home_immo', 
      name: 'Bali Home Immo', 
      mapper: 'Bali Home Immo',
      expectedOwnership: ['RENT']
    },
    { 
      id: 'bali_villa_realty', 
      name: 'Bali Villa Realty', 
      mapper: 'Bali Villa Realty',
      expectedOwnership: ['RENT']
    },
    { 
      id: 'bali_coconut_living', 
      name: 'Bali Coconut Living', 
      mapper: 'Generic Mapper',
      expectedOwnership: ['FREEHOLD', 'LEASEHOLD', 'RENT']
    }
  ];
  
  const results = {};
  let totalSuccess = 0;
  let totalFailed = 0;
  let totalProcessed = 0;
  
  try {
    for (const website of websites) {
      console.log(`\n${'='.repeat(80)}`);
      console.log(`🌐 Testing ${website.name} (${website.id})`);
      console.log(`${'='.repeat(80)}`);
      
      // Get 5 URLs from queue for this website
      const queueItems = await db
        .select()
        .from(scrapingQueue)
        .where(eq(scrapingQueue.website_id, website.id))
        .limit(5);
      
      if (queueItems.length === 0) {
        console.log(`❌ No URLs found in queue for ${website.name}`);
        results[website.id] = { 
          name: website.name,
          success: 0, 
          failed: 0, 
          total: 0,
          urls: [],
          ownershipTypes: {}
        };
        continue;
      }
      
      console.log(`📊 Found ${queueItems.length} URLs to test:`);
      queueItems.forEach((item, i) => {
        console.log(`   ${i + 1}. ${item.url.substring(0, 70)}...`);
      });
      
      const urls = queueItems.map(item => item.url);
      
      console.log(`\n🔄 Processing ${urls.length} URLs with ${website.mapper} mapper...`);
      
      // Process URLs with appropriate mapper
      const batchResult = await runExtractBatch(website.id, urls, {});

      // Extract the actual results from the new return structure
      const batchResults = batchResult.extractedData || [];
      const processedResults = batchResult.processedResults || [];

      let successCount = 0;
      let failCount = 0;
      const processedUrls = [];
      const ownershipTypes = {};

      console.log(`\n📊 Results for ${website.name}:`);
      console.log('-'.repeat(60));

      batchResults.forEach((result, i) => {
        const url = urls[i];
        const shortUrl = url.substring(0, 50) + '...';
        
        if (result) {
          successCount++;
          const property = result;
          
          // Track ownership types
          if (property.ownership_type) {
            ownershipTypes[property.ownership_type] = (ownershipTypes[property.ownership_type] || 0) + 1;
          } else {
            ownershipTypes['NO_OWNERSHIP'] = (ownershipTypes['NO_OWNERSHIP'] || 0) + 1;
          }
          
          console.log(`\n✅ SUCCESS ${i + 1}/${batchResults.length}:`);
          console.log(`   🔗 URL: ${shortUrl}`);
          console.log(`   🏠 Title: ${property.title}`);
          console.log(`   💰 Price: ${property.price ? 'IDR ' + property.price.toLocaleString() : (property.rent_price ? 'IDR ' + property.rent_price.toLocaleString() + '/month' : 'N/A')}`);
          console.log(`   📍 Location: ${property.city}, ${property.country}`);
          console.log(`   🛏️  Specs: ${property.bedrooms || 'N/A'} bed, ${property.bathrooms || 'N/A'} bath`);
          console.log(`   📐 Size: ${property.size_sqft ? Math.round(property.size_sqft) + ' sqft' : 'N/A'}`);
          console.log(`   🌍 Land: ${property.lot_size_sqft ? Math.round(property.lot_size_sqft) + ' sqft' : 'N/A'}`);
          console.log(`   🚗 Parking: ${property.parking_spaces || 'N/A'}`);
          console.log(`   📜 Ownership: ${property.ownership_type || 'N/A'}`);
          console.log(`   ⏰ Lease: ${property.lease_duration_years ? property.lease_duration_years + ' years' : 'N/A'}`);
          console.log(`   🆔 Property ID: ${property.media?.external_id || 'N/A'}`);
          console.log(`   📸 Images: ${property.media?.image_count || 0}`);
          console.log(`   🎯 Amenities: ${property.amenities?.raw_amenities?.length || 0}`);
          
          processedUrls.push({
            url: shortUrl,
            title: property.title,
            ownership: property.ownership_type,
            lease_years: property.lease_duration_years,
            price: property.price || property.rent_price,
            success: true
          });
          
        } else {
          failCount++;
          console.log(`\n❌ FAILED ${i + 1}/${batchResults.length}:`);
          console.log(`   🔗 URL: ${shortUrl}`);
          console.log(`   ❌ Error: ${result.error || 'Unknown error'}`);
          
          processedUrls.push({
            url: shortUrl,
            success: false,
            error: 'Failed to process'
          });
        }
      });
      
      totalSuccess += successCount;
      totalFailed += failCount;
      totalProcessed += batchResults.length;
      
      results[website.id] = {
        name: website.name,
        success: successCount,
        failed: failCount,
        total: batchResults.length,
        successRate: Math.round((successCount / batchResults.length) * 100),
        urls: processedUrls,
        ownershipTypes: ownershipTypes,
        expectedOwnership: website.expectedOwnership
      };
      
      console.log(`\n📈 ${website.name} Summary:`);
      console.log(`   ✅ Successful: ${successCount}/${batchResults.length}`);
      console.log(`   ❌ Failed: ${failCount}/${batchResults.length}`);
      console.log(`   📊 Success Rate: ${Math.round((successCount / batchResults.length) * 100)}%`);
      
      if (Object.keys(ownershipTypes).length > 0) {
        console.log(`   📜 Ownership Types Found:`);
        Object.entries(ownershipTypes).forEach(([type, count]) => {
          const isExpected = website.expectedOwnership.includes(type);
          const icon = isExpected ? '✅' : (type === 'NO_OWNERSHIP' ? '⚠️' : '❓');
          console.log(`      ${icon} ${type}: ${count} properties`);
        });
      }
    }
    
    // Overall summary
    console.log(`\n${'='.repeat(80)}`);
    console.log('🏆 OVERALL TEST RESULTS - ALL 5 WEBSITES');
    console.log(`${'='.repeat(80)}`);
    
    const overallSuccessRate = totalProcessed > 0 ? Math.round((totalSuccess / totalProcessed) * 100) : 0;
    
    console.log(`\n🎯 FINAL SUMMARY:`);
    console.log(`   ✅ Total Successful: ${totalSuccess}/${totalProcessed}`);
    console.log(`   ❌ Total Failed: ${totalFailed}/${totalProcessed}`);
    console.log(`   📊 Overall Success Rate: ${overallSuccessRate}%`);
    
    // Detailed website results
    console.log(`\n📊 DETAILED RESULTS BY WEBSITE:`);
    Object.values(results).forEach(result => {
      if (result.total > 0) {
        console.log(`\n🌐 ${result.name}:`);
        console.log(`   📊 Success: ${result.success}/${result.total} (${result.successRate}%)`);
        
        if (Object.keys(result.ownershipTypes).length > 0) {
          console.log(`   📜 Ownership Distribution:`);
          Object.entries(result.ownershipTypes).forEach(([type, count]) => {
            const percentage = Math.round((count / result.success) * 100);
            const isExpected = result.expectedOwnership.includes(type);
            const status = isExpected ? '✅ Expected' : (type === 'NO_OWNERSHIP' ? '⚠️  Missing' : '❓ Unexpected');
            console.log(`      - ${type}: ${count} (${percentage}%) ${status}`);
          });
        }
      }
    });
    
    // Performance evaluation
    if (overallSuccessRate >= 80) {
      console.log('\n🏆 EXCELLENT! All 5 websites are working very well!');
    } else if (overallSuccessRate >= 60) {
      console.log('\n✅ GOOD! Most websites are working well with some minor issues.');
    } else {
      console.log('\n⚠️  NEEDS IMPROVEMENT! Several websites have issues.');
    }
    
    // Check database for newly created properties
    console.log(`\n🔍 Checking database for new properties with ownership data...`);
    
    const recentProperties = await db
      .select({
        title: properties.title,
        ownership_type: properties.ownership_type,
        lease_duration_years: properties.lease_duration_years,
        source_id: properties.source_id,
        price: properties.price,
        rent_price: properties.rent_price,
        created_at: properties.created_at
      })
      .from(properties)
      .where(inArray(properties.source_id, ['villa_bali_sale', 'betterplace', 'bali_home_immo', 'bali_villa_realty', 'bali_coconut_living']))
      .orderBy(desc(properties.created_at))
      .limit(20);
    
    console.log(`\n📋 Latest 20 properties in database:`);
    recentProperties.forEach((prop, i) => {
      const priceStr = prop.price ? `IDR ${prop.price.toLocaleString()}` : (prop.rent_price ? `IDR ${prop.rent_price.toLocaleString()}/mo` : 'No price');
      console.log(`   ${i + 1}. ${prop.source_id}: ${prop.title.substring(0, 40)}...`);
      console.log(`      📜 ${prop.ownership_type || 'No ownership'} ${prop.lease_duration_years ? '(' + prop.lease_duration_years + ' years)' : ''}`);
      console.log(`      💰 ${priceStr}`);
      console.log(`      📅 ${new Date(prop.created_at).toLocaleString()}`);
    });
    
    console.log('\n🎉 All 5 websites ownership field testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test
testAll5WebsitesOwnership()
  .then(() => {
    console.log('✅ All 5 websites test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
