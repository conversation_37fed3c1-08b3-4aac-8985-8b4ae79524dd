// Test only Bali Coconut Living with debugging
require('dotenv').config();
const { db, scrapingQueue, properties, closeConnection } = require('../drizzle_client');
const { eq, desc, limit } = require('drizzle-orm');
const { FirecrawlKeyManager } = require('../scrape_worker/firecrawl_key_manager');
const { WebsiteRegistry } = require('../scrape_worker/website_registry');

async function testBaliCoconutOnly() {
  console.log('🥥 Testing Only Bali Coconut Living\n');
  
  try {
    // Initialize components
    const keyManager = new FirecrawlKeyManager();
    const registry = new WebsiteRegistry();
    
    // Get 1 URL from Bali Coconut Living queue
    const urls = await db
      .select()
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'bali_coconut_living'))
      .limit(1);
    
    if (urls.length === 0) {
      console.log('❌ No URLs found in queue for Bali Coconut Living');
      return;
    }
    
    console.log(`🔍 Testing ${urls.length} URL(s):`);
    urls.forEach((url, i) => {
      console.log(`   ${i + 1}. ${url.url}`);
    });
    console.log('');
    
    // Process each URL
    for (const urlRecord of urls) {
      console.log(`🔄 Processing: ${urlRecord.url}`);
      
      try {
        // Get the mapper for this website
        const mapper = registry.getMapper('bali_coconut_living');
        
        // Scrape the URL
        const crawlResult = await keyManager.scrapeUrl(urlRecord.url);
        
        if (!crawlResult.success) {
          console.log(`❌ Scraping failed: ${crawlResult.error}`);
          continue;
        }
        
        console.log('✅ Scraping successful, processing with mapper...');
        
        // Process with mapper
        const mappedProperty = await mapper.mapProperty(crawlResult.data, urlRecord.url);
        
        if (mappedProperty) {
          console.log('✅ Mapping successful!');
          console.log('📊 Mapped property:');
          console.log(`   Title: ${mappedProperty.title}`);
          console.log(`   Sale Price: ${mappedProperty.price ? `IDR ${mappedProperty.price.toLocaleString()}` : 'Not found'}`);
          console.log(`   Rent Price: ${mappedProperty.rent_price ? `IDR ${mappedProperty.rent_price.toLocaleString()}` : 'Not found'}`);
          console.log(`   Location: ${mappedProperty.city}`);
          console.log(`   Bedrooms: ${mappedProperty.bedrooms}`);
          console.log(`   Bathrooms: ${mappedProperty.bathrooms}`);
          console.log(`   Ownership: ${mappedProperty.ownership_type}`);
        } else {
          console.log('❌ Mapping failed - returned null');
        }
        
      } catch (error) {
        console.log(`❌ Error processing ${urlRecord.url}: ${error.message}`);
        console.log(error.stack);
      }
    }
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    console.error(error.stack);
  } finally {
    closeConnection();
  }
}

testBaliCoconutOnly();
