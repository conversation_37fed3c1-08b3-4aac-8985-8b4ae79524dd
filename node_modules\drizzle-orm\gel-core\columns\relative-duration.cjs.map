{"version": 3, "sources": ["../../../src/gel-core/columns/relative-duration.ts"], "sourcesContent": ["import type { RelativeDuration } from 'gel';\nimport type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelRelDurationBuilderInitial<TName extends string> = GelRelDurationBuilder<{\n\tname: TName;\n\tdataType: 'relDuration';\n\tcolumnType: 'GelRelDuration';\n\tdata: RelativeDuration;\n\tdriverParam: RelativeDuration;\n\tenumValues: undefined;\n}>;\n\nexport class GelRelDurationBuilder<T extends ColumnBuilderBaseConfig<'relDuration', 'GelRelDuration'>>\n\textends GelColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelRelDurationBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t) {\n\t\tsuper(name, 'relDuration', 'GelRelDuration');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelRelDuration<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelRelDuration<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class GelRelDuration<T extends ColumnBaseConfig<'relDuration', 'GelRelDuration'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelRelDuration';\n\n\tgetSQLType(): string {\n\t\treturn `edgedbt.relative_duration_t`;\n\t}\n}\n\nexport function relDuration(): GelRelDurationBuilderInitial<''>;\nexport function relDuration<TName extends string>(name: TName): GelRelDurationBuilderInitial<TName>;\nexport function relDuration(name?: string) {\n\treturn new GelRelDurationBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,oBAA2B;AAE3B,oBAA4C;AAWrC,MAAM,8BACJ,+BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YACC,MACC;AACD,UAAM,MAAM,eAAe,gBAAgB;AAAA,EAC5C;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBAAoF,wBAAa;AAAA,EAC7G,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,YAAY,MAAe;AAC1C,SAAO,IAAI,sBAAsB,QAAQ,EAAE;AAC5C;", "names": []}