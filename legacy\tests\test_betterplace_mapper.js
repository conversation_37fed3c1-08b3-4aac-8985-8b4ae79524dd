// Test BetterPlace mapper with sample data
require('dotenv').config();
const { mapBetterPlace } = require('./scrape_worker/mappers');

async function testBetterPlaceMapper() {
  console.log('🧪 Testing BetterPlace Mapper with Enhanced Amenities');
  console.log('='.repeat(60));
  
  // Sample BetterPlace data structure
  const sampleData = {
    title: 'Beautiful 4 Bedroom Villa with Pool in Canggu',
    price: 'USD 450,000',
    location: 'Canggu, Bali',
    bedrooms: 4,
    bathrooms: 3,
    description: 'This stunning villa features a private swimming pool, fully furnished interiors, air conditioning throughout, modern kitchen, and beautiful garden. Located close to the beach with rooftop terrace and parking space.',
    amenities: ['Private Pool', 'WiFi', 'Kitchen'],
    images: ['image1.jpg', 'image2.jpg'],
    property_id: 'BPVL02999',
    furnishing: 'Fully-Furnished',
    pool_type: 'Private',
    parking_type: 'Private Car Parking',
    size: {
      building_size_sqm: 200,
      land_size_sqm: 300
    }
  };
  
  try {
    console.log('📊 Input Data:');
    console.log('Title:', sampleData.title);
    console.log('Price:', sampleData.price);
    console.log('Location:', sampleData.location);
    console.log('Bedrooms:', sampleData.bedrooms);
    console.log('Description length:', sampleData.description.length);
    console.log('Original amenities:', sampleData.amenities);
    console.log('Furnishing:', sampleData.furnishing);
    console.log('Pool type:', sampleData.pool_type);
    console.log('Parking type:', sampleData.parking_type);
    console.log('Size:', sampleData.size);
    
    console.log('\n🗺️  Processing with Enhanced Mapper...');
    const mappedData = await mapBetterPlace(sampleData);
    
    console.log('\n✅ Mapped Data:');
    console.log('Title:', mappedData.title);
    console.log('Price (IDR):', mappedData.price?.toLocaleString());
    console.log('City:', mappedData.city);
    console.log('State:', mappedData.state);
    console.log('Bedrooms:', mappedData.bedrooms);
    console.log('Bathrooms:', mappedData.bathrooms);
    console.log('Description:', mappedData.description.substring(0, 100) + '...');
    console.log('Ownership type:', mappedData.ownership_type);
    console.log('Size (sqft):', mappedData.size_sqft);
    console.log('Lot size (sqft):', mappedData.lot_size_sqft);
    
    console.log('\n🎯 Enhanced Amenities:');
    if (mappedData.amenities && mappedData.amenities.raw_amenities) {
      console.log(`Count: ${mappedData.amenities.raw_amenities.length}`);
      mappedData.amenities.raw_amenities.forEach((amenity, i) => {
        console.log(`   ${i + 1}. ${amenity}`);
      });
    } else {
      console.log('❌ No amenities found');
    }
    
    console.log('\n📋 Media:');
    console.log('Images:', mappedData.media?.images?.length || 0);
    console.log('External ID:', mappedData.media?.external_id);
    
    console.log('\n✅ Mapper test completed successfully!');
    
  } catch (error) {
    console.error('❌ Mapper test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testBetterPlaceMapper();
