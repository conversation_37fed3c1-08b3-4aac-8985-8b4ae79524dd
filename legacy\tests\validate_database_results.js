// Comprehensive database validation script - can be called on demand
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc, eq } = require('drizzle-orm');

async function validateDatabaseResults(options = {}) {
  const {
    limit = 10,
    websiteFilter = null,
    showDetails = true,
    validateContent = true
  } = options;

  console.log('🔍 Database Results Validation');
  console.log('='.repeat(50));
  console.log(`📊 Analyzing last ${limit} properties${websiteFilter ? ` from ${websiteFilter}` : ''}`);
  console.log('');

  try {
    // Get recent properties with all fields
    let query = db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(limit);

    if (websiteFilter) {
      // Note: This would need to be implemented with a proper where clause
      // For now, we'll filter after retrieval
    }

    const recentProperties = await query;
    
    // Filter by website if specified
    const filteredProperties = websiteFilter 
      ? recentProperties.filter(prop => prop.media?.source_id === websiteFilter)
      : recentProperties;

    console.log(`📋 Found ${filteredProperties.length} properties to validate:`);
    console.log('');

    const validationResults = {
      totalProperties: filteredProperties.length,
      validationErrors: [],
      fieldStats: {},
      qualityScores: [],
      websiteStats: {}
    };

    // Validate each property
    filteredProperties.forEach((prop, index) => {
      const sourceId = prop.media?.source_id || 'unknown';
      const propertyErrors = [];
      const fieldValues = {};

      console.log(`${index + 1}. 🏠 ${prop.title || 'UNTITLED PROPERTY'}`);
      console.log(`   🌐 Source: ${sourceId}`);
      console.log(`   🆔 ID: ${prop.id} | External: ${prop.media?.external_id || 'NULL'}`);
      console.log(`   🔗 URL: ${prop.media?.source_url || 'NULL'}`);
      console.log(`   📅 Created: ${new Date(prop.created_at).toLocaleString()}`);
      console.log('');

      // Validate Title
      if (!prop.title || prop.title.trim() === '') {
        propertyErrors.push('❌ Title is missing or empty');
        fieldValues.title = 'MISSING';
      } else if (prop.title.toLowerCase().includes('untitled')) {
        propertyErrors.push('⚠️  Title contains "untitled"');
        fieldValues.title = 'PROBLEMATIC';
      } else {
        fieldValues.title = 'VALID';
      }
      console.log(`   📝 Title: ${prop.title || 'NULL'}`);

      // Validate Price/Rent Price
      const hasPrice = prop.price && prop.price > 0;
      const hasRentPrice = prop.rent_price && prop.rent_price > 0;
      
      if (!hasPrice && !hasRentPrice) {
        propertyErrors.push('❌ No price or rent price found');
        fieldValues.price = 'MISSING';
      } else if (hasPrice && prop.price < 1000) {
        propertyErrors.push('⚠️  Price seems too low (< 1000)');
        fieldValues.price = 'SUSPICIOUS';
      } else if (hasRentPrice && prop.rent_price < 100) {
        propertyErrors.push('⚠️  Rent price seems too low (< 100)');
        fieldValues.price = 'SUSPICIOUS';
      } else {
        fieldValues.price = 'VALID';
      }
      console.log(`   💰 Price: ${prop.price || 'NULL'} | Rent: ${prop.rent_price || 'NULL'}`);

      // Validate Bedrooms
      if (!prop.bedrooms) {
        propertyErrors.push('❌ Bedrooms missing');
        fieldValues.bedrooms = 'MISSING';
      } else if (prop.bedrooms < 1 || prop.bedrooms > 20) {
        propertyErrors.push(`⚠️  Unusual bedroom count: ${prop.bedrooms}`);
        fieldValues.bedrooms = 'SUSPICIOUS';
      } else {
        fieldValues.bedrooms = 'VALID';
      }
      console.log(`   🛏️  Bedrooms: ${prop.bedrooms || 'NULL'}`);

      // Validate Bathrooms
      if (!prop.bathrooms) {
        propertyErrors.push('❌ Bathrooms missing');
        fieldValues.bathrooms = 'MISSING';
      } else if (prop.bathrooms < 1 || prop.bathrooms > 15) {
        propertyErrors.push(`⚠️  Unusual bathroom count: ${prop.bathrooms}`);
        fieldValues.bathrooms = 'SUSPICIOUS';
      } else {
        fieldValues.bathrooms = 'VALID';
      }
      console.log(`   🚿 Bathrooms: ${prop.bathrooms || 'NULL'}`);

      // Validate Size
      if (!prop.size_sqft) {
        propertyErrors.push('❌ Size (sqft) missing');
        fieldValues.size = 'MISSING';
      } else if (prop.size_sqft < 100 || prop.size_sqft > 50000) {
        propertyErrors.push(`⚠️  Unusual size: ${prop.size_sqft} sqft`);
        fieldValues.size = 'SUSPICIOUS';
      } else {
        fieldValues.size = 'VALID';
      }
      console.log(`   📐 Size: ${prop.size_sqft || 'NULL'} sqft | Lot: ${prop.lot_size_sqft || 'NULL'} sqft`);

      // Validate Location
      if (!prop.address && !prop.city) {
        propertyErrors.push('❌ No location information (address/city)');
        fieldValues.location = 'MISSING';
      } else if (prop.city && prop.city.toLowerCase() === 'null') {
        propertyErrors.push('⚠️  City field contains "null"');
        fieldValues.location = 'PROBLEMATIC';
      } else {
        fieldValues.location = 'VALID';
      }
      console.log(`   📍 Address: ${prop.address || 'NULL'}`);
      console.log(`   🏙️  City: ${prop.city || 'NULL'} | State: ${prop.state || 'NULL'}`);

      // Validate Year Built
      if (prop.year_built) {
        if (prop.year_built < 1900 || prop.year_built > new Date().getFullYear() + 2) {
          propertyErrors.push(`⚠️  Unusual year built: ${prop.year_built}`);
          fieldValues.year_built = 'SUSPICIOUS';
        } else {
          fieldValues.year_built = 'VALID';
        }
      } else {
        fieldValues.year_built = 'MISSING';
      }
      console.log(`   📅 Year Built: ${prop.year_built || 'NULL'}`);

      // Validate Ownership
      if (prop.ownership_type) {
        if (!['FREEHOLD', 'LEASEHOLD'].includes(prop.ownership_type)) {
          propertyErrors.push(`⚠️  Invalid ownership type: ${prop.ownership_type}`);
          fieldValues.ownership = 'INVALID';
        } else {
          fieldValues.ownership = 'VALID';
        }
      } else {
        fieldValues.ownership = 'MISSING';
      }
      console.log(`   🏠 Ownership: ${prop.ownership_type || 'NULL'}`);
      console.log(`   📋 Lease: ${prop.lease_duration_years || 'NULL'} years | ${prop.lease_duration_text || 'NULL'}`);

      // Validate Description
      if (!prop.description) {
        propertyErrors.push('❌ Description missing');
        fieldValues.description = 'MISSING';
      } else if (prop.description.length < 20) {
        propertyErrors.push('⚠️  Description too short (< 20 chars)');
        fieldValues.description = 'TOO_SHORT';
      } else if (validateContent) {
        // Check for problematic content
        const problematicPatterns = [
          'WhatsApp', 'wa.me', 'wp-content', '_next/image', 
          'digitaloceanspaces', '[![', 'Online |', 'Contact us',
          'Phone:', 'Email:', 'Free Consultation'
        ];
        
        const foundProblems = problematicPatterns.filter(pattern => 
          prop.description.toLowerCase().includes(pattern.toLowerCase())
        );
        
        if (foundProblems.length > 0) {
          propertyErrors.push(`⚠️  Description contains technical content: ${foundProblems.join(', ')}`);
          fieldValues.description = 'TECHNICAL_CONTENT';
        } else {
          fieldValues.description = 'VALID';
        }
      } else {
        fieldValues.description = 'VALID';
      }
      
      const descPreview = prop.description ? prop.description.substring(0, 100) : 'NULL';
      console.log(`   📝 Description: "${descPreview}${prop.description && prop.description.length > 100 ? '...' : ''}"`);

      // Validate Media
      if (!prop.media?.external_id) {
        propertyErrors.push('❌ External ID missing');
        fieldValues.external_id = 'MISSING';
      } else {
        fieldValues.external_id = 'VALID';
      }

      if (!prop.media?.source_url) {
        propertyErrors.push('❌ Source URL missing');
        fieldValues.source_url = 'MISSING';
      } else {
        fieldValues.source_url = 'VALID';
      }

      // Calculate quality score
      const totalFields = Object.keys(fieldValues).length;
      const validFields = Object.values(fieldValues).filter(v => v === 'VALID').length;
      const qualityScore = ((validFields / totalFields) * 100).toFixed(1);

      console.log(`   📊 Quality Score: ${qualityScore}% (${validFields}/${totalFields} valid fields)`);

      // Show errors if any
      if (propertyErrors.length > 0) {
        console.log(`   🚨 Issues Found:`);
        propertyErrors.forEach(error => console.log(`      ${error}`));
      } else {
        console.log(`   ✅ No validation issues found`);
      }

      console.log('');

      // Store results
      validationResults.validationErrors.push(...propertyErrors);
      validationResults.qualityScores.push(parseFloat(qualityScore));
      
      // Update field stats
      Object.entries(fieldValues).forEach(([field, status]) => {
        if (!validationResults.fieldStats[field]) {
          validationResults.fieldStats[field] = {};
        }
        if (!validationResults.fieldStats[field][status]) {
          validationResults.fieldStats[field][status] = 0;
        }
        validationResults.fieldStats[field][status]++;
      });

      // Update website stats
      if (!validationResults.websiteStats[sourceId]) {
        validationResults.websiteStats[sourceId] = {
          count: 0,
          avgQuality: 0,
          issues: 0
        };
      }
      validationResults.websiteStats[sourceId].count++;
      validationResults.websiteStats[sourceId].issues += propertyErrors.length;
    });

    // Calculate summary statistics
    console.log('📊 VALIDATION SUMMARY');
    console.log('='.repeat(50));

    const avgQuality = validationResults.qualityScores.length > 0 
      ? (validationResults.qualityScores.reduce((a, b) => a + b, 0) / validationResults.qualityScores.length).toFixed(1)
      : 0;

    console.log(`📈 Overall Quality Score: ${avgQuality}%`);
    console.log(`🚨 Total Issues Found: ${validationResults.validationErrors.length}`);
    console.log(`🏠 Properties Analyzed: ${validationResults.totalProperties}`);
    console.log('');

    // Field statistics
    console.log('📋 Field Quality Statistics:');
    Object.entries(validationResults.fieldStats).forEach(([field, stats]) => {
      const total = Object.values(stats).reduce((a, b) => a + b, 0);
      const valid = stats.VALID || 0;
      const percentage = ((valid / total) * 100).toFixed(1);
      console.log(`   ${field}: ${percentage}% valid (${valid}/${total})`);
      
      // Show breakdown if there are issues
      const issues = Object.entries(stats).filter(([status]) => status !== 'VALID');
      if (issues.length > 0) {
        issues.forEach(([status, count]) => {
          console.log(`      ${status}: ${count}`);
        });
      }
    });

    console.log('');

    // Website statistics
    console.log('🌐 Website Quality Statistics:');
    Object.entries(validationResults.websiteStats).forEach(([website, stats]) => {
      const avgWebsiteQuality = validationResults.qualityScores
        .slice(0, stats.count)
        .reduce((a, b) => a + b, 0) / stats.count;
      
      console.log(`   ${website}:`);
      console.log(`      Properties: ${stats.count}`);
      console.log(`      Avg Quality: ${avgWebsiteQuality.toFixed(1)}%`);
      console.log(`      Issues: ${stats.issues}`);
    });

    console.log('');

    // Recommendations
    console.log('💡 RECOMMENDATIONS');
    console.log('='.repeat(50));

    if (avgQuality >= 90) {
      console.log('✅ Excellent data quality! System is production-ready.');
    } else if (avgQuality >= 75) {
      console.log('✅ Good data quality. Minor improvements recommended.');
    } else if (avgQuality >= 60) {
      console.log('⚠️  Fair data quality. Several improvements needed.');
    } else {
      console.log('❌ Poor data quality. Major improvements required.');
    }

    // Specific recommendations based on field stats
    Object.entries(validationResults.fieldStats).forEach(([field, stats]) => {
      const total = Object.values(stats).reduce((a, b) => a + b, 0);
      const valid = stats.VALID || 0;
      const percentage = (valid / total) * 100;

      if (percentage < 50) {
        console.log(`🔧 Fix ${field} extraction - only ${percentage.toFixed(1)}% valid`);
      }
    });

    return validationResults;

  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    console.error(error.stack);
    return null;
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};

  // Parse command line arguments
  args.forEach(arg => {
    if (arg.startsWith('--limit=')) {
      options.limit = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--website=')) {
      options.websiteFilter = arg.split('=')[1];
    } else if (arg === '--no-content-validation') {
      options.validateContent = false;
    } else if (arg === '--brief') {
      options.showDetails = false;
    }
  });

  console.log('🚀 Starting Database Validation...');
  console.log(`Options: ${JSON.stringify(options, null, 2)}`);
  console.log('');

  validateDatabaseResults(options)
    .then(results => {
      if (results) {
        console.log('✅ Validation completed successfully!');
        process.exit(0);
      } else {
        console.log('❌ Validation failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Validation error:', error.message);
      process.exit(1);
    });
}

module.exports = { validateDatabaseResults };
