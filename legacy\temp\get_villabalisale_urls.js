// Get 3 URLs from villabalisale.com queue
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq, and } = require('drizzle-orm');

async function getVillaBaliSaleUrls() {
  try {
    console.log('🏠 Getting villabalisale.com URLs from queue...');
    
    const urls = await db
      .select({ 
        url: scrapingQueue.url, 
        id: scrapingQueue.id,
        status: scrapingQueue.status,
        attempts: scrapingQueue.attempts
      })
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, 'villabalisale.com'),
        eq(scrapingQueue.status, 'pending')
      ))
      .limit(5);
    
    console.log(`Found ${urls.length} pending villabalisale.com URLs:`);
    
    if (urls.length > 0) {
      urls.forEach((url, i) => {
        console.log(`\n${i + 1}. ${url.url}`);
        console.log(`   ID: ${url.id}`);
        console.log(`   Status: ${url.status}`);
        console.log(`   Attempts: ${url.attempts}`);
      });
      
      console.log(`\n✅ Ready to process ${Math.min(3, urls.length)} URLs`);
    } else {
      console.log('❌ No pending villabalisale.com URLs found');
      
      // Check other statuses
      const allUrls = await db
        .select({ 
          status: scrapingQueue.status,
          count: db.select().from(scrapingQueue).where(eq(scrapingQueue.website_id, 'villabalisale.com'))
        })
        .from(scrapingQueue)
        .where(eq(scrapingQueue.website_id, 'villabalisale.com'))
        .limit(5);
      
      console.log('\nChecking other statuses...');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

getVillaBaliSaleUrls();
