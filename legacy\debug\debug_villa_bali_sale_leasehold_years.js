// Debug Villa Bali Sale leasehold years - why not extracted?
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq } = require('drizzle-orm');
const { getKeyManager } = require('./scrape_worker/key_manager');

async function debugVillaBaliSaleLeasehold() {
  console.log('🔍 Debug Villa Bali Sale Leasehold Years');
  console.log('='.repeat(50));
  
  try {
    // Find the specific property
    const property = await db
      .select()
      .from(properties)
      .where(eq(properties.id, '5464d1d6-3666-4460-b058-c51a987a14c9'))
      .limit(1);
    
    if (property.length === 0) {
      console.log('❌ Property not found');
      return;
    }
    
    const prop = property[0];
    
    console.log('🔍 Property Details:');
    console.log(`ID: ${prop.id}`);
    console.log(`Title: "${prop.title}"`);
    console.log(`Source URL: ${prop.source_url}`);
    console.log(`Ownership Type: ${prop.ownership_type}`);
    console.log(`Lease Duration (years): ${prop.lease_duration_years} ❌ NULL!`);
    console.log(`Lease Duration (text): ${prop.lease_duration_text} ❌ NULL!`);
    console.log(`Created: ${new Date(prop.created_at).toLocaleString()}`);
    
    // Now let's scrape this URL again to see what's on the page
    const testUrl = prop.source_url;
    console.log(`\n📡 Re-scraping URL to find leasehold info: ${testUrl}`);
    
    const keyManager = getKeyManager();
    const currentKey = keyManager.getCurrentKey();
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.markdown) {
      const content = result.data.markdown;
      console.log(`📝 Markdown length: ${content.length} chars`);
      
      // Search for leasehold duration patterns
      console.log('\n🏛️ Searching for Leasehold Duration patterns:');
      
      const leaseholdPatterns = [
        /lease.*?(\d+).*?year/i,
        /(\d+).*?year.*?lease/i,
        /leasehold.*?(\d+)/i,
        /(\d+).*?leasehold/i,
        /duration.*?(\d+).*?year/i,
        /(\d+).*?year.*?duration/i,
        /remaining.*?(\d+).*?year/i,
        /(\d+).*?year.*?remaining/i,
        /expires.*?(\d+)/i,
        /(\d+).*?expires/i,
        /until.*?(\d+)/i,
        /(\d+).*?until/i,
        /term.*?(\d+).*?year/i,
        /(\d+).*?year.*?term/i
      ];
      
      leaseholdPatterns.forEach((pattern, i) => {
        const matches = [...content.matchAll(new RegExp(pattern.source, 'gi'))];
        if (matches.length > 0) {
          console.log(`\n✅ Pattern ${i+1} (${pattern.source}) found ${matches.length} matches:`);
          matches.slice(0, 3).forEach(match => {
            const start = Math.max(0, match.index - 40);
            const end = Math.min(content.length, match.index + match[0].length + 40);
            const context = content.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
            console.log(`   → Years: ${match[1]}`);
          });
        }
      });
      
      // Search for any mention of years
      console.log('\n📅 Searching for ANY year mentions:');
      const yearMentions = [...content.matchAll(/\b(\d{1,3})\s*year/gi)];
      if (yearMentions.length > 0) {
        console.log(`Found ${yearMentions.length} year mentions:`);
        yearMentions.slice(0, 10).forEach(match => {
          const start = Math.max(0, match.index - 30);
          const end = Math.min(content.length, match.index + match[0].length + 30);
          const context = content.substring(start, end);
          console.log(`   "${context.replace(/\n/g, ' ')}" → ${match[1]} years`);
        });
      }
      
      // Search for leasehold-related terms
      console.log('\n🔍 Searching for leasehold-related terms:');
      const leaseholdTerms = [
        'leasehold', 'lease', 'duration', 'expires', 'remaining', 'term', 'until', 'validity'
      ];
      
      leaseholdTerms.forEach(term => {
        const regex = new RegExp(term, 'gi');
        const matches = [...content.matchAll(regex)];
        if (matches.length > 0) {
          console.log(`\n📋 "${term}" found ${matches.length} times:`);
          matches.slice(0, 3).forEach(match => {
            const start = Math.max(0, match.index - 50);
            const end = Math.min(content.length, match.index + match[0].length + 50);
            const context = content.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      // Look for specific sections that might contain lease info
      console.log('\n📄 Looking for property details sections:');
      const lines = content.split('\n');
      lines.forEach((line, i) => {
        if (/lease|duration|expires|remaining|term|validity/i.test(line) && line.trim().length > 10) {
          console.log(`\nLine ${i+1}: "${line.trim()}"`);
        }
      });
      
    } else {
      console.log('❌ Failed to get markdown');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugVillaBaliSaleLeasehold().then(() => process.exit(0));
