// Test price pattern matching for Bali Coconut Living
require('dotenv').config();

async function testPricePatterns() {
  console.log('🥥 Testing Bali Coconut Living Price Patterns\n');
  
  // Sample content from the Villa Jangmi page
  const sampleContent = `
    # A NICE 2BR VILLA IN PETITENGET FOR YEARLY AND SALE

    **Sale & Rent Type**
    Freehold and Yearly

    IDR 5.000.000.000
    IDR 380.000.000

    Add to Wishlist Export Pdf
  `;

  // Test different price patterns
  const patterns = [
    /IDR\s*([\d,.]+)/gi,
    /IDR\s*([\d,.]+(?:\.000)*)/gi,
    /IDR\s*([0-9]{1,3}(?:\.[0-9]{3})*(?:\.[0-9]{3})*)/gi,
    /\bIDR\s*([1-9]\d{0,2}(?:\.\d{3})*(?:\.\d{3})*)\b/gi,
    /IDR\s*([\d.]+)/gi
  ];

  console.log('📝 Sample content:');
  console.log(sampleContent.trim());
  console.log('\n💰 Testing price patterns:\n');

  patterns.forEach((pattern, i) => {
    console.log(`Pattern ${i + 1}: ${pattern}`);
    const matches = [...sampleContent.matchAll(pattern)];
    console.log(`   Matches: ${matches.length}`);
    matches.forEach((match, j) => {
      console.log(`   ${j + 1}. "${match[0]}" -> "${match[1]}"`);
    });
    console.log('');
  });

  // Test currency conversion
  console.log('🔄 Testing currency conversion:\n');
  
  const { getCurrencyService } = require('../scrape_worker/currency_service');
  const currencyService = getCurrencyService();
  
  const testPrices = [
    'IDR 5.000.000.000',
    'IDR 380.000.000',
    '5.000.000.000',
    '380.000.000'
  ];

  for (const priceString of testPrices) {
    try {
      console.log(`💰 Testing: "${priceString}"`);
      const converted = await currencyService.convertPriceToIDR(priceString);
      console.log(`   Converted: ${converted ? converted.toLocaleString() : 'null'} IDR`);
      console.log(`   Valid: ${converted && converted >= 10_000_000 && converted <= 100_000_000_000 ? '✅' : '❌'}`);
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    console.log('');
  }
}

testPricePatterns();
