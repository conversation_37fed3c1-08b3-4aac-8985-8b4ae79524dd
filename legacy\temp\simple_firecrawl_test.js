// Simple Firecrawl test for BetterPlace
require('dotenv').config();
const Firecrawl = require('firecrawl').default;

async function simpleFirecrawlTest() {
  console.log('🔍 Simple Firecrawl Test for BetterPlace');
  console.log('='.repeat(50));
  
  const firecrawl = new Firecrawl({ apiKey: process.env.FIRECRAWL_API_KEY });
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02348';
  
  try {
    console.log(`🔄 Testing URL: ${testUrl}`);
    
    // Simple scrape first
    console.log('\n📡 Step 1: Simple Scrape');
    const simpleResult = await firecrawl.scrapeUrl(testUrl, {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 30000
    });
    
    if (simpleResult.success) {
      console.log('✅ Simple scrape successful');
      console.log('Content length:', simpleResult.data?.markdown?.length || 0);
      console.log('First 500 chars:', simpleResult.data?.markdown?.substring(0, 500));
    } else {
      console.log('❌ Simple scrape failed');
      console.log('Error:', simpleResult.error);
    }
    
    // JSON extraction
    console.log('\n📡 Step 2: JSON Extraction');
    const jsonResult = await firecrawl.scrapeUrl(testUrl, {
      formats: ['json'],
      jsonOptions: {
        prompt: "Extract property title, price, location, bedrooms, bathrooms, description",
        schema: {
          type: "object",
          properties: {
            title: { type: "string" },
            price: { type: "string" },
            location: { type: "string" },
            bedrooms: { type: "integer" },
            bathrooms: { type: "integer" },
            description: { type: "string" }
          }
        }
      },
      onlyMainContent: true,
      timeout: 60000
    });
    
    if (jsonResult.success) {
      console.log('✅ JSON extraction successful');
      console.log('Extracted data:', JSON.stringify(jsonResult.data, null, 2));
    } else {
      console.log('❌ JSON extraction failed');
      console.log('Error:', jsonResult.error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    process.exit(0);
  }
}

simpleFirecrawlTest();
