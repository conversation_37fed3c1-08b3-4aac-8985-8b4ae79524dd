// Test Generic Mapper directly with hardcoded config
require('dotenv').config();
const { GenericMapper } = require('../scrape_worker/generic_mapper');

async function testDirectMapper() {
  console.log('🥥 Testing Generic Mapper Directly\n');
  
  // Hardcoded Bali Coconut Living config
  const config = {
    id: 'bali_coconut_living',
    name: 'Bali Coconut Living',
    domain: 'balicoconutliving.com',
    baseUrl: 'https://balicoconutliving.com',
    
    urlPatterns: {
      sale: ['/bali-villa-sale-freehold/', '/bali-villa-sale-leasehold/', '/bali-land-sale-'],
      rent: ['/bali-villa-yearly-rental/', '/bali-villa-monthly-rental/', '/property/villa-for-'],
      listing: ['/bali-villa-', '/property/']
    },
    
    extraction: {
      currency: 'IDR',
      pricePatterns: [
        /IDR\s*([\d,.]+(?:\.000)*)/gi,
        /Price[:\s]*IDR\s*([\d,.]+(?:\.000)*)/gi,
        /IDR\s*([0-9]{1,3}(?:\.[0-9]{3})*(?:\.[0-9]{3})*)/gi,
        /\bIDR\s*([1-9]\d{0,2}(?:\.\d{3})*(?:\.\d{3})*)\b/gi,
        /(?:Freehold|Yearly|Monthly)[^0-9]*IDR\s*([\d,.]+)/gi
      ],
      bedroomPatterns: [
        /([1-9]|1[0-5])\s*Bed/i,
        /([1-9]|1[0-5])\s*bedroom/i,
        /([1-9]|1[0-5])\s*BR/i,
        /Bedroom\(s\):\s*([1-9]|1[0-5])/i
      ],
      bathroomPatterns: [
        /([1-9]|1[0-5])\s*Bath/i,
        /([1-9]|1[0-5])\s*bathroom/i,
        /Bathroom\(s\):\s*([1-9]|1[0-5])/i
      ],
      landSizePatterns: [
        /Land Size:\s*(\d+)\s*m2/i,
        /(\d+)\s*m2/i,
        /(\d+)\s*sqm/i,
        /land.*?(\d+)\s*m2/i
      ],
      buildingSizePatterns: [
        /Building Size:\s*(\d+)\s*m2/i,
        /(\d+)\s*m2.*?building/i,
        /building.*?(\d+)\s*m2/i
      ],
      skipPhrases: ['TBA', 'To Be Announced', 'Contact for Price', 'Price on Request']
    },
    
    validation: {
      requiredFields: ['title'], // Don't require specific price field - let validation handle it
      priceRange: { min: 10000000, max: 100000000000 },
      bedroomRange: { min: 1, max: 15 },
      skipOnMissingPrice: false // This will check for either price OR rent_price
    }
  };
  
  // Sample content from the Villa Jangmi page
  const sampleContent = `
    # A NICE 2BR VILLA IN PETITENGET FOR YEARLY AND SALE

    ## Villa Jangmi Detail
    * ID: V010-4221
    * Type: Villa
    * Location: Petitenget
    * Bedroom(s): 2
    * Bathroom(s): 2
    * Swimming Pool: Yes
    * Furniture: Fully Furnished
    * Living Room: Close
    * Land Size: 155 m2
    * Building Size: 150 m2
    * No. of Floor: 1

    ## Villa Jangmi
    Petitenget
    Villa | V010-4221

    **Sale & Rent Type**
    Freehold and Yearly

    IDR *************
    IDR 380.000.000

    Add to Wishlist Export Pdf
  `;

  const testUrl = 'https://balicoconutliving.com/bali-villa-yearly-rental/Petitenget/4569-V010-4221/Villa-Jangmi';
  
  try {
    console.log('📋 Using hardcoded Bali Coconut Living config');
    
    // Create mapper
    const mapper = new GenericMapper(config);
    
    // Test full mapping
    console.log('🗺️  Testing full mapping...');
    const mapped = await mapper.mapProperty({
      markdown: sampleContent,
      url: testUrl,
      metadata: { title: 'A NICE 2BR VILLA IN PETITENGET FOR YEARLY AND SALE' }
    });
    
    if (mapped) {
      console.log('\n✅ Full mapping successful:');
      console.log(`   Title: ${mapped.title}`);
      console.log(`   Location: ${mapped.city}`);
      console.log(`   Bedrooms: ${mapped.bedrooms}`);
      console.log(`   Bathrooms: ${mapped.bathrooms}`);
      console.log(`   Sale Price: ${mapped.price ? `IDR ${mapped.price.toLocaleString()}` : 'Not found'}`);
      console.log(`   Rent Price: ${mapped.rent_price ? `IDR ${mapped.rent_price.toLocaleString()}` : 'Not found'}`);
      console.log(`   Ownership: ${mapped.ownership_type}`);
      console.log(`   Land Size: ${mapped.lot_size_sqft ? `${mapped.lot_size_sqft} sqft` : 'Not found'}`);
      console.log(`   Building Size: ${mapped.size_sqft ? `${mapped.size_sqft} sqft` : 'Not found'}`);
      
      // Check if it passes validation
      const validation = mapper.validateProperty(mapped);
      console.log(`\n🔍 Validation: ${validation.isValid ? '✅ PASS' : '❌ FAIL'}`);
      if (!validation.isValid) {
        console.log(`   Errors: ${validation.errors.join(', ')}`);
      }
    } else {
      console.log('❌ Full mapping failed - returned null');
    }
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    console.error(error.stack);
  }
}

testDirectMapper();
