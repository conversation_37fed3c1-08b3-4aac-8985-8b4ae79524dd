// Get test URLs from queue for specific websites
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq, and } = require('drizzle-orm');

async function getTestUrls() {
  console.log('🔍 Getting test URLs from queue...');
  
  const websites = ['betterplace', 'bali_villa_realty', 'villa_bali_sale'];
  
  for (const website of websites) {
    const urls = await db
      .select({ url: scrapingQueue.url })
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, website),
        eq(scrapingQueue.status, 'pending')
      ))
      .limit(5);
    
    console.log(`\n${website.toUpperCase()} (first 3 will be tested):`);
    urls.forEach((row, i) => {
      if (i < 3) {
        console.log(`   ✅ ${i + 1}. ${row.url}`);
      } else {
        console.log(`   📋 ${i + 1}. ${row.url}`);
      }
    });
  }
  
  process.exit(0);
}

getTestUrls().catch(console.error);
