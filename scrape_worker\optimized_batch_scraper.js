// Optimized batch scraper using Firecrawl v2 batch API efficiently
require('dotenv').config();
const { keyManager } = require('./key_manager');

class OptimizedBatchScraper {
  constructor() {
    this.maxBatchSize = 25; // Reduced to 25 URLs per batch for better stability
    this.pollInterval = 8000; // Poll every 8 seconds for faster response
    this.maxRetries = 8; // Increased retries for better success rate
  }

  /**
   * Scrape multiple URLs using true batch processing
   * @param {string[]} urls - Array of URLs to scrape
   * @returns {Promise<Object[]>} Array of scraped results
   */
  async scrapeBatch(urls) {
    if (!urls || urls.length === 0) {
      return [];
    }

    console.log(`🚀 Starting optimized batch scrape for ${urls.length} URLs`);
    
    // Split into optimal batch sizes for Firecrawl API
    const batches = this.chunkArray(urls, this.maxBatchSize);
    console.log(`📦 Split into ${batches.length} batches of max ${this.maxBatchSize} URLs each`);
    
    const allResults = [];
    
    // Process batches sequentially to respect rate limits
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`\n📦 Processing batch ${i + 1}/${batches.length} (${batch.length} URLs)`);
      
      try {
        const batchResults = await this.scrapeUrlBatch(batch);
        allResults.push(...batchResults);
        
        console.log(`✅ Batch ${i + 1}/${batches.length} completed: ${batchResults.length} results`);
        
        // Wait between batches to respect rate limits and reduce server load
        if (i < batches.length - 1) {
          console.log(`⏳ Waiting 45 seconds before next batch...`);
          await this.sleep(45000); // Increased delay for better stability
        }
        
      } catch (error) {
        console.error(`❌ Batch ${i + 1}/${batches.length} failed:`, error.message);
        // Add null results for failed batch
        allResults.push(...batch.map(() => null));
      }
    }
    
    console.log(`🎉 Optimized batch scraping completed: ${allResults.filter(r => r !== null).length}/${urls.length} successful`);
    return allResults;
  }

  /**
   * Scrape a single batch of URLs using Firecrawl batch API
   * @param {string[]} urls - URLs to scrape in this batch
   * @returns {Promise<Object[]>} Scraped results
   */
  async scrapeUrlBatch(urls) {
    let lastError = null;
    
    // Try with different API keys if needed
    for (let keyAttempt = 0; keyAttempt < keyManager.keys.length; keyAttempt++) {
      const currentKey = keyManager.getCurrentKey();
      console.log(`   🔑 Using key ${currentKey.index + 1}/${keyManager.keys.length} (${currentKey.maskedKey})`);
      
      try {
        // Start batch scrape job
        const jobId = await this.startBatchScrapeJob(urls, currentKey.key);
        console.log(`   📋 Started batch job: ${jobId}`);
        
        // Poll for completion
        const results = await this.pollBatchJob(jobId, currentKey.key);
        console.log(`   ✅ Batch job completed: ${results.length} results`);
        
        keyManager.markKeySuccess(currentKey.index);
        return results;
        
      } catch (error) {
        console.error(`   ❌ Key ${currentKey.index + 1} failed:`, error.message);
        lastError = error;
        keyManager.markKeyError(currentKey.index);
        
        // Try next key if available
        if (keyAttempt < keyManager.keys.length - 1) {
          console.log(`   🔄 Trying next API key...`);
          continue;
        }
      }
    }
    
    throw new Error(`All API keys failed. Last error: ${lastError?.message}`);
  }

  /**
   * Start a batch scrape job
   * @param {string[]} urls - URLs to scrape
   * @param {string} apiKey - Firecrawl API key
   * @returns {Promise<string>} Job ID
   */
  async startBatchScrapeJob(urls, apiKey) {
    const response = await fetch('https://api.firecrawl.dev/v1/batch/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        urls: urls,
        formats: ['markdown'], // Markdown only for cost efficiency
        onlyMainContent: true,
        timeout: 90000, // 90 second timeout per URL for better success rate
        maxConcurrency: 5, // Reduced concurrency to prevent server overload
        ignoreInvalidURLs: true,
        blockAds: true,
        proxy: 'auto',
        waitFor: 2000,
        removeBase64Images: true
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Batch scrape start failed: HTTP ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    
    if (!result.success || !result.id) {
      throw new Error(`Batch scrape start failed: ${JSON.stringify(result)}`);
    }

    return result.id;
  }

  /**
   * Poll batch job until completion
   * @param {string} jobId - Job ID to poll
   * @param {string} apiKey - Firecrawl API key
   * @returns {Promise<Object[]>} Scraped results
   */
  async pollBatchJob(jobId, apiKey) {
    let attempts = 0;
    const maxAttempts = 60; // 10 minutes max (60 * 10 seconds)
    
    while (attempts < maxAttempts) {
      console.log(`   ⏳ Polling job ${jobId}... (attempt ${attempts + 1}/${maxAttempts})`);
      
      try {
        const response = await fetch(`https://api.firecrawl.dev/v1/batch/scrape/${jobId}`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`
          }
        });

        if (!response.ok) {
          // Handle server errors with exponential backoff
          if (response.status >= 500 && response.status < 600) {
            const backoffDelay = Math.min(5000 * Math.pow(1.5, Math.floor(attempts / 5)), 60000);
            console.log(`   🔄 Server error ${response.status}, waiting ${backoffDelay/1000}s before retry...`);
            await this.sleep(backoffDelay);
            attempts++;
            continue;
          }
          throw new Error(`Poll failed: HTTP ${response.status}`);
        }

        const result = await response.json();

        if (result.status === 'completed') {
          console.log(`   🎉 Job completed successfully`);
          return result.data || [];
        } else if (result.status === 'failed') {
          const errorMsg = result.error || 'Unknown batch job failure';
          console.log(`   ❌ Job failed: ${errorMsg}`);
          throw new Error(`Job failed: ${errorMsg}`);
        } else if (result.status === 'cancelled') {
          throw new Error('Job was cancelled');
        } else {
          // Job still running - show detailed progress
          const progress = result.completed || 0;
          const total = result.total || 0;
          const percentage = total > 0 ? Math.round((progress / total) * 100) : 0;
          console.log(`   🔄 Job status: ${result.status}, progress: ${progress}/${total} (${percentage}%)`);
        }
        
      } catch (error) {
        console.error(`   ⚠️ Poll attempt ${attempts + 1} failed:`, error.message);
      }
      
      attempts++;
      await this.sleep(this.pollInterval);
    }
    
    throw new Error(`Job ${jobId} timed out after ${maxAttempts} attempts`);
  }

  /**
   * Split array into chunks
   * @param {Array} array - Array to chunk
   * @param {number} size - Chunk size
   * @returns {Array[]} Array of chunks
   */
  chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Sleep for specified milliseconds
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = { OptimizedBatchScraper };
