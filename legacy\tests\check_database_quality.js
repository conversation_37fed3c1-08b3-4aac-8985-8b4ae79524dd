// Check database data quality after mapper fixes
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc } = require('drizzle-orm');

async function checkDatabaseQuality() {
  console.log('📊 Checking Database Data Quality');
  console.log('='.repeat(40));
  
  try {
    const recentProperties = await db
      .select({
        id: properties.id,
        title: properties.title,
        price: properties.price,
        rent_price: properties.rent_price,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        parking_spaces: properties.parking_spaces,
        size_sqft: properties.size_sqft,
        lot_size_sqft: properties.lot_size_sqft,
        year_built: properties.year_built,
        address: properties.address,
        city: properties.city,
        media: properties.media,
        created_at: properties.created_at
      })
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(15);
    
    console.log(`📋 Found ${recentProperties.length} recent properties:`);
    
    let nullCount = 0;
    let totalFields = 0;
    const sourceStats = {};
    
    recentProperties.forEach((prop, index) => {
      const sourceId = prop.media?.source_id;
      if (sourceId && ['betterplace', 'bali_villa_realty', 'bali_home_immo'].includes(sourceId)) {
        
        // Track by source
        if (!sourceStats[sourceId]) {
          sourceStats[sourceId] = { total: 0, nulls: 0 };
        }
        
        console.log(`\n${index + 1}. ${prop.title || 'UNTITLED'}`);
        console.log(`   Source: ${sourceId}`);
        console.log(`   Price: ${prop.price || prop.rent_price || 'NULL'}`);
        console.log(`   Bedrooms: ${prop.bedrooms || 'NULL'}`);
        console.log(`   Bathrooms: ${prop.bathrooms || 'NULL'}`);
        console.log(`   Parking: ${prop.parking_spaces || 'NULL'}`);
        console.log(`   Size (sqft): ${prop.size_sqft || 'NULL'}`);
        console.log(`   Lot Size (sqft): ${prop.lot_size_sqft || 'NULL'}`);
        console.log(`   Year Built: ${prop.year_built || 'NULL'}`);
        console.log(`   Address: ${prop.address || 'NULL'}`);
        console.log(`   City: ${prop.city || 'NULL'}`);
        console.log(`   Created: ${new Date(prop.created_at).toLocaleString()}`);
        
        // Count NULL values
        const fields = [
          prop.price || prop.rent_price,
          prop.bedrooms,
          prop.bathrooms,
          prop.parking_spaces,
          prop.size_sqft,
          prop.lot_size_sqft,
          prop.year_built,
          prop.address,
          prop.city
        ];
        
        fields.forEach(field => {
          totalFields++;
          sourceStats[sourceId].total++;
          if (!field) {
            nullCount++;
            sourceStats[sourceId].nulls++;
          }
        });
      }
    });
    
    const nullPercentage = totalFields > 0 ? ((nullCount / totalFields) * 100).toFixed(1) : 0;
    
    console.log('\n📊 Overall Data Quality:');
    console.log(`   Total fields checked: ${totalFields}`);
    console.log(`   NULL fields: ${nullCount}`);
    console.log(`   NULL percentage: ${nullPercentage}%`);
    console.log(`   Data completeness: ${(100 - nullPercentage).toFixed(1)}%`);
    
    console.log('\n📊 Data Quality by Source:');
    Object.entries(sourceStats).forEach(([source, stats]) => {
      const sourceNullPercentage = ((stats.nulls / stats.total) * 100).toFixed(1);
      console.log(`   ${source}: ${sourceNullPercentage}% NULL (${stats.nulls}/${stats.total})`);
    });
    
    if (nullPercentage < 20) {
      console.log('\n✅ Data quality is GOOD (< 20% NULL values)');
    } else if (nullPercentage < 50) {
      console.log('\n⚠️  Data quality is FAIR (20-50% NULL values)');
    } else {
      console.log('\n❌ Data quality is POOR (> 50% NULL values)');
    }
    
    console.log('\n🔧 Mapper Fix Status:');
    console.log('✅ Mappers have been updated to use parsed markdown data');
    console.log('✅ Fallback to helper functions for missing fields');
    console.log('✅ Proper data structure mapping implemented');
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
    console.error(error.stack);
  }
}

checkDatabaseQuality().catch(console.error);
