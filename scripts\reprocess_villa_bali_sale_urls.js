// Reprocess Villa Bali Sale URLs with new patterns
require('dotenv').config();
const { db, discoveredUrls, scrapingQueue, closeConnection } = require('../drizzle_client');
const { eq, sql } = require('drizzle-orm');
const { getWebsiteConfig } = require('../scrape_worker/website_configs');

async function reprocessVillaBaliSaleUrls() {
  try {
    console.log('🏠 Reprocessing Villa Bali Sale URLs with New Patterns\n');
    
    // Get Villa Bali Sale config
    const config = getWebsiteConfig('villabalisale');
    if (!config) {
      console.error('❌ Villa Bali Sale config not found');
      return;
    }
    
    console.log('📋 Villa Bali Sale config loaded');
    console.log(`   Property patterns: ${config.property_url_patterns.property_patterns.length}`);
    console.log(`   Exclude patterns: ${config.property_url_patterns.exclude_patterns.length}`);
    
    // Get all discovered URLs for Villa Bali Sale
    const discoveredVillaBaliSaleUrls = await db
      .select()
      .from(discoveredUrls)
      .where(eq(discoveredUrls.website_id, 'villabalisale.com'));
    
    console.log(`\n🔍 Found ${discoveredVillaBaliSaleUrls.length} discovered URLs`);
    
    // Test patterns on discovered URLs
    let propertyCount = 0;
    let excludedCount = 0;
    let alreadyInQueue = 0;
    
    const propertyPatterns = config.property_url_patterns.property_patterns.map(p => new RegExp(p));
    const excludePatterns = config.property_url_patterns.exclude_patterns.map(p => new RegExp(p));
    
    const propertyUrls = [];
    
    for (const urlRecord of discoveredVillaBaliSaleUrls) {
      const url = urlRecord.url;
      
      // Check if excluded
      const isExcluded = excludePatterns.some(pattern => pattern.test(url));
      if (isExcluded) {
        excludedCount++;
        continue;
      }
      
      // Check if matches property patterns
      const isProperty = propertyPatterns.some(pattern => pattern.test(url));
      if (isProperty) {
        // Check if already in queue
        const existingInQueue = await db
          .select()
          .from(scrapingQueue)
          .where(eq(scrapingQueue.url, url))
          .limit(1);
        
        if (existingInQueue.length > 0) {
          alreadyInQueue++;
        } else {
          propertyUrls.push({
            url: url,
            website_id: 'villabalisale.com',
            priority: 1,
            created_at: new Date(),
            updated_at: new Date()
          });
          propertyCount++;
        }
      }
    }
    
    console.log(`\n📊 URL Analysis Results:`);
    console.log(`   ✅ Property URLs found: ${propertyCount}`);
    console.log(`   ❌ Excluded URLs: ${excludedCount}`);
    console.log(`   🔄 Already in queue: ${alreadyInQueue}`);
    console.log(`   📝 Total discovered: ${discoveredVillaBaliSaleUrls.length}`);
    
    // Add property URLs to queue
    if (propertyUrls.length > 0) {
      console.log(`\n➕ Adding ${propertyUrls.length} property URLs to scraping queue...`);
      
      // Show first 5 URLs as examples
      console.log('\n📋 Example URLs being added:');
      propertyUrls.slice(0, 5).forEach((urlRecord, i) => {
        console.log(`   ${i + 1}. ${urlRecord.url}`);
      });
      
      if (propertyUrls.length > 5) {
        console.log(`   ... and ${propertyUrls.length - 5} more`);
      }
      
      // Insert in batches
      const batchSize = 100;
      for (let i = 0; i < propertyUrls.length; i += batchSize) {
        const batch = propertyUrls.slice(i, i + batchSize);
        await db.insert(scrapingQueue).values(batch);
        console.log(`   ✅ Added batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(propertyUrls.length / batchSize)}`);
      }
      
      console.log(`\n🎉 Successfully added ${propertyUrls.length} Villa Bali Sale property URLs to queue!`);
    } else {
      console.log('\n⚠️  No new property URLs to add to queue');
    }
    
    // Final queue check
    const finalQueueCount = await db
      .select({ count: sql`count(*)` })
      .from(scrapingQueue)
      .where(eq(scrapingQueue.website_id, 'villabalisale.com'));
    
    console.log(`\n📊 Final Villa Bali Sale queue status: ${finalQueueCount[0].count} URLs`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    closeConnection();
  }
}

reprocessVillaBaliSaleUrls();
