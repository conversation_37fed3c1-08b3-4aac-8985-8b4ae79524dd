// Test single BetterPlace URL to debug validation issues
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testSingleBetterPlace() {
  console.log('🧪 Test Single BetterPlace URL');
  console.log('='.repeat(40));
  
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02179';
  
  console.log(`📍 URL: ${testUrl}`);
  console.log('🔍 Testing with detailed validation logging...\n');
  
  try {
    const results = await runExtractBatch('betterplace', [testUrl], {});
    
    console.log('\n📊 RESULTS:');
    console.log('Type:', typeof results);
    console.log('Keys:', Object.keys(results || {}));
    
    if (results && results.extractedData && results.extractedData.length > 0) {
      const data = results.extractedData[0];
      console.log('\n✅ EXTRACTED DATA:');
      console.log(`   Title: "${data.title}"`);
      console.log(`   Price: ${data.price}`);
      console.log(`   Rent Price: ${data.rent_price}`);
      console.log(`   Bedrooms: ${data.bedrooms}`);
      console.log(`   Bathrooms: ${data.bathrooms}`);
      console.log(`   Parking: ${data.parking_spaces}`);
      console.log(`   Lot Size: ${data.lot_size_sqft}`);
      console.log(`   Lease Text: ${data.lease_duration_text}`);
      console.log(`   Lease Years: ${data.lease_duration_years}`);
      console.log(`   Source URL ID: ${data.source_url_id}`);
    }
    
    if (results && results.processedResults && results.processedResults.length > 0) {
      const processed = results.processedResults[0];
      console.log('\n💾 DATABASE RESULT:');
      console.log(`   Success: ${processed.ok}`);
      if (processed.ok) {
        console.log(`   ID: ${processed.id}`);
        console.log(`   ✅ Property saved to database!`);
      } else {
        console.log(`   Error: ${processed.error}`);
        console.log(`   ❌ Property NOT saved to database!`);
      }
    }
    
    if (!results || (!results.extractedData?.length && !results.processedResults?.length)) {
      console.log('\n❌ NO RESULTS - Something went wrong');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run test
if (require.main === module) {
  testSingleBetterPlace().catch(console.error);
}

module.exports = { testSingleBetterPlace };
