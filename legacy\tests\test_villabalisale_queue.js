// Test Villa Bali Sale using existing QueueManager
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, properties } = require('./drizzle_client');
const { desc, sql, eq } = require('drizzle-orm');

async function testVillaBaliSaleQueue() {
  console.log('🧪 Testing Villa Bali Sale Queue Processing');
  console.log('='.repeat(50));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  const queueManager = new QueueManager();
  
  try {
    // Get initial count
    const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`📊 Initial properties in database: ${initialCount[0].count}`);
    
    // Check existing villa_bali_sale properties
    const existingVillaBaliSale = await db
      .select({ count: sql`count(*)` })
      .from(properties)
      .where(eq(properties.source_id, 'villa_bali_sale'));
    console.log(`📊 Existing villa_bali_sale properties: ${existingVillaBaliSale[0].count}`);
    
    console.log('\n🔄 Processing Villa Bali Sale URLs from queue...');
    const startTime = Date.now();
    
    // Process 3 URLs from villabalisale.com queue
    // Note: The queue has website_id 'villabalisale.com' but mapper expects 'villa_bali_sale'
    const result = await queueManager.processQueue('villabalisale.com', 3);
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    
    if (result && result.processed > 0) {
      console.log(`✅ Successfully processed ${result.processed} URLs in ${duration}s`);
      console.log(`   Successful: ${result.successful || 0}`);
      console.log(`   Failed: ${result.failed || 0}`);
      console.log(`   Skipped: ${result.skipped || 0}`);
    } else {
      console.log(`⚠️  No URLs processed in ${duration}s`);
    }
    
    // Check final count and show new properties
    const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
    const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
    console.log(`\n🆕 New properties added to database: ${newProperties}`);
    
    if (newProperties > 0) {
      console.log('\n🏠 Latest Villa Bali Sale Properties Added:');
      const latestProps = await db
        .select({
          id: properties.id,
          title: properties.title,
          external_id: properties.external_id,
          price: properties.price,
          bedrooms: properties.bedrooms,
          city: properties.city,
          ownership_type: properties.ownership_type,
          amenities: properties.amenities,
          description: properties.description,
          created_at: properties.created_at
        })
        .from(properties)
        .where(eq(properties.source_id, 'villa_bali_sale'))
        .orderBy(desc(properties.created_at))
        .limit(newProperties);
      
      latestProps.forEach((prop, i) => {
        console.log(`\n   ${i + 1}. [${prop.external_id}] ${prop.title}`);
        if (prop.price) {
          console.log(`      💰 IDR ${prop.price.toLocaleString()}`);
        }
        console.log(`      🏠 ${prop.bedrooms} bed | 📍 ${prop.city}`);
        if (prop.ownership_type) {
          console.log(`      🏛️  ${prop.ownership_type}`);
        }
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
        
        // Check amenities
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`      ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        } else {
          console.log(`      ❌ No amenities found`);
        }
        
        if (prop.description) {
          console.log(`      📝 Description: ${prop.description.substring(0, 80)}...`);
        } else {
          console.log(`      ⚠️  No description`);
        }
      });
    } else {
      // Show existing properties to verify field population
      console.log('\n🏠 Existing Villa Bali Sale Properties (for field verification):');
      const existingProps = await db
        .select({
          id: properties.id,
          title: properties.title,
          external_id: properties.external_id,
          price: properties.price,
          bedrooms: properties.bedrooms,
          city: properties.city,
          ownership_type: properties.ownership_type,
          amenities: properties.amenities,
          description: properties.description,
          size_sqft: properties.size_sqft,
          lot_size_sqft: properties.lot_size_sqft,
          created_at: properties.created_at
        })
        .from(properties)
        .where(eq(properties.source_id, 'villa_bali_sale'))
        .orderBy(desc(properties.created_at))
        .limit(3);
      
      existingProps.forEach((prop, i) => {
        console.log(`\n   ${i + 1}. [${prop.external_id}] ${prop.title}`);
        console.log(`      💰 IDR ${prop.price?.toLocaleString() || 'No price'}`);
        console.log(`      🏠 ${prop.bedrooms || 'No bedrooms'} bed | 📍 ${prop.city || 'No city'}`);
        console.log(`      🏛️  ${prop.ownership_type || 'No ownership type'}`);
        console.log(`      📐 Size: ${prop.size_sqft ? Math.round(prop.size_sqft) + ' sqft' : 'No size'}`);
        console.log(`      🌿 Lot: ${prop.lot_size_sqft ? Math.round(prop.lot_size_sqft) + ' sqft' : 'No lot size'}`);
        
        // Check amenities
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`      ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        } else {
          console.log(`      ❌ No amenities found`);
        }
        
        console.log(`      📝 Description: ${prop.description ? prop.description.substring(0, 80) + '...' : 'No description'}`);
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
      });
    }
    
    console.log('\n✅ Villa Bali Sale queue test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testVillaBaliSaleQueue();
