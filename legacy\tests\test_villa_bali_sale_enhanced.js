// Test enhanced Villa Bali Sale mapper with photos, amenities, year built
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testVillaBaliSaleEnhanced() {
  console.log('🔍 Testing Enhanced Villa Bali Sale Mapper');
  console.log('='.repeat(50));
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/amed/ocean-views-three-bedroom-freehold-villa-in-amed-vl3341';
  
  console.log(`📡 Testing URL: ${testUrl}`);
  console.log('');
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('villabalisale', [testUrl], {});
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);
    
    console.log(`⏱️ Duration: ${duration}s`);
    
    if (results && results.processedResults && results.processedResults.length > 0) {
      const result = results.processedResults[0];
      if (result && result.ok && result.data) {
        const prop = result.data;
        
        console.log('\n📊 Extracted Data:');
        console.log(`Title: "${prop.title}"`);
        console.log(`Bedrooms: ${prop.bedrooms}`);
        console.log(`Bathrooms: ${prop.bathrooms}`);
        console.log(`Building Size: ${prop.size_sqft} sqft`);
        console.log(`Lot Size: ${prop.lot_size_sqft} sqft`);
        console.log(`Year Built: ${prop.year_built} ✅ NEW!`);
        console.log(`Ownership: ${prop.ownership_type}`);
        console.log(`Price: ${prop.price || prop.rent_price}`);
        console.log(`Location: ${prop.city}, ${prop.state}`);
        
        // Check amenities
        console.log('\n🏖️ Amenities:');
        if (prop.amenities && prop.amenities.raw_amenities) {
          const amenities = prop.amenities.raw_amenities;
          console.log(`Count: ${amenities.length} ✅ NEW!`);
          amenities.forEach(amenity => {
            console.log(`   - ${amenity}`);
          });
        } else {
          console.log('❌ No amenities extracted');
        }
        
        // Check images
        console.log('\n📸 Images:');
        if (prop.media && prop.media.images) {
          const images = prop.media.images;
          console.log(`Count: ${images.length} ✅ NEW!`);
          images.slice(0, 5).forEach((image, i) => {
            console.log(`   ${i+1}. ${image.substring(0, 80)}...`);
          });
          if (images.length > 5) {
            console.log(`   ... and ${images.length - 5} more images`);
          }
        } else {
          console.log('❌ No images extracted');
        }
        
        // Check external ID
        console.log('\n🆔 External ID:');
        if (prop.media && prop.media.external_id) {
          console.log(`External ID: ${prop.media.external_id} ✅ NEW!`);
        } else {
          console.log('❌ No external ID extracted');
        }
        
        if (prop.description) {
          console.log(`\nDescription: ${prop.description.substring(0, 100)}...`);
        }
        
        console.log('\n✅ Enhanced Villa Bali Sale extraction test complete!');
        
        // Summary of new features
        console.log('\n📋 New Features Summary:');
        console.log(`✅ Year Built: ${prop.year_built ? 'EXTRACTED' : 'MISSING'}`);
        console.log(`✅ Amenities: ${prop.amenities?.raw_amenities?.length || 0} items`);
        console.log(`✅ Images: ${prop.media?.images?.length || 0} photos`);
        console.log(`✅ External ID: ${prop.media?.external_id ? 'EXTRACTED' : 'MISSING'}`);
        
      } else {
        console.log('❌ No valid data extracted');
        console.log('Result:', JSON.stringify(result, null, 2));
      }
    } else {
      console.log('❌ No results returned');
      console.log('Results:', JSON.stringify(results, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testVillaBaliSaleEnhanced().then(() => process.exit(0));
