// Clean up old BrightData and other obsolete errors from the queue
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq, like, or } = require('drizzle-orm');

async function cleanupOldErrors() {
  console.log('🧹 Cleaning Up Old Errors from Queue');
  console.log('='.repeat(50));

  try {
    // Find all obsolete error types that should be cleaned up
    const obsoleteErrors = [
      'this.brightDataScraper.scrapeUrls is not a function',
      'chooseMapper is not a function',
      'Unknown scraping error'
    ];

    console.log('🔍 Finding obsolete errors...');
    
    for (const errorType of obsoleteErrors) {
      const errorUrls = await db
        .select()
        .from(scrapingQueue)
        .where(like(scrapingQueue.error_message, `%${errorType}%`));

      console.log(`\n❌ Found ${errorUrls.length} URLs with error: "${errorType}"`);
      
      if (errorUrls.length > 0) {
        // Group by website
        const websiteGroups = {};
        errorUrls.forEach(url => {
          if (!websiteGroups[url.website_id]) {
            websiteGroups[url.website_id] = 0;
          }
          websiteGroups[url.website_id]++;
        });

        Object.entries(websiteGroups).forEach(([website, count]) => {
          console.log(`   📍 ${website}: ${count} URLs`);
        });

        // Reset these URLs to pending for retry with new system
        if (errorType === 'chooseMapper is not a function') {
          console.log(`   🔄 Resetting ${errorUrls.length} URLs to pending (error is fixed)`);
          
          for (const url of errorUrls) {
            await db
              .update(scrapingQueue)
              .set({ 
                status: 'pending',
                error_message: null,
                attempts: 0,
                processed_at: null
              })
              .where(eq(scrapingQueue.id, url.id));
          }
          
          console.log(`   ✅ Reset ${errorUrls.length} URLs to pending`);
        } else {
          // Archive other obsolete errors
          console.log(`   🗄️ Archiving ${errorUrls.length} URLs (obsolete error type)`);
          
          for (const url of errorUrls) {
            await db
              .update(scrapingQueue)
              .set({ 
                status: 'archived',
                error_message: `ARCHIVED: ${url.error_message}`,
                processed_at: new Date()
              })
              .where(eq(scrapingQueue.id, url.id));
          }
          
          console.log(`   ✅ Archived ${errorUrls.length} URLs`);
        }
      }
    }

    // Check for database insert errors (these might be fixable)
    console.log('\n🔍 Checking database insert errors...');
    
    const dbErrors = await db
      .select()
      .from(scrapingQueue)
      .where(like(scrapingQueue.error_message, '%Failed query: insert into "property"%'));

    console.log(`📊 Found ${dbErrors.length} database insert errors`);
    
    if (dbErrors.length > 0) {
      // Group by website
      const websiteGroups = {};
      dbErrors.forEach(url => {
        if (!websiteGroups[url.website_id]) {
          websiteGroups[url.website_id] = 0;
        }
        websiteGroups[url.website_id]++;
      });

      Object.entries(websiteGroups).forEach(([website, count]) => {
        console.log(`   📍 ${website}: ${count} URLs`);
      });

      // Reset these to pending for retry with fixed mapper
      console.log(`   🔄 Resetting ${dbErrors.length} URLs to pending (mapper might be fixed)`);
      
      for (const url of dbErrors) {
        await db
          .update(scrapingQueue)
          .set({ 
            status: 'pending',
            error_message: null,
            attempts: 0,
            processed_at: null
          })
          .where(eq(scrapingQueue.id, url.id));
      }
      
      console.log(`   ✅ Reset ${dbErrors.length} URLs to pending`);
    }

    // Summary of current queue status
    console.log('\n📊 Updated Queue Status:');
    console.log('-'.repeat(40));
    
    const statusCounts = await db
      .select({
        status: scrapingQueue.status,
        count: db.count()
      })
      .from(scrapingQueue)
      .groupBy(scrapingQueue.status);

    statusCounts.forEach(({ status, count }) => {
      console.log(`   ${status}: ${count} URLs`);
    });

    console.log('\n🎉 Cleanup completed successfully!');
    console.log('✅ Obsolete errors archived');
    console.log('✅ Fixed errors reset to pending');
    console.log('✅ Database ready for efficient batch scraping');

  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
    throw error;
  }
}

// Run cleanup
async function runCleanup() {
  try {
    await cleanupOldErrors();
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    process.exit(0);
  }
}

if (require.main === module) {
  runCleanup();
}

module.exports = { cleanupOldErrors };
