// Debug land size extraction for Bali Villa Realty
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugLandSize() {
  console.log('🔍 Debug Land Size Extraction - Bali Villa Realty');
  console.log('='.repeat(50));
  
  const testUrl = 'https://balivillarealty.com/property/modern-2-bedroom-villa-for-sale-leasehold-in-prime-petitenget/';
  
  console.log(`📡 Testing URL: ${testUrl}`);
  console.log('');
  
  try {
    // Override console.log temporarily to capture mapper debug info
    const originalLog = console.log;
    const logs = [];
    console.log = (...args) => {
      const message = args.join(' ');
      logs.push(message);
      originalLog(...args);
    };
    
    const results = await runExtractBatch('bali_villa_realty', [testUrl], {});
    
    // Restore console.log
    console.log = originalLog;
    
    if (results && results.processedResults && results.processedResults.length > 0) {
      const result = results.processedResults[0];
      if (result && result.ok && result.data) {
        const prop = result.data;
        
        console.log('\n📊 Extracted Data:');
        console.log(`Title: ${prop.title}`);
        console.log(`Building Size: ${prop.size?.building_size_sqm} sqm → ${prop.size_sqft} sqft`);
        console.log(`Land Size: ${prop.size?.land_size_sqm} sqm → ${prop.lot_size_sqft} sqft`);
        console.log(`Year Built: ${prop.year_built}`);
        console.log(`Ownership: ${prop.ownership_type}`);
        console.log(`Lease Duration: ${prop.lease_duration_years} years`);
        
        // Look for land size related logs
        console.log('\n🔍 Land Size Debug Logs:');
        const landLogs = logs.filter(log => 
          log.includes('land') || 
          log.includes('Land') || 
          log.includes('lot') || 
          log.includes('🏞️') ||
          log.includes('sqm')
        );
        
        landLogs.forEach(log => console.log(`   ${log}`));
        
        if (!prop.size?.land_size_sqm) {
          console.log('\n❌ Land size not extracted! Checking patterns...');
          
          // Get the markdown to analyze manually
          console.log('\n📝 Need to check markdown content for land size patterns');
        }
        
      } else {
        console.log('❌ No valid data extracted');
      }
    } else {
      console.log('❌ No results returned');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugLandSize().then(() => process.exit(0));
