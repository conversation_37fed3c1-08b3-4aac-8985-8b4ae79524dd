{"version": 3, "sources": ["../../../src/aws-data-api/pg/driver.ts"], "sourcesContent": ["import { RDSDataClient, type RDSDataClientConfig } from '@aws-sdk/client-rds-data';\nimport { entityKind, is } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport type { PgColumn, PgInsertConfig, PgTable, TableConfig } from '~/pg-core/index.ts';\nimport { PgArray } from '~/pg-core/index.ts';\nimport type { PgRaw } from '~/pg-core/query-builders/raw.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { Param, type SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport { Table } from '~/table.ts';\nimport type { DrizzleConfig, UpdateSet } from '~/utils.ts';\nimport type { AwsDataApiClient, AwsDataApiPgQueryResult, AwsDataApiPgQueryResultHKT } from './session.ts';\nimport { AwsDataApiSession } from './session.ts';\n\nexport interface PgDriverOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n\tdatabase: string;\n\tresourceArn: string;\n\tsecretArn: string;\n}\n\nexport interface DrizzleAwsDataApiPgConfig<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends DrizzleConfig<TSchema> {\n\tdatabase: string;\n\tresourceArn: string;\n\tsecretArn: string;\n}\n\nexport class AwsDataApiPgDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<AwsDataApiPgQueryResultHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'AwsDataApiPgDatabase';\n\n\toverride execute<\n\t\tTRow extends Record<string, unknown> = Record<string, unknown>,\n\t>(query: SQLWrapper | string): PgRaw<AwsDataApiPgQueryResult<TRow>> {\n\t\treturn super.execute(query);\n\t}\n}\n\nexport class AwsPgDialect extends PgDialect {\n\tstatic override readonly [entityKind]: string = 'AwsPgDialect';\n\n\toverride escapeParam(num: number): string {\n\t\treturn `:${num + 1}`;\n\t}\n\n\toverride buildInsertQuery(\n\t\t{ table, values, onConflict, returning, select, withList }: PgInsertConfig<PgTable<TableConfig>>,\n\t): SQL<unknown> {\n\t\tconst columns: Record<string, PgColumn> = table[Table.Symbol.Columns];\n\n\t\tif (!select) {\n\t\t\tfor (const value of (values as Record<string, Param | SQL>[])) {\n\t\t\t\tfor (const fieldName of Object.keys(columns)) {\n\t\t\t\t\tconst colValue = value[fieldName];\n\t\t\t\t\tif (\n\t\t\t\t\t\tis(colValue, Param) && colValue.value !== undefined && is(colValue.encoder, PgArray)\n\t\t\t\t\t\t&& Array.isArray(colValue.value)\n\t\t\t\t\t) {\n\t\t\t\t\t\tvalue[fieldName] = sql`cast(${colValue} as ${sql.raw(colValue.encoder.getSQLType())})`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn super.buildInsertQuery({ table, values, onConflict, returning, withList });\n\t}\n\n\toverride buildUpdateSet(table: PgTable<TableConfig>, set: UpdateSet): SQL<unknown> {\n\t\tconst columns: Record<string, PgColumn> = table[Table.Symbol.Columns];\n\n\t\tfor (const [colName, colValue] of Object.entries(set)) {\n\t\t\tconst currentColumn = columns[colName];\n\t\t\tif (\n\t\t\t\tcurrentColumn && is(colValue, Param) && colValue.value !== undefined && is(colValue.encoder, PgArray)\n\t\t\t\t&& Array.isArray(colValue.value)\n\t\t\t) {\n\t\t\t\tset[colName] = sql`cast(${colValue} as ${sql.raw(colValue.encoder.getSQLType())})`;\n\t\t\t}\n\t\t}\n\t\treturn super.buildUpdateSet(table, set);\n\t}\n}\n\nfunction construct<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: AwsDataApiClient,\n\tconfig: DrizzleAwsDataApiPgConfig<TSchema>,\n): AwsDataApiPgDatabase<TSchema> & {\n\t$client: AwsDataApiClient;\n} {\n\tconst dialect = new AwsPgDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new AwsDataApiSession(client, dialect, schema, { ...config, logger, cache: config.cache }, undefined);\n\tconst db = new AwsDataApiPgDatabase(dialect, session, schema as any);\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends AwsDataApiClient = RDSDataClient,\n>(\n\t...params: [\n\t\tTClient,\n\t\tDrizzleAwsDataApiPgConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t| (\n\t\t\t\t& DrizzleConfig<TSchema>\n\t\t\t\t& {\n\t\t\t\t\tconnection: RDSDataClientConfig & Omit<DrizzleAwsDataApiPgConfig, keyof DrizzleConfig>;\n\t\t\t\t}\n\t\t\t)\n\t\t\t| (\n\t\t\t\t& DrizzleAwsDataApiPgConfig<TSchema>\n\t\t\t\t& {\n\t\t\t\t\tclient: TClient;\n\t\t\t\t}\n\t\t\t)\n\t\t),\n\t]\n): AwsDataApiPgDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\t// eslint-disable-next-line no-instanceof/no-instanceof\n\tif (params[0] instanceof RDSDataClient || params[0].constructor.name !== 'Object') {\n\t\treturn construct(params[0] as TClient, params[1] as DrizzleAwsDataApiPgConfig<TSchema>) as any;\n\t}\n\n\tif ((params[0] as { client?: TClient }).client) {\n\t\tconst { client, ...drizzleConfig } = params[0] as {\n\t\t\tclient: TClient;\n\t\t} & DrizzleAwsDataApiPgConfig<TSchema>;\n\n\t\treturn construct(client, drizzleConfig) as any;\n\t}\n\n\tconst { connection, ...drizzleConfig } = params[0] as {\n\t\tconnection: RDSDataClientConfig & Omit<DrizzleAwsDataApiPgConfig, keyof DrizzleConfig>;\n\t} & DrizzleConfig<TSchema>;\n\tconst { resourceArn, database, secretArn, ...rdsConfig } = connection;\n\n\tconst instance = new RDSDataClient(rdsConfig);\n\treturn construct(instance, { resourceArn, database, secretArn, ...drizzleConfig }) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig: DrizzleAwsDataApiPgConfig<TSchema>,\n\t): AwsDataApiPgDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAAwD;AACxD,oBAA+B;AAE/B,oBAA8B;AAC9B,gBAA2B;AAC3B,qBAA0B;AAE1B,qBAAwB;AAExB,uBAKO;AACP,iBAAsD;AACtD,mBAAsB;AAGtB,qBAAkC;AAkB3B,MAAM,6BAEH,qBAAgD;AAAA,EACzD,QAA0B,wBAAU,IAAY;AAAA,EAEvC,QAEP,OAAkE;AACnE,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC3B;AACD;AAEO,MAAM,qBAAqB,yBAAU;AAAA,EAC3C,QAA0B,wBAAU,IAAY;AAAA,EAEvC,YAAY,KAAqB;AACzC,WAAO,IAAI,MAAM,CAAC;AAAA,EACnB;AAAA,EAES,iBACR,EAAE,OAAO,QAAQ,YAAY,WAAW,QAAQ,SAAS,GAC1C;AACf,UAAM,UAAoC,MAAM,mBAAM,OAAO,OAAO;AAEpE,QAAI,CAAC,QAAQ;AACZ,iBAAW,SAAU,QAA0C;AAC9D,mBAAW,aAAa,OAAO,KAAK,OAAO,GAAG;AAC7C,gBAAM,WAAW,MAAM,SAAS;AAChC,kBACC,kBAAG,UAAU,gBAAK,KAAK,SAAS,UAAU,cAAa,kBAAG,SAAS,SAAS,sBAAO,KAChF,MAAM,QAAQ,SAAS,KAAK,GAC9B;AACD,kBAAM,SAAS,IAAI,sBAAW,QAAQ,OAAO,eAAI,IAAI,SAAS,QAAQ,WAAW,CAAC,CAAC;AAAA,UACpF;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO,MAAM,iBAAiB,EAAE,OAAO,QAAQ,YAAY,WAAW,SAAS,CAAC;AAAA,EACjF;AAAA,EAES,eAAe,OAA6B,KAA8B;AAClF,UAAM,UAAoC,MAAM,mBAAM,OAAO,OAAO;AAEpE,eAAW,CAAC,SAAS,QAAQ,KAAK,OAAO,QAAQ,GAAG,GAAG;AACtD,YAAM,gBAAgB,QAAQ,OAAO;AACrC,UACC,qBAAiB,kBAAG,UAAU,gBAAK,KAAK,SAAS,UAAU,cAAa,kBAAG,SAAS,SAAS,sBAAO,KACjG,MAAM,QAAQ,SAAS,KAAK,GAC9B;AACD,YAAI,OAAO,IAAI,sBAAW,QAAQ,OAAO,eAAI,IAAI,SAAS,QAAQ,WAAW,CAAC,CAAC;AAAA,MAChF;AAAA,IACD;AACA,WAAO,MAAM,eAAe,OAAO,GAAG;AAAA,EACvC;AACD;AAEA,SAAS,UACR,QACA,QAGC;AACD,QAAM,UAAU,IAAI,aAAa,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC1D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,iCAAkB,QAAQ,SAAS,QAAQ,EAAE,GAAG,QAAQ,QAAQ,OAAO,OAAO,MAAM,GAAG,MAAS;AACpH,QAAM,KAAK,IAAI,qBAAqB,SAAS,SAAS,MAAa;AACnE,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AAEA,SAAO;AACR;AAEO,SAAS,WAIZ,QAqBF;AAED,MAAI,OAAO,CAAC,aAAa,wCAAiB,OAAO,CAAC,EAAE,YAAY,SAAS,UAAU;AAClF,WAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AAAA,EACvF;AAEA,MAAK,OAAO,CAAC,EAA2B,QAAQ;AAC/C,UAAM,EAAE,QAAQ,GAAGA,eAAc,IAAI,OAAO,CAAC;AAI7C,WAAO,UAAU,QAAQA,cAAa;AAAA,EACvC;AAEA,QAAM,EAAE,YAAY,GAAG,cAAc,IAAI,OAAO,CAAC;AAGjD,QAAM,EAAE,aAAa,UAAU,WAAW,GAAG,UAAU,IAAI;AAE3D,QAAM,WAAW,IAAI,qCAAc,SAAS;AAC5C,SAAO,UAAU,UAAU,EAAE,aAAa,UAAU,WAAW,GAAG,cAAc,CAAC;AAClF;AAAA,CAEO,CAAUC,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["drizzleConfig", "drizzle"]}