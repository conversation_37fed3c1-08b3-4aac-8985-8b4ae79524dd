// Verify Villa Bali Sale sitemap URL update
require('dotenv').config();
const { db, websiteConfigs, closeConnection } = require('../drizzle_client');
const { eq } = require('drizzle-orm');

async function verifyVillaBaliSaleSitemap() {
  try {
    console.log('🔍 Verifying Villa Bali Sale Sitemap URL Update\n');
    
    // Get Villa Bali Sale configuration
    const villaBaliSaleConfig = await db
      .select()
      .from(websiteConfigs)
      .where(eq(websiteConfigs.website_id, 'villabalisale.com'))
      .limit(1);
    
    if (villaBaliSaleConfig.length === 0) {
      console.error('❌ Villa Bali Sale configuration not found');
      return;
    }
    
    const config = villaBaliSaleConfig[0];
    
    console.log('📊 Villa Bali Sale Configuration:');
    console.log(`   Website ID: ${config.website_id}`);
    console.log(`   Name: ${config.name}`);
    console.log(`   Base URL: ${config.base_url}`);
    console.log(`   Sitemap Enabled: ${config.sitemap_enabled}`);
    
    // Parse sitemap URLs
    let sitemapUrls = [];
    try {
      sitemapUrls = JSON.parse(config.sitemap_urls);
    } catch (error) {
      console.error('❌ Error parsing sitemap URLs:', error.message);
      return;
    }
    
    console.log(`   Sitemap URLs: ${sitemapUrls.length} URL(s)`);
    sitemapUrls.forEach((url, i) => {
      console.log(`     ${i + 1}. ${url}`);
    });
    
    // Check if the new sitemap URL is correct
    const expectedUrl = 'https://www.villabalisale.com/sitemap_property.xml';
    const hasCorrectUrl = sitemapUrls.includes(expectedUrl);
    
    console.log(`\n✅ Verification Results:`);
    console.log(`   Expected URL: ${expectedUrl}`);
    console.log(`   URL Found: ${hasCorrectUrl ? '✅ YES' : '❌ NO'}`);
    
    if (hasCorrectUrl) {
      console.log('\n🎉 Villa Bali Sale sitemap URL successfully updated!');
      console.log('✅ The new sitemap URL is correctly configured');
      console.log('✅ Ready for sitemap crawling with the new URL');
    } else {
      console.log('\n⚠️  Sitemap URL update verification failed');
      console.log('❌ The expected URL was not found in the configuration');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    closeConnection();
  }
}

verifyVillaBaliSaleSitemap();
