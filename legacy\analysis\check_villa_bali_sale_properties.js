// Check existing Villa Bali Sale properties
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function checkVillaBaliSaleProperties() {
  try {
    console.log('🏠 Checking existing Villa Bali Sale properties...');
    
    const villaBaliSaleProps = await db
      .select({
        id: properties.id,
        title: properties.title,
        external_id: properties.external_id,
        source_url: properties.source_url,
        price: properties.price,
        rent_price: properties.rent_price,
        bedrooms: properties.bedrooms,
        city: properties.city,
        amenities: properties.amenities,
        created_at: properties.created_at
      })
      .from(properties)
      .where(eq(properties.source_id, 'villa_bali_sale'))
      .limit(5);
    
    console.log(`Found ${villaBaliSaleProps.length} Villa Bali Sale properties:`);
    
    if (villaBaliSaleProps.length > 0) {
      villaBaliSaleProps.forEach((prop, i) => {
        const price = prop.price ? `IDR ${prop.price.toLocaleString()}` : 
                     prop.rent_price ? `IDR ${prop.rent_price.toLocaleString()}/month` : 'Price on request';
        console.log(`\n${i + 1}. ${prop.title}`);
        console.log(`   ID: ${prop.id}`);
        console.log(`   External ID: ${prop.external_id}`);
        console.log(`   Source URL: ${prop.source_url}`);
        console.log(`   Price: ${price}`);
        console.log(`   Bedrooms: ${prop.bedrooms}`);
        console.log(`   City: ${prop.city}`);
        
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`   ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        } else {
          console.log(`   ❌ No amenities found`);
        }
        
        console.log(`   Created: ${new Date(prop.created_at).toLocaleString()}`);
      });
    } else {
      console.log('❌ No Villa Bali Sale properties found in database');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    process.exit(0);
  }
}

checkVillaBaliSaleProperties();
