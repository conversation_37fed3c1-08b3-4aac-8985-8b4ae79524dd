// Test URL extraction from existing crawl data
require('dotenv').config();
const { SmartCrawler } = require('./scrape_worker/smart_crawler');
const { db, crawlJobs } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function testUrlExtraction() {
  console.log('🧪 Testing URL extraction from existing crawl data...\n');

  const crawler = new SmartCrawler();

  try {
    // Get a completed crawl job
    const completedJobs = await db.select().from(crawlJobs)
      .where(eq(crawlJobs.status, 'completed'))
      .limit(1);

    if (completedJobs.length === 0) {
      console.log('❌ No completed crawl jobs found');
      return;
    }

    const job = completedJobs[0];
    console.log(`🔍 Re-processing crawl job: ${job.id}`);
    console.log(`   Website: ${job.website_id}`);
    console.log(`   Firecrawl ID: ${job.firecrawl_job_id}`);

    // Get the crawl data again and process it
    const status = await crawler.checkCrawlStatus(job.id);
    
    if (status.data && status.data.length > 0) {
      console.log(`\n📊 Found ${status.data.length} items to re-process`);
      
      // Process the crawl results again with the fixed URL extraction
      await crawler.processCrawlResults(job.id, status.data);
      
      console.log('✅ Re-processing completed');
    } else {
      console.log('❌ No crawl data available');
    }

  } catch (error) {
    console.error('❌ Error testing URL extraction:', error.message);
  } finally {
    process.exit(0);
  }
}

testUrlExtraction();
