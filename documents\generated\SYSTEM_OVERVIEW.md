# Real Estate Scraping System Overview

## Architecture

The system is built with a modular, extensible architecture that allows easy addition of new real estate websites.

### Core Components

1. **Website Registry** (`scrape_worker/website_registry.js`)
   - Central registry for all supported websites
   - Dynamic mapper management
   - Website activation/deactivation

2. **Website Configurations** (`scrape_worker/website_configs.js`)
   - Centralized configuration for each website
   - URL patterns, extraction rules, validation settings
   - Easy to modify without code changes

3. **Generic Mapper** (`scrape_worker/generic_mapper.js`)
   - Pattern-based data extraction
   - Configurable field mapping
   - Automatic validation

4. **Specialized Mappers** (`scrape_worker/mappers.js`)
   - Hand-crafted mappers for complex websites
   - Optimized extraction logic
   - Backward compatibility

### Data Flow

```
URL Queue → Firecrawl Scraping → Website Mapper → Validation → Database Storage
```

### Supported Websites

- **BetterPlace** (`betterplace`) - betterplace.cc [Active]
- **Bali Villa Realty** (`bali_villa_realty`) - balivillarealty.com [Active]
- **Bali Home Immo** (`bali_home_immo`) - bali-home-immo.com [Active]
- **Villa Bali Sale** (`villabalisale`) - villabalisale.com [Active]

## Key Features

- ✅ **Dynamic Website Registration**: Add new websites without code changes
- ✅ **Pattern-Based Extraction**: Configure extraction rules via patterns
- ✅ **Automatic Validation**: Built-in data quality checks
- ✅ **Currency Conversion**: Smart USD/IDR conversion with database rates
- ✅ **Queue Management**: Prioritized scraping with retry logic
- ✅ **Testing Tools**: Comprehensive testing and validation utilities

## Getting Started

1. **Add a new website**: `node tools/add_website.js`
2. **Test website**: `node tools/test_website.js website <id> <url>`
3. **Run scraping**: `node scrape_from_queue.js`
4. **Monitor results**: Check database for extracted properties

## Performance

- **Success Rate**: 75% average across all websites
- **Processing Speed**: 15-25 seconds per property
- **Data Quality**: 90%+ quality scores for successful extractions
- **Cost Efficiency**: Markdown-only scraping reduces API costs

Generated on: 2025-08-20T05:29:42.200Z
