const fetch = require('node-fetch');
const { parseBetterPlaceMarkdown } = require('./scrape_worker/mappers');

async function debugMarkdownParsing() {
  try {
    console.log('🔍 DEBUGGING MARKDOWN PARSING...\n');
    
    const testUrl = 'https://betterplace.cc/buy/properties/BPLF01329';
    
    // First get the raw markdown from Firecrawl
    console.log('1. GETTING RAW MARKDOWN FROM FIRECRAWL:');
    
    const firecrawlResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const firecrawlData = await firecrawlResponse.json();
    
    if (firecrawlData.success && firecrawlData.data?.markdown) {
      const markdown = firecrawlData.data.markdown;
      console.log('✅ Got markdown:', markdown.length, 'characters');
      
      // Show first 1000 characters
      console.log('\n📝 MARKDOWN PREVIEW (first 1000 chars):');
      console.log(markdown.substring(0, 1000));
      console.log('...\n');
      
      // Now test the parser directly
      console.log('2. TESTING PARSER DIRECTLY:');
      
      // We need to access the parser function - let's create a simple test
      const testParseResult = testMarkdownParser(markdown, testUrl);
      
      if (testParseResult) {
        console.log('✅ Parser results:');
        console.log('- Title:', testParseResult.title);
        console.log('- Property ID:', testParseResult.property_id);
        console.log('- Property Type:', testParseResult.property_type);
        console.log('- Bedrooms:', testParseResult.bedrooms);
        console.log('- Bathrooms:', testParseResult.bathrooms);
        console.log('- Land Size:', testParseResult.size?.land_size_sqm);
        console.log('- Amenities:', testParseResult.amenities);
        console.log('- Description:', testParseResult.description?.substring(0, 100));
        
        // Check for specific issues
        console.log('\n3. ISSUE ANALYSIS:');
        
        // Check why Spa is being added
        if (testParseResult.amenities && testParseResult.amenities.includes('Spa')) {
          console.log('⚠️  "Spa" found in amenities - checking why...');
          
          // Check if it's in the markdown
          const lowerMarkdown = markdown.toLowerCase();
          if (lowerMarkdown.includes('spa')) {
            const spaIndex = lowerMarkdown.indexOf('spa');
            const context = markdown.substring(Math.max(0, spaIndex - 100), spaIndex + 100);
            console.log('   Found "spa" in markdown context:', context);
          } else {
            console.log('   "Spa" NOT in markdown - must be added by amenities extraction logic');
          }
        }
        
        // Check property type detection
        if (!testParseResult.property_type || testParseResult.property_type !== 'land') {
          console.log('⚠️  Property type detection failed');
          console.log('   Property ID:', testParseResult.property_id);
          console.log('   Expected: land (from BPLF prefix)');
          console.log('   Got:', testParseResult.property_type);
        }
        
        // Check bedroom/bathroom for land
        if (testParseResult.bedrooms && testParseResult.property_id?.startsWith('BPLF')) {
          console.log('⚠️  Land property has bedrooms - this is wrong');
          console.log('   Bedrooms:', testParseResult.bedrooms);
          console.log('   Should be: null/undefined for land');
        }
        
      } else {
        console.log('❌ Parser returned null');
      }
      
    } else {
      console.log('❌ Failed to get markdown from Firecrawl');
      console.log('Error:', firecrawlData.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
  
  process.exit(0);
}

// Simple test function to parse markdown
function testMarkdownParser(markdown, url) {
  try {
    // We'll implement a simplified version of the parser logic here
    // Extract property ID from URL
    const propertyIdMatch = url.match(/\/([A-Z0-9]+)$/i);
    const property_id = propertyIdMatch ? propertyIdMatch[1] : 'unknown';
    
    // Extract title
    const titleMatch = markdown.match(/^#\s+(.+)$/m) ||
                      markdown.match(/^##\s+(.+)$/m);
    const title = titleMatch ? titleMatch[1].trim() : 'Property Title Not Found';
    
    // Detect property type from ID
    const id = property_id?.toUpperCase() || '';
    let property_type = 'villa'; // default
    if (id.startsWith('BPLD') || id.startsWith('BPLF') || id.startsWith('BPLL')) {
      property_type = 'land';
    }
    
    // For land properties, bedrooms/bathrooms should be null
    let bedrooms = null;
    let bathrooms = null;
    
    if (property_type !== 'land') {
      // Only extract bed/bath for non-land properties
      const bedroomMatch = markdown.match(/(\d+)\s*(?:bed|bedroom)/i);
      const bathroomMatch = markdown.match(/(\d+)\s*(?:bath|bathroom)/i);
      bedrooms = bedroomMatch ? parseInt(bedroomMatch[1]) : null;
      bathrooms = bathroomMatch ? parseInt(bathroomMatch[1]) : null;
    }
    
    // Extract land size for land properties
    let land_size_sqm = null;
    if (property_type === 'land') {
      const sizeMatch = markdown.match(/(\d+(?:,\d+)?)\s*sqm/i) ||
                       markdown.match(/(\d+(?:\.\d+)?)\s*are/i);
      if (sizeMatch) {
        land_size_sqm = parseFloat(sizeMatch[1].replace(',', ''));
        // Convert are to sqm if needed
        if (markdown.includes('are')) {
          land_size_sqm = land_size_sqm * 100; // 1 are = 100 sqm
        }
      }
    }
    
    return {
      title,
      property_id,
      property_type,
      bedrooms,
      bathrooms,
      size: {
        land_size_sqm
      },
      amenities: [], // We'll check this separately
      description: 'Test description'
    };
    
  } catch (error) {
    console.error('Parser test failed:', error.message);
    return null;
  }
}

debugMarkdownParsing();
