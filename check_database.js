require('dotenv').config();
const { Pool } = require('pg');

async function checkDatabase() {
  const pool = new Pool({
    connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL
  });

  try {
    console.log('🔍 Checking database schema...');

    // First check what tables exist
    const tablesResult = await pool.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
    `);

    console.log('📋 Available tables:');
    tablesResult.rows.forEach(row => console.log(`  - ${row.table_name}`));

    // Try to find the property table
    const propertyTable = tablesResult.rows.find(row =>
      row.table_name.toLowerCase().includes('property') ||
      row.table_name.toLowerCase().includes('listing')
    );

    if (!propertyTable) {
      console.log('❌ No property-related table found');
      return;
    }

    console.log(`\n🔍 Checking ${propertyTable.table_name} for BPHL00994...`);

    const result = await pool.query(`
      SELECT *
      FROM ${propertyTable.table_name}
      WHERE external_id = 'BPHL00994'
        OR id = 'dc7a4988-10a8-469f-a98e-8f2f023bd05f'
      LIMIT 1
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ No property found with external_id BPHL00994 or id dc7a4988-10a8-469f-a98e-8f2f023bd05f');
    } else {
      console.log('✅ Found property:');
      const property = result.rows[0];
      console.log(`\n📊 CURRENT DATABASE VALUES:`);
      console.log(`   ID: ${property.id}`);
      console.log(`   External ID: ${property.external_id}`);
      console.log(`   Bedrooms: ${property.bedrooms} ❌ (should be 27)`);
      console.log(`   Bathrooms: ${property.bathrooms} ❌ (should be 27)`);
      console.log(`   Building Size: ${property.size_sqft} sqft (≈ ${Math.round(property.size_sqft * 0.092903)} sqm)`);
      console.log(`   Year Built: ${property.year_built} ❌ (should be 2024)`);
      console.log(`   Ownership: ${property.ownership_type}`);
      console.log(`   Lease Duration: ${property.lease_duration_years} years`);
      console.log(`   Last Scraped: ${property.last_scraped_at}`);

      console.log(`\n🔄 This property needs to be re-scraped with the new CSS-based extraction!`);
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await pool.end();
  }
}

checkDatabase();
