# API Reference

## Website Registry

### `websiteRegistry.registerWebsite(websiteId, options)`

Register a new website with the system.

**Parameters**:
- `websiteId` (string): Unique identifier for the website
- `options` (object): Registration options
  - `mapper` (function): Custom mapper function
  - `type` ('specialized' | 'generic'): Mapper type
  - `active` (boolean): Whether website is active

**Returns**: Registration object

### `websiteRegistry.getMapper(websiteId)`

Get the mapper function for a website.

**Parameters**:
- `websiteId` (string): Website identifier

**Returns**: Mapper function

### `websiteRegistry.testWebsiteMapper(websiteId, testData)`

Test a website mapper with sample data.

**Parameters**:
- `websiteId` (string): Website identifier
- `testData` (object): Test data with markdown and url

**Returns**: Test result object

## Generic Mapper

### `new GenericMapper(websiteId)`

Create a generic mapper for a website.

**Parameters**:
- `websiteId` (string): Website identifier

### `mapper.mapProperty(rawData)`

Map raw scraped data to normalized property format.

**Parameters**:
- `rawData` (object): Raw scraped data
  - `markdown` (string): Scraped markdown content
  - `metadata` (object): Page metadata
  - `url` (string): Source URL

**Returns**: Normalized property object

## Website Configuration

### Configuration Object Structure

```javascript
{
  id: string,
  name: string,
  domain: string,
  baseUrl: string,
  
  scraping: {
    formats: string[],
    onlyMainContent: boolean,
    timeout: number
  },
  
  urlPatterns: {
    sale: string[],
    rent: string[],
    listing: string[]
  },
  
  extraction: {
    currency: string,
    pricePatterns: RegExp[],
    bedroomPatterns: RegExp[],
    bathroomPatterns: RegExp[],
    skipPhrases: string[]
  },
  
  validation: {
    requiredFields: string[],
    priceRange: { min: number, max: number },
    bedroomRange: { min: number, max: number },
    skipOnMissingPrice: boolean
  },
  
  queue: {
    priority: number,
    batchSize: number,
    retryAttempts: number
  }
}
```

## Testing Tools

### `WebsiteTester`

Comprehensive testing utilities for website mappers.

#### Methods

- `testAllWebsites(testUrls)`: Test all registered websites
- `testWebsite(websiteId, testUrl)`: Test specific website
- `testExtractionPatterns(websiteId, content)`: Test pattern matching

### Command Line Usage

```bash
# Test all websites
node tools/test_website.js all

# Test specific website
node tools/test_website.js website <websiteId> <testUrl>

# Test extraction patterns
node tools/test_website.js patterns <websiteId> [content]
```

Generated on: 2025-08-20T05:29:42.206Z
