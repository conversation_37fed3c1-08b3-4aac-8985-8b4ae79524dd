// Simple test to debug mapper issues
require('dotenv').config();

async function testMappers() {
  console.log('🔍 Testing Mappers with Mock Data');
  console.log('='.repeat(40));
  
  try {
    const { mapBetterPlace } = require('./scrape_worker/mappers');
    
    const mockMarkdown = `
# Modern 3 Bedroom Villa with Rooftop in Tumbak Bayuh

**Price:** USD 450,000

**Location:** Tumbak Bayuh, Canggu, Bali

**Property Details:**
- 3 bedrooms
- 2 bathrooms  
- Building size: 180 sqm
- Land size: 250 sqm
- Year built: 2022
- Parking: 2 cars
- Ownership: Freehold
`;

    console.log('📡 Testing BetterPlace mapper...');
    
    const result = await mapBetterPlace({
      markdown: mockMarkdown,
      url: 'https://betterplace.cc/buy/properties/BPVL02270'
    });
    
    console.log('\n📊 Mapper Result:');
    console.log(JSON.stringify(result, null, 2));
    
    console.log('\n🔍 Key Fields Analysis:');
    console.log(`Title: ${result?.title || 'MISSING'}`);
    console.log(`Price: ${result?.price || 'MISSING'}`);
    console.log(`Bedrooms: ${result?.bedrooms || 'MISSING'}`);
    console.log(`Bathrooms: ${result?.bathrooms || 'MISSING'}`);
    console.log(`Address: ${result?.address || 'MISSING'}`);
    console.log(`City: ${result?.city || 'MISSING'}`);
    console.log(`Size (sqft): ${result?.size_sqft || 'MISSING'}`);
    console.log(`Parking: ${result?.parking_spaces || 'MISSING'}`);
    console.log(`Year Built: ${result?.year_built || 'MISSING'}`);
    console.log(`External ID: ${result?.external_id || result?.media?.external_id || 'MISSING'}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

testMappers().catch(console.error);
