// Test async scraping for all supported sites
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { testConnection } = require('./drizzle_client');

async function testAllSitesAsync() {
  console.log('🧪 Testing async scraping for all supported sites...\n');
  
  // Test database connection first
  console.log('1. Testing database connection...');
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ Database connection failed. Exiting.');
    return;
  }
  
  // Test URLs for each supported site
  const testSites = {
    betterplace: [
      'https://betterplace.cc/buy/properties/BPVL02232'
    ],
    bali_home_immo: [
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508'
    ],
    bali_villa_realty: [
      'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/'
    ]
  };
  
  console.log('\n2. Testing async scraping for each site...');
  
  const results = {};
  
  for (const [siteId, urls] of Object.entries(testSites)) {
    console.log(`\n🔄 Testing ${siteId}...`);
    console.log(`   URL: ${urls[0]}`);
    
    try {
      const startTime = Date.now();
      const siteResults = await runExtractBatch(siteId, urls, {});
      const endTime = Date.now();
      const duration = ((endTime - startTime) / 1000).toFixed(2);
      
      const successful = siteResults.filter(r => r.ok);
      const failed = siteResults.filter(r => !r.ok);
      
      results[siteId] = {
        success: successful.length > 0,
        duration: duration,
        successCount: successful.length,
        failCount: failed.length,
        properties: successful.map(r => r.title),
        errors: failed.map(r => r.error)
      };
      
      if (successful.length > 0) {
        console.log(`   ✅ ${siteId}: SUCCESS in ${duration}s`);
        successful.forEach(result => {
          console.log(`      📋 ${result.title}`);
        });
      } else {
        console.log(`   ❌ ${siteId}: FAILED in ${duration}s`);
        failed.forEach(result => {
          console.log(`      ❌ ${result.error}`);
        });
      }
      
    } catch (error) {
      console.log(`   ❌ ${siteId}: ERROR - ${error.message}`);
      results[siteId] = {
        success: false,
        error: error.message
      };
    }
  }
  
  // Summary
  console.log('\n📊 ASYNC SCRAPING TEST SUMMARY:');
  console.log('='.repeat(50));
  
  const successfulSites = Object.entries(results).filter(([_, result]) => result.success);
  const failedSites = Object.entries(results).filter(([_, result]) => !result.success);
  
  console.log(`✅ Successful Sites: ${successfulSites.length}/${Object.keys(testSites).length}`);
  console.log(`❌ Failed Sites: ${failedSites.length}/${Object.keys(testSites).length}`);
  
  if (successfulSites.length > 0) {
    console.log('\n✅ WORKING SITES:');
    successfulSites.forEach(([siteId, result]) => {
      console.log(`   ${siteId}: ${result.duration}s, ${result.successCount} properties`);
    });
  }
  
  if (failedSites.length > 0) {
    console.log('\n❌ FAILED SITES:');
    failedSites.forEach(([siteId, result]) => {
      console.log(`   ${siteId}: ${result.error || 'Unknown error'}`);
    });
  }
  
  // Conclusion
  const allWorking = successfulSites.length === Object.keys(testSites).length;
  console.log(`\n🎯 CONCLUSION: Async scraping ${allWorking ? 'WORKS' : 'PARTIALLY WORKS'} for all tested sites`);
  
  return results;
}

// Run the test
if (require.main === module) {
  testAllSitesAsync().catch(console.error);
}

module.exports = { testAllSitesAsync };
