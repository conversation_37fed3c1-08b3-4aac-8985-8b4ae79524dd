// Analyze Villa Bali Sale markdown for bedroom/bathroom/size patterns
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function analyzeVillaBaliSaleMarkdown() {
  console.log('🔍 Analyzing Villa Bali Sale Markdown');
  console.log('='.repeat(50));
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/lovina/property-for-sale-in-singaraja---lovina';
  
  try {
    console.log(`📡 Scraping: ${testUrl}`);
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.markdown) {
      const markdown = result.data.markdown;
      console.log(`📝 Markdown length: ${markdown.length} chars`);
      
      // Look for bedroom/bathroom patterns
      console.log('\n🛏️ Searching for bedroom patterns...');
      
      const bedroomPatterns = [
        /(\d+)\s*bed/gi,
        /(\d+)\s*bedroom/gi,
        /bed.*?(\d+)/gi,
        /bedroom.*?(\d+)/gi,
        /(\d+)\s*BR/gi,
        /BR.*?(\d+)/gi,
        /kamar.*?(\d+)/gi,
        /(\d+).*?kamar/gi
      ];
      
      bedroomPatterns.forEach((pattern, i) => {
        const matches = [...markdown.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\nBedroom Pattern ${i+1} (${pattern.source}):`);
          matches.slice(0, 3).forEach(match => {
            const start = Math.max(0, match.index - 30);
            const end = Math.min(markdown.length, match.index + match[0].length + 30);
            const context = markdown.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      console.log('\n🚿 Searching for bathroom patterns...');
      
      const bathroomPatterns = [
        /(\d+)\s*bath/gi,
        /(\d+)\s*bathroom/gi,
        /bath.*?(\d+)/gi,
        /bathroom.*?(\d+)/gi,
        /(\d+)\s*BA/gi,
        /BA.*?(\d+)/gi,
        /toilet/gi,
        /wc/gi
      ];
      
      bathroomPatterns.forEach((pattern, i) => {
        const matches = [...markdown.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\nBathroom Pattern ${i+1} (${pattern.source}):`);
          matches.slice(0, 3).forEach(match => {
            const start = Math.max(0, match.index - 30);
            const end = Math.min(markdown.length, match.index + match[0].length + 30);
            const context = markdown.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      console.log('\n📐 Searching for size patterns...');
      
      const sizePatterns = [
        /(\d+)\s*sqm/gi,
        /(\d+)\s*m2/gi,
        /(\d+)\s*m²/gi,
        /(\d+)\s*sq/gi,
        /size.*?(\d+)/gi,
        /area.*?(\d+)/gi,
        /building.*?(\d+)/gi,
        /land.*?(\d+)/gi,
        /lot.*?(\d+)/gi
      ];
      
      sizePatterns.forEach((pattern, i) => {
        const matches = [...markdown.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\nSize Pattern ${i+1} (${pattern.source}):`);
          matches.slice(0, 3).forEach(match => {
            const start = Math.max(0, match.index - 50);
            const end = Math.min(markdown.length, match.index + match[0].length + 50);
            const context = markdown.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      // Show a sample of the markdown
      console.log('\n📄 Markdown Sample (first 1000 chars):');
      console.log(markdown.substring(0, 1000));
      
    } else {
      console.log('❌ Failed to get markdown');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

analyzeVillaBaliSaleMarkdown().then(() => process.exit(0));
