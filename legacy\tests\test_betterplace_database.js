// Test BetterPlace markdown scraping and database storage
require('dotenv').config();
const Firecrawl = require('firecrawl').default;
const { db, properties } = require('./drizzle_client');
const { mapBetterPlace } = require('./scrape_worker/mappers');
const { eq } = require('drizzle-orm');

async function testBetterPlaceDatabase() {
  console.log('🧪 Testing BetterPlace Markdown Scraping & Database Storage');
  console.log('='.repeat(70));
  
  const firecrawl = new Firecrawl({ apiKey: process.env.FIRECRAWL_API_KEY });
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02270';
  
  try {
    console.log(`🔄 Step 1: Scraping URL: ${testUrl}`);
    
    // Scrape with markdown format (new cost-effective method)
    const result = await firecrawl.scrapeUrl(testUrl, {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 60000
    });
    
    if (!result.success) {
      console.log('❌ Scraping failed:', result.error);
      return;
    }
    
    console.log('✅ Scraping successful');
    console.log('📊 Full result structure:');
    console.log(JSON.stringify(result, null, 2));

    const markdownContent = result.data?.markdown || result.markdown || '';
    console.log(`📄 Markdown length: ${markdownContent.length} characters`);

    if (!markdownContent) {
      console.log('❌ No markdown content received');
      return;
    }

    console.log('\n🔄 Step 2: Mapping scraped data');

    // Map the scraped data using our new markdown parser
    const mappedData = await mapBetterPlace({
      markdown: markdownContent,
      url: testUrl
    });
    
    if (!mappedData) {
      console.log('❌ Mapping failed - no data returned');
      return;
    }
    
    console.log('✅ Mapping successful');
    console.log('📊 Mapped data:');
    console.log(JSON.stringify(mappedData, null, 2));
    
    console.log('\n🔄 Step 3: Saving to database');
    
    // Check if property already exists
    const existingProperty = await db
      .select()
      .from(properties)
      .where(eq(properties.external_id, mappedData.media.external_id))
      .limit(1);
    
    if (existingProperty.length > 0) {
      console.log('⚠️  Property already exists, updating...');
      
      const updateResult = await db
        .update(properties)
        .set({
          ...mappedData,
          updated_at: new Date()
        })
        .where(eq(properties.external_id, mappedData.media.external_id))
        .returning();
      
      console.log('✅ Property updated successfully');
      console.log(`📋 Updated property ID: ${updateResult[0].id}`);
      
    } else {
      console.log('➕ Creating new property...');
      
      const insertResult = await db
        .insert(properties)
        .values({
          ...mappedData,
          created_at: new Date(),
          updated_at: new Date()
        })
        .returning();
      
      console.log('✅ Property created successfully');
      console.log(`📋 New property ID: ${insertResult[0].id}`);
    }
    
    console.log('\n🔄 Step 4: Verifying database storage');
    
    // Verify the data was stored correctly
    const storedProperty = await db
      .select()
      .from(properties)
      .where(eq(properties.external_id, mappedData.media.external_id))
      .limit(1);
    
    if (storedProperty.length > 0) {
      const prop = storedProperty[0];
      console.log('✅ Database verification successful');
      console.log('📋 Stored property details:');
      console.log(`   ID: ${prop.id}`);
      console.log(`   Title: ${prop.title}`);
      console.log(`   Price: ${prop.price}`);
      console.log(`   Location: ${prop.address}, ${prop.city}, ${prop.state}`);
      console.log(`   Bedrooms: ${prop.bedrooms}`);
      console.log(`   Bathrooms: ${prop.bathrooms}`);
      console.log(`   Size: ${prop.size_sqft} sqft (building), ${prop.lot_size_sqft} sqft (lot)`);
      console.log(`   Images: ${prop.media?.image_count || 0} images`);
      console.log(`   External ID: ${prop.external_id}`);
      console.log(`   Source URL: ${prop.media?.source_url}`);
      console.log(`   Created: ${prop.created_at}`);
      console.log(`   Updated: ${prop.updated_at}`);
      
      // Check if all critical fields are populated
      const criticalFields = ['title', 'price', 'address', 'bedrooms', 'bathrooms'];
      const missingFields = criticalFields.filter(field => !prop[field]);
      
      if (missingFields.length === 0) {
        console.log('✅ All critical fields populated correctly');
      } else {
        console.log(`⚠️  Missing critical fields: ${missingFields.join(', ')}`);
      }
      
    } else {
      console.log('❌ Database verification failed - property not found');
    }
    
    console.log('\n🎉 Test completed successfully!');
    console.log('💰 Cost savings: Using markdown instead of JSON (5x cheaper)');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testBetterPlaceDatabase().catch(console.error);
