// Debug BetterPlace bedrooms bug - why 3741 bedrooms?
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function debugBetterPlaceBedrooms() {
  console.log('🚨 Debug BetterPlace Bedrooms Bug');
  console.log('='.repeat(50));
  
  try {
    // Find the property with 3741 bedrooms
    const buggyProperty = await db
      .select()
      .from(properties)
      .where(eq(properties.id, '47ab5d50-b2b0-456f-a839-82691e164e86'))
      .limit(1);
    
    if (buggyProperty.length === 0) {
      console.log('❌ Property not found');
      return;
    }
    
    const prop = buggyProperty[0];
    
    console.log('🔍 Property Details:');
    console.log(`ID: ${prop.id}`);
    console.log(`Title: "${prop.title}"`);
    console.log(`Source URL: ${prop.source_url}`);
    console.log(`Bedrooms: ${prop.bedrooms} ❌ WRONG!`);
    console.log(`Bathrooms: ${prop.bathrooms}`);
    console.log(`Building Size: ${prop.size_sqft} sqft`);
    console.log(`Created: ${new Date(prop.created_at).toLocaleString()}`);
    
    // Now let's scrape this URL again to see what's happening
    const testUrl = prop.source_url;
    console.log(`\n📡 Re-scraping URL to debug: ${testUrl}`);
    
    const { getKeyManager } = require('./scrape_worker/key_manager');
    const keyManager = getKeyManager();
    const currentKey = keyManager.getCurrentKey();
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.markdown) {
      const content = result.data.markdown;
      console.log(`📝 Markdown length: ${content.length} chars`);
      
      // Test bedroom patterns manually
      console.log('\n🛏️ Testing Bedroom Patterns:');
      
      const bedroomPatterns = [
        /([1-9])\s*(?:bed|bedroom|kamar tidur)s?(?!\d)/i,
        /(?:bed|bedroom)s?[:\s]+([1-9])(?!\d)/i,
        /([1-9])\s*BR(?!\d)/i,
        /[-•]\s*([1-9])\s*(?:bed|bedroom)s?/i,
        /\d+\s*(?:bed|bedroom|BR)s?[\/\s]*([1-9])\s*(?:bath|bathroom|BA)s?/i,
        /([1-9])\s*kamar\s*tidur/i,
        /(?:bed|bedroom)s?\s*[|\s]+([1-9])/i,
        /(1[0-5])\s*(?:bed|bedroom)s?(?!\d)/i
      ];
      
      bedroomPatterns.forEach((pattern, i) => {
        const matches = [...content.matchAll(new RegExp(pattern.source, 'gi'))];
        if (matches.length > 0) {
          console.log(`\n✅ Pattern ${i+1} (${pattern.source}) found ${matches.length} matches:`);
          matches.slice(0, 5).forEach(match => {
            const start = Math.max(0, match.index - 30);
            const end = Math.min(content.length, match.index + match[0].length + 30);
            const context = content.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
            console.log(`   → Bedroom count: ${match[1]}`);
          });
        }
      });
      
      // Look for any occurrence of "3741"
      console.log('\n🔍 Searching for "3741" in markdown:');
      const matches3741 = [...content.matchAll(/3741/g)];
      if (matches3741.length > 0) {
        console.log(`Found ${matches3741.length} occurrences of "3741":`);
        matches3741.forEach(match => {
          const start = Math.max(0, match.index - 50);
          const end = Math.min(content.length, match.index + 4 + 50);
          const context = content.substring(start, end);
          console.log(`   "${context.replace(/\n/g, ' ')}"`);
        });
      } else {
        console.log('❌ No "3741" found in markdown');
      }
      
      // Look for any large numbers that might be misinterpreted
      console.log('\n🔍 Searching for large numbers:');
      const largeNumbers = [...content.matchAll(/\b\d{3,}\b/g)];
      if (largeNumbers.length > 0) {
        console.log(`Found ${largeNumbers.length} large numbers:`);
        largeNumbers.slice(0, 10).forEach(match => {
          const start = Math.max(0, match.index - 30);
          const end = Math.min(content.length, match.index + match[0].length + 30);
          const context = content.substring(start, end);
          console.log(`   "${context.replace(/\n/g, ' ')}"`);
        });
      }
      
    } else {
      console.log('❌ Failed to get markdown');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugBetterPlaceBedrooms().then(() => process.exit(0));
