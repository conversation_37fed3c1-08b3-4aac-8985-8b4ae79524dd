// Site-specific mappers -> NormalizedProperty

const { getCurrencyService } = require('./currency_service');
const USD_TO_IDR = Number(process.env.USD_TO_IDR || 15800); // Fallback only

// Initialize currency service
const currencyService = getCurrencyService();

function parseIdr(str) {
  if (!str) return undefined;
  const n = parseInt(String(str).replace(/[^\d]/g, ''), 10);
  return Number.isFinite(n) ? n : undefined;
}

function parseUsd(str) {
  if (!str) return undefined;
  const n = parseFloat(String(str).replace(/[^\d.]/g, ''));
  return Number.isFinite(n) ? n : undefined;
}

// Smart price conversion using database exchange rates
async function convertPriceToIDR(priceString) {
  if (!priceString) return undefined;

  try {
    const idrPrice = await currencyService.convertPriceToIDR(priceString);
    return idrPrice;
  } catch (error) {
    console.log(`⚠️  Smart conversion failed for "${priceString}": ${error.message}`);

    // Fallback to old method
    const hasUsd = /\$|USD/i.test(priceString);
    if (hasUsd) {
      const usdAmount = parseUsd(priceString);
      return usdAmount ? usdAmount * USD_TO_IDR : undefined;
    } else {
      return parseIdr(priceString);
    }
  }
}

function splitLocation(loc) {
  if (!loc) return { city: '', state: undefined };
  const [city, state] = String(loc).split(',').map(s => s.trim());

  // Auto-detect Bali state for common Bali cities
  const baliCities = [
    'canggu', 'seminyak', 'ubud', 'sanur', 'denpasar', 'kuta', 'legian',
    'jimbaran', 'nusa dua', 'uluwatu', 'bukit', 'pererenan', 'berawa',
    'echo beach', 'bingin', 'padang padang', 'dreamland', 'balangan',
    'lovina', 'amed', 'candidasa', 'tabanan', 'mengwi', 'bedugul',
    'munduk', 'pemuteran', 'tulamben', 'karangasem', 'klungkung',
    'gianyar', 'bangli', 'negara', 'singaraja', 'oberoi'
  ];

  let finalState = state;
  if (!finalState && city) {
    const cityLower = city.toLowerCase();
    if (baliCities.includes(cityLower)) {
      finalState = 'Bali';
    }
  }

  return { city: city || '', state: finalState || undefined };
}

function extractYear(raw){
  // Prefer explicit year_built field
  let y = raw?.year_built;
  if (typeof y === 'string') {
    const m = y.match(/(19|20)\d{2}/);
    if (m) y = parseInt(m[0], 10);
  }
  if (typeof y === 'number' && y >= 1900 && y <= new Date().getFullYear()+1) return y;
  // Fallback: try description/text
  const text = [raw?.description, raw?.details, raw?.content].filter(Boolean).join(' ');
  const m2 = text.match(/(built|year\s*built|constructed|renovated)\D{0,10}((19|20)\d{2})/i);
  if (m2) {
    const yr = parseInt(m2[2], 10);
    if (yr >= 1900 && yr <= new Date().getFullYear()+1) return yr;
  }
  return undefined;
}

function extractParkingSpaces(parkingStr) {
  if (!parkingStr) return undefined;

  // Convert to string and lowercase for parsing
  const parking = String(parkingStr).toLowerCase();

  // Look for explicit numbers first
  const numbers = parking.match(/(\d+)\s*(car|vehicle|space|parking)/);
  if (numbers) {
    return parseInt(numbers[1], 10);
  }

  // Look for standalone numbers
  const standaloneNumbers = parking.match(/\b(\d+)\b/);
  if (standaloneNumbers) {
    const num = parseInt(standaloneNumbers[1], 10);
    if (num >= 1 && num <= 10) return num; // Reasonable range for parking spaces
  }

  // Default mappings for common terms
  if (parking.includes('garage') || parking.includes('covered')) return 1;
  if (parking.includes('open') || parking.includes('available')) return 1;
  if (parking.includes('carport')) return 1;
  if (parking.includes('no parking') || parking.includes('none')) return 0;

  // If we have parking info but can't parse it, assume 1 space
  if (parking.trim().length > 0) return 1;

  return undefined;
}

// Sanitize text for database insertion
function sanitizeText(text) {
  if (!text || typeof text !== 'string') return '';

  return text
    // Replace problematic quotes and apostrophes
    .replace(/'/g, "'")  // Replace smart quotes with regular apostrophes
    .replace(/"/g, '"')  // Replace smart quotes with regular quotes
    .replace(/"/g, '"')  // Replace smart quotes with regular quotes
    .replace(/–/g, '-')  // Replace en-dash with regular dash
    .replace(/—/g, '-')  // Replace em-dash with regular dash
    // Remove or replace other problematic characters
    .replace(/[\u2018\u2019]/g, "'")  // Replace left/right single quotes
    .replace(/[\u201C\u201D]/g, '"')  // Replace left/right double quotes
    .replace(/[\u2013\u2014]/g, '-')  // Replace en/em dashes
    .replace(/\u2026/g, '...')        // Replace ellipsis
    // Trim whitespace and limit length
    .trim()
    .substring(0, 5000); // Limit to 5000 characters
}

// Enhanced amenities extraction for BetterPlace
function extractBetterPlaceAmenities(data) {
  const amenities = [];

  // Start with existing amenities array
  if (data?.amenities && Array.isArray(data.amenities)) {
    amenities.push(...data.amenities);
  }

  // Extract from description
  const description = (data?.description || '').toLowerCase();
  const title = (data?.title || '').toLowerCase();
  const allText = `${title} ${description}`;

  // Common BetterPlace amenities patterns
  const amenityPatterns = {
    'Swimming Pool': ['pool', 'swimming pool', 'private pool'],
    'Furnished': ['furnished', 'fully furnished', 'semi furnished'],
    'Air Conditioning': ['air conditioning', 'air conditioner', 'ac', 'aircon'],
    'WiFi/Internet': ['wifi', 'internet', 'fiber optic', 'broadband'],
    'Kitchen': ['kitchen', 'equipped kitchen', 'modern kitchen'],
    'Parking': ['parking', 'garage', 'carport'],
    'Garden': ['garden', 'landscaped', 'tropical garden'],
    'Security': ['security', '24h security', 'gated'],
    'Balcony/Terrace': ['balcony', 'terrace', 'deck', 'patio'],
    'Rooftop': ['rooftop', 'roof terrace', 'rooftop terrace'],
    'Enclosed Living': ['enclosed living', 'living room'],
    'TV': ['tv', 'television', 'smart tv'],
    'Washing Machine': ['washing machine', 'laundry'],
    'Rice Field View': ['rice field', 'ricefield', 'paddy view'],
    'Close to Beach': ['beach', 'close to beach', 'near beach']
  };

  // Check for amenity patterns in text
  Object.entries(amenityPatterns).forEach(([amenity, patterns]) => {
    if (patterns.some(pattern => allText.includes(pattern))) {
      if (!amenities.some(existing => existing.toLowerCase().includes(amenity.toLowerCase()))) {
        amenities.push(amenity);
      }
    }
  });

  // Extract from specific fields
  if (data?.furnishing && data.furnishing !== 'Unfurnished') {
    amenities.push(data.furnishing);
  }

  if (data?.pool_type) {
    amenities.push(`${data.pool_type} Pool`);
  }

  if (data?.parking_type) {
    amenities.push(data.parking_type);
  }

  // Extract from size information
  if (data?.size?.building_size_sqm) {
    amenities.push(`Building Size: ${data.size.building_size_sqm} sqm`);
  }

  if (data?.size?.land_size_sqm) {
    amenities.push(`Land Size: ${data.size.land_size_sqm} sqm`);
  }

  // Remove duplicates and empty values
  return [...new Set(amenities.filter(a => a && a.trim()))];
}

// Universal ownership information extraction
function extractOwnershipInfo(raw) {
  let ownership_type = null;
  let lease_duration_years = null;
  let lease_duration_text = null;

  // Combine all text sources for analysis
  const textSources = [
    raw?.title || '',
    raw?.description || '',
    raw?.details || '',
    raw?.ownership || '',
    raw?.tenure || '',
    raw?.lease_info || ''
  ];

  const allText = textSources.join(' ').toLowerCase();

  // Extract ownership type
  if (allText.includes('freehold')) {
    ownership_type = 'FREEHOLD';
  } else if (allText.includes('leasehold') || allText.includes('lease hold')) {
    ownership_type = 'LEASEHOLD';
  } else if (allText.includes('rental') || allText.includes('for rent') || allText.includes('monthly rent')) {
    ownership_type = 'RENT';
  }

  // Extract lease duration for leasehold properties
  if (ownership_type === 'LEASEHOLD' || allText.includes('year')) {
    // Look for patterns like "25 years", "30 year lease", "25+25 years", "99-year"
    const yearPatterns = [
      /(\d+)\s*\+\s*(\d+)\s*years?/i,  // "25+25 years"
      /(\d+)\s*years?\s*lease/i,        // "30 year lease"
      /(\d+)\s*-\s*years?/i,            // "99-year"
      /(\d+)\s*years?/i                 // "25 years"
    ];

    for (const pattern of yearPatterns) {
      const match = allText.match(pattern);
      if (match) {
        lease_duration_text = match[0];

        // For patterns like "25+25", use the first number
        lease_duration_years = parseInt(match[1], 10);

        // If we found a lease duration, set ownership to LEASEHOLD
        if (!ownership_type) {
          ownership_type = 'LEASEHOLD';
        }
        break;
      }
    }
  }

  // Check URL for ownership hints (like Villa Bali Sale)
  if (!ownership_type && raw?.url) {
    if (raw.url.includes('/freehold/') || raw.url.includes('freehold')) {
      ownership_type = 'FREEHOLD';
    } else if (raw.url.includes('/leasehold/') || raw.url.includes('leasehold')) {
      ownership_type = 'LEASEHOLD';
    } else if (raw.url.includes('/rent/') || raw.url.includes('/rental/')) {
      ownership_type = 'RENT';
    }
  }

  // Default to FREEHOLD for sale properties if not specified
  if (!ownership_type && raw?.price && !raw?.rent_price) {
    // Only assume FREEHOLD for high-value properties (likely sales)
    const priceStr = String(raw.price).replace(/[^\d]/g, '');
    const priceNum = parseInt(priceStr, 10);
    if (priceNum > 1000000) { // > 1M IDR suggests sale, not rent
      ownership_type = 'FREEHOLD';
    }
  }

  return {
    ownership_type,
    lease_duration_years,
    lease_duration_text
  };
}

// BetterPlace (sale, often USD) - ASYNC for smart currency conversion
async function mapBetterPlace(raw) {
  // Handle both direct data and scraped data structure
  let data;

  // Try markdown parsing first (cost-effective)
  if (raw?.markdown) {
    console.log('   📝 Using markdown data for BetterPlace');
    data = parseBetterPlaceMarkdown(raw.markdown, raw.url || '');

    // If parsing returned null (e.g., SOLD property), skip this property
    if (data === null) {
      console.log('   🚫 Property skipped by parser (likely SOLD/RENTED/INACTIVE)');
      return null;
    }
  } else {
    // Fallback to JSON data
    console.log('   📊 Using JSON data for BetterPlace');
    data = raw?.json || raw;
  }

  const price = await convertPriceToIDR(data?.price);
  const { city, state } = splitLocation(data?.location);
  const year_built = data?.year_built || extractYear(data);
  const parking_spaces = data?.parking || extractParkingSpaces(data?.parking);

  // Preserve original location for debugging
  const originalLocation = data?.location;

  // Smart classification - use parsed property_type if available, otherwise classify from text
  let category, type;
  if (data?.property_type) {
    // Use property type from BetterPlace parser
    const propertyType = data.property_type.toLowerCase();
    if (propertyType === 'land') {
      category = 'LAND';
      type = 'LAND';
    } else if (propertyType === 'villa') {
      category = 'RESIDENTIAL';
      type = 'VILLA';
    } else if (propertyType === 'apartment') {
      category = 'RESIDENTIAL';
      type = 'APARTMENT';
    } else if (propertyType === 'hotel') {
      category = 'COMMERCIAL';
      type = 'OTHER';
    } else {
      // Fallback to text-based classification
      const classified = classifyProperty(data?.title || '', data?.description || '');
      category = classified.category;
      type = classified.type;
    }
  } else {
    // Fallback to text-based classification
    const classified = classifyProperty(data?.title || '', data?.description || '');
    category = classified.category;
    type = classified.type;
  }

  // Extract ownership information - use parsed data first, then fallback
  const ownership_type = data?.ownership_type || extractOwnershipInfo(data)?.ownership_type;
  const lease_duration_years = data?.lease_duration_years || extractOwnershipInfo(data)?.lease_duration_years;
  const lease_duration_text = data?.lease_duration_text || extractOwnershipInfo(data)?.lease_duration_text;

  // Extract enhanced amenities - use parsed data first, then fallback
  const extractedAmenities = data?.amenities || extractBetterPlaceAmenities(data);

  return {
    title: sanitizeText(data?.title || ''),
    category,
    type,
    status: data?.status || 'AVAILABLE', // Use status from parsed data
    address: sanitizeText(originalLocation || ''), // Use original location
    city,
    state,
    country: 'Indonesia',
    location: originalLocation, // Add location field for compatibility
    description: sanitizeText(data?.description || ''),
    price,
    bedrooms: data?.bedrooms ?? undefined,
    bathrooms: data?.bathrooms ?? undefined,
    parking_spaces,
    size_sqft: data?.size?.building_size_sqm ? data.size.building_size_sqm * 10.764 : undefined,
    lot_size_sqft: data?.size?.land_size_sqm ? data.size.land_size_sqm * 10.764 : undefined,
    year_built,
    ownership_type,
    lease_duration_years,
    lease_duration_text,
    // Note: Database uses 'type' field, not 'property_type'
    amenities: extractedAmenities, // Direct JSON, not wrapped in raw_amenities
    media: {
      images: data?.images || [],
      image_count: data?.images?.length || 0,
      source_id: 'betterplace',
      external_id: data?.property_id || '',
      source_url: raw?.url || data?.detail_url || ''
    }
  };
}

// BetterPlace-specific markdown parser
function parseBetterPlaceMarkdown(markdown, url) {
  try {
    console.log(`   🔍 Parsing BetterPlace markdown (${markdown.length} chars)...`);

    // Extract property ID from URL
    const propertyIdMatch = url.match(/\/([A-Z0-9]+)$/i);
    const property_id = propertyIdMatch ? propertyIdMatch[1] : 'unknown';

    // Extract title - BetterPlace usually has title as first heading
    const titleMatch = markdown.match(/^#\s+(.+)$/m) ||
                      markdown.match(/^##\s+(.+)$/m) ||
                      markdown.match(/!\[([^\]]+)\]/) ||
                      markdown.match(/\*\*([^*]+)\*\*/);
    const title = titleMatch ? titleMatch[1].trim() : 'Property Title Not Found';

    // Extract price - BetterPlace often shows USD prices with enhanced validation
    let price = null;

    // Define price patterns with priority order
    const pricePatternGroups = [
      // Group 1: Explicit price context (highest priority)
      {
        patterns: [
          /(?:price|cost|value|asking)[:\s]*(?:USD|US\$|\$)\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i,
          /(?:price|cost|value|asking)[:\s]*(?:IDR|Rp)\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i
        ],
        minUSD: 10000,    // Minimum $10,000 for explicit price context
        minIDR: 100000000 // Minimum 100M IDR for explicit price context
      },

      // Group 2: Currency with reasonable context
      {
        patterns: [
          /(?:USD|US\$|\$)\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i,
          /(?:IDR|Rp)\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i
        ],
        minUSD: 50000,     // Minimum $50,000 for general USD
        minIDR: 500000000  // Minimum 500M IDR for general IDR
      },

      // Group 3: Reverse patterns (lower priority)
      {
        patterns: [
          /([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)\s*(?:USD|US\$|\$)/i,
          /([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)\s*(?:IDR|Rp)/i
        ],
        minUSD: 100000,    // Minimum $100,000 for reverse patterns
        minIDR: 1000000000 // Minimum 1B IDR for reverse patterns
      }
    ];

    for (const group of pricePatternGroups) {
      for (const pattern of group.patterns) {
        const match = markdown.match(pattern);
        if (match) {
          let numericValue = parseFloat(match[1].replace(/,/g, ''));
          const fullMatch = match[0].toLowerCase();

          // Handle multipliers
          if (match[1].toLowerCase().includes('million') || match[1].toLowerCase().includes('juta')) {
            numericValue *= 1000000;
          } else if (match[1].toLowerCase().includes('billion') || match[1].toLowerCase().includes('miliar')) {
            numericValue *= 1000000000;
          }

          // Currency-specific validation
          const isIDR = fullMatch.includes('idr') || fullMatch.includes('rp');
          const isUSD = fullMatch.includes('usd') || fullMatch.includes('$');

          let isValid = false;
          if (isIDR && numericValue >= group.minIDR) {
            isValid = true;
          } else if (isUSD && numericValue >= group.minUSD) {
            isValid = true;
          }

          // Additional context validation for edge cases
          if (isValid) {
            // Check surrounding context to avoid false matches
            const contextBefore = markdown.substring(Math.max(0, match.index - 30), match.index);
            const contextAfter = markdown.substring(match.index + match[0].length, match.index + match[0].length + 30);
            const fullContext = (contextBefore + ' ' + contextAfter).toLowerCase();

            // Skip if appears in non-price contexts
            if (fullContext.includes('year') ||
                fullContext.includes('bedroom') ||
                fullContext.includes('bathroom') ||
                fullContext.includes('sqm') ||
                fullContext.includes('m2') ||
                fullContext.includes('parking') ||
                fullContext.includes('built') ||
                fullContext.includes('size')) {
              continue;
            }

            price = match[0];
            console.log(`   💰 Found valid price: ${price} (${numericValue})`);
            break;
          }
        }
      }

      // If we found a valid price, stop searching
      if (price) break;
    }

    // Detect property type first to determine if we should extract bedrooms/bathrooms
    const propertyType = detectBetterPlacePropertyType(property_id, title, markdown);

    // Extract bedrooms/bathrooms only for non-land properties
    let bedrooms = null;
    let bathrooms = null;

    if (propertyType !== 'land') {
      // Extract bedrooms - prioritize BetterPlace icon-based extraction first
      let bedroomMatch = null;

      // First try BetterPlace-specific CSS class pattern (highest priority)
      // Look for bedroom icon followed by details_item__info__value__ramxJ class
      const bedroomCssPattern = /bedrooms\.7a6788f7\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
      bedroomMatch = markdown.match(bedroomCssPattern);
      if (bedroomMatch) {
        bedroomMatch = [bedroomMatch[0], bedroomMatch[1]]; // Format like other matches
        console.log(`   🎯 Using CSS-based bedroom extraction: ${bedroomMatch[1]} bedrooms`);
      }

      // Strategy 2: Look for bedroom count that matches the title
      if (!bedroomMatch) {
        const titleBedroomMatch = title.match(/(\d+)\s*bedroom/i);
        if (titleBedroomMatch) {
          const titleBedroomCount = parseInt(titleBedroomMatch[1]);
          // Find icon pattern that matches the title bedroom count
          const iconPattern = new RegExp(`!\\[bedrooms?\\]\\([^)]+\\)(${titleBedroomCount})`, 'i');
          bedroomMatch = markdown.match(iconPattern);
        }
      }

      // Strategy 3: Global search as final fallback
      if (!bedroomMatch) {
        bedroomMatch = markdown.match(/!\[bedrooms?\]\([^)]+\)([1-9]|1[0-5])/i);
      }

      // If no icon match, try title-based extraction - SAFE: only 1-15 bedrooms
      if (!bedroomMatch) {
        bedroomMatch = markdown.match(/(?:exclusive|modern|luxury|beautiful|stunning|amazing|new|brand\s*new)?\s*([1-9]|1[0-5])-?(?:bed|bedroom)(?!\d)/i);
      }

      // If no title match, try other patterns - ALL SAFE: only 1-15 bedrooms
      if (!bedroomMatch) {
        bedroomMatch =
          // Hyphenated patterns: "1-Bedroom", "2-Bed", "3-BR" - SAFE
          markdown.match(/([1-9]|1[0-5])-(?:bed|bedroom|BR)(?!\d)/i) ||
          // Standard patterns: "2 bedrooms", "3 bed", "2 kamar tidur" - SAFE
          markdown.match(/([1-9]|1[0-5])\s*(?:bed|bedroom|kamar tidur|BR)(?!\d)/i) ||
          // Colon/space patterns: "Bedrooms: 2", "Bed: 3" - SAFE
          markdown.match(/(?:bed|bedroom)s?[:\s]+([1-9]|1[0-5])(?!\d)/i) ||
          // Abbreviation patterns: "2 BR", "3BR" - SAFE
          markdown.match(/([1-9]|1[0-5])\s*BR(?!\d)/i) ||
          // Property details patterns: "- 2 bedrooms", "• 3 bed" - SAFE
          markdown.match(/[-•]\s*([1-9]|1[0-5])\s*(?:bed|bedroom)s?/i) ||
          // Slash patterns: "2/3" (bed/bath), "3BR/2BA" - SAFE
          markdown.match(/([1-9]|1[0-5])\s*(?:bed|bedroom|BR)s?[\/\s]*\d+\s*(?:bath|bathroom|BA)s?/i) ||
          // Indonesian patterns: "2 kamar tidur" - SAFE
          markdown.match(/([1-9]|1[0-5])\s*kamar\s*tidur/i) ||
          // Table/list patterns: "Bedrooms | 2", "Bed | 3" - SAFE
          markdown.match(/(?:bed|bedroom)s?\s*[|\s]+([1-9]|1[0-5])/i);
      }

      bedrooms = bedroomMatch ? parseInt(bedroomMatch[1]) : null;

      // Additional validation for bedroom count (reasonable range for properties)
      if (bedrooms && (bedrooms > 15 || bedrooms < 1)) {
        console.log(`   ⚠️  Suspicious bedroom count: ${bedrooms}, setting to null`);
        bedrooms = null;
      }

      // Extract bathrooms - comprehensive patterns with better validation
      let bathroomMatch = null;

      // First try BetterPlace-specific CSS class pattern (highest priority)
      // Look for bathroom icon followed by details_item__info__value__ramxJ class
      const bathroomCssPattern = /bathrooms\.45d31171\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
      bathroomMatch = markdown.match(bathroomCssPattern);
      if (bathroomMatch) {
        bathroomMatch = [bathroomMatch[0], bathroomMatch[1]]; // Format like other matches
        console.log(`   🎯 Using CSS-based bathroom extraction: ${bathroomMatch[1]} bathrooms`);
      }

      // Strategy 2: Look for bathroom count that matches expected range for the property
      if (!bathroomMatch) {
        const titleBedroomMatch = title.match(/(\d+)\s*bedroom/i);
        if (titleBedroomMatch) {
          const titleBedroomCount = parseInt(titleBedroomMatch[1]);
          // Look for reasonable bathroom count (typically bedroom count or bedroom count + 1)
          const expectedBathrooms = [titleBedroomCount - 1, titleBedroomCount, titleBedroomCount + 1];
          for (const expectedCount of expectedBathrooms) {
            if (expectedCount >= 1 && expectedCount <= 9) {
              const iconPattern = new RegExp(`!\\[bathrooms?\\]\\([^)]+\\)(${expectedCount})`, 'i');
              bathroomMatch = markdown.match(iconPattern);
              if (bathroomMatch) break;
            }
          }
        }
      }

      // If no match after title, try global patterns
      if (!bathroomMatch) {
        bathroomMatch =
          // BetterPlace specific: "![bathrooms](url)3"
          markdown.match(/!\[bathrooms?\]\([^)]+\)([1-9])/i) ||
          // Standard patterns: "2 bathrooms", "3 bath", "2 kamar mandi" (limit to 1 digit for safety)
          markdown.match(/([1-9])\s*(?:bath|bathroom|kamar mandi)s?(?!\d)/i) ||
        // Colon/space patterns: "Bathrooms: 2", "Bath: 3"
        markdown.match(/(?:bath|bathroom)s?[:\s]+([1-9])(?!\d)/i) ||
        // Abbreviation patterns: "2 BA", "3BA"
        markdown.match(/([1-9])\s*BA(?!\d)/i) ||
        // Property details patterns: "- 2 bathrooms", "• 3 bath"
        markdown.match(/[-•]\s*([1-9])\s*(?:bath|bathroom)s?/i) ||
        // Slash patterns: "2/3" (bed/bath), "3BR/2BA"
        markdown.match(/\d+\s*(?:bed|bedroom|BR)s?[\/\s]*([1-9])\s*(?:bath|bathroom|BA)s?/i) ||
        // Indonesian patterns: "2 kamar mandi"
        markdown.match(/([1-9])\s*kamar\s*mandi/i) ||
        // Table/list patterns: "Bathrooms | 2", "Bath | 3"
        markdown.match(/(?:bath|bathroom)s?\s*[|\s]+([1-9])/i) ||
        // Two-digit patterns only for reasonable ranges (10-15)
        markdown.match(/(1[0-5])\s*(?:bath|bathroom)s?(?!\d)/i);
      }

      bathrooms = bathroomMatch ? parseInt(bathroomMatch[1]) : null;

      // Additional validation for bathroom count (reasonable range for properties)
      if (bathrooms && (bathrooms > 15 || bathrooms < 1)) {
        console.log(`   ⚠️  Suspicious bathroom count: ${bathrooms}, setting to null`);
        bathrooms = null;
      }
    } else {
      console.log(`   🏞️  Land property detected - skipping bedroom/bathroom extraction`);
    }

    // Extract additional property details using CSS class patterns
    // (Using existing variables declared later in the function)

    // Year built - look for calendar icon with year
    const yearBuiltPattern = /calendar\.e2660fd6\.svg.*?details_item__info__value__ramxJ">(\d{4})/s;
    const yearBuiltMatch = markdown.match(yearBuiltPattern);
    if (yearBuiltMatch) {
      year_built = parseInt(yearBuiltMatch[1]);
    }

    // Land size - look for land-size icon (will be used later in main extraction)
    const landSizePattern = /land-size\.c80864e6\.svg.*?details_item__info__value__ramxJ">(\d+(?:[,\.]\d+)?)\s*sqm/s;
    const landSizeMatchEarly = markdown.match(landSizePattern);
    let land_size_from_css = null;
    if (landSizeMatchEarly) {
      land_size_from_css = parseFloat(landSizeMatchEarly[1].replace(',', ''));
    }

    // Building size - look for building-size icon (will be used later if existing extraction fails)
    const buildingSizePattern = /building-size\.a9958407\.svg.*?details_item__info__value__ramxJ">(\d+(?:[,\.]\d+)?)\s*sqm/s;
    const buildingSizeMatch = markdown.match(buildingSizePattern);
    let building_size_from_css = null;
    if (buildingSizeMatch) {
      building_size_from_css = parseFloat(buildingSizeMatch[1].replace(',', ''));
    }

    // Levels/floors - look for floor icon
    const levelsPattern = /floor\.989d0fa8\.svg.*?details_item__info__value__ramxJ">(\d+)/s;
    const levelsMatch = markdown.match(levelsPattern);
    if (levelsMatch) {
      levels = parseInt(levelsMatch[1]);
    }

    // Ownership type - look for ownership-type icon
    const ownershipPattern = /ownership-type\.63c3a4ac\.svg.*?details_item__info__value__ramxJ">([^<]+)</s;
    const ownershipMatch = markdown.match(ownershipPattern);
    if (ownershipMatch) {
      ownership_type = ownershipMatch[1].trim();
    }

    // Lease duration - look for calendar-clock icon
    const leasePattern = /calendar-clock\.b333fcad\.svg.*?details_item__info__value__ramxJ">([^<]+)</s;
    const leaseMatch = markdown.match(leasePattern);
    if (leaseMatch) {
      lease_duration = leaseMatch[1].trim();
    }

    // Furnishing - look for furnishing icon
    const furnishingPattern = /furnishing\.3589f0c4\.svg.*?details_item__info__value__ramxJ">([^<]+)</s;
    const furnishingMatch = markdown.match(furnishingPattern);
    if (furnishingMatch) {
      furnishing = furnishingMatch[1].trim();
    }

    // Parking - look for parking icon
    const parkingPattern = /parking\.46a83bad\.svg.*?details_item__info__value__ramxJ">([^<]+)</s;
    const parkingMatch = markdown.match(parkingPattern);
    if (parkingMatch) {
      const parkingText = parkingMatch[1].trim().toLowerCase();
      if (parkingText.includes('private') || parkingText.includes('car')) {
        parking_spaces = 1; // Default to 1 for private parking
      }
    }

    // Extract location - BetterPlace shows location info (improved patterns)
    const locationMatch = markdown.match(/(Canggu|Seminyak|Ubud|Kerobokan|Sanur|Denpasar|Jimbaran|Nusa Dua|Uluwatu|Pecatu|Bukit|Umalas|Berawa|Pererenan|Kedungu|Tanah Lot|Seseh|Cemagi|Tabanan|Badung|Gianyar)/i) ||
                         markdown.match(/Prime\s+([A-Za-z\s]+)\s+Location/i) ||
                         markdown.match(/in\s+([A-Za-z\s]+)\s+Location/i) ||
                         markdown.match(/Bukit\s*-\s*([A-Za-z\s]+)/i) ||
                         markdown.match(/Location[:\s]*([^\n]+)/i) ||
                         markdown.match(/Address[:\s]*([^\n]+)/i);

    let location = locationMatch ? locationMatch[1]?.trim() || locationMatch[0]?.trim() : 'Bali, Indonesia';

    // Filter out invalid location values
    const invalidLocations = ['map', 'click', 'here', 'view', 'show', 'location', 'address', ''];
    if (invalidLocations.includes(location.toLowerCase())) {
      location = 'Bali, Indonesia';
    }

    // Extract images - look for image URLs
    const imageMatches = markdown.match(/!\[.*?\]\((https?:\/\/[^\)]+)\)/g) || [];
    const images = imageMatches.map(match => {
      const urlMatch = match.match(/\((https?:\/\/[^\)]+)\)/);
      return urlMatch ? urlMatch[1] : null;
    }).filter(Boolean);

    // Extract size information - enhanced patterns with better fallback logic
    let building_size_sqm = null;

    // Try specific building size patterns first
    const buildingSizePatterns = [
      // Explicit building patterns: "Building size: 180 sqm", "Built size 180 m2"
      /(?:building|built|house|villa|property)\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Reverse patterns: "180 sqm building", "180 m2 built"
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m).*(?:building|built|house|villa|property)/i,
      // Simple patterns: "Building: 180 sqm", "Size: 180 m2"
      /(?:building|size|area)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Bali Villa Realty specific: "Area Size" is building size
      /area\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)\s*area\s*size/i,
      // Bali Villa Realty reverse pattern: "**84 Sqm** - Area Size"
      /\*\*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)\*\*\s*-\s*area\s*size/i,
      // List patterns: "- Building size: 180 sqm", "• 180 m2"
      /[-•]\s*(?:building|size|area)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i,
      // Table patterns: "Size | 180 sqm", "Building | 180 m2"
      /(?:size|building|area)\s*[|\s]+(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i,
      // Property details patterns: "3 bedrooms, 2 bathrooms, 180 sqm"
      /\d+\s*(?:bed|bedroom).*?(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i,
      // Floor area patterns: "Floor area: 180 sqm", "Living area: 180 m2"
      /(?:floor|living|internal)\s*area[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Generic size patterns with context: "Total: 180 sqm", "Space: 180 m2"
      /(?:total|space|interior)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i
    ];

    for (const pattern of buildingSizePatterns) {
      const match = markdown.match(pattern);
      if (match) {
        const size = parseFloat(match[1].replace(',', ''));
        // Validate reasonable building size (20-2000 sqm)
        if (size >= 20 && size <= 2000) {
          building_size_sqm = size;
          break;
        }
      }
    }

    // If no specific building size found, try CSS-based extraction first, then generic patterns
    if (!building_size_sqm && building_size_from_css) {
      building_size_sqm = building_size_from_css;
      console.log(`   🎯 Using CSS-based building size: ${building_size_sqm} sqm`);
    }

    if (!building_size_sqm) {
      const genericSizePatterns = [
        // First occurrence of size in property context
        /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot|plot|site))/i,
        // Size with minimal context
        /(?:^|\s)(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)(?!\s*(?:land|lot))/i
      ];

      for (const pattern of genericSizePatterns) {
        const match = markdown.match(pattern);
        if (match) {
          const size = parseFloat(match[1].replace(',', ''));
          // More restrictive validation for generic patterns (50-1000 sqm)
          if (size >= 50 && size <= 1000) {
            building_size_sqm = size;
            console.log(`   📐 Using generic size pattern: ${size} sqm`);
            break;
          }
        }
      }
    }

    // Extract land size with enhanced patterns and validation
    let land_size_sqm = null;

    // First try CSS-based extraction from earlier
    if (land_size_from_css) {
      land_size_sqm = land_size_from_css;
      console.log(`   🎯 Using CSS-based land size: ${land_size_sqm} sqm`);
    }

    // If CSS extraction failed, try traditional patterns
    if (!land_size_sqm) {
      const landSizePatterns = [
      // Are patterns: "42.3 are", "42.3 are Land", "Land: 42.3 are" (1 are = 100 sqm)
      /(\d+(?:[,\.]\d+)?)\s*are(?:\s+land)?/i,
      /(?:land|lot|plot|site)[:\s]*(\d+(?:[,\.]\d+)?)\s*are/i,
      // Explicit land patterns: "Land size: 250 sqm", "Lot size 250 m2"
      /(?:land|lot|plot|site)\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Reverse patterns: "250 sqm land", "250 m2 lot"
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m).*(?:land|lot|plot|site)/i,
      // Simple patterns: "Land: 250 sqm", "Lot: 250 m2"
      /(?:land|lot|plot|site)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // List patterns: "- Land size: 250 sqm", "• 250 m2 lot"
      /[-•]\s*(?:land|lot|plot|site)\s*(?:size)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Table patterns: "Land | 250 sqm", "Lot | 250 m2"
      /(?:land|lot|plot|site)\s*[|\s]+(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Garden/outdoor patterns: "Garden: 250 sqm", "Outdoor area: 250 m2"
      /(?:garden|outdoor|yard)\s*(?:area|space)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i
    ];

    for (const pattern of landSizePatterns) {
      const match = markdown.match(pattern);
      if (match) {
        let size = parseFloat(match[1].replace(',', ''));

        // Convert are to sqm if needed (1 are = 100 sqm)
        if (pattern.source.includes('are')) {
          size = size * 100;
          console.log(`   🏞️ Found land size: ${match[1]} are = ${size} sqm`);
        } else {
          console.log(`   🏞️ Found land size: ${size} sqm`);
        }

        // Validate reasonable land size (50-50000 sqm)
        if (size >= 50 && size <= 50000) {
          land_size_sqm = size;
          break;
        }
      }
    }
    } // Close the if (!land_size_sqm) block

    // Extract amenities - improved keyword matching that avoids URLs and filenames
    const amenityKeywords = [
      'swimming pool', 'pool', 'garden', 'parking', 'garage', 'kitchen',
      'air conditioning', 'wifi', 'security', 'furnished', 'unfurnished',
      'balcony', 'terrace', 'gym', 'spa', 'jacuzzi', 'bbq', 'maid service',
      'internet', 'cable tv', 'washing machine', 'dryer', 'dishwasher'
    ];
    const amenities = [];

    // Clean markdown by removing URLs and image references to avoid false positives
    const cleanedMarkdown = markdown
      .replace(/https?:\/\/[^\s)]+/g, '') // Remove URLs
      .replace(/!\[[^\]]*\]\([^)]+\)/g, '') // Remove image markdown
      .replace(/digitaloceanspaces/g, '') // Remove specific false positive
      .toLowerCase();

    amenityKeywords.forEach(keyword => {
      if (cleanedMarkdown.includes(keyword)) {
        // Additional validation: check if keyword appears in a meaningful context
        const keywordRegex = new RegExp(`\\b${keyword}\\b`, 'i');
        if (keywordRegex.test(cleanedMarkdown)) {
          amenities.push(keyword.charAt(0).toUpperCase() + keyword.slice(1));
        }
      }
    });

    // Extract ownership type and lease duration - BetterPlace often mentions this
    let ownership_type = null;
    let lease_duration_years = null;
    let lease_duration_text = null;

    const lowerMarkdown = markdown.toLowerCase();

    if (lowerMarkdown.includes('freehold')) {
      ownership_type = 'FREEHOLD';
    } else if (lowerMarkdown.includes('leasehold')) {
      ownership_type = 'LEASEHOLD';

      // Extract lease duration - enhanced patterns
      const leaseMatch = markdown.match(/(\d+)[-\s]*year[s]?\s*lease/i) ||
                        markdown.match(/lease[:\s]*(\d+)\s*year[s]?/i) ||
                        markdown.match(/(\d+)[-\s]*year[s]?\s*leasehold/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*remaining/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*left/i) ||
                        markdown.match(/(\d+)\+(\d+)\s*year[s]?\s*lease/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*tenure/i) ||
                        markdown.match(/tenure[:\s]*(\d+)\s*year[s]?/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*term/i) ||
                        markdown.match(/term[:\s]*(\d+)\s*year[s]?/i);

      if (leaseMatch) {
        lease_duration_years = parseInt(leaseMatch[1]);
        lease_duration_text = `${lease_duration_years}-year lease`;
      }
    }

    // Extract parking info - enhanced patterns
    const parkingPatterns = [
      // Standard patterns: "2 car parking", "3 parking spaces"
      /(\d+)\s*(?:car\s*)?(?:parking|garage)(?:\s*space)?s?/i,
      // Colon patterns: "Parking: 2", "Garage: 3"
      /(?:parking|garage)[:\s]*(\d+)/i,
      // List patterns: "- 2 parking spaces", "• 3 car garage"
      /[-•]\s*(\d+)\s*(?:car\s*)?(?:parking|garage)(?:\s*space)?s?/i,
      // Table patterns: "Parking | 2", "Garage | 3"
      /(?:parking|garage)\s*[|\s]+(\d+)/i,
      // Descriptive patterns: "2 covered parking", "3 secure parking"
      /(\d+)\s*(?:covered|secure|private|open)?\s*(?:car\s*)?(?:parking|garage)/i,
      // Indonesian patterns: "2 tempat parkir", "3 garasi"
      /(\d+)\s*(?:tempat\s*parkir|garasi)/i,
      // Property details patterns: "Parking spaces: 2", "Car spaces: 3"
      /(?:parking|car)\s*spaces?[:\s]*(\d+)/i,
      // Amenity list patterns: "parking for 2 cars", "garage for 3 vehicles"
      /(?:parking|garage)\s*for\s*(\d+)\s*(?:car|vehicle)s?/i
    ];

    let parking_spaces = null;
    for (const pattern of parkingPatterns) {
      const match = markdown.match(pattern);
      if (match) {
        const spaces = parseInt(match[1]);
        // Validate reasonable parking count (1-10 spaces)
        if (spaces >= 1 && spaces <= 10) {
          parking_spaces = spaces;
          console.log(`   🚗 Found parking: ${spaces} spaces`);
          break;
        }
      }
    }

    // Extract year built - enhanced patterns with better context detection
    let year_built = null;
    const currentYear = new Date().getFullYear();

    // Try multiple pattern groups in order of specificity
    const yearPatternGroups = [
      // Group 1: Explicit year built patterns (highest priority)
      [
        /(?:year\s*built|built|constructed|completed|finished)[:\s]*(\d{4})/i,
        /(\d{4})\s*(?:built|constructed|completed|finished)/i,
        /(?:built|constructed|completed|finished)\s+in\s+(\d{4})/i,
        /[-•]\s*(?:year\s*built|built|constructed)[:\s]*(\d{4})/i
      ],

      // Group 2: Age and new construction patterns
      [
        /(\d{1,2})\s*years?\s*old/i, // Special handling needed
        /(?:new|brand\s*new|recently\s*built).*(\d{4})/i,
        /(\d{4}).*(?:new|brand\s*new|recently\s*built)/i,
        /(?:modern|contemporary).*(\d{4})/i
      ],

      // Group 3: Table and structured data patterns
      [
        /(?:year|built|constructed|age)\s*[|\s:]+(\d{4})/i,
        /(\d{4})\s*[|\s]+(?:year|built|constructed)/i,
        /(?:property\s*details|specifications).*?(\d{4})/i
      ],

      // Group 4: Context-based patterns (lower priority)
      [
        /(?:villa|house|building|property).*?(\d{4})/i,
        /(\d{4}).*(?:villa|house|building|property)/i,
        /(?:development|project).*?(\d{4})/i,
        /(\d{4}).*(?:development|project)/i
      ],

      // Group 5: Generic 4-digit year patterns (last resort)
      [
        /(?:since|from|established).*?(\d{4})/i,
        /(\d{4})(?!\s*(?:USD|IDR|sqm|m2|bedroom|bathroom))/i
      ]
    ];

    for (const patternGroup of yearPatternGroups) {
      for (const pattern of patternGroup) {
        const match = markdown.match(pattern);
        if (match) {
          const extractedYear = parseInt(match[1]);

          // Special handling for "years old" pattern
          if (pattern.source.includes('years?\\s*old') && extractedYear <= 50) {
            const calculatedYear = currentYear - extractedYear;
            if (calculatedYear >= 1900 && calculatedYear <= currentYear) {
              year_built = calculatedYear;
              console.log(`   📅 Calculated year from age: ${extractedYear} years old = ${calculatedYear}`);
              break;
            }
          }
          // Standard year validation
          else if (extractedYear >= 1900 && extractedYear <= currentYear + 2) {
            // Additional context validation for generic patterns
            if (pattern.source.includes('villa|house|building|property') ||
                pattern.source.includes('USD|IDR|sqm')) {
              // For generic patterns, be more strict about context
              const contextBefore = markdown.substring(Math.max(0, match.index - 50), match.index);
              const contextAfter = markdown.substring(match.index + match[0].length, match.index + match[0].length + 50);
              const fullContext = (contextBefore + ' ' + contextAfter).toLowerCase();

              // Skip if year appears in price, size, or other non-year contexts
              if (fullContext.includes('usd') || fullContext.includes('idr') ||
                  fullContext.includes('sqm') || fullContext.includes('bedroom') ||
                  fullContext.includes('bathroom') || fullContext.includes('price')) {
                continue;
              }
            }

            year_built = extractedYear;
            console.log(`   📅 Found year built: ${extractedYear}`);
            break;
          }
        }
      }

      // If we found a valid year, stop searching
      if (year_built) break;
    }

    // Extract description - use BetterPlace-specific extraction first, then fallback
    let description = extractBetterPlaceDescription(markdown);
    if (!description) {
      description = extractCleanDescription(markdown);
    }

    // For land properties, generate a meaningful description if none found
    if (!description && propertyType === 'land') {
      description = generateLandDescription(title, land_size_sqm, ownership_type, location);
    }

    description = description || 'No description provided';

    // Smart status detection based on title and description
    const status = detectPropertyStatus(title, description);

    // Skip SOLD/RENTED/INACTIVE properties to keep database clean
    if (status === 'SOLD' || status === 'RENTED' || status === 'INACTIVE') {
      console.log(`   ⚠️ Skipping property with status: ${status}`);
      return null;
    }

    const extractedData = {
      title: title,
      price: price,
      location: location,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      description: description,
      images: images.slice(0, 10), // Max 10 images
      property_id: property_id,
      detail_url: url,
      property_type: detectBetterPlacePropertyType(property_id, title, markdown), // Smart property type detection
      status: status, // Use smart status detection instead of hardcoded 'available'
      amenities: amenities,
      year_built: year_built,
      parking: parking_spaces,
      ownership_type: ownership_type,
      lease_duration_years: lease_duration_years,
      lease_duration_text: lease_duration_text,
      size: {
        building_size_sqm: building_size_sqm,
        land_size_sqm: land_size_sqm
      }
    };

    // Smart logging based on property type
    const logSuffix = propertyType === 'land' ?
      `(${land_size_sqm || 'unknown'}sqm land) in ${location} - ${price}` :
      `(${bedrooms}bed/${bathrooms}bath) in ${location} - ${price}`;
    console.log(`   ✅ Extracted: ${title} ${logSuffix}`);
    return extractedData;

  } catch (error) {
    console.log(`   ❌ BetterPlace markdown parsing failed: ${error.message}`);
    return null;
  }
}

// Generate meaningful description for land properties
function generateLandDescription(title, landSizeSqm, ownershipType, location) {
  try {
    let description = '';

    // Extract key information from title
    const titleLower = (title || '').toLowerCase();
    const hasGem = titleLower.includes('gem') || titleLower.includes('hidden');
    const hasInvestment = titleLower.includes('investment') || titleLower.includes('opportunity');
    const hasPrime = titleLower.includes('prime') || titleLower.includes('premium');

    // Start with property type and size
    if (landSizeSqm) {
      const areSize = (landSizeSqm / 100).toFixed(1);
      description += `This ${ownershipType?.toLowerCase() || ''} land property spans ${areSize} are (${landSizeSqm} sqm)`;
    } else {
      description += `This ${ownershipType?.toLowerCase() || ''} land property`;
    }

    // Add location context
    if (location) {
      description += ` located in ${location}, Bali`;
    } else {
      description += ` located in Bali, Indonesia`;
    }

    // Add investment potential
    if (hasGem || hasPrime) {
      description += '. This premium land offers exceptional investment potential';
    } else if (hasInvestment) {
      description += '. This land presents an excellent investment opportunity';
    } else {
      description += '. This property offers great potential for development or investment';
    }

    // Add development possibilities
    description += ', perfect for building your dream villa, resort, or commercial development';

    // Add ownership benefits
    if (ownershipType === 'FREEHOLD') {
      description += '. With freehold ownership, you have complete control and security over your investment';
    } else if (ownershipType === 'LEASEHOLD') {
      description += '. The leasehold structure provides an accessible entry point into the Bali property market';
    }

    // Close with location benefits
    description += '. Bali\'s growing tourism industry and infrastructure development make this an attractive long-term investment.';

    return description;

  } catch (error) {
    console.log(`   ⚠️  Error generating land description: ${error.message}`);
    return 'Prime land property in Bali offering excellent investment potential for development or future use.';
  }
}

// Detect BetterPlace property type from property ID and content
function detectBetterPlacePropertyType(property_id, title, markdown) {
  const id = property_id?.toUpperCase() || '';
  const text = `${title} ${markdown}`.toLowerCase();

  // Property ID based detection (most reliable)
  if (id.startsWith('BPVL')) return 'villa'; // Villa Leasehold
  if (id.startsWith('BPVF')) return 'villa'; // Villa Freehold
  if (id.startsWith('BPHL')) return 'hotel'; // Hotel/Villa Complex Leasehold
  if (id.startsWith('BPAP')) return 'apartment'; // Apartment
  if (id.startsWith('BPLD') || id.startsWith('BPLF')) return 'land'; // Land (Leasehold/Freehold)
  if (id.startsWith('BPLL')) return 'land'; // Land Leasehold
  if (id.startsWith('BPFL')) return 'land'; // Land Freehold

  // Content based detection as fallback
  if (text.includes('land') || text.includes('plot') || text.includes('lot')) return 'land';
  if (text.includes('apartment') || text.includes('condo')) return 'apartment';
  if (text.includes('hotel') || text.includes('resort')) return 'hotel';
  if (text.includes('villa') || text.includes('house')) return 'villa';

  // Default fallback
  return 'villa';
}

// Bali Villa Realty-specific markdown parser
function parseBaliVillaRealtyMarkdown(markdown, url) {
  try {
    console.log(`   🔍 Parsing Bali Villa Realty markdown (${markdown.length} chars)...`);

    // Extract property ID from URL
    const propertyIdMatch = url.match(/\/([^\/]+)\/?$/i);
    const property_id = propertyIdMatch ? propertyIdMatch[1] : 'unknown';

    // Extract title - Bali Villa Realty usually has title as first heading
    const titleMatch = markdown.match(/^#\s+(.+)$/m) ||
                      markdown.match(/^##\s+(.+)$/m) ||
                      markdown.match(/\*\*([^*]+)\*\*/);
    const title = titleMatch ? titleMatch[1].trim() : 'Property Title Not Found';

    // Extract price - Bali Villa Realty often shows USD rental prices with enhanced validation
    let price = null;

    // Define price patterns with priority order (rental-focused)
    const pricePatternGroups = [
      // Group 1: Explicit price context (highest priority)
      {
        patterns: [
          /(?:price|cost|value|rent|rental)[:\s]*(?:USD|US\$|\$)\s*([\d,\.]+)(?:\s*\/\s*month)?/i,
          /(?:price|cost|value|rent|rental)[:\s]*(?:IDR|Rp)\s*([\d,\.]+)(?:\s*\/\s*month)?/i
        ],
        minUSD: 500,      // Minimum $500/month for rental context
        minIDR: 5000000   // Minimum 5M IDR/month for rental context
      },

      // Group 2: Currency with reasonable context
      {
        patterns: [
          /(?:USD|US\$|\$)\s*([\d,\.]+)(?:\s*\/\s*month)?/i,
          /(?:IDR|Rp)\s*([\d,\.]+)(?:\s*\/\s*month)?/i
        ],
        minUSD: 1000,     // Minimum $1,000 for general USD
        minIDR: 10000000  // Minimum 10M IDR for general IDR
      },

      // Group 3: Reverse patterns (lower priority)
      {
        patterns: [
          /([\d,\.]+)\s*(?:USD|US\$|\$)(?:\s*\/\s*month)?/i,
          /([\d,\.]+)\s*(?:IDR|Rp)(?:\s*\/\s*month)?/i
        ],
        minUSD: 2000,     // Minimum $2,000 for reverse patterns
        minIDR: 20000000  // Minimum 20M IDR for reverse patterns
      }
    ];

    for (const group of pricePatternGroups) {
      for (const pattern of group.patterns) {
        const match = markdown.match(pattern);
        if (match) {
          const numericValue = parseFloat(match[1].replace(/,/g, ''));
          const fullMatch = match[0].toLowerCase();

          // Currency-specific validation
          const isIDR = fullMatch.includes('idr') || fullMatch.includes('rp');
          const isUSD = fullMatch.includes('usd') || fullMatch.includes('$');

          let isValid = false;
          if (isIDR && numericValue >= group.minIDR) {
            isValid = true;
          } else if (isUSD && numericValue >= group.minUSD) {
            isValid = true;
          }

          // Additional context validation for edge cases
          if (isValid) {
            // Check surrounding context to avoid false matches
            const contextBefore = markdown.substring(Math.max(0, match.index - 30), match.index);
            const contextAfter = markdown.substring(match.index + match[0].length, match.index + match[0].length + 30);
            const fullContext = (contextBefore + ' ' + contextAfter).toLowerCase();

            // Skip if appears in non-price contexts
            if (fullContext.includes('year') ||
                fullContext.includes('bedroom') ||
                fullContext.includes('bathroom') ||
                fullContext.includes('sqm') ||
                fullContext.includes('m2') ||
                fullContext.includes('parking') ||
                fullContext.includes('built') ||
                fullContext.includes('size')) {
              continue;
            }

            price = match[0];
            console.log(`   💰 Found valid price: ${price} (${numericValue})`);
            break;
          }
        }
      }

      // If we found a valid price, stop searching
      if (price) break;
    }

    // Extract bedrooms - comprehensive patterns including title extraction
    const bedroomMatch =
      // Standard patterns: "2 bedrooms", "3 bed", "2 kamar tidur"
      markdown.match(/(\d+)\s*(?:bed|bedroom|kamar tidur|BR)(?!\d)/i) ||
      // Hyphenated patterns: "1-Bedroom", "2-Bed", "3-BR"
      markdown.match(/(\d+)-(?:bed|bedroom|BR)(?!\d)/i) ||
      // Colon/space patterns: "Bedrooms: 2", "Bed: 3"
      markdown.match(/(?:bed|bedroom)s?[:\s]+(\d+)(?!\d)/i) ||
      // Abbreviation patterns: "2 BR", "3BR"
      markdown.match(/(\d+)\s*BR(?!\d)/i) ||
      // Property details patterns: "- 2 bedrooms", "• 3 bed"
      markdown.match(/[-•]\s*(\d+)\s*(?:bed|bedroom)s?/i) ||
      // Slash patterns: "2/3" (bed/bath), "3BR/2BA"
      markdown.match(/(\d+)\s*(?:bed|bedroom|BR)s?[\/\s]*\d+\s*(?:bath|bathroom|BA)s?/i) ||
      // Indonesian patterns: "2 kamar tidur"
      markdown.match(/(\d+)\s*kamar\s*tidur/i) ||
      // Table/list patterns: "Bedrooms | 2", "Bed | 3"
      markdown.match(/(?:bed|bedroom)s?\s*[|\s]+(\d+)/i) ||
      // Title extraction: "Cozy 1-Bedroom Villa", "Modern 2-Bed House"
      markdown.match(/(?:cozy|modern|luxury|beautiful|stunning|amazing|new|brand\s*new)?\s*(\d+)-?(?:bed|bedroom)/i);

    const bedrooms = bedroomMatch ? parseInt(bedroomMatch[1]) : null;

    // Extract bathrooms - comprehensive patterns for better detection
    const bathroomMatch =
      // Standard patterns: "2 bathrooms", "3 bath", "2 kamar mandi"
      markdown.match(/(\d{1,2})\s*(?:bath|bathroom|kamar mandi)s?(?!\d)/i) ||
      // Colon/space patterns: "Bathrooms: 2", "Bath: 3"
      markdown.match(/(?:bath|bathroom)s?[:\s]+(\d{1,2})(?!\d)/i) ||
      // Abbreviation patterns: "2 BA", "3BA"
      markdown.match(/(\d{1,2})\s*BA(?!\d)/i) ||
      // Property details patterns: "- 2 bathrooms", "• 3 bath"
      markdown.match(/[-•]\s*(\d{1,2})\s*(?:bath|bathroom)s?/i) ||
      // Slash patterns: "2/3" (bed/bath), "3BR/2BA"
      markdown.match(/\d+\s*(?:bed|bedroom|BR)s?[\/\s]*(\d{1,2})\s*(?:bath|bathroom|BA)s?/i) ||
      // Indonesian patterns: "2 kamar mandi"
      markdown.match(/(\d{1,2})\s*kamar\s*mandi/i) ||
      // Table/list patterns: "Bathrooms | 2", "Bath | 3"
      markdown.match(/(?:bath|bathroom)s?\s*[|\s]+(\d{1,2})/i);

    let bathrooms = bathroomMatch ? parseInt(bathroomMatch[1]) : null;

    // Validate bathroom count (reasonable range for properties)
    if (bathrooms && (bathrooms > 15 || bathrooms < 1)) {
      console.log(`   ⚠️  Suspicious bathroom count: ${bathrooms}, setting to null`);
      bathrooms = null;
    }

    // Extract location - Bali Villa Realty shows location info
    const locationMatch = markdown.match(/(Canggu|Seminyak|Ubud|Kerobokan|Sanur|Denpasar|Jimbaran|Nusa Dua|Uluwatu|Pecatu|Bukit|Umalas|Berawa|Pererenan|Kedungu|Tanah Lot|Seseh|Cemagi|Tabanan|Badung|Gianyar|Tumbak Bayuh)/i) ||
                         markdown.match(/Location[:\s]*([^\n]+)/i) ||
                         markdown.match(/Address[:\s]*([^\n]+)/i);
    const location = locationMatch ? locationMatch[1].trim() : 'Bali, Indonesia';

    // Extract images - look for image URLs
    const imageMatches = markdown.match(/!\[.*?\]\((https?:\/\/[^\)]+)\)/g) || [];
    const images = imageMatches.map(match => {
      const urlMatch = match.match(/\((https?:\/\/[^\)]+)\)/);
      return urlMatch ? urlMatch[1] : null;
    }).filter(Boolean);

    // Extract size information - enhanced patterns with better fallback logic
    let building_size_sqm = null;

    // Try specific building size patterns first
    const buildingSizePatterns = [
      // Explicit building patterns: "Building size: 180 sqm", "Built size 180 m2"
      /(?:building|built|house|villa|property)\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Reverse patterns: "180 sqm building", "180 m2 built"
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m).*(?:building|built|house|villa|property)/i,
      // Simple patterns: "Building: 180 sqm", "Size: 180 m2"
      /(?:building|size|area)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Bali Villa Realty specific: "Area Size" is building size
      /area\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)\s*area\s*size/i,
      // Bali Villa Realty reverse pattern: "**84 Sqm** - Area Size"
      /\*\*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)\*\*\s*-\s*area\s*size/i,
      // List patterns: "- Building size: 180 sqm", "• 180 m2"
      /[-•]\s*(?:building|size|area)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i,
      // Table patterns: "Size | 180 sqm", "Building | 180 m2"
      /(?:size|building|area)\s*[|\s]+(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i,
      // Property details patterns: "3 bedrooms, 2 bathrooms, 180 sqm"
      /\d+\s*(?:bed|bedroom).*?(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i,
      // Floor area patterns: "Floor area: 180 sqm", "Living area: 180 m2"
      /(?:floor|living|internal)\s*area[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Generic size patterns with context: "Total: 180 sqm", "Space: 180 m2"
      /(?:total|space|interior)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i
    ];

    for (const pattern of buildingSizePatterns) {
      const match = markdown.match(pattern);
      if (match) {
        const size = parseFloat(match[1].replace(',', ''));
        // Validate reasonable building size (20-2000 sqm)
        if (size >= 20 && size <= 2000) {
          building_size_sqm = size;
          break;
        }
      }
    }

    // If no specific building size found, try generic size patterns as fallback
    if (!building_size_sqm) {
      const genericSizePatterns = [
        // First occurrence of size in property context
        /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot|plot|site))/i,
        // Size with minimal context
        /(?:^|\s)(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)(?!\s*(?:land|lot))/i
      ];

      for (const pattern of genericSizePatterns) {
        const match = markdown.match(pattern);
        if (match) {
          const size = parseFloat(match[1].replace(',', ''));
          // More restrictive validation for generic patterns (50-1000 sqm)
          if (size >= 50 && size <= 1000) {
            building_size_sqm = size;
            console.log(`   📐 Using generic size pattern: ${size} sqm`);
            break;
          }
        }
      }
    }

    // Extract land size with enhanced patterns and validation
    let land_size_sqm = null;

    const landSizePatterns = [
      // Explicit land patterns: "Land size: 250 sqm", "Lot size 250 m2"
      /(?:land|lot|plot|site)\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Reverse patterns: "250 sqm land", "250 m2 lot"
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m).*(?:land|lot|plot|site)/i,
      // Simple patterns: "Land: 250 sqm", "Lot: 250 m2"
      /(?:land|lot|plot|site)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // List patterns: "- Land size: 250 sqm", "• 250 m2 lot"
      /[-•]\s*(?:land|lot|plot|site)\s*(?:size)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Table patterns: "Land | 250 sqm", "Lot | 250 m2"
      /(?:land|lot|plot|site)\s*[|\s]+(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Garden/outdoor patterns: "Garden: 250 sqm", "Outdoor area: 250 m2"
      /(?:garden|outdoor|yard)\s*(?:area|space)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Property details patterns: "Total area: 250 sqm", "Total size: 250 m2"
      /(?:total|overall)\s*(?:area|size)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Compound patterns: "Building 100 sqm, Land 250 sqm"
      /(?:building|house).*?(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²).*?(?:land|lot).*?(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)/i,
      // Second size pattern (when building size comes first): "100 sqm building, 250 sqm land"
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²).*?(?:building|house).*?(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²).*?(?:land|lot)/i,
      // Property specification patterns: "Land area 250 sqm", "Site area 250 m2"
      /(?:land|lot|site|plot)\s*area[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Indonesian patterns: "Luas tanah: 250 m2", "Tanah 250 sqm"
      /(?:luas\s*tanah|tanah)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Bali Villa Realty specific patterns: "Land Area" is the lot size
      /land\s*area[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Property card patterns: "300 sqm" under "Land Area"
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)\s*land\s*area/i,
      // Bali Villa Realty reverse pattern: "**256 Sqm** - Land Area"
      /\*\*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)\*\*\s*-\s*land\s*area/i,
      // Generic second size (when first is building): Look for second occurrence
      /(?:sqm|m2|m²).*?(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)/i
    ];

    for (const pattern of landSizePatterns) {
      const match = markdown.match(pattern);
      if (match) {
        let size = null;

        // Handle compound patterns with both building and land sizes
        if (pattern.source.includes('building|house') && match[2]) {
          // Pattern has both building and land - use the second value (land)
          size = parseFloat(match[2].replace(',', ''));
        } else if (pattern.source.includes('sqm|m2|m²).*?(\d+') && match[1]) {
          // Generic second size pattern - use first captured group
          size = parseFloat(match[1].replace(',', ''));
        } else if (match[1]) {
          // Standard single size pattern
          size = parseFloat(match[1].replace(',', ''));
        }

        // Validate reasonable land size (50-50000 sqm)
        if (size && size >= 50 && size <= 50000) {
          land_size_sqm = size;
          console.log(`   🏞️ Found land size: ${size} sqm`);
          break;
        }
      }
    }

    // Extract amenities - Bali Villa Realty specific features
    const amenityKeywords = [
      'swimming pool', 'pool', 'garden', 'parking', 'garage', 'kitchen',
      'air conditioning', 'wifi', 'security', 'furnished', 'unfurnished',
      'balcony', 'terrace', 'gym', 'spa', 'jacuzzi', 'bbq', 'maid service',
      'internet', 'cable tv', 'washing machine', 'dryer', 'dishwasher'
    ];
    const amenities = [];
    const lowerMarkdown = markdown.toLowerCase();

    amenityKeywords.forEach(keyword => {
      if (lowerMarkdown.includes(keyword)) {
        amenities.push(keyword.charAt(0).toUpperCase() + keyword.slice(1));
      }
    });

    // Extract ownership type and lease duration - Bali Villa Realty often mentions this
    let ownership_type = null;
    let lease_duration_years = null;
    let lease_duration_text = null;

    if (lowerMarkdown.includes('freehold')) {
      ownership_type = 'FREEHOLD';
    } else if (lowerMarkdown.includes('leasehold')) {
      ownership_type = 'LEASEHOLD';

      // Extract lease duration - enhanced patterns
      const leaseMatch = markdown.match(/\/\s*(\d+)\s*years?/i) ||  // "/ 25 Years" (Bali Villa Realty price format)
                        markdown.match(/(\d+)[-\s]*year[s]?\s*lease/i) ||
                        markdown.match(/lease[:\s]*(\d+)\s*year[s]?/i) ||
                        markdown.match(/(\d+)[-\s]*year[s]?\s*leasehold/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*remaining/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*left/i) ||
                        markdown.match(/(\d+)\+(\d+)\s*year[s]?\s*lease/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*tenure/i) ||
                        markdown.match(/tenure[:\s]*(\d+)\s*year[s]?/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*term/i) ||
                        markdown.match(/term[:\s]*(\d+)\s*year[s]?/i);

      if (leaseMatch) {
        lease_duration_years = parseInt(leaseMatch[1]);
        lease_duration_text = `${lease_duration_years}-year lease`;
      }
    }

    // Extract parking info - enhanced patterns
    const parkingPatterns = [
      // Standard patterns: "2 car parking", "3 parking spaces"
      /(\d+)\s*(?:car\s*)?(?:parking|garage)(?:\s*space)?s?/i,
      // Colon patterns: "Parking: 2", "Garage: 3"
      /(?:parking|garage)[:\s]*(\d+)/i,
      // List patterns: "- 2 parking spaces", "• 3 car garage"
      /[-•]\s*(\d+)\s*(?:car\s*)?(?:parking|garage)(?:\s*space)?s?/i,
      // Table patterns: "Parking | 2", "Garage | 3"
      /(?:parking|garage)\s*[|\s]+(\d+)/i,
      // Descriptive patterns: "2 covered parking", "3 secure parking"
      /(\d+)\s*(?:covered|secure|private|open)?\s*(?:car\s*)?(?:parking|garage)/i,
      // Indonesian patterns: "2 tempat parkir", "3 garasi"
      /(\d+)\s*(?:tempat\s*parkir|garasi)/i,
      // Property details patterns: "Parking spaces: 2", "Car spaces: 3"
      /(?:parking|car)\s*spaces?[:\s]*(\d+)/i,
      // Amenity list patterns: "parking for 2 cars", "garage for 3 vehicles"
      /(?:parking|garage)\s*for\s*(\d+)\s*(?:car|vehicle)s?/i
    ];

    let parking_spaces = null;
    for (const pattern of parkingPatterns) {
      const match = markdown.match(pattern);
      if (match) {
        const spaces = parseInt(match[1]);
        // Validate reasonable parking count (1-10 spaces)
        if (spaces >= 1 && spaces <= 10) {
          parking_spaces = spaces;
          console.log(`   🚗 Found parking: ${spaces} spaces`);
          break;
        }
      }
    }

    // Extract year built - enhanced patterns with better context detection
    let year_built = null;
    const currentYear = new Date().getFullYear();

    // Try multiple pattern groups in order of specificity
    const yearPatternGroups = [
      // Group 1: Explicit year built patterns (highest priority)
      [
        /(?:year\s*built|built|constructed|completed|finished)[:\s]*(\d{4})/i,
        /(\d{4})\s*(?:built|constructed|completed|finished)/i,
        /(?:built|constructed|completed|finished)\s+in\s+(\d{4})/i,
        /[-•]\s*(?:year\s*built|built|constructed)[:\s]*(\d{4})/i
      ],

      // Group 2: Age and new construction patterns
      [
        /(\d{1,2})\s*years?\s*old/i, // Special handling needed
        /(?:new|brand\s*new|recently\s*built).*(\d{4})/i,
        /(\d{4}).*(?:new|brand\s*new|recently\s*built)/i,
        /(?:modern|contemporary).*(\d{4})/i
      ],

      // Group 3: Table and structured data patterns
      [
        /(?:year|built|constructed|age)\s*[|\s:]+(\d{4})/i,
        /(\d{4})\s*[|\s]+(?:year|built|constructed)/i,
        /(?:property\s*details|specifications).*?(\d{4})/i
      ],

      // Group 4: Context-based patterns (lower priority)
      [
        /(?:villa|house|building|property).*?(\d{4})/i,
        /(\d{4}).*(?:villa|house|building|property)/i,
        /(?:development|project).*?(\d{4})/i,
        /(\d{4}).*(?:development|project)/i
      ],

      // Group 5: Generic 4-digit year patterns (last resort)
      [
        /(?:since|from|established).*?(\d{4})/i,
        /(\d{4})(?!\s*(?:USD|IDR|sqm|m2|bedroom|bathroom))/i
      ]
    ];

    for (const patternGroup of yearPatternGroups) {
      for (const pattern of patternGroup) {
        const match = markdown.match(pattern);
        if (match) {
          const extractedYear = parseInt(match[1]);

          // Special handling for "years old" pattern
          if (pattern.source.includes('years?\\s*old') && extractedYear <= 50) {
            const calculatedYear = currentYear - extractedYear;
            if (calculatedYear >= 1900 && calculatedYear <= currentYear) {
              year_built = calculatedYear;
              console.log(`   📅 Calculated year from age: ${extractedYear} years old = ${calculatedYear}`);
              break;
            }
          }
          // Standard year validation
          else if (extractedYear >= 1900 && extractedYear <= currentYear + 2) {
            // Additional context validation for generic patterns
            if (pattern.source.includes('villa|house|building|property') ||
                pattern.source.includes('USD|IDR|sqm')) {
              // For generic patterns, be more strict about context
              const contextBefore = markdown.substring(Math.max(0, match.index - 50), match.index);
              const contextAfter = markdown.substring(match.index + match[0].length, match.index + match[0].length + 50);
              const fullContext = (contextBefore + ' ' + contextAfter).toLowerCase();

              // Skip if year appears in price, size, or other non-year contexts
              if (fullContext.includes('usd') || fullContext.includes('idr') ||
                  fullContext.includes('sqm') || fullContext.includes('bedroom') ||
                  fullContext.includes('bathroom') || fullContext.includes('price')) {
                continue;
              }
            }

            year_built = extractedYear;
            console.log(`   📅 Found year built: ${extractedYear}`);
            break;
          }
        }
      }

      // If we found a valid year, stop searching
      if (year_built) break;
    }

    // Extract description - find meaningful property description
    const cleanDescription = extractCleanDescription(markdown);
    const description = cleanDescription || 'No description provided';

    // Smart status detection based on title and description
    const status = detectPropertyStatus(title, description);

    // Skip SOLD/RENTED/INACTIVE properties to keep database clean
    if (status === 'SOLD' || status === 'RENTED' || status === 'INACTIVE') {
      console.log(`   ⚠️ Skipping property with status: ${status}`);
      return null;
    }

    const extractedData = {
      title: title,
      price: price,
      location: location,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      description: description,
      images: images.slice(0, 10), // Max 10 images
      property_id: property_id,
      detail_url: url,
      property_type: 'villa', // Default for Bali Villa Realty
      status: status, // Use smart status detection instead of hardcoded 'available'
      amenities: amenities,
      year_built: year_built,
      parking: parking_spaces,
      ownership_type: ownership_type,
      lease_duration_years: lease_duration_years,
      lease_duration_text: lease_duration_text,
      size: {
        building_size_sqm: building_size_sqm,
        land_size_sqm: land_size_sqm
      }
    };

    console.log(`   ✅ Extracted: ${title} (${bedrooms}bed/${bathrooms}bath) in ${location} - ${price}`);
    return extractedData;

  } catch (error) {
    console.log(`   ❌ Bali Villa Realty markdown parsing failed: ${error.message}`);
    return null;
  }
}

// Bali Home Immo-specific markdown parser
function parseBaliHomeImmoMarkdown(markdown, url) {
  try {
    console.log(`   🔍 Parsing Bali Home Immo markdown (${markdown.length} chars)...`);

    // Extract property ID from URL
    const propertyIdMatch = url.match(/\/([^\/]+)\/?$/i);
    const property_id = propertyIdMatch ? propertyIdMatch[1] : 'unknown';

    // Extract title - Bali Home Immo usually has title as first heading
    const titleMatch = markdown.match(/^#\s+(.+)$/m) ||
                      markdown.match(/^##\s+(.+)$/m) ||
                      markdown.match(/\*\*([^*]+)\*\*/);
    const title = titleMatch ? titleMatch[1].trim() : 'Property Title Not Found';

    // Pre-filter: Skip properties with "Price on Request" or "Not Available"
    const priceOnRequestPatterns = [
      /price\s*on\s*request/i,
      /not\s*available/i,
      /contact\s*for\s*price/i,
      /call\s*for\s*price/i,
      /inquire\s*for\s*price/i,
      /price\s*upon\s*request/i,
      /price\s*tba/i,
      /\btba\b/i,
      /price\s*:\s*-/i,  // Price: -
      /price\s*:\s*n\/a/i,  // Price: N/A
      /price\s*:\s*$/i  // Empty price field
    ];

    const hasPriceOnRequest = priceOnRequestPatterns.some(pattern => pattern.test(markdown));
    if (hasPriceOnRequest) {
      console.log(`   ⚠️ Skipping property: Price on request or not available`);
      return null; // Skip this property entirely - this is safe and expected
    }

    // Extract price - Bali Home Immo often shows IDR rental prices with enhanced validation
    let price = null;

    // Define price patterns with priority order (IDR-focused for rentals)
    const pricePatternGroups = [
      // Group 1: Explicit price context (highest priority)
      {
        patterns: [
          /(?:price|cost|value|rent|rental)[:\s]*(?:IDR|Rp)\s*([\d,\.]+)(?:\s*\/\s*month)?/i,
          /(?:price|cost|value|rent|rental)[:\s]*(?:USD|US\$|\$)\s*([\d,\.]+)(?:\s*\/\s*month)?/i
        ],
        minIDR: 5000000,  // Minimum 5M IDR/month for rental context (~$300)
        minUSD: 300       // Minimum $300/month for rental context
      },

      // Group 2: Currency with reasonable context
      {
        patterns: [
          /(?:IDR|Rp)\s*([\d,\.]+)(?:\s*\/\s*month)?/i,
          /(?:USD|US\$|\$)\s*([\d,\.]+)(?:\s*\/\s*month)?/i
        ],
        minIDR: 10000000, // Minimum 10M IDR for general IDR (~$600)
        minUSD: 500       // Minimum $500 for general USD
      },

      // Group 3: Reverse patterns (lower priority)
      {
        patterns: [
          /([\d,\.]+)\s*(?:IDR|Rp)(?:\s*\/\s*month)?/i,
          /([\d,\.]+)\s*(?:USD|US\$|\$)(?:\s*\/\s*month)?/i
        ],
        minIDR: 20000000, // Minimum 20M IDR for reverse patterns (~$1200)
        minUSD: 1000      // Minimum $1,000 for reverse patterns
      }
    ];

    for (const group of pricePatternGroups) {
      for (const pattern of group.patterns) {
        const match = markdown.match(pattern);
        if (match) {
          const numericValue = parseFloat(match[1].replace(/,/g, ''));
          const fullMatch = match[0].toLowerCase();

          // Currency-specific validation
          const isIDR = fullMatch.includes('idr') || fullMatch.includes('rp');
          const isUSD = fullMatch.includes('usd') || fullMatch.includes('$');

          let isValid = false;
          if (isIDR && numericValue >= group.minIDR) {
            isValid = true;
          } else if (isUSD && numericValue >= group.minUSD) {
            isValid = true;
          }

          // Additional context validation for edge cases
          if (isValid) {
            // Check surrounding context to avoid false matches
            const contextBefore = markdown.substring(Math.max(0, match.index - 30), match.index);
            const contextAfter = markdown.substring(match.index + match[0].length, match.index + match[0].length + 30);
            const fullContext = (contextBefore + ' ' + contextAfter).toLowerCase();

            // Skip if appears in non-price contexts
            if (fullContext.includes('year') ||
                fullContext.includes('bedroom') ||
                fullContext.includes('bathroom') ||
                fullContext.includes('sqm') ||
                fullContext.includes('m2') ||
                fullContext.includes('parking') ||
                fullContext.includes('built') ||
                fullContext.includes('size')) {
              continue;
            }

            price = match[0];
            console.log(`   💰 Found valid price: ${price} (${numericValue})`);
            break;
          }
        }
      }

      // If we found a valid price, stop searching
      if (price) break;
    }

    // Extract bedrooms - comprehensive patterns including title extraction
    const bedroomMatch =
      // Standard patterns: "2 bedrooms", "3 bed", "2 kamar tidur"
      markdown.match(/(\d+)\s*(?:bed|bedroom|kamar tidur|BR)(?!\d)/i) ||
      // Hyphenated patterns: "1-Bedroom", "2-Bed", "3-BR"
      markdown.match(/(\d+)-(?:bed|bedroom|BR)(?!\d)/i) ||
      // Colon/space patterns: "Bedrooms: 2", "Bed: 3"
      markdown.match(/(?:bed|bedroom)s?[:\s]+(\d+)(?!\d)/i) ||
      // Abbreviation patterns: "2 BR", "3BR"
      markdown.match(/(\d+)\s*BR(?!\d)/i) ||
      // Property details patterns: "- 2 bedrooms", "• 3 bed"
      markdown.match(/[-•]\s*(\d+)\s*(?:bed|bedroom)s?/i) ||
      // Slash patterns: "2/3" (bed/bath), "3BR/2BA"
      markdown.match(/(\d+)\s*(?:bed|bedroom|BR)s?[\/\s]*\d+\s*(?:bath|bathroom|BA)s?/i) ||
      // Indonesian patterns: "2 kamar tidur"
      markdown.match(/(\d+)\s*kamar\s*tidur/i) ||
      // Table/list patterns: "Bedrooms | 2", "Bed | 3"
      markdown.match(/(?:bed|bedroom)s?\s*[|\s]+(\d+)/i) ||
      // Title extraction: "1 bedroom apartment", "2 bedroom villa"
      markdown.match(/(\d+)\s*bedroom\s*(?:apartment|villa|house|townhouse|property)/i);

    const bedrooms = bedroomMatch ? parseInt(bedroomMatch[1]) : null;

    // Extract bathrooms - comprehensive patterns for better detection
    const bathroomMatch =
      // Standard patterns: "2 bathrooms", "3 bath", "2 kamar mandi"
      markdown.match(/(\d{1,2})\s*(?:bath|bathroom|kamar mandi)s?(?!\d)/i) ||
      // Colon/space patterns: "Bathrooms: 2", "Bath: 3"
      markdown.match(/(?:bath|bathroom)s?[:\s]+(\d{1,2})(?!\d)/i) ||
      // Abbreviation patterns: "2 BA", "3BA"
      markdown.match(/(\d{1,2})\s*BA(?!\d)/i) ||
      // Property details patterns: "- 2 bathrooms", "• 3 bath"
      markdown.match(/[-•]\s*(\d{1,2})\s*(?:bath|bathroom)s?/i) ||
      // Slash patterns: "2/3" (bed/bath), "3BR/2BA"
      markdown.match(/\d+\s*(?:bed|bedroom|BR)s?[\/\s]*(\d{1,2})\s*(?:bath|bathroom|BA)s?/i) ||
      // Indonesian patterns: "2 kamar mandi"
      markdown.match(/(\d{1,2})\s*kamar\s*mandi/i) ||
      // Table/list patterns: "Bathrooms | 2", "Bath | 3"
      markdown.match(/(?:bath|bathroom)s?\s*[|\s]+(\d{1,2})/i);

    let bathrooms = bathroomMatch ? parseInt(bathroomMatch[1]) : null;

    // Validate bathroom count (reasonable range for properties)
    if (bathrooms && (bathrooms > 15 || bathrooms < 1)) {
      console.log(`   ⚠️  Suspicious bathroom count: ${bathrooms}, setting to null`);
      bathrooms = null;
    }

    // Extract location - Bali Home Immo shows location info
    const locationMatch = markdown.match(/(Canggu|Seminyak|Ubud|Kerobokan|Sanur|Denpasar|Jimbaran|Nusa Dua|Uluwatu|Pecatu|Bukit|Umalas|Berawa|Pererenan|Kedungu|Tanah Lot|Seseh|Cemagi|Tabanan|Badung|Gianyar|Tumbak Bayuh)/i) ||
                         markdown.match(/Location[:\s]*([^\n]+)/i) ||
                         markdown.match(/Address[:\s]*([^\n]+)/i);
    const location = locationMatch ? locationMatch[1].trim() : 'Bali, Indonesia';

    // Extract images - look for image URLs
    const imageMatches = markdown.match(/!\[.*?\]\((https?:\/\/[^\)]+)\)/g) || [];
    const images = imageMatches.map(match => {
      const urlMatch = match.match(/\((https?:\/\/[^\)]+)\)/);
      return urlMatch ? urlMatch[1] : null;
    }).filter(Boolean);

    // Extract size information - enhanced patterns with better fallback logic
    let building_size_sqm = null;

    // Try specific building size patterns first
    const buildingSizePatterns = [
      // Explicit building patterns: "Building size: 180 sqm", "Built size 180 m2"
      /(?:building|built|house|villa|property)\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Reverse patterns: "180 sqm building", "180 m2 built"
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m).*(?:building|built|house|villa|property)/i,
      // Simple patterns: "Building: 180 sqm", "Size: 180 m2"
      /(?:building|size|area)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // List patterns: "- Building size: 180 sqm", "• 180 m2"
      /[-•]\s*(?:building|size|area)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i,
      // Table patterns: "Size | 180 sqm", "Building | 180 m2"
      /(?:size|building|area)\s*[|\s]+(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i,
      // Property details patterns: "3 bedrooms, 2 bathrooms, 180 sqm"
      /\d+\s*(?:bed|bedroom).*?(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i,
      // Floor area patterns: "Floor area: 180 sqm", "Living area: 180 m2"
      /(?:floor|living|internal)\s*area[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Generic size patterns with context: "Total: 180 sqm", "Space: 180 m2"
      /(?:total|space|interior)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot))/i
    ];

    for (const pattern of buildingSizePatterns) {
      const match = markdown.match(pattern);
      if (match) {
        const size = parseFloat(match[1].replace(',', ''));
        // Validate reasonable building size (20-2000 sqm)
        if (size >= 20 && size <= 2000) {
          building_size_sqm = size;
          break;
        }
      }
    }

    // If no specific building size found, try generic size patterns as fallback
    if (!building_size_sqm) {
      const genericSizePatterns = [
        // First occurrence of size in property context
        /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)(?!\s*(?:land|lot|plot|site))/i,
        // Size with minimal context
        /(?:^|\s)(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)(?!\s*(?:land|lot))/i
      ];

      for (const pattern of genericSizePatterns) {
        const match = markdown.match(pattern);
        if (match) {
          const size = parseFloat(match[1].replace(',', ''));
          // More restrictive validation for generic patterns (50-1000 sqm)
          if (size >= 50 && size <= 1000) {
            building_size_sqm = size;
            console.log(`   📐 Using generic size pattern: ${size} sqm`);
            break;
          }
        }
      }
    }

    // Extract land size with enhanced patterns and validation
    let land_size_sqm = null;

    const landSizePatterns = [
      // Explicit land patterns: "Land size: 250 sqm", "Lot size 250 m2"
      /(?:land|lot|plot|site)\s*size[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Reverse patterns: "250 sqm land", "250 m2 lot"
      /(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m).*(?:land|lot|plot|site)/i,
      // Simple patterns: "Land: 250 sqm", "Lot: 250 m2"
      /(?:land|lot|plot|site)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // List patterns: "- Land size: 250 sqm", "• 250 m2 lot"
      /[-•]\s*(?:land|lot|plot|site)\s*(?:size)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Table patterns: "Land | 250 sqm", "Lot | 250 m2"
      /(?:land|lot|plot|site)\s*[|\s]+(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i,
      // Garden/outdoor patterns: "Garden: 250 sqm", "Outdoor area: 250 m2"
      /(?:garden|outdoor|yard)\s*(?:area|space)?[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²|sq\.?m)/i
    ];

    for (const pattern of landSizePatterns) {
      const match = markdown.match(pattern);
      if (match) {
        const size = parseFloat(match[1].replace(',', ''));
        // Validate reasonable land size (50-50000 sqm)
        if (size >= 50 && size <= 50000) {
          land_size_sqm = size;
          break;
        }
      }
    }

    // Extract amenities - Bali Home Immo specific features
    const amenityKeywords = [
      'swimming pool', 'pool', 'garden', 'parking', 'garage', 'kitchen',
      'air conditioning', 'wifi', 'security', 'furnished', 'unfurnished',
      'balcony', 'terrace', 'gym', 'spa', 'jacuzzi', 'bbq', 'maid service',
      'internet', 'cable tv', 'washing machine', 'dryer', 'dishwasher'
    ];
    const amenities = [];
    const lowerMarkdown = markdown.toLowerCase();

    amenityKeywords.forEach(keyword => {
      if (lowerMarkdown.includes(keyword)) {
        amenities.push(keyword.charAt(0).toUpperCase() + keyword.slice(1));
      }
    });

    // Extract ownership type and lease duration - Bali Home Immo often mentions this
    let ownership_type = null;
    let lease_duration_years = null;
    let lease_duration_text = null;

    if (lowerMarkdown.includes('freehold')) {
      ownership_type = 'FREEHOLD';
    } else if (lowerMarkdown.includes('leasehold')) {
      ownership_type = 'LEASEHOLD';

      // Extract lease duration - enhanced patterns
      const leaseMatch = markdown.match(/(\d+)[-\s]*year[s]?\s*lease/i) ||
                        markdown.match(/lease[:\s]*(\d+)\s*year[s]?/i) ||
                        markdown.match(/(\d+)[-\s]*year[s]?\s*leasehold/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*remaining/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*left/i) ||
                        markdown.match(/(\d+)\+(\d+)\s*year[s]?\s*lease/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*tenure/i) ||
                        markdown.match(/tenure[:\s]*(\d+)\s*year[s]?/i) ||
                        markdown.match(/(\d+)\s*year[s]?\s*term/i) ||
                        markdown.match(/term[:\s]*(\d+)\s*year[s]?/i);

      if (leaseMatch) {
        lease_duration_years = parseInt(leaseMatch[1]);
        lease_duration_text = `${lease_duration_years}-year lease`;
      }
    }

    // Extract parking info
    const parkingMatch = markdown.match(/(\d+)\s*(?:car\s*)?(?:parking|garage)/i) ||
                        markdown.match(/parking[:\s]*(\d+)/i);
    const parking_spaces = parkingMatch ? parseInt(parkingMatch[1]) : null;

    // Extract year built - enhanced patterns with better context detection
    let year_built = null;
    const currentYear = new Date().getFullYear();

    // Try multiple pattern groups in order of specificity
    const yearPatternGroups = [
      // Group 1: Explicit year built patterns (highest priority)
      [
        /(?:year\s*built|built|constructed|completed|finished)[:\s]*(\d{4})/i,
        /(\d{4})\s*(?:built|constructed|completed|finished)/i,
        /(?:built|constructed|completed|finished)\s+in\s+(\d{4})/i,
        /[-•]\s*(?:year\s*built|built|constructed)[:\s]*(\d{4})/i
      ],

      // Group 2: Age and new construction patterns
      [
        /(\d{1,2})\s*years?\s*old/i, // Special handling needed
        /(?:new|brand\s*new|recently\s*built).*(\d{4})/i,
        /(\d{4}).*(?:new|brand\s*new|recently\s*built)/i,
        /(?:modern|contemporary).*(\d{4})/i
      ],

      // Group 3: Table and structured data patterns
      [
        /(?:year|built|constructed|age)\s*[|\s:]+(\d{4})/i,
        /(\d{4})\s*[|\s]+(?:year|built|constructed)/i,
        /(?:property\s*details|specifications).*?(\d{4})/i
      ],

      // Group 4: Context-based patterns (lower priority)
      [
        /(?:villa|house|building|property).*?(\d{4})/i,
        /(\d{4}).*(?:villa|house|building|property)/i,
        /(?:development|project).*?(\d{4})/i,
        /(\d{4}).*(?:development|project)/i
      ],

      // Group 5: Generic 4-digit year patterns (last resort)
      [
        /(?:since|from|established).*?(\d{4})/i,
        /(\d{4})(?!\s*(?:USD|IDR|sqm|m2|bedroom|bathroom))/i
      ]
    ];

    for (const patternGroup of yearPatternGroups) {
      for (const pattern of patternGroup) {
        const match = markdown.match(pattern);
        if (match) {
          const extractedYear = parseInt(match[1]);

          // Special handling for "years old" pattern
          if (pattern.source.includes('years?\\s*old') && extractedYear <= 50) {
            const calculatedYear = currentYear - extractedYear;
            if (calculatedYear >= 1900 && calculatedYear <= currentYear) {
              year_built = calculatedYear;
              console.log(`   📅 Calculated year from age: ${extractedYear} years old = ${calculatedYear}`);
              break;
            }
          }
          // Standard year validation
          else if (extractedYear >= 1900 && extractedYear <= currentYear + 2) {
            // Additional context validation for generic patterns
            if (pattern.source.includes('villa|house|building|property') ||
                pattern.source.includes('USD|IDR|sqm')) {
              // For generic patterns, be more strict about context
              const contextBefore = markdown.substring(Math.max(0, match.index - 50), match.index);
              const contextAfter = markdown.substring(match.index + match[0].length, match.index + match[0].length + 50);
              const fullContext = (contextBefore + ' ' + contextAfter).toLowerCase();

              // Skip if year appears in price, size, or other non-year contexts
              if (fullContext.includes('usd') || fullContext.includes('idr') ||
                  fullContext.includes('sqm') || fullContext.includes('bedroom') ||
                  fullContext.includes('bathroom') || fullContext.includes('price')) {
                continue;
              }
            }

            year_built = extractedYear;
            console.log(`   📅 Found year built: ${extractedYear}`);
            break;
          }
        }
      }

      // If we found a valid year, stop searching
      if (year_built) break;
    }

    // Extract description - find meaningful property description
    const cleanDescription = extractCleanDescription(markdown);
    const description = cleanDescription || 'No description provided';

    // Smart status detection based on title and description
    const status = detectPropertyStatus(title, description);

    // Skip SOLD/RENTED/INACTIVE properties to keep database clean
    if (status === 'SOLD' || status === 'RENTED' || status === 'INACTIVE') {
      console.log(`   ⚠️ Skipping property with status: ${status}`);
      return null;
    }

    const extractedData = {
      title: title,
      price: price,
      location: location,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      description: description,
      images: images.slice(0, 10), // Max 10 images
      property_id: property_id,
      detail_url: url,
      property_type: 'villa', // Default for Bali Home Immo
      status: status, // Use smart status detection instead of hardcoded 'available'
      amenities: amenities,
      year_built: year_built,
      parking: parking_spaces,
      ownership_type: ownership_type,
      lease_duration_years: lease_duration_years,
      lease_duration_text: lease_duration_text,
      size: {
        building_size_sqm: building_size_sqm,
        land_size_sqm: land_size_sqm
      }
    };

    console.log(`   ✅ Extracted: ${title} (${bedrooms}bed/${bathrooms}bath) in ${location} - ${price}`);
    return extractedData;

  } catch (error) {
    console.log(`   ❌ Bali Home Immo markdown parsing failed: ${error.message}`);
    return null;
  }
}

// BetterPlace-specific description extractor - looks for "Description" section
function extractBetterPlaceDescription(markdown) {
  try {
    const lines = markdown.split('\n');

    // Look for the "Description" section header
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Check if this line is a "Description" header
      if (line.toLowerCase().includes('description') &&
          (line.startsWith('#') || line.startsWith('**') || line.length < 50)) {

        // Look for the actual description content in the next few lines
        for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
          const contentLine = lines[j].trim();

          // Skip empty lines and short lines
          if (contentLine.length < 50) continue;

          // Skip lines that are clearly not description content
          if (contentLine.startsWith('#') ||
              contentLine.startsWith('*') ||
              contentLine.startsWith('-') ||
              contentLine.startsWith('|') ||
              contentLine.includes('![')) continue;

          // If we find a substantial line with property content, this is likely our description
          if (contentLine.length > 100 &&
              contentLine.toLowerCase().includes('bali') &&
              (contentLine.toLowerCase().includes('land') ||
               contentLine.toLowerCase().includes('property') ||
               contentLine.toLowerCase().includes('development') ||
               contentLine.toLowerCase().includes('villa') ||
               contentLine.toLowerCase().includes('investment') ||
               contentLine.toLowerCase().includes('imagine'))) {

            return contentLine;
          }
        }
      }

      // Also check for lines that start with property descriptions directly
      if (line.length > 200 &&
          line.toLowerCase().includes('imagine stepping into') &&
          line.toLowerCase().includes('bali')) {
        return line;
      }
    }

    return null;
  } catch (error) {
    console.log(`   ⚠️  BetterPlace description extraction failed: ${error.message}`);
    return null;
  }
}

// Extract clean description from markdown, filtering out technical content
function extractCleanDescription(markdown) {
  try {
    // Split into lines and clean each line
    const lines = markdown.split('\n').map(line => line.trim());

    // Filter out problematic lines - comprehensive filtering
    const cleanLines = lines.filter(line => {
      // Skip empty or very short lines
      if (line.length < 15) return false;

      const lowerLine = line.toLowerCase();

      // Skip lines with contact/technical content - enhanced filtering
      if (lowerLine.includes('whatsapp') ||
          lowerLine.includes('wa.me') ||
          lowerLine.includes('wp-content') ||
          lowerLine.includes('_next/image') ||
          lowerLine.includes('digitaloceanspaces') ||
          lowerLine.includes('contact us') ||
          lowerLine.includes('phone:') ||
          lowerLine.includes('email:') ||
          lowerLine.includes('call us') ||
          lowerLine.includes('reach out') ||
          lowerLine.includes('get in touch') ||
          lowerLine.includes('schedule') ||
          lowerLine.includes('consultation') ||
          lowerLine.includes('booking') ||
          lowerLine.includes('reserve') ||
          lowerLine.includes('inquiry') ||
          lowerLine.includes('enquiry') ||
          lowerLine.includes('online') ||
          lowerLine.includes('website') ||
          lowerLine.includes('click') ||
          lowerLine.includes('visit us') ||
          lowerLine.includes('visit our') ||
          lowerLine.includes('browse') ||
          lowerLine.includes('download') ||
          lowerLine.includes('subscribe') ||
          lowerLine.includes('newsletter') ||
          lowerLine.includes('follow us') ||
          lowerLine.includes('social media') ||
          lowerLine.includes('facebook') ||
          lowerLine.includes('instagram') ||
          lowerLine.includes('twitter') ||
          lowerLine.includes('linkedin') ||
          lowerLine.includes('youtube') ||
          lowerLine.includes('telegram') ||
          lowerLine.includes('free consultation') ||
          lowerLine.includes('free advice') ||
          lowerLine.includes('no obligation') ||
          lowerLine.includes('terms and conditions') ||
          lowerLine.includes('privacy policy') ||
          lowerLine.includes('cookie policy') ||
          lowerLine.includes('disclaimer') ||
          lowerLine.includes('answer few questions') ||
          lowerLine.includes('we will offer') ||
          lowerLine.includes('real estate solutions') ||
          lowerLine.includes('fill out') ||
          lowerLine.includes('form') ||
          lowerLine.includes('submit') ||
          lowerLine.includes('send us') ||
          lowerLine.includes('contact form') ||
          lowerLine.includes('get quote') ||
          lowerLine.includes('request info') ||
          lowerLine.includes('more information') ||
          lowerLine.includes('call to action') ||
          lowerLine.includes('cta') ||
          lowerLine.includes('button') ||
          lowerLine.includes('link')) {
        return false;
      }

      // Skip lines with markdown/HTML markup
      if (line.includes('[![') ||
          line.includes('](http') ||
          line.includes('<img') ||
          line.includes('<a href') ||
          line.includes('![') ||
          line.startsWith('#') ||
          line.startsWith('*') ||
          line.startsWith('-') ||
          line.startsWith('|') ||
          line.startsWith('>')) {
        return false;
      }

      // Skip lines with currency/pricing info - but only if they're short pricing lines, not descriptive paragraphs
      if (line.length < 100 && (
          lowerLine.includes('usd') ||
          lowerLine.includes('idr') ||
          lowerLine.includes('aud') ||
          lowerLine.includes('eur') ||
          lowerLine.includes('price') ||
          lowerLine.includes('cost') ||
          lowerLine.includes('$') ||
          lowerLine.includes('rp ') ||
          lowerLine.includes('€') ||
          lowerLine.includes('£'))) {
        return false;
      }

      // Skip lines with property specifications - but only if they're short spec lines, not descriptive paragraphs
      if (line.length < 80 && (
          lowerLine.includes('bedroom') ||
          lowerLine.includes('bathroom') ||
          lowerLine.includes('sqm') ||
          lowerLine.includes('m2') ||
          lowerLine.includes('m²') ||
          lowerLine.includes('parking') ||
          lowerLine.includes('year built') ||
          lowerLine.includes('ownership') ||
          lowerLine.includes('built:') ||
          lowerLine.includes('size:'))) {
        return false;
      }

      // Always skip very short freehold/leasehold lines as they're usually just labels
      if (line.length < 50 && (lowerLine.includes('leasehold') || lowerLine.includes('freehold'))) {
        return false;
      }

      // Skip lines with navigation/website elements - enhanced filtering
      if (lowerLine.includes('home') && lowerLine.includes('about') ||
          lowerLine.includes('menu') ||
          lowerLine.includes('navigation') ||
          lowerLine.includes('search') ||
          lowerLine.includes('filter') ||
          lowerLine.includes('sort by') ||
          lowerLine.includes('view all') ||
          lowerLine.includes('load more') ||
          lowerLine.includes('next page') ||
          lowerLine.includes('previous') ||
          lowerLine.includes('breadcrumb') ||
          lowerLine.includes('sidebar') ||
          lowerLine.includes('footer') ||
          lowerLine.includes('header') ||
          lowerLine.includes('login') ||
          lowerLine.includes('register') ||
          lowerLine.includes('sign up') ||
          lowerLine.includes('sign in') ||
          lowerLine.includes('logout') ||
          lowerLine.includes('account') ||
          lowerLine.includes('dashboard') ||
          lowerLine.includes('profile') ||
          lowerLine.includes('settings') ||
          lowerLine.includes('preferences') ||
          lowerLine.includes('cart') ||
          lowerLine.includes('checkout') ||
          lowerLine.includes('payment') ||
          lowerLine.includes('shipping') ||
          lowerLine.includes('delivery') ||
          lowerLine.includes('return policy') ||
          lowerLine.includes('refund') ||
          lowerLine.includes('warranty') ||
          lowerLine.includes('support') ||
          lowerLine.includes('help') ||
          lowerLine.includes('faq') ||
          lowerLine.includes('frequently asked') ||
          lowerLine.includes('customer service') ||
          lowerLine.includes('live chat') ||
          lowerLine.includes('chat with us') ||
          lowerLine.includes('talk to') ||
          lowerLine.includes('speak with')) {
        return false;
      }

      // Skip lines that are mostly URLs or technical markup
      if (line.includes('http') && line.length < 100) return false;

      // Skip lines with mostly special characters
      const specialCharCount = (line.match(/[^a-zA-Z0-9\s]/g) || []).length;
      if (specialCharCount > line.length * 0.3) return false;

      return true;
    });

    // Collect all potential descriptions and score them
    const candidates = [];

    for (const line of cleanLines) {
      if (line.length > 30 && line.length < 800) {
        // Clean up any remaining markdown and technical content
        let cleaned = line
          .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
          .replace(/\[.*?\]\(.*?\)/g, '') // Remove links
          .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
          .replace(/\*(.*?)\*/g, '$1') // Remove italic
          .replace(/`(.*?)`/g, '$1') // Remove code
          .replace(/#{1,6}\s*/g, '') // Remove headers
          .replace(/\s+/g, ' ') // Normalize whitespace
          .replace(/[^\w\s.,!?()-]/g, ' ') // Remove special characters except basic punctuation
          .trim();

        // Additional content quality checks - enhanced validation
        const cleanedLower = cleaned.toLowerCase();
        if (cleaned.length > 50 &&
            !cleanedLower.includes('answer few questions') &&
            !cleanedLower.includes('receive handpicked updates') &&
            !cleanedLower.includes('click here') &&
            !cleanedLower.includes('read more') &&
            !cleanedLower.includes('view details') &&
            !cleanedLower.includes('learn more') &&
            !cleanedLower.includes('find out more') &&
            !cleanedLower.includes('discover more') &&
            !cleanedLower.includes('explore more') &&
            !cleanedLower.includes('see more') &&
            !cleanedLower.includes('show more') &&
            !cleanedLower.includes('load more') &&
            !cleanedLower.includes('view all') &&
            !cleanedLower.includes('see all') &&
            !cleanedLower.includes('browse all') &&
            !cleanedLower.includes('check out') &&
            !cleanedLower.includes('take a look') &&
            !cleanedLower.includes('have a look') &&
            !cleanedLower.includes('get started') &&
            !cleanedLower.includes('start now') &&
            !cleanedLower.includes('join now') &&
            !cleanedLower.includes('sign up') &&
            !cleanedLower.includes('register') &&
            !cleanedLower.includes('subscribe') &&
            !cleanedLower.includes('follow') &&
            !cleanedLower.includes('like us') &&
            !cleanedLower.includes('share') &&
            !cleanedLower.includes('tweet') &&
            !cleanedLower.includes('post') &&
            !cleanedLower.includes('comment') &&
            !cleanedLower.includes('review') &&
            !cleanedLower.includes('rate us') &&
            !cleanedLower.includes('feedback') &&
            !cleanedLower.includes('testimonial') &&
            cleaned.split(' ').length > 8) { // At least 8 words

          // Ensure it's a proper sentence and contains property-related content
          const hasPropertyContent = cleanedLower.includes('villa') ||
                                   cleanedLower.includes('house') ||
                                   cleanedLower.includes('property') ||
                                   cleanedLower.includes('home') ||
                                   cleanedLower.includes('residence') ||
                                   cleanedLower.includes('apartment') ||
                                   cleanedLower.includes('townhouse') ||
                                   cleanedLower.includes('building') ||
                                   cleanedLower.includes('location') ||
                                   cleanedLower.includes('area') ||
                                   cleanedLower.includes('neighborhood') ||
                                   cleanedLower.includes('district') ||
                                   cleanedLower.includes('beach') ||
                                   cleanedLower.includes('ocean') ||
                                   cleanedLower.includes('view') ||
                                   cleanedLower.includes('design') ||
                                   cleanedLower.includes('architecture') ||
                                   cleanedLower.includes('style') ||
                                   cleanedLower.includes('modern') ||
                                   cleanedLower.includes('traditional') ||
                                   cleanedLower.includes('luxury') ||
                                   cleanedLower.includes('spacious') ||
                                   cleanedLower.includes('comfortable') ||
                                   cleanedLower.includes('beautiful') ||
                                   cleanedLower.includes('stunning') ||
                                   cleanedLower.includes('perfect') ||
                                   cleanedLower.includes('ideal') ||
                                   cleanedLower.includes('excellent') ||
                                   cleanedLower.includes('amazing') ||
                                   cleanedLower.includes('wonderful') ||
                                   cleanedLower.includes('fantastic') ||
                                   cleanedLower.includes('great') ||
                                   cleanedLower.includes('good') ||
                                   cleanedLower.includes('nice') ||
                                   cleanedLower.includes('charming') ||
                                   cleanedLower.includes('elegant') ||
                                   cleanedLower.includes('sophisticated') ||
                                   cleanedLower.includes('investment') ||
                                   cleanedLower.includes('opportunity') ||
                                   cleanedLower.includes('potential') ||
                                   cleanedLower.includes('rental') ||
                                   cleanedLower.includes('family') ||
                                   cleanedLower.includes('living') ||
                                   cleanedLower.includes('lifestyle');

          // For land properties, be more lenient with content requirements
          const hasLandContent = cleanedLower.includes('land') ||
                                cleanedLower.includes('plot') ||
                                cleanedLower.includes('lot') ||
                                cleanedLower.includes('terrain') ||
                                cleanedLower.includes('acre') ||
                                cleanedLower.includes('are') ||
                                cleanedLower.includes('hectare') ||
                                cleanedLower.includes('sqm') ||
                                cleanedLower.includes('m2') ||
                                cleanedLower.includes('freehold') ||
                                cleanedLower.includes('leasehold') ||
                                cleanedLower.includes('investment') ||
                                cleanedLower.includes('development');

          // Ensure it's a proper sentence and has property-related content (including land-specific content)
          if ((cleaned.match(/[.!?]$/) || cleaned.length > 100) && (hasPropertyContent || hasLandContent)) {
            return cleaned;
          }
        }
      }
    }

    // If no good paragraph found, try to construct from multiple shorter lines
    const shortLines = cleanLines.filter(line => line.length > 20 && line.length < 200);
    if (shortLines.length > 0) {
      const combined = shortLines.slice(0, 3).join(' ').trim();
      if (combined.length > 50) {
        return combined.substring(0, 300);
      }
    }

    return null;
  } catch (error) {
    console.log(`   ⚠️  Description extraction failed: ${error.message}`);
    return null;
  }
}

// Universal smart property classification for all websites
function classifyProperty(title, description) {
  const text = `${title} ${description}`.toLowerCase();

  // Commercial property detection - use more precise patterns to avoid false positives
  const commercialPatterns = [
    /\b(retail\s+shop|shop\s+for\s+sale|shop\s+for\s+rent)\b/,
    /\b(office\s+building|office\s+space|office\s+for\s+sale|office\s+for\s+rent)\b/,
    /\b(commercial\s+property|commercial\s+building|commercial\s+space)\b/,
    /\b(store\s+for\s+sale|store\s+for\s+rent|retail\s+store)\b/,
    /\b(warehouse\s+for\s+sale|warehouse\s+for\s+rent|storage\s+facility)\b/,
    /\b(business\s+premises|business\s+property)\b/
  ];

  // Check if any commercial pattern matches
  const isCommercial = commercialPatterns.some(pattern => pattern.test(text));

  if (isCommercial) {
    if (/\b(office\s+building|office\s+space|office\s+for\s+sale|office\s+for\s+rent)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'OFFICE' };
    }
    if (/\b(retail\s+shop|shop\s+for\s+sale|shop\s+for\s+rent|store\s+for\s+sale|store\s+for\s+rent|retail\s+store)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'RETAIL' };
    }
    if (/\b(warehouse\s+for\s+sale|warehouse\s+for\s+rent|storage\s+facility)\b/.test(text)) {
      return { category: 'COMMERCIAL', type: 'WAREHOUSE' };
    }
    return { category: 'COMMERCIAL', type: 'OTHER' };
  }

  // Residential property detection
  if (text.includes('villa')) return { category: 'RESIDENTIAL', type: 'VILLA' };
  if (text.includes('apartment') || text.includes('apt')) return { category: 'RESIDENTIAL', type: 'APARTMENT' };
  if (text.includes('house') || text.includes('home')) return { category: 'RESIDENTIAL', type: 'HOUSE' };
  if (text.includes('condo') || text.includes('condominium')) return { category: 'RESIDENTIAL', type: 'CONDO' };
  if (text.includes('townhouse') || text.includes('town house')) return { category: 'RESIDENTIAL', type: 'TOWNHOUSE' };

  // Land detection
  if (text.includes('land') || text.includes('plot') || text.includes('lot') || text.includes('terrain')) return { category: 'LAND', type: 'LAND' };

  // Industrial detection
  if (text.includes('factory') || text.includes('manufacturing') || text.includes('industrial')) return { category: 'INDUSTRIAL', type: 'OTHER' };

  // Default to residential villa for Bali properties
  return { category: 'RESIDENTIAL', type: 'VILLA' };
}

// Smart status detection based on title and description
function detectPropertyStatus(title, description) {
  const text = (title + ' ' + description).toLowerCase();

  // Check for sold indicators
  if (text.includes('(sold)') || text.includes('sold out') || text.includes('no longer available')) {
    return 'SOLD';
  }

  // Check for rented indicators
  if (text.includes('(rented)') || text.includes('rented out') || text.includes('no longer for rent')) {
    return 'RENTED';
  }

  // Check for pending indicators
  if (text.includes('(pending)') || text.includes('under offer') || text.includes('reserved')) {
    return 'PENDING';
  }

  // Check for unavailable indicators (be specific to avoid false positives)
  if (text.includes('not available') || text.includes('no longer available') ||
      text.includes('unavailable') || text.includes('off market') ||
      text.includes('withdrawn from market') || text.includes('removed from market')) {
    return 'INACTIVE';
  }

  // Default to available
  return 'AVAILABLE';
}

// Helper function to check if property should be skipped based on status
function shouldSkipProperty(status, skipSoldProperties = true) {
  if (skipSoldProperties && (status === 'SOLD' || status === 'RENTED' || status === 'INACTIVE')) {
    return true;
  }
  return false;
}

// Bali Home Immo (rental, IDR strings)
function mapBaliHomeImmo(raw) {
  // Handle both direct data and scraped data structure
  let data;

  // Try markdown parsing first (cost-effective)
  if (raw?.markdown) {
    console.log('   📝 Using markdown data for Bali Home Immo');
    data = parseBaliHomeImmoMarkdown(raw.markdown, raw.url || '');

    // If markdown parsing returned null (e.g., price on request), skip this property
    if (data === null) {
      console.log('   ⚠️ Skipping property: Markdown parsing returned null');
      return null;
    }
  } else {
    // Fallback to JSON data
    console.log('   📊 Using JSON data for Bali Home Immo');
    data = raw?.json || raw;
  }

  // Additional safety check: ensure data is not null before proceeding
  if (!data) {
    console.log('   ❌ No valid data found for Bali Home Immo property');
    return null;
  }

  const rent_price = parseIdr(data?.price);
  const { city, state } = splitLocation(data?.location);
  const year_built = data?.year_built || extractYear(data);
  const parking_spaces = data?.parking || extractParkingSpaces(data?.parking);

  // Smart classification based on title and description
  const { category, type } = classifyProperty(data?.title || '', data?.description || '');

  // Extract ownership information - use parsed data first, then fallback
  const ownership_type = data?.ownership_type || extractOwnershipInfo(data)?.ownership_type;
  const lease_duration_years = data?.lease_duration_years || extractOwnershipInfo(data)?.lease_duration_years;
  const lease_duration_text = data?.lease_duration_text || extractOwnershipInfo(data)?.lease_duration_text;

  return {
    title: sanitizeText(data?.title || ''),
    category,
    type,
    status: 'AVAILABLE',
    address: sanitizeText(data?.location || ''),
    city,
    state,
    country: 'Indonesia',
    description: sanitizeText(data?.description || ''),
    rent_price,
    bedrooms: data?.bedrooms ?? undefined,
    bathrooms: data?.bathrooms ?? undefined,
    parking_spaces,
    size_sqft: data?.size?.building_size_sqm ? data.size.building_size_sqm * 10.764 : undefined,
    lot_size_sqft: data?.size?.land_size_sqm ? data.size.land_size_sqm * 10.764 : undefined,
    year_built,
    ownership_type,
    lease_duration_years,
    lease_duration_text,
    amenities: { raw_amenities: data?.amenities || [] },
    media: {
      images: data?.images || [],
      image_count: data?.images?.length || 0,
      source_id: 'bali_home_immo',
      external_id: data?.property_id || '',
      source_url: raw?.url || data?.detail_url || ''
    }
  };
}

// Bali Villa Realty (rental, USD per month) - ASYNC for smart currency conversion
async function mapBaliVillaRealty(raw) {
  // Handle both direct data and scraped data structure
  let data;

  // Try markdown parsing first (cost-effective)
  if (raw?.markdown) {
    console.log('   📝 Using markdown data for Bali Villa Realty');
    data = parseBaliVillaRealtyMarkdown(raw.markdown, raw.url || '');

    // If parsing returned null (e.g., SOLD property), skip this property
    if (data === null) {
      console.log('   🚫 Property skipped by parser (likely SOLD/RENTED/INACTIVE)');
      return null;
    }
  } else {
    // Fallback to JSON data
    console.log('   📊 Using JSON data for Bali Villa Realty');
    data = raw?.json || raw;
  }

  // Determine if this is a sale or rental based on URL - more comprehensive patterns
  const isSale = raw?.url && (
    raw.url.includes('/for-sale/') ||
    raw.url.includes('-for-sale-') ||
    raw.url.includes('/sale/') ||
    raw.url.includes('-sale-') ||
    raw.url.includes('/land-') ||  // Land sales
    raw.url.includes('/villa-') ||  // Villa sales
    !raw.url.includes('/for-rent/') && !raw.url.includes('-for-rent-')  // Default to sale if not rental
  );
  const isRental = raw?.url && (raw.url.includes('/for-rent/') || raw.url.includes('-for-rent-'));

  // Convert price and assign to correct field
  const convertedPrice = await convertPriceToIDR(data?.price);
  const price = (isSale && convertedPrice) ? convertedPrice : undefined;
  const rent_price = (isRental && convertedPrice) ? convertedPrice : undefined;

  const { city, state } = splitLocation(data?.location);
  const year_built = data?.year_built || extractYear(data);
  const parking_spaces = data?.parking || extractParkingSpaces(data?.parking);

  // Smart classification based on title and description
  const { category, type } = classifyProperty(data?.title || '', data?.description || '');

  // Use status from parsed data (already detected in parsing function)
  const status = data?.status || 'AVAILABLE';

  // Extract ownership information - use parsed data first, then fallback
  const ownership_type = data?.ownership_type || extractOwnershipInfo(data)?.ownership_type;
  const lease_duration_years = data?.lease_duration_years || extractOwnershipInfo(data)?.lease_duration_years;
  const lease_duration_text = data?.lease_duration_text || extractOwnershipInfo(data)?.lease_duration_text;

  return {
    title: sanitizeText(data?.title || ''),
    category,
    type,
    status,
    address: sanitizeText(data?.location || ''),
    city,
    state,
    country: 'Indonesia',
    description: sanitizeText(data?.description || ''),
    price,
    rent_price,
    bedrooms: data?.bedrooms ?? undefined,
    bathrooms: data?.bathrooms ?? undefined,
    parking_spaces,
    size_sqft: data?.size?.building_size_sqm ? data.size.building_size_sqm * 10.764 : undefined,
    lot_size_sqft: data?.size?.land_size_sqm ? data.size.land_size_sqm * 10.764 : undefined,
    year_built,
    ownership_type,
    lease_duration_years,
    lease_duration_text,
    amenities: { raw_amenities: data?.amenities || [] },
    media: {
      images: data?.images || [],
      image_count: data?.images?.length || 0,
      source_id: 'bali_villa_realty',
      external_id: data?.property_id || '',
      source_url: raw?.url || data?.detail_url || ''
    }
  };
}

// Villa Bali Sale (sale/rent, mixed currencies) - ASYNC for smart currency conversion
async function mapVillaBaliSale(raw) {
  // Villa Bali Sale - extract everything from markdown only
  const content = raw?.markdown || '';

  let title = '';
  let priceText = '';
  let location = '';
  let bedrooms = undefined;
  let bathrooms = undefined;
  let description = '';
  let images = [];
  let amenities = [];
  let size_sqft = undefined;
  let lot_size_sqft = undefined;
  let year_built = undefined;
  let parking_spaces = undefined;
  let external_id = '';
  let ownership_type_enum = null;
  let lease_duration_years = null;
  let lease_duration_text = null;

  if (content) {
    // Extract title from metadata first (most reliable), then content
    if (raw?.metadata?.title) {
      title = sanitizeText(raw.metadata.title);
    } else if (raw?.metadata?.ogTitle) {
      title = sanitizeText(raw.metadata.ogTitle);
    } else {
      // Extract title from content patterns
      const titlePatterns = [
        /<title[^>]*>([^<]+)<\/title>/i,
        /<h1[^>]*>([^<]+)<\/h1>/i,
        /^#\s*(.+)$/m
      ];

      for (const pattern of titlePatterns) {
        const titleMatch = content.match(pattern);
        if (titleMatch) {
          title = sanitizeText(titleMatch[1].replace(/\s+/g, ' ').trim());
          break;
        }
      }
    }

    // Extract price - look for IDR, USD, EUR patterns
    const pricePatterns = [
      /IDR\s*([\d,\.]+)/i,
      /USD\s*([\d,\.]+)/i,
      /EUR\s*([\d,\.]+)/i,
      /\$\s*([\d,\.]+)/,
      /€\s*([\d,\.]+)/,
      /Rp\s*([\d,\.]+)/i
    ];

    for (const pattern of pricePatterns) {
      const priceMatch = content.match(pattern);
      if (priceMatch) {
        priceText = priceMatch[0];
        break;
      }
    }

    // Extract bedrooms - Villa Bali Sale specific patterns
    const bedroomPatterns = [
      /(\w+)\s*bedroom/i,  // "six bedroom"
      /(\d+)\s*bed/i,      // "6 bed"
      /bed.*?(\d+)/i       // "bed 6"
    ];

    for (const pattern of bedroomPatterns) {
      const bedroomMatch = content.match(pattern);
      if (bedroomMatch && !bedrooms) {
        const value = bedroomMatch[1];
        if (isNaN(value)) {
          // Convert word to number: "six" -> 6
          const wordToNumber = {
            'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
            'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10
          };
          bedrooms = wordToNumber[value.toLowerCase()] || undefined;
        } else {
          bedrooms = parseInt(value);
        }
        if (bedrooms) break;
      }
    }

    // Extract bathrooms - Villa Bali Sale specific patterns
    const bathroomPatterns = [
      /bathtub\.svg\)(\d+)/i,  // "bathtub.svg)7"
      /(\d+)\s*bath/i,         // "7 bath"
      /bath.*?(\d+)/i          // "bath 7"
    ];

    for (const pattern of bathroomPatterns) {
      const bathroomMatch = content.match(pattern);
      if (bathroomMatch && !bathrooms) {
        bathrooms = parseInt(bathroomMatch[1]);
        break;
      }
    }

    // Extract building size - Villa Bali Sale specific patterns
    const buildingSizePatterns = [
      /\*\*Building:\*\*\s*(\d+)m2/i,  // "**Building:** 400m2"
      /building.*?(\d+)\s*m2/i,        // "building 400 m2"
      /(\d+)\s*m2.*?building/i         // "400 m2 building"
    ];

    for (const pattern of buildingSizePatterns) {
      const sizeMatch = content.match(pattern);
      if (sizeMatch && !size_sqft) {
        const sqm = parseInt(sizeMatch[1]);
        size_sqft = sqm * 10.764; // Convert m2 to sqft
        break;
      }
    }

    // Extract land size - Villa Bali Sale specific patterns (support decimals)
    const landSizePatterns = [
      /\*\*Land:\*\*\s*([\d.]+)\s*Are/i,   // "**Land:** 0.72 Are"
      /land.*?([\d.]+)\s*are/i,           // "land 0.72 are"
      /([\d.]+)\s*are.*?land/i,           // "0.72 are land"
      /land.*?([\d.]+)\s*m2/i,            // "land 2700 m2"
      /([\d.]+)\s*m2.*?land/i             // "2700 m2 land"
    ];

    for (const pattern of landSizePatterns) {
      const landMatch = content.match(pattern);
      if (landMatch && !lot_size_sqft) {
        const value = parseFloat(landMatch[1]); // Use parseFloat for decimal support
        if (pattern.source.toLowerCase().includes('are') || landMatch[0].toLowerCase().includes('are')) {
          // Convert Are to sqft (1 Are = 100 m2 = 1076.4 sqft)
          lot_size_sqft = value * 100 * 10.764;
        } else {
          // Already in m2, convert to sqft
          lot_size_sqft = value * 10.764;
        }
        break;
      }
    }

    // Extract year built - Villa Bali Sale specific patterns
    const yearPatterns = [
      /year.*?built.*?(\d{4})/i,        // "Year Built2026"
      /built.*?(\d{4})/i,               // "Built 2026"
      /delivered.*?(\d{4})/i,           // "delivered in September 2026"
      /september.*?(\d{4})/i,           // "September 2026"
      /construction.*?(\d{4})/i,        // "construction 2026"
      /completed.*?(\d{4})/i            // "completed 2026"
    ];

    for (const pattern of yearPatterns) {
      const yearMatch = content.match(pattern);
      if (yearMatch && !year_built) {
        const year = parseInt(yearMatch[1]);
        if (year >= 1900 && year <= 2030) { // Reasonable year range
          year_built = year;
          break;
        }
      }
    }

    // Extract amenities - Villa Bali Sale specific patterns
    const amenityMatches = content.match(/facilities([\s\S]*?)(?:distance|location|$)/i);
    if (amenityMatches) {
      const facilitiesSection = amenityMatches[1];
      const amenityPatterns = [
        /_wifi_\s*wifi/i,
        /_pool_\s*pool/i,
        /_kitchen_\s*kitchen/i,
        /_ac_unit_\s*air\s*conditioner/i,
        /_power_\s*electricity/i,
        /_free_breakfast_\s*dining\s*area/i,
        /_water_drop_\s*water\s*source/i,
        /_live_tv_\s*cable\s*tv/i,
        /_two_wheeler_\s*parking/i,
        /_hot_tub_\s*bath\s*tub/i,
        /_public_\s*internet/i,
        /_storage_\s*storage/i,
        /_stairs_\s*level/i
      ];

      amenityPatterns.forEach(pattern => {
        const match = facilitiesSection.match(pattern);
        if (match) {
          const amenityName = match[0].replace(/_[^_]*_\s*/g, '').trim();
          if (amenityName) {
            amenities.push(amenityName);
          }
        }
      });
    }

    // Extract images - Villa Bali Sale specific patterns
    const imagePatterns = [
      /!\[.*?\]\((https:\/\/www\.villabalisale\.com\/uploads\/images\/property\/[^)]+)\)/g,
      /https:\/\/www\.villabalisale\.com\/uploads\/images\/property\/[^\s)]+\.(?:jpg|jpeg|png|gif|webp)/g
    ];

    imagePatterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern)];
      matches.forEach(match => {
        const imageUrl = match[1] || match[0];
        if (imageUrl && !imageUrl.includes('watermark') && !images.includes(imageUrl)) {
          images.push(imageUrl);
        }
      });
    });

    // Extract external ID from URL
    if (raw?.url) {
      const urlMatch = raw.url.match(/\/([^\/]+)$/);
      if (urlMatch) {
        external_id = urlMatch[1];
      }
    }

    // Extract ownership type from URL (Villa Bali Sale has it in URL)
    if (raw?.url) {
      if (raw.url.includes('/freehold/')) {
        ownership_type_enum = 'FREEHOLD';
      } else if (raw.url.includes('/leasehold/')) {
        ownership_type_enum = 'LEASEHOLD';
      }
    }

    // Extract leasehold duration - Villa Bali Sale specific patterns
    if (ownership_type_enum === 'LEASEHOLD') {
      const leaseholdPatterns = [
        /leasehold\s*\/\s*(\d+)\s*years?/i,        // "Leasehold / 27 years"
        /lease.*?(\d+)\s*years?/i,                // "lease 27 years"
        /(\d+)\s*years?.*?lease/i,                // "27 years lease"
        /duration.*?(\d+)\s*years?/i,             // "duration 27 years"
        /(\d+)\s*years?.*?duration/i,             // "27 years duration"
        /remaining.*?(\d+)\s*years?/i,            // "remaining 27 years"
        /(\d+)\s*years?.*?remaining/i             // "27 years remaining"
      ];

      for (const pattern of leaseholdPatterns) {
        const leaseMatch = content.match(pattern);
        if (leaseMatch && !lease_duration_years) {
          const years = parseInt(leaseMatch[1]);
          if (years >= 1 && years <= 999) { // Reasonable lease duration range
            lease_duration_years = years;
            lease_duration_text = `${years} years`;
            break;
          }
        }
      }
    }

    // Extract location from URL or content
    if (raw?.url) {
      const urlMatch = raw.url.match(/\/([\w-]+)\/([^\/]+)$/);
      if (urlMatch) {
        location = urlMatch[1].replace(/-/g, ' ');
      }
    }

    // Extract description from metadata or content
    if (raw?.metadata?.description) {
      description = sanitizeText(raw.metadata.description);
    } else if (raw?.metadata?.ogDescription) {
      description = sanitizeText(raw.metadata.ogDescription);
    } else {
      // Extract description from content patterns
      const descPatterns = [
        /description[:\s]*([^#\n]+)/i,
        /about[:\s]*([^#\n]+)/i,
        /details[:\s]*([^#\n]+)/i,
        /overview[:\s]*([^#\n]+)/i
      ];

      for (const pattern of descPatterns) {
        const match = content.match(pattern);
        if (match && match[1] && match[1].trim().length > 20) {
          description = sanitizeText(match[1].trim());
          break;
        }
      }

      // If still no description, extract first meaningful paragraph
      if (!description) {
        const paragraphs = content.split('\n').filter(line =>
          line.trim().length > 50 &&
          !line.includes('|') &&
          !line.startsWith('#') &&
          !line.includes('IDR') &&
          !line.includes('USD') &&
          !line.includes('bedroom') &&
          !line.includes('bathroom')
        );

        if (paragraphs.length > 0) {
          description = sanitizeText(paragraphs[0]);
        }
      }
    }

  }

  // Fallback: use URL to extract basic info
  if (!title && raw?.url) {
    const urlParts = raw.url.split('/');
    const lastPart = urlParts[urlParts.length - 1];
    title = lastPart.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  // Convert price to IDR
  let price = await convertPriceToIDR(priceText);

  // Determine if it's sale or rent based on URL
  const isRental = raw?.url?.includes('/a-louer/') ||
                   raw?.url?.includes('/for-rent/') ||
                   raw?.url?.includes('/rent/');

  const sale_price = !isRental ? price : undefined;
  const rent_price = isRental ? price : undefined;

  // For validation compatibility, use 'price' field for sale properties
  const validationPrice = !isRental ? price : undefined;

  const { city, state } = splitLocation(location);

  // Enhanced state detection for Villa Bali Sale
  let finalState = state;
  if (!finalState) {
    // Default to Bali for Villa Bali Sale properties
    finalState = 'Bali';

    // Try to extract state from location or URL
    if (location) {
      const locationLower = location.toLowerCase();
      if (locationLower.includes('bali') || locationLower.includes('denpasar') ||
          locationLower.includes('ubud') || locationLower.includes('canggu') ||
          locationLower.includes('seminyak') || locationLower.includes('kuta') ||
          locationLower.includes('sanur') || locationLower.includes('jimbaran') ||
          locationLower.includes('amed') || locationLower.includes('candidasa')) {
        finalState = 'Bali';
      }
    }
  }

  // Smart classification based on title and URL
  const { category, type } = classifyProperty(title, description);

  // Smart status detection
  const status = detectPropertyStatus(title, description);

  return {
    title: title || 'Villa Bali Sale Property',
    category,
    type,
    status,
    address: location || '',
    city: city || location || 'Bali',
    state: finalState,
    country: 'Indonesia',
    description: description || '',
    price: validationPrice, // For validation compatibility
    rent_price,
    bedrooms,
    bathrooms,
    parking_spaces,
    size_sqft,
    lot_size_sqft,
    year_built,
    ownership_type: ownership_type_enum,
    lease_duration_years,
    lease_duration_text,
    amenities: amenities, // Direct JSON, not wrapped
    media: {
      images,
      image_count: images.length,
      source_id: 'villa_bali_sale',
      external_id: external_id,
      source_url: raw?.url || ''
    }
  };
}

// Choose the appropriate mapper based on website ID
function chooseMapper(websiteId) {
  const mappers = {
    'betterplace': mapBetterPlace,
    'bali_home_immo': mapBaliHomeImmo,
    'bali_villa_realty': mapBaliVillaRealty,
    'villa_bali_sale': mapVillaBaliSale,
    'villabalisale': mapVillaBaliSale  // Alternative name for same mapper
  };

  return mappers[websiteId] || null;
}

module.exports = {
  mapBetterPlace,
  mapBaliHomeImmo,
  mapBaliVillaRealty,
  mapVillaBaliSale,
  chooseMapper
};

