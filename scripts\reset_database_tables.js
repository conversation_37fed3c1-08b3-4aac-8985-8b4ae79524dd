// Reset Database Tables - Clean property and scraping_queue tables, update website_configs
require('dotenv').config();
const { db, properties, scrapingQueue, discoveredUrls, websiteConfigs, crawlJobs, closeConnection } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function resetDatabaseTables() {
  console.log('🗑️  Resetting Database Tables for Fresh Start\n');
  
  try {
    // 1. Clear property table
    console.log('🏠 Clearing property table...');
    const deletedProperties = await db.delete(properties);
    console.log(`   ✅ Cleared property table`);
    
    // 2. Clear scraping_queue table
    console.log('📋 Clearing scraping_queue table...');
    const deletedQueue = await db.delete(scrapingQueue);
    console.log(`   ✅ Cleared scraping_queue table`);
    
    // 3. Clear discovered_urls table
    console.log('🔗 Clearing discovered_urls table...');
    const deletedUrls = await db.delete(discoveredUrls);
    console.log(`   ✅ Cleared discovered_urls table`);
    
    // 4. Clear crawl_jobs table
    console.log('🕷️  Clearing crawl_jobs table...');
    const deletedJobs = await db.delete(crawlJobs);
    console.log(`   ✅ Cleared crawl_jobs table`);
    
    // 5. Update website_configs with latest configurations
    console.log('⚙️  Updating website_configs table...');
    
    // Delete existing configs
    await db.delete(websiteConfigs);
    console.log('   🗑️  Removed old website configurations');
    
    // Insert updated website configurations
    const websiteConfigsData = [
      {
        website_id: 'betterplace',
        name: 'BetterPlace',
        base_url: 'https://betterplace.cc',
        sitemap_enabled: true,
        sitemap_urls: '["https://betterplace.cc/sitemap_index.xml"]',
        sitemap_filters: '{"include": ["real-estate", "buy", "rent"], "exclude": ["blog", "pages", "search"]}',
        property_url_patterns: {
          patterns: [
            '/buy/properties/BPVL\\d+$',  // Villa leasehold
            '/buy/properties/BPVF\\d+$',  // Villa freehold
            '/rent/properties/BPVR\\d+$'  // Rental properties
          ],
          keywords: ['bedroom', 'bathroom', 'villa', 'apartment', 'price', 'sqm']
        },
        crawl_frequency_hours: 24,
        sitemap_check_frequency_hours: 6,
        is_active: true
      },
      {
        website_id: 'bali_home_immo',
        name: 'Bali Home Immo',
        base_url: 'https://bali-home-immo.com',
        sitemap_enabled: true,
        sitemap_urls: '["https://bali-home-immo.com/sitemap.xml"]',
        sitemap_filters: '{"include": ["realestate-property"], "exclude": ["blog", "pages", "search", "category"]}',
        property_url_patterns: {
          patterns: ['/realestate-property/[^/]+/$'],
          keywords: ['bedroom', 'villa', 'apartment', 'sale', 'rent', 'bali']
        },
        crawl_frequency_hours: 24,
        sitemap_check_frequency_hours: 6,
        is_active: true
      },
      {
        website_id: 'bali_villa_realty',
        name: 'Bali Villa Realty',
        base_url: 'https://balivillarealty.com',
        sitemap_enabled: true,
        sitemap_urls: '["https://balivillarealty.com/property-sitemap.xml", "https://balivillarealty.com/property-sitemap2.xml"]',
        sitemap_filters: '{"include": ["property"], "exclude": ["blog", "pages", "search", "category"]}',
        property_url_patterns: {
          patterns: ['/property/[^/]+/$'],
          keywords: ['bedroom', 'villa', 'sale', 'rent', 'leasehold', 'freehold', 'bali']
        },
        crawl_frequency_hours: 24,
        sitemap_check_frequency_hours: 6,
        is_active: true
      },
      {
        website_id: 'villabalisale.com',
        name: 'Villa Bali Sale',
        base_url: 'https://www.villabalisale.com',
        sitemap_enabled: true,
        sitemap_urls: '["https://www.villabalisale.com/sitemap_realestate.xml"]',
        sitemap_filters: '{"include": ["realestate-property", "unique-villas"], "exclude": ["blog", "pages", "search"]}',
        property_url_patterns: {
          patterns: [
            '/realestate-property/for-sale/villa/all/[^<]+',
            '/realestate-property/for-sale/villa/freehold/[^<]+',
            '/realestate-property/for-sale/villa/leasehold/[^<]+',
            '/unique-villas'
          ],
          keywords: ['bedroom', 'villa', 'sale', 'leasehold', 'freehold', 'bali']
        },
        crawl_frequency_hours: 24,
        sitemap_check_frequency_hours: 6,
        is_active: true
      }
    ];
    
    // Insert new configurations
    for (const config of websiteConfigsData) {
      await db.insert(websiteConfigs).values(config);
      console.log(`   ✅ Added configuration for ${config.name}`);
    }
    
    // 6. Reset sequences and verify tables
    console.log('\n🔍 Verifying table states...');
    
    // Check property table
    const propertyCount = await db.execute(sql`SELECT COUNT(*) as count FROM property`);
    console.log(`   📊 Properties: ${propertyCount[0]?.count || 0} records`);

    // Check scraping_queue table
    const queueCount = await db.execute(sql`SELECT COUNT(*) as count FROM scraping_queue`);
    console.log(`   📋 Scraping Queue: ${queueCount[0]?.count || 0} records`);

    // Check discovered_urls table
    const urlsCount = await db.execute(sql`SELECT COUNT(*) as count FROM discovered_urls`);
    console.log(`   🔗 Discovered URLs: ${urlsCount[0]?.count || 0} records`);

    // Check website_configs table
    const configsCount = await db.execute(sql`SELECT COUNT(*) as count FROM website_configs`);
    console.log(`   ⚙️  Website Configs: ${configsCount[0]?.count || 0} records`);
    
    // Show website configurations
    console.log('\n📋 Updated Website Configurations:');
    const configs = await db.select().from(websiteConfigs);
    configs.forEach((config, i) => {
      console.log(`   ${i + 1}. ${config.name} (${config.website_id})`);
      console.log(`      🌐 Base URL: ${config.base_url}`);
      console.log(`      🗺️  Sitemap: ${config.sitemap_enabled ? 'Enabled' : 'Disabled'}`);
      console.log(`      ⏰ Crawl Frequency: ${config.crawl_frequency_hours}h`);
      console.log(`      ✅ Active: ${config.is_active}`);
    });
    
    console.log('\n🎉 Database reset completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Run sitemap crawling to populate discovered_urls');
    console.log('   2. Run queue processing to populate scraping_queue');
    console.log('   3. Run test scripts to scrape properties');
    console.log('\n💡 Suggested commands:');
    console.log('   node scripts/test_all_4_websites_ownership.js');
    
  } catch (error) {
    console.error('❌ Error resetting database tables:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the reset
resetDatabaseTables()
  .then(() => {
    console.log('✅ Database reset completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Database reset failed:', error);
    process.exit(1);
  })
  .finally(() => {
    closeConnection();
  });
