// Debug mapper output to see what's happening
require('dotenv').config();

async function debugMapperOutput() {
  console.log('🔍 Debugging Mapper Output');
  console.log('='.repeat(30));
  
  try {
    const { mapBetterPlace } = require('./scrape_worker/mappers');
    
    // Mock markdown data similar to what we get from scraping
    const mockData = {
      markdown: `
# Modern 3 Bedroom Villa with Rooftop in Tumbak Bayuh

**Price:** IDR 7,621,513,727

**Location:** Canggu, Bali

**Property Details:**
- 3 bedrooms
- 2 bathrooms  
- Building size: 180 sqm
- Land size: 250 sqm
- Year built: 2022
- Parking: 2 cars
- Ownership: Freehold

**Description:**
This stunning modern villa features contemporary design with high-quality finishes throughout. Located in the peaceful area of Tumbak Bayuh, just minutes from Canggu's famous beaches and vibrant dining scene.

**Amenities:**
- Swimming pool
- Garden
- Air conditioning
- Fully furnished
`,
      url: 'https://betterplace.cc/buy/properties/BPVL02270'
    };
    
    console.log('📡 Testing BetterPlace mapper with mock data...');
    const result = await mapBetterPlace(mockData);
    
    console.log('\n📊 Full Mapper Result:');
    console.log(JSON.stringify(result, null, 2));
    
    console.log('\n🔍 Key Fields Analysis:');
    console.log(`Title: ${result?.title || 'MISSING'}`);
    console.log(`Price: ${result?.price || 'MISSING'}`);
    console.log(`Bedrooms: ${result?.bedrooms || 'MISSING'}`);
    console.log(`Bathrooms: ${result?.bathrooms || 'MISSING'}`);
    console.log(`Address: ${result?.address || 'MISSING'}`);
    console.log(`City: ${result?.city || 'MISSING'}`);
    console.log(`Size (sqft): ${result?.size_sqft || 'MISSING'}`);
    console.log(`Lot Size (sqft): ${result?.lot_size_sqft || 'MISSING'}`);
    console.log(`Year Built: ${result?.year_built || 'MISSING'}`);
    console.log(`Parking: ${result?.parking_spaces || 'MISSING'}`);
    console.log(`Ownership: ${result?.ownership_type || 'MISSING'}`);
    console.log(`External ID: ${result?.media?.external_id || 'MISSING'}`);
    
    if (result?.description) {
      console.log(`Description: "${result.description.substring(0, 100)}..."`);
    } else {
      console.log(`Description: MISSING`);
    }
    
    console.log('\n✅ Mapper debug complete!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
}

debugMapperOutput().catch(console.error);
