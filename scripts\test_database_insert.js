// Test database insert with ownership fields
require('dotenv').config();
const { db } = require('../drizzle_client');
const { sql } = require('drizzle-orm');

async function testDatabaseInsert() {
  console.log('🔍 Testing database insert with ownership fields...\n');
  
  try {
    // Test 1: Simple insert with ownership fields
    console.log('1. Testing simple insert...');
    const result1 = await db.execute(sql`
      INSERT INTO property (
        title, category, type, status, address, city, country,
        price, bedrooms, bathrooms, parking_spaces, size_sqft, lot_size_sqft,
        ownership_type, lease_duration_years, lease_duration_text,
        amenities, media, source_id, external_id, source_url
      ) VALUES (
        'Test Villa', 'RESIDENTIAL', 'VILLA', 'AVAILABLE', 'Test Address', 'Test City', 'Indonesia',
        1000000, 2, 2, 1, 1000.0, 2000.0,
        'FREEHOLD', NULL, NULL,
        '{}', '{}', 'test_source', 'TEST123', 'https://test.com'
      )
      RETURNING id
    `);
    
    if (result1.length > 0) {
      console.log('✅ SUCCESS! Simple insert worked');
      console.log('   Inserted ID:', result1[0].id);
      
      // Clean up
      await db.execute(sql`DELETE FROM property WHERE external_id = 'TEST123'`);
      console.log('   ✅ Test record cleaned up');
    }
    
    // Test 2: Insert with LEASEHOLD and lease duration
    console.log('\n2. Testing LEASEHOLD insert...');
    const result2 = await db.execute(sql`
      INSERT INTO property (
        title, category, type, status, address, city, country,
        price, bedrooms, bathrooms, parking_spaces, size_sqft, lot_size_sqft,
        ownership_type, lease_duration_years, lease_duration_text,
        amenities, media, source_id, external_id, source_url
      ) VALUES (
        'Test Leasehold Villa', 'RESIDENTIAL', 'VILLA', 'AVAILABLE', 'Test Address', 'Test City', 'Indonesia',
        2000000, 3, 3, 1, 1500.0, 3000.0,
        'LEASEHOLD', 27, '27 years',
        '{}', '{}', 'test_source', 'TEST456', 'https://test2.com'
      )
      RETURNING id
    `);
    
    if (result2.length > 0) {
      console.log('✅ SUCCESS! LEASEHOLD insert worked');
      console.log('   Inserted ID:', result2[0].id);
      
      // Clean up
      await db.execute(sql`DELETE FROM property WHERE external_id = 'TEST456'`);
      console.log('   ✅ Test record cleaned up');
    }
    
    // Test 3: Insert with very long description (like Villa Bali Sale)
    console.log('\n3. Testing long description insert...');
    const longDescription = 'This rare freehold opportunity in Amed offers stunning ocean and mountain views, combining natural beauty with modern comfort. Set on 2.5 are of land with a 230 sqm building size, the villa is designed to maximize the surrounding scenery, creating a serene retreat in one of East Bali\'s most picturesque coastal towns. The villa will feature three spacious bedrooms, open-plan living areas, and elegant finishes, making it ideal as both a private residence or a prospective investment property in Amed\'s growing tourism market.';
    
    const result3 = await db.execute(sql`
      INSERT INTO property (
        title, category, type, status, address, city, country, description,
        price, bedrooms, bathrooms, parking_spaces, size_sqft, lot_size_sqft,
        ownership_type, lease_duration_years, lease_duration_text,
        amenities, media, source_id, external_id, source_url
      ) VALUES (
        'Test Long Description Villa', 'RESIDENTIAL', 'VILLA', 'AVAILABLE', 'Test Address', 'Test City', 'Indonesia', ${longDescription},
        3000000, 3, 3, 1, 2000.0, 4000.0,
        'FREEHOLD', NULL, NULL,
        '{}', '{}', 'test_source', 'TEST789', 'https://test3.com'
      )
      RETURNING id
    `);
    
    if (result3.length > 0) {
      console.log('✅ SUCCESS! Long description insert worked');
      console.log('   Inserted ID:', result3[0].id);
      
      // Clean up
      await db.execute(sql`DELETE FROM property WHERE external_id = 'TEST789'`);
      console.log('   ✅ Test record cleaned up');
    }
    
    // Test 4: Check if the issue is with JSON fields
    console.log('\n4. Testing complex JSON fields...');
    const complexAmenities = JSON.stringify({
      raw_amenities: ["Wifi", "Pool", "Kitchen", "Air Conditioner", "Electricity", "Dining Area", "Water Source", "Cable Tv", "Parking / Carport", "Bath Tub", "Internet", "Storage", "Level"]
    });
    
    const complexMedia = JSON.stringify({
      images: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
      image_count: 2,
      source_id: "test_source",
      external_id: "TEST999",
      source_url: "https://test4.com"
    });
    
    const result4 = await db.execute(sql`
      INSERT INTO property (
        title, category, type, status, address, city, country,
        price, bedrooms, bathrooms, parking_spaces, size_sqft, lot_size_sqft,
        ownership_type, lease_duration_years, lease_duration_text,
        amenities, media, source_id, external_id, source_url
      ) VALUES (
        'Test Complex JSON Villa', 'RESIDENTIAL', 'VILLA', 'AVAILABLE', 'Test Address', 'Test City', 'Indonesia',
        4000000, 4, 4, 2, 2500.0, 5000.0,
        'LEASEHOLD', 99, '99 years',
        ${complexAmenities}, ${complexMedia}, 'test_source', 'TEST999', 'https://test4.com'
      )
      RETURNING id
    `);
    
    if (result4.length > 0) {
      console.log('✅ SUCCESS! Complex JSON insert worked');
      console.log('   Inserted ID:', result4[0].id);
      
      // Clean up
      await db.execute(sql`DELETE FROM property WHERE external_id = 'TEST999'`);
      console.log('   ✅ Test record cleaned up');
    }
    
    console.log('\n🎉 All database insert tests passed!');
    console.log('💡 The ownership fields are working correctly in the database.');
    console.log('🔍 The issue must be elsewhere in the insert process.');
    
  } catch (error) {
    console.error('❌ Database insert error:', error.message);
    console.error('Error code:', error.code);
    console.error('Error detail:', error.detail);
    console.error('Error constraint:', error.constraint);
    
    if (error.message.includes('duplicate key')) {
      console.log('\n💡 This might be a unique constraint violation.');
      console.log('   The property might already exist in the database.');
    }
    
    if (error.message.includes('invalid input syntax')) {
      console.log('\n💡 This might be a data type issue.');
      console.log('   Check if the data types match the database schema.');
    }
    
  } finally {
    process.exit(0);
  }
}

testDatabaseInsert();
