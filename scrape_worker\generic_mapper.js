// Generic Mapper System
// Configurable data extraction based on website configurations

const { getWebsiteConfig } = require('./website_configs');
const { getCurrencyService } = require('./currency_service');

class GenericMapper {
  constructor(websiteIdOrConfig) {
    // Support both website ID string and config object
    if (typeof websiteIdOrConfig === 'string') {
      this.websiteId = websiteIdOrConfig;
      this.config = getWebsiteConfig(websiteIdOrConfig);

      if (!this.config) {
        throw new Error(`No configuration found for website: ${websiteIdOrConfig}`);
      }
    } else if (typeof websiteIdOrConfig === 'object' && websiteIdOrConfig.id) {
      // Direct config object
      this.websiteId = websiteIdOrConfig.id;
      this.config = websiteIdOrConfig;
    } else {
      throw new Error(`Invalid constructor argument: expected website ID string or config object`);
    }

    this.currencyService = getCurrencyService();
  }

  // Extract data using configured patterns
  async mapProperty(rawData) {
    const content = rawData?.markdown || '';
    const metadata = rawData?.metadata || {};
    const url = rawData?.url || '';

    try {
      // Extract basic information
      const title = this.extractTitle(content, metadata);
      const description = this.extractDescription(content, metadata);
      const location = this.extractLocation(content, url);
      
      // Extract property details
      const bedrooms = this.extractBedrooms(content);
      const bathrooms = this.extractBathrooms(content);
      const buildingSize = this.extractBuildingSize(content);
      const lotSize = this.extractLotSize(content);
      const yearBuilt = this.extractYearBuilt(content);
      
      // Extract pricing
      const pricingResult = await this.extractPricing(content, url);
      console.log(`   💰 Pricing result:`, pricingResult);
      
      // Extract ownership details
      const ownershipType = this.extractOwnershipType(content, url);
      const { leaseDurationYears, leaseDurationText } = this.extractLeaseDuration(content);
      
      // Extract media and amenities
      const images = this.extractImages(content);
      const amenities = this.extractAmenities(content);
      const externalId = this.extractExternalId(url);

      // Extract status from title and description
      const status = this.extractStatus(title, description);

      // Build normalized property object
      const property = {
        title,
        description,
        category: 'RESIDENTIAL',
        type: 'VILLA',
        status,

        // Location
        address: location.address,
        city: location.city,
        state: location.state,
        country: 'Indonesia',

        // Property details
        bedrooms,
        bathrooms,
        parking_spaces: null,
        size_sqft: buildingSize,
        lot_size_sqft: lotSize,
        year_built: yearBuilt,

        // Ownership
        ownership_type: ownershipType,
        lease_duration_years: leaseDurationYears,
        lease_duration_text: leaseDurationText,

        // Pricing
        price: pricingResult?.price || null,
        rent_price: pricingResult?.rentPrice || null,

        // Source information - FIXED: Add source_url
        source_id: this.websiteId,
        external_id: externalId,
        source_url: url, // Add the source URL

        // Media and amenities
        amenities: amenities.length > 0 ? { raw_amenities: amenities } : null,
        media: {
          source_id: this.websiteId,
          external_id: externalId,
          images,
          image_count: images.length
        }
      };

      // Validate the extracted property
      const validation = this.validateProperty(property);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      return property;

    } catch (error) {
      console.error(`❌ Generic mapping failed for ${this.websiteId}:`, error.message);
      throw error;
    }
  }

  // Pattern-based extraction methods
  extractTitle(content, metadata) {
    // Try metadata first
    if (metadata.title && metadata.title !== 'Villa Bali Sale Property') {
      return this.sanitizeText(metadata.title);
    }
    if (metadata.ogTitle) {
      return this.sanitizeText(metadata.ogTitle);
    }

    // Fallback to content patterns
    const titlePatterns = [
      /<h1[^>]*>(.*?)<\/h1>/i,
      /^#\s+(.+)$/m,
      /title[:\s]*([^\n]+)/i
    ];

    for (const pattern of titlePatterns) {
      const match = content.match(pattern);
      if (match && match[1] && match[1].trim().length > 5) {
        return this.sanitizeText(match[1].trim());
      }
    }

    return `${this.config.name} Property`;
  }

  extractBedrooms(content) {
    const patterns = this.config.extraction.bedroomPatterns || [
      /([1-9]|1[0-5])\s*(?:bed|bedroom)s?(?!\d)/i,
      /(?:bed|bedroom)s?[:\s]+([1-9]|1[0-5])(?!\d)/i
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        const bedrooms = parseInt(match[1]);
        if (bedrooms >= 1 && bedrooms <= 15) {
          return bedrooms;
        }
      }
    }
    return null;
  }

  extractBathrooms(content) {
    const patterns = this.config.extraction.bathroomPatterns || [
      /([1-9]|1[0-5])\s*(?:bath|bathroom)s?(?!\d)/i,
      /(?:bath|bathroom)s?[:\s]+([1-9]|1[0-5])(?!\d)/i
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        const bathrooms = parseInt(match[1]);
        if (bathrooms >= 1 && bathrooms <= 15) {
          return bathrooms;
        }
      }
    }
    return null;
  }

  async extractPricing(content, url) {
    const patterns = this.config.extraction.pricePatterns || [];
    const skipPhrases = this.config.extraction.skipPhrases || [];

    // Check for skip phrases first
    for (const phrase of skipPhrases) {
      if (content.toLowerCase().includes(phrase.toLowerCase())) {
        return { price: null, rentPrice: null };
      }
    }

    // Extract ALL price matches, not just the first one
    const allPrices = [];
    for (const pattern of patterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match && match[0]) {
          allPrices.push(match[0]);
        }
      }
    }

    if (allPrices.length === 0) {
      return { price: null, rentPrice: null };
    }

    // Try to convert all found prices
    const convertedPrices = [];
    for (const priceString of allPrices) {
      try {
        const convertedPrice = await this.currencyService.convertPriceToIDR(priceString);
        if (convertedPrice && convertedPrice > 0) {
          convertedPrices.push({ original: priceString, converted: convertedPrice });
        }
      } catch (error) {
        console.warn(`Price conversion failed for "${priceString}":`, error.message);
      }
    }

    if (convertedPrices.length === 0) {
      return { price: null, rentPrice: null };
    }

    // Determine property type from URL
    const isSale = this.isSaleProperty(url);
    const isRent = this.isRentProperty(url);

    // For Bali Coconut Living: try to detect both sale and rental prices
    if (convertedPrices.length > 1 && url.includes('balicoconutliving.com')) {
      // Sort prices by value (highest first)
      convertedPrices.sort((a, b) => b.converted - a.converted);

      // Assume highest price is sale, lower price is rental
      const salePrice = convertedPrices[0].converted;
      const rentPrice = convertedPrices[1].converted;

      // Return both if they make sense (sale > rent)
      if (salePrice > rentPrice * 10) { // Sale should be at least 10x rental
        return { price: salePrice, rentPrice: rentPrice };
      }
    }

    // Default behavior: use URL to determine type
    const bestPrice = convertedPrices[0].converted;
    console.log(`   💰 Best price found: ${bestPrice} IDR`);

    if (isSale) {
      console.log(`   ✅ Assigning as sale price: ${bestPrice}`);
      return { price: bestPrice, rentPrice: null };
    } else if (isRent) {
      console.log(`   ✅ Assigning as rent price: ${bestPrice}`);
      return { price: null, rentPrice: bestPrice };
    } else {
      console.log(`   ⚠️  URL type unclear, defaulting to sale price: ${bestPrice}`);
      return { price: bestPrice, rentPrice: null };
    }
  }

  // Helper methods
  isSaleProperty(url) {
    const salePatterns = this.config.urlPatterns?.sale || [];
    const isSale = salePatterns.some(pattern => url.includes(pattern));
    console.log(`   🏷️  Sale check for ${url}: ${isSale} (patterns: ${salePatterns.join(', ')})`);
    return isSale;
  }

  isRentProperty(url) {
    const rentPatterns = this.config.urlPatterns?.rent || [];
    const isRent = rentPatterns.some(pattern => url.includes(pattern));
    console.log(`   🏷️  Rent check for ${url}: ${isRent} (patterns: ${rentPatterns.join(', ')})`);
    return isRent;
  }

  sanitizeText(text) {
    if (!text) return '';
    return text.replace(/\s+/g, ' ').trim();
  }

  validateProperty(property) {
    const errors = [];
    const requiredFields = this.config.validation.requiredFields || [];

    // Skip SOLD properties if configured to do so
    if (this.config.validation?.skipSoldProperties && property.status === 'SOLD') {
      errors.push('Property is SOLD - skipping as configured');
      return {
        isValid: false,
        errors
      };
    }

    // Check required fields
    for (const field of requiredFields) {
      if (!property[field]) {
        errors.push(`Missing required field: ${field}`);
      }
    }

    // Validate price range
    if (property.price || property.rent_price) {
      const price = property.price || property.rent_price;
      const range = this.config.validation.priceRange;
      if (range && (price < range.min || price > range.max)) {
        errors.push(`Price ${price} outside valid range ${range.min}-${range.max}`);
      }
    } else if (!this.config.validation.skipOnMissingPrice) {
      errors.push('No valid price found');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Additional extraction methods
  extractDescription(content, metadata) {
    // Try metadata first
    if (metadata.description && metadata.description.length > 20) {
      return this.sanitizeText(metadata.description);
    }

    // Extract from content
    const descPatterns = [
      /description[:\s]*([^\n]{50,500})/i,
      /about[:\s]*([^\n]{50,500})/i
    ];

    for (const pattern of descPatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        return this.sanitizeText(match[1]);
      }
    }

    return '';
  }

  extractLocation(content, url) {
    // Extract from URL path
    const urlParts = url.split('/');
    let city = '';
    let address = '';

    // Look for location in URL structure
    for (let i = 0; i < urlParts.length; i++) {
      const part = urlParts[i];
      if (part && part.length > 2 && !part.includes('.') && !part.startsWith('V0')) {
        // Common Bali locations
        const baliLocations = [
          'canggu', 'seminyak', 'ubud', 'sanur', 'denpasar', 'kuta', 'legian',
          'jimbaran', 'nusa-dua', 'uluwatu', 'bukit', 'pererenan', 'berawa',
          'kerobokan', 'umalas', 'mengwi', 'tabanan', 'gianyar', 'karangasem',
          'padonan', 'babakan', 'tibubeneng', 'pecatu'
        ];

        if (baliLocations.includes(part.toLowerCase())) {
          city = part.charAt(0).toUpperCase() + part.slice(1).toLowerCase();
          break;
        }
      }
    }

    // Extract from content
    if (!city) {
      const locationPatterns = [
        /located in ([^,\n]+)/i,
        /situated in ([^,\n]+)/i,
        /area[:\s]*([^,\n]+)/i,
        /(Canggu|Seminyak|Ubud|Sanur|Denpasar|Kuta|Legian|Jimbaran|Uluwatu|Pererenan|Berawa|Kerobokan)/i
      ];

      for (const pattern of locationPatterns) {
        const match = content.match(pattern);
        if (match && match[1]) {
          city = this.sanitizeText(match[1]);
          break;
        }
      }
    }

    return {
      address: address || city,
      city: city || 'Bali',
      state: 'Bali'
    };
  }

  extractBuildingSize(content) {
    const patterns = this.config.extraction.buildingSizePatterns || [
      /(\d+)\s*m2.*?building/i,
      /building.*?(\d+)\s*m2/i,
      /(\d+)\s*sqm.*?building/i
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        const size = parseInt(match[1]);
        if (size > 0 && size < 10000) {
          return Math.round(size * 10.764); // Convert m2 to sqft
        }
      }
    }
    return null;
  }

  extractLotSize(content) {
    const patterns = this.config.extraction.landSizePatterns || [
      /(\d+)\s*m2/i,
      /(\d+)\s*sqm/i,
      /land.*?(\d+)\s*m2/i
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        const size = parseInt(match[1]);
        if (size > 0 && size < 50000) {
          return Math.round(size * 10.764); // Convert m2 to sqft
        }
      }
    }
    return null;
  }

  extractYearBuilt(content) {
    // FIXED: Better year extraction logic
    const patterns = [
      /built.*?(\d{4})/i,
      /constructed.*?(\d{4})/i,
      /year.*?(\d{4})/i,
      /new.*?(\d{4})/i,
      /brand new/i // Special case for "brand new"
    ];

    // Check for "brand new" first - indicates recent construction
    if (content.toLowerCase().includes('brand new')) {
      const currentYear = new Date().getFullYear();
      return currentYear; // Assume current year for brand new properties
    }

    const currentYear = new Date().getFullYear();
    const validYears = [];

    for (const pattern of patterns) {
      if (pattern.source === 'brand new') continue; // Skip the brand new pattern

      const matches = content.match(pattern);
      if (matches) {
        for (const match of matches) {
          const yearStr = match.replace(/\D/g, '');
          if (yearStr.length === 4) {
            const year = parseInt(yearStr);
            // More restrictive year range - avoid random 4-digit numbers
            if (year >= 1980 && year <= currentYear + 2) {
              validYears.push(year);
            }
          }
        }
      }
    }

    // Return the most recent valid year, or null if none found
    return validYears.length > 0 ? Math.max(...validYears) : null;
  }

  extractOwnershipType(content, url) {
    if (url.includes('freehold') || content.toLowerCase().includes('freehold')) {
      return 'FREEHOLD';
    }
    if (url.includes('leasehold') || content.toLowerCase().includes('leasehold')) {
      return 'LEASEHOLD';
    }
    return null;
  }

  extractLeaseDuration(content) {
    const patterns = [
      /(\d+)\s*years?\s*lease/i,
      /lease.*?(\d+)\s*years?/i,
      /until\s*(\d{4})/i
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        const years = parseInt(match[1]);
        if (years > 0 && years <= 99) {
          return {
            leaseDurationYears: years,
            leaseDurationText: `${years} years`
          };
        }
      }
    }
    return { leaseDurationYears: null, leaseDurationText: null };
  }

  extractImages(content) {
    // For now, return empty array - images would need special handling
    return [];
  }

  extractAmenities(content) {
    const amenityKeywords = [
      'pool', 'swimming pool', 'parking', 'garden', 'kitchen', 'balcony',
      'terrace', 'air conditioning', 'wifi', 'furnished', 'security'
    ];

    const foundAmenities = [];
    const lowerContent = content.toLowerCase();

    for (const amenity of amenityKeywords) {
      if (lowerContent.includes(amenity)) {
        foundAmenities.push(amenity);
      }
    }

    return foundAmenities;
  }

  extractExternalId(url) {
    // Extract ID from URL pattern like V005-5801
    const idMatch = url.match(/V\d{3}-\d{4}/);
    return idMatch ? idMatch[0] : null;
  }

  extractStatus(title, description) {
    const text = (title + ' ' + description).toLowerCase();

    // Check for sold indicators
    if (text.includes('(sold)') || text.includes('sold out') || text.includes('no longer available')) {
      return 'SOLD';
    }

    // Check for rented indicators
    if (text.includes('(rented)') || text.includes('rented out') || text.includes('no longer for rent')) {
      return 'RENTED';
    }

    // Check for pending indicators
    if (text.includes('(pending)') || text.includes('under offer') || text.includes('reserved')) {
      return 'PENDING';
    }

    // Default to available
    return 'AVAILABLE';
  }
}

module.exports = { GenericMapper };
