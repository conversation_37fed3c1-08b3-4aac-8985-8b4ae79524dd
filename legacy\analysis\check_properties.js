// Check Properties Status
require('dotenv').config();
const { db, properties } = require('./drizzle_client');

async function checkProperties() {
  console.log('🏠 Properties Status Check - ' + new Date().toLocaleTimeString());
  
  const totalProps = await db.select().from(properties);
  console.log(`📊 Total properties: ${totalProps.length}`);
  
  const today = new Date('2025-08-14');
  const todayProps = totalProps.filter(prop => prop.created_at >= today);
  console.log(`📅 Properties added today: ${todayProps.length}`);
  
  console.log('\n🏠 Latest 5 properties:');
  totalProps.slice(-5).forEach((prop, index) => {
    console.log(`${index + 1}. ${prop.title.substring(0, 60)}...`);
    console.log(`   Source: ${prop.source_id}, Created: ${prop.created_at.toISOString().split('T')[0]}`);
    console.log(`   Category: ${prop.category}/${prop.type}`);
    console.log('');
  });
}

checkProperties()
  .then(() => {
    console.log('✅ Properties check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Error:', error);
    process.exit(1);
  });
