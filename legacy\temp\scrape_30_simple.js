// Simple script to scrape 30 URLs from Bali Home Immo
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');

async function scrape30Simple() {
  console.log('🚀 Scraping 30 URLs from Bali Home Immo...\n');

  const qm = new QueueManager();

  try {
    // Process 30 URLs in 3 batches of 10
    let totalProcessed = 0;
    let totalSuccessful = 0;
    
    for (let batch = 1; batch <= 3; batch++) {
      console.log(`\n📦 Batch ${batch}/3:`);
      
      // Process 10 URLs
      await qm.processQueue('bali_home_immo', 10);
      
      console.log(`✅ Batch ${batch} completed`);
      
      // Wait between batches
      if (batch < 3) {
        console.log('⏳ Waiting 60 seconds before next batch...');
        await new Promise(resolve => setTimeout(resolve, 60000));
      }
    }

    // Check final results
    console.log('\n📊 Final Summary:');
    const { db, properties } = require('./drizzle_client');
    const { eq } = require('drizzle-orm');
    
    const baliHomeProps = await db.select().from(properties)
      .where(eq(properties.source_id, 'bali_home_immo'));
    
    console.log(`   Bali Home Immo properties: ${baliHomeProps.length}`);
    
    // Show latest 5 properties
    console.log('\n🏠 Latest properties:');
    baliHomeProps.slice(-5).forEach((prop, index) => {
      console.log(`   ${index + 1}. ${prop.title}`);
      console.log(`      Category: ${prop.category}/${prop.type}`);
      console.log(`      Price: ${prop.price ? (prop.price/1000000).toFixed(1) + 'M' : 'N/A'} IDR`);
      console.log(`      Rent: ${prop.rent_price ? (prop.rent_price/1000000).toFixed(1) + 'M' : 'N/A'} IDR`);
    });

  } catch (error) {
    console.error('❌ Error during scraping:', error.message);
    throw error;
  }
}

// Run the scraping
scrape30Simple()
  .then(() => {
    console.log('\n🎉 30 URL scraping completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Scraping failed:', error);
    process.exit(1);
  });
