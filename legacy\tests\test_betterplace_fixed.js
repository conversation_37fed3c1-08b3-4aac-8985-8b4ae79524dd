// Test BetterPlace with markdown fallback
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { testConnection } = require('./drizzle_client');

async function testBetterPlaceFixed() {
  console.log('🧪 Testing BetterPlace with markdown fallback...\n');
  
  // Test database connection first
  console.log('1. Testing database connection...');
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ Database connection failed. Exiting.');
    return;
  }
  
  // Test BetterPlace URL
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02232';
  
  console.log(`\n2. Testing BetterPlace with markdown fallback...`);
  console.log(`   URL: ${testUrl}`);
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('betterplace', [testUrl], {});
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n3. ✅ Test completed in ${duration} seconds!`);
    console.log('\n📊 Results:');
    
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}`);
    console.log(`   ❌ Failed: ${failed.length}`);
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully scraped BetterPlace property:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title}`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ Failed properties:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. Error: ${result.error}`);
      });
    }
    
    // Check what was saved to database
    console.log('\n4. Checking database for new BetterPlace data...');
    
  } catch (error) {
    console.error(`❌ Test failed: ${error.message}`);
  }
  
  console.log('\n🎯 BetterPlace markdown fallback test completed!');
}

// Run the test
if (require.main === module) {
  testBetterPlaceFixed().catch(console.error);
}

module.exports = { testBetterPlaceFixed };
