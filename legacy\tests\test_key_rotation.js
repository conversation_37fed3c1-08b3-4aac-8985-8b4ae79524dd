// Test key rotation system with dummy keys
require('dotenv').config();

// Add dummy keys for testing
process.env.FIRECRAWL_API_KEY_2 = 'fc-dummy-key-2-should-fail';
process.env.FIRECRAWL_API_KEY_3 = 'fc-dummy-key-3-should-fail';

const { getKeyManager } = require('./scrape_worker/key_manager');
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testKeyRotation() {
  console.log('🔑 Testing key rotation system...\n');
  
  // Initialize key manager with dummy keys
  const keyManager = getKeyManager();
  
  console.log('1. Initial key manager state:');
  keyManager.printStats();
  
  console.log('\n2. Testing with a URL that should trigger key rotation...');
  
  // Test with a single URL - the dummy keys should fail and rotate to working key
  const testUrl = 'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/';
  
  try {
    console.log(`\n🔄 Attempting to scrape: ${testUrl}`);
    const results = await runExtractBatch('bali_villa_realty', [testUrl], {});
    
    console.log('\n3. Results:');
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}`);
    console.log(`   ❌ Failed: ${failed.length}`);
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully scraped properties:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title}`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ Failed properties:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. Error: ${result.error}`);
      });
    }
    
  } catch (error) {
    console.error(`❌ Test failed: ${error.message}`);
  }
  
  console.log('\n4. Final key manager state:');
  keyManager.printStats();
  
  console.log('\n📊 Key Rotation Test Analysis:');
  const stats = keyManager.getKeyStats();
  
  console.log(`   Total Keys: ${stats.totalKeys}`);
  console.log(`   Active Keys: ${stats.activeKeys}`);
  console.log(`   Current Key: ${stats.currentKey}`);
  
  // Check if rotation worked
  const workingKeys = stats.keys.filter(k => k.successRate !== 'N/A' && parseFloat(k.successRate) > 0);
  const failedKeys = stats.keys.filter(k => k.consecutiveErrors > 0);
  
  console.log(`   Working Keys: ${workingKeys.length}`);
  console.log(`   Failed Keys: ${failedKeys.length}`);
  
  if (workingKeys.length > 0 && failedKeys.length > 0) {
    console.log('\n✅ KEY ROTATION SYSTEM WORKING:');
    console.log('   - Dummy keys failed as expected');
    console.log('   - System rotated to working key');
    console.log('   - Scraping succeeded with working key');
  } else if (workingKeys.length > 0) {
    console.log('\n⚠️  KEY ROTATION PARTIALLY WORKING:');
    console.log('   - Scraping succeeded but no key failures detected');
  } else {
    console.log('\n❌ KEY ROTATION NOT WORKING:');
    console.log('   - No working keys found');
  }
  
  console.log('\n🎯 Key Rotation Test Completed!');
}

// Run the test
if (require.main === module) {
  testKeyRotation().catch(console.error);
}

module.exports = { testKeyRotation };
