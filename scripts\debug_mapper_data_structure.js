// Debug what data structure the mappers actually receive
require('dotenv').config();
const { runExtractBatch } = require('../scrape_worker/run_batch');

async function debugMapperDataStructure() {
  console.log('🔍 Debugging Mapper Data Structure\n');
  
  // Test one URL from each website to see data structure
  const testUrls = [
    {
      website: 'betterplace',
      url: 'https://betterplace.cc/buy/properties/BPVL02339',
      name: 'BetterPlace'
    },
    {
      website: 'bali_home_immo', 
      url: 'https://bali-home-immo.com/realestate-property/for-sale/villa/freehold/pandawa/luxurious-cliff-front-ocean-view-7-bedrooms-villa-for-sale-freehold-in-bali-near-pandawa-beach-rf6138a',
      name: 'Bali Home Immo'
    },
    {
      website: 'bali_villa_realty',
      url: 'https://balivillarealty.com/property/1-bedroom-modern-townhouse-in-batu-bolong/',
      name: 'Bali Villa Realty'
    }
  ];
  
  // Override mappers to log data structure
  const originalMappers = {};
  
  try {
    const mappers = require('../scrape_worker/mappers');
    
    // Store original mappers
    originalMappers.mapBetterPlace = mappers.mapBetterPlace;
    originalMappers.mapBaliHomeImmo = mappers.mapBaliHomeImmo;
    originalMappers.mapBaliVillaRealty = mappers.mapBaliVillaRealty;
    
    // Override with debug versions
    mappers.mapBetterPlace = async function(raw) {
      console.log('\n🔍 BetterPlace Raw Data Structure:');
      console.log('Type:', typeof raw);
      console.log('Keys:', raw ? Object.keys(raw) : 'null/undefined');
      console.log('Sample data:', JSON.stringify(raw, null, 2).substring(0, 1000));
      
      // Try original mapper
      try {
        return await originalMappers.mapBetterPlace(raw);
      } catch (error) {
        console.log('❌ Original mapper error:', error.message);
        throw error;
      }
    };
    
    mappers.mapBaliHomeImmo = function(raw) {
      console.log('\n🔍 Bali Home Immo Raw Data Structure:');
      console.log('Type:', typeof raw);
      console.log('Keys:', raw ? Object.keys(raw) : 'null/undefined');
      console.log('Sample data:', JSON.stringify(raw, null, 2).substring(0, 1000));
      
      // Try original mapper
      try {
        return originalMappers.mapBaliHomeImmo(raw);
      } catch (error) {
        console.log('❌ Original mapper error:', error.message);
        throw error;
      }
    };
    
    mappers.mapBaliVillaRealty = async function(raw) {
      console.log('\n🔍 Bali Villa Realty Raw Data Structure:');
      console.log('Type:', typeof raw);
      console.log('Keys:', raw ? Object.keys(raw) : 'null/undefined');
      console.log('Sample data:', JSON.stringify(raw, null, 2).substring(0, 1000));
      
      // Try original mapper
      try {
        return await originalMappers.mapBaliVillaRealty(raw);
      } catch (error) {
        console.log('❌ Original mapper error:', error.message);
        throw error;
      }
    };
    
    // Test each website
    for (const test of testUrls) {
      console.log(`\n${'='.repeat(60)}`);
      console.log(`🌐 Testing ${test.name} (${test.website})`);
      console.log(`🔗 URL: ${test.url}`);
      console.log(`${'='.repeat(60)}`);
      
      try {
        const results = await runExtractBatch(test.website, [test.url], {});
        
        if (results.length > 0) {
          const result = results[0];
          console.log(`\n📊 Result for ${test.name}:`);
          console.log(`Success: ${result.success}`);
          if (result.success) {
            console.log('✅ Mapping successful');
          } else {
            console.log(`❌ Error: ${result.error}`);
          }
        }
        
      } catch (error) {
        console.error(`❌ Test failed for ${test.name}:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
}

debugMapperDataStructure();
