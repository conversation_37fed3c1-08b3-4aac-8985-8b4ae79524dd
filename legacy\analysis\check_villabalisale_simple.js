// Simple check of Villa Bali Sale properties
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq, desc } = require('drizzle-orm');

async function checkVillaBaliSaleSimple() {
  try {
    console.log('🏠 Checking Villa Bali Sale Properties (Simple)');
    console.log('='.repeat(50));
    
    const villaBaliSaleProps = await db
      .select()
      .from(properties)
      .where(eq(properties.source_id, 'villa_bali_sale'))
      .orderBy(desc(properties.created_at))
      .limit(3);
    
    console.log(`Found ${villaBaliSaleProps.length} Villa Bali Sale properties:`);
    
    villaBaliSaleProps.forEach((prop, i) => {
      console.log(`\n${i + 1}. [${prop.external_id}] ${prop.title}`);
      console.log(`   💰 Price: ${prop.price ? `IDR ${prop.price.toLocaleString()}` : 'No price'}`);
      console.log(`   🏠 ${prop.bedrooms || 'No'} bed | 🚿 ${prop.bathrooms || 'No'} bath`);
      console.log(`   📍 ${prop.city || 'No city'}, ${prop.state || 'No state'}`);
      console.log(`   🏛️  ${prop.ownership_type || 'No ownership type'}`);
      console.log(`   📐 ${prop.size_sqft ? Math.round(prop.size_sqft) + ' sqft' : 'No size'}`);
      console.log(`   🌿 ${prop.lot_size_sqft ? Math.round(prop.lot_size_sqft) + ' sqft lot' : 'No lot size'}`);
      
      // Amenities check
      if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
        console.log(`   ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
      } else {
        console.log(`   ❌ No amenities`);
      }
      
      console.log(`   📝 Description: ${prop.description ? prop.description.length + ' chars' : 'No description'}`);
      console.log(`   📸 Images: ${prop.media?.images?.length || 0}`);
      console.log(`   ⏰ ${new Date(prop.created_at).toLocaleString()}`);
    });
    
    console.log('\n✅ Villa Bali Sale check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    process.exit(0);
  }
}

checkVillaBaliSaleSimple();
