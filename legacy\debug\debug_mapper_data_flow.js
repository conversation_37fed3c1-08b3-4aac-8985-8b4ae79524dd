// Debug data flow through mappers to find where data is lost
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc, eq } = require('drizzle-orm');

async function debugMapperDataFlow() {
  console.log('🔍 Debugging Mapper Data Flow - Finding Missing Data');
  console.log('='.repeat(60));
  
  try {
    // Get recent properties with NULL values
    console.log('🔄 Step 1: Analyzing recent properties with NULL values...');
    
    const recentProperties = await db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    console.log(`📋 Found ${recentProperties.length} recent properties:`);
    
    recentProperties.forEach((prop, index) => {
      console.log(`\n${index + 1}. ${prop.title || 'UNTITLED'}`);
      console.log(`   ID: ${prop.id}`);
      console.log(`   Source: ${prop.media?.source_id || 'UNKNOWN'}`);
      console.log(`   External ID: ${prop.external_id || 'NULL'}`);
      console.log(`   Price: ${prop.price || 'NULL'}`);
      console.log(`   Bedrooms: ${prop.bedrooms || 'NULL'}`);
      console.log(`   Bathrooms: ${prop.bathrooms || 'NULL'}`);
      console.log(`   Parking: ${prop.parking_spaces || 'NULL'}`);
      console.log(`   Size (sqft): ${prop.size_sqft || 'NULL'}`);
      console.log(`   Lot Size (sqft): ${prop.lot_size_sqft || 'NULL'}`);
      console.log(`   Year Built: ${prop.year_built || 'NULL'}`);
      console.log(`   Address: ${prop.address || 'NULL'}`);
      console.log(`   City: ${prop.city || 'NULL'}`);
      console.log(`   State: ${prop.state || 'NULL'}`);
      console.log(`   Created: ${prop.created_at}`);
      
      // Check if amenities data exists
      if (prop.amenities) {
        console.log(`   Amenities: ${JSON.stringify(prop.amenities).substring(0, 100)}...`);
      } else {
        console.log(`   Amenities: NULL`);
      }
    });
    
    // Step 2: Test each mapper with mock data
    console.log('\n🔄 Step 2: Testing mappers with mock markdown data...');
    
    // Test BetterPlace mapper
    console.log('\n📡 Testing BetterPlace mapper...');
    await testBetterPlaceMapper();
    
    // Test Bali Villa Realty mapper
    console.log('\n📡 Testing Bali Villa Realty mapper...');
    await testBaliVillaRealtyMapper();
    
    // Test Bali Home Immo mapper
    console.log('\n📡 Testing Bali Home Immo mapper...');
    await testBaliHomeImmoMapper();
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  }
}

async function testBetterPlaceMapper() {
  const { mapBetterPlace } = require('./scrape_worker/mappers');
  
  const mockMarkdown = `
# Modern 3 Bedroom Villa with Rooftop in Tumbak Bayuh

**Price:** USD 450,000

**Location:** Tumbak Bayuh, Canggu, Bali

**Property Details:**
- 3 bedrooms
- 2 bathrooms  
- Building size: 180 sqm
- Land size: 250 sqm
- Year built: 2022
- Parking: 2 cars
- Ownership: Freehold

**Description:**
This stunning modern villa features contemporary design with high-quality finishes throughout.

**Amenities:**
- Swimming pool
- Garden
- Air conditioning
- Fully furnished
`;

  try {
    const result = await mapBetterPlace({
      markdown: mockMarkdown,
      url: 'https://betterplace.cc/buy/properties/BPVL02270'
    });
    
    console.log('   ✅ BetterPlace mapper result:');
    console.log(`      Title: ${result?.title || 'NULL'}`);
    console.log(`      Price: ${result?.price || 'NULL'}`);
    console.log(`      Bedrooms: ${result?.bedrooms || 'NULL'}`);
    console.log(`      Bathrooms: ${result?.bathrooms || 'NULL'}`);
    console.log(`      Address: ${result?.address || 'NULL'}`);
    console.log(`      City: ${result?.city || 'NULL'}`);
    console.log(`      Size (sqft): ${result?.size_sqft || 'NULL'}`);
    console.log(`      Lot Size (sqft): ${result?.lot_size_sqft || 'NULL'}`);
    console.log(`      Year Built: ${result?.year_built || 'NULL'}`);
    console.log(`      Parking: ${result?.parking_spaces || 'NULL'}`);
    console.log(`      External ID: ${result?.media?.external_id || result?.external_id || 'NULL'}`);
    
  } catch (error) {
    console.log(`   ❌ BetterPlace mapper failed: ${error.message}`);
  }
}

async function testBaliVillaRealtyMapper() {
  const { mapBaliVillaRealty } = require('./scrape_worker/mappers');
  
  const mockMarkdown = `
# Brand New 2 Bedroom Villa For Sale in Tumbak Bayuh

**Price:** USD 169,000

**Location:** Tumbak Bayuh, Canggu, Bali

**Property Details:**
- 2 bedrooms
- 2 bathrooms  
- Building size: 120 sqm
- Land size: 150 sqm
- Year built: 2023
- Parking: 1 car
- Ownership: Leasehold

**Description:**
Charming villa in peaceful location near Canggu beaches.
`;

  try {
    const result = await mapBaliVillaRealty({
      markdown: mockMarkdown,
      url: 'https://balivillarealty.com/property/test-villa/'
    });
    
    console.log('   ✅ Bali Villa Realty mapper result:');
    console.log(`      Title: ${result?.title || 'NULL'}`);
    console.log(`      Price: ${result?.price || 'NULL'}`);
    console.log(`      Bedrooms: ${result?.bedrooms || 'NULL'}`);
    console.log(`      Bathrooms: ${result?.bathrooms || 'NULL'}`);
    console.log(`      Address: ${result?.address || 'NULL'}`);
    console.log(`      City: ${result?.city || 'NULL'}`);
    console.log(`      Size (sqft): ${result?.size_sqft || 'NULL'}`);
    console.log(`      Lot Size (sqft): ${result?.lot_size_sqft || 'NULL'}`);
    console.log(`      Year Built: ${result?.year_built || 'NULL'}`);
    console.log(`      Parking: ${result?.parking_spaces || 'NULL'}`);
    console.log(`      External ID: ${result?.media?.external_id || result?.external_id || 'NULL'}`);
    
  } catch (error) {
    console.log(`   ❌ Bali Villa Realty mapper failed: ${error.message}`);
  }
}

async function testBaliHomeImmoMapper() {
  const { mapBaliHomeImmo } = require('./scrape_worker/mappers');
  
  const mockMarkdown = `
# 2 Bedroom Townhouse For Rent in Berawa

**Price:** IDR 25,000,000 / month

**Location:** Berawa, Canggu, Bali

**Property Details:**
- 2 bedrooms
- 2 bathrooms  
- Building size: 100 sqm
- Land size: 80 sqm
- Year built: 2021
- Parking: 1 car
- Ownership: Leasehold

**Description:**
Modern townhouse perfect for monthly rental.
`;

  try {
    const result = await mapBaliHomeImmo({
      markdown: mockMarkdown,
      url: 'https://bali-home-immo.com/property/test-townhouse/'
    });
    
    console.log('   ✅ Bali Home Immo mapper result:');
    console.log(`      Title: ${result?.title || 'NULL'}`);
    console.log(`      Price: ${result?.price || 'NULL'}`);
    console.log(`      Bedrooms: ${result?.bedrooms || 'NULL'}`);
    console.log(`      Bathrooms: ${result?.bathrooms || 'NULL'}`);
    console.log(`      Address: ${result?.address || 'NULL'}`);
    console.log(`      City: ${result?.city || 'NULL'}`);
    console.log(`      Size (sqft): ${result?.size_sqft || 'NULL'}`);
    console.log(`      Lot Size (sqft): ${result?.lot_size_sqft || 'NULL'}`);
    console.log(`      Year Built: ${result?.year_built || 'NULL'}`);
    console.log(`      Parking: ${result?.parking_spaces || 'NULL'}`);
    console.log(`      External ID: ${result?.media?.external_id || result?.external_id || 'NULL'}`);
    
  } catch (error) {
    console.log(`   ❌ Bali Home Immo mapper failed: ${error.message}`);
  }
}

// Run the debug
debugMapperDataFlow().catch(console.error);
