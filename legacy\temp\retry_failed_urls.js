// Retry Failed URLs - Smart retry mechanism for failed scraping attempts
require('dotenv').config();
const { db, scrapingQueue } = require('./drizzle_client');
const { eq, and, lt } = require('drizzle-orm');
const { EfficientBatchScraper } = require('./efficient_batch_scraper');

class FailedUrlRetryManager {
  constructor() {
    this.maxRetries = 3;
    this.retryDelayHours = 24; // Wait 24 hours before retry
  }

  async analyzeFailures() {
    console.log('🔍 Analyzing Failed URLs');
    console.log('='.repeat(50));

    try {
      // Get all failed URLs
      const failures = await db
        .select()
        .from(scrapingQueue)
        .where(eq(scrapingQueue.status, 'failed'));

      console.log(`📊 Total failed URLs: ${failures.length}`);

      // Group by error type
      const errorGroups = {};
      failures.forEach(failure => {
        const error = failure.error_message || 'Unknown error';
        if (!errorGroups[error]) {
          errorGroups[error] = [];
        }
        errorGroups[error].push(failure);
      });

      console.log('\n📋 Failure Analysis by Error Type:');
      console.log('-'.repeat(40));

      Object.entries(errorGroups).forEach(([error, urls]) => {
        console.log(`\n❌ ${error} (${urls.length} URLs)`);
        
        // Group by website
        const websiteGroups = {};
        urls.forEach(url => {
          if (!websiteGroups[url.website_id]) {
            websiteGroups[url.website_id] = 0;
          }
          websiteGroups[url.website_id]++;
        });

        Object.entries(websiteGroups).forEach(([website, count]) => {
          console.log(`   📍 ${website}: ${count} URLs`);
        });
      });

      return { failures, errorGroups };

    } catch (error) {
      console.error('❌ Error analyzing failures:', error.message);
      throw error;
    }
  }

  async getRetryableUrls() {
    console.log('\n🔄 Finding URLs Ready for Retry');
    console.log('-'.repeat(40));

    try {
      const retryableUrls = await db
        .select()
        .from(scrapingQueue)
        .where(
          and(
            eq(scrapingQueue.status, 'failed'),
            lt(scrapingQueue.attempts, this.maxRetries)
          )
        );

      console.log(`📋 Found ${retryableUrls.length} URLs ready for retry`);

      // Filter by specific error types that are worth retrying
      const worthRetrying = retryableUrls.filter(url => {
        const error = url.error_message || '';
        
        // Skip these error types (not worth retrying)
        const skipErrors = [
          'chooseMapper is not a function', // Fixed in new version
          'Unknown scraping error', // WhatsApp links, etc.
          'No data extracted' // Likely bad URLs
        ];

        return !skipErrors.some(skipError => error.includes(skipError));
      });

      console.log(`✅ ${worthRetrying.length} URLs worth retrying`);
      console.log(`❌ ${retryableUrls.length - worthRetrying.length} URLs skipped (not worth retrying)`);

      return worthRetrying;

    } catch (error) {
      console.error('❌ Error finding retryable URLs:', error.message);
      throw error;
    }
  }

  async retryFailedUrls(batchSize = 10) {
    console.log('\n🚀 Starting Failed URL Retry Process');
    console.log('='.repeat(50));

    try {
      const retryableUrls = await this.getRetryableUrls();

      if (retryableUrls.length === 0) {
        console.log('✅ No URLs need retrying at this time');
        return { retried: 0, successful: 0, failed: 0 };
      }

      // Reset status to pending for retry
      const urlsToRetry = retryableUrls.slice(0, batchSize);
      console.log(`\n🔄 Retrying ${urlsToRetry.length} URLs...`);

      for (const url of urlsToRetry) {
        await db
          .update(scrapingQueue)
          .set({ 
            status: 'pending',
            error_message: null,
            processed_at: null
          })
          .where(eq(scrapingQueue.id, url.id));
      }

      console.log('✅ URLs reset to pending status');

      // Use efficient batch scraper for retry
      const scraper = new EfficientBatchScraper();
      const results = await scraper.processQueueEfficiently(batchSize, 1);

      console.log('\n📊 Retry Results:');
      console.log(`   Retried: ${urlsToRetry.length}`);
      console.log(`   Successful: ${results.totalSuccessful}`);
      console.log(`   Failed: ${results.totalFailed}`);
      console.log(`   Success rate: ${results.totalProcessed > 0 ? ((results.totalSuccessful / results.totalProcessed) * 100).toFixed(1) : 0}%`);

      return {
        retried: urlsToRetry.length,
        successful: results.totalSuccessful,
        failed: results.totalFailed
      };

    } catch (error) {
      console.error('❌ Error retrying failed URLs:', error.message);
      throw error;
    }
  }

  async cleanupOldFailures() {
    console.log('\n🧹 Cleaning Up Old Failures');
    console.log('-'.repeat(40));

    try {
      // Find URLs that have failed max times and are old
      const oldFailures = await db
        .select()
        .from(scrapingQueue)
        .where(
          and(
            eq(scrapingQueue.status, 'failed'),
            eq(scrapingQueue.attempts, this.maxRetries)
          )
        );

      console.log(`📋 Found ${oldFailures.length} URLs that have reached max retry attempts`);

      // Group by error type for reporting
      const errorGroups = {};
      oldFailures.forEach(failure => {
        const error = failure.error_message || 'Unknown error';
        if (!errorGroups[error]) {
          errorGroups[error] = 0;
        }
        errorGroups[error]++;
      });

      console.log('\n📊 Permanent Failures by Error Type:');
      Object.entries(errorGroups).forEach(([error, count]) => {
        console.log(`   ❌ ${error}: ${count} URLs`);
      });

      // Option to archive these instead of deleting
      console.log('\n💡 Consider archiving these URLs for manual review');

      return oldFailures.length;

    } catch (error) {
      console.error('❌ Error cleaning up failures:', error.message);
      throw error;
    }
  }
}

// Test the retry manager
async function testRetryManager() {
  const retryManager = new FailedUrlRetryManager();
  
  try {
    // Analyze current failures
    await retryManager.analyzeFailures();
    
    // Find retryable URLs
    await retryManager.getRetryableUrls();
    
    // Clean up old failures
    await retryManager.cleanupOldFailures();
    
    console.log('\n🎉 Retry analysis completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    process.exit(0);
  }
}

if (require.main === module) {
  testRetryManager();
}

module.exports = { FailedUrlRetryManager };
