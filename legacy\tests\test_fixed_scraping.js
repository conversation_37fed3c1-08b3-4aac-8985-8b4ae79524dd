// Test the fixed scraping system with real URLs
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testFixedScraping() {
  console.log('🧪 Test Fixed Scraping System');
  console.log('='.repeat(40));
  
  const testCases = [
    {
      website: 'bali_villa_realty',
      url: 'https://balivillarealty.com/property/cozy-1-bedroom-villa-in-prime-petitenget-location-exceptional-investment-opportunity/',
      expectedBedrooms: 1,
      expectedBathrooms: 2
    },
    {
      website: 'betterplace', 
      url: 'https://betterplace.cc/buy/properties/BPVL02209',
      expectedBedrooms: 2,
      expectedBathrooms: 2
    }
  ];
  
  let totalSuccess = 0;
  let totalTests = testCases.length;
  
  for (const testCase of testCases) {
    console.log(`\n🏠 Testing ${testCase.website}`);
    console.log(`📍 URL: ${testCase.url}`);
    console.log(`🎯 Expected: ${testCase.expectedBedrooms} bed / ${testCase.expectedBathrooms} bath`);
    console.log('─'.repeat(60));
    
    try {
      const results = await runExtractBatch(testCase.website, [testCase.url], {});
      
      if (results && results.extractedData && results.extractedData.length > 0) {
        const data = results.extractedData[0];
        
        console.log('📊 RESULTS:');
        console.log(`   Title: "${data.title}"`);
        console.log(`   Bedrooms: ${data.bedrooms} (expected: ${testCase.expectedBedrooms})`);
        console.log(`   Bathrooms: ${data.bathrooms} (expected: ${testCase.expectedBathrooms})`);
        console.log(`   Size: ${data.size_sqft} sqft`);
        console.log(`   Price: ${data.price || data.rent_price}`);
        console.log(`   Year: ${data.year_built}`);
        console.log(`   Ownership: ${data.ownership_type}`);
        
        // Validation
        const bedroomMatch = data.bedrooms === testCase.expectedBedrooms;
        const bathroomMatch = data.bathrooms === testCase.expectedBathrooms;
        
        console.log('\n🎯 VALIDATION:');
        console.log(`   Bedrooms: ${bedroomMatch ? '✅' : '❌'} ${data.bedrooms} vs ${testCase.expectedBedrooms}`);
        console.log(`   Bathrooms: ${bathroomMatch ? '✅' : '❌'} ${data.bathrooms} vs ${testCase.expectedBathrooms}`);
        
        if (bedroomMatch && bathroomMatch) {
          console.log('   🎉 SUCCESS: Field extraction working!');
          totalSuccess++;
        } else {
          console.log('   ❌ FAILED: Field extraction not working');
        }
        
        // Check database insert
        if (results.processedResults && results.processedResults.length > 0) {
          const dbResult = results.processedResults[0];
          if (dbResult.ok) {
            console.log(`   💾 Database: ✅ Saved with ID ${dbResult.id}`);
          } else {
            console.log(`   💾 Database: ❌ Failed to save`);
          }
        }
        
      } else {
        console.log('❌ No data extracted');
      }
      
    } catch (error) {
      console.log(`❌ Test failed: ${error.message}`);
    }
  }
  
  console.log('\n📊 FINAL RESULTS');
  console.log('='.repeat(40));
  console.log(`✅ Successful tests: ${totalSuccess}/${totalTests}`);
  console.log(`📈 Success rate: ${((totalSuccess / totalTests) * 100).toFixed(1)}%`);
  
  if (totalSuccess === totalTests) {
    console.log('🎉 ALL TESTS PASSED! System is working correctly!');
    console.log('✅ Bedroom/bathroom extraction fixed');
    console.log('✅ Database storage working');
    console.log('✅ Ready for production scraping');
  } else {
    console.log('⚠️  Some tests failed. Need further investigation.');
  }
}

// Run test
if (require.main === module) {
  testFixedScraping().catch(console.error);
}

module.exports = { testFixedScraping };
