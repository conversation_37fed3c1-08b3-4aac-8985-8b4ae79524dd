// Debug Bali Villa Realty extraction issues
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugBaliVillaRealty() {
  console.log('🔍 Debug Bali Villa Realty Extraction');
  console.log('='.repeat(50));
  
  // Test the specific URL that had issues
  const testUrl = 'https://balivillarealty.com/property/stunning-1-bedroom-villa-for-sale-leasehold-in-prime-petitenget/';
  
  console.log(`📍 URL: ${testUrl}`);
  console.log('🔍 Debugging lot size and lease duration extraction...\n');
  
  try {
    const results = await runExtractBatch('bali_villa_realty', [testUrl], {});
    
    if (results && results.extractedData && results.extractedData.length > 0) {
      const data = results.extractedData[0];
      
      console.log('📊 EXTRACTED DATA:');
      console.log(`   Title: "${data.title}"`);
      console.log(`   Bedrooms: ${data.bedrooms}`);
      console.log(`   Bathrooms: ${data.bathrooms}`);
      console.log(`   Size: ${data.size_sqft} sqft`);
      console.log(`   Lot Size: ${data.lot_size_sqft} sqft`);
      console.log(`   Ownership: ${data.ownership_type}`);
      console.log(`   Lease Text: ${data.lease_duration_text}`);
      console.log(`   Lease Years: ${data.lease_duration_years}`);
      console.log(`   Year Built: ${data.year_built}`);
      console.log(`   Parking: ${data.parking_spaces}`);
      console.log('');
      
      // Analysis
      console.log('🔍 ISSUE ANALYSIS:');
      
      // Lot Size Analysis
      if (data.lot_size_sqft) {
        console.log(`   ✅ Lot Size: Found ${data.lot_size_sqft} sqft`);
      } else {
        console.log('   ❌ Lot Size: NOT FOUND');
        console.log('      Possible reasons:');
        console.log('      - Property content doesn\'t contain lot size info');
        console.log('      - Regex patterns don\'t match the format used');
        console.log('      - Size info is in a different section');
      }
      
      // Lease Duration Analysis
      if (data.lease_duration_text && data.lease_duration_years) {
        console.log(`   ✅ Lease Duration: Found ${data.lease_duration_text} (${data.lease_duration_years} years)`);
      } else {
        console.log('   ❌ Lease Duration: NOT FOUND');
        console.log('      Possible reasons:');
        console.log('      - Title says "Leasehold" but no duration specified');
        console.log('      - Duration info is in a different format');
        console.log('      - Content doesn\'t contain lease term details');
      }
      
      // Parking Analysis
      if (data.parking_spaces) {
        console.log(`   ✅ Parking: Found ${data.parking_spaces} spaces`);
      } else {
        console.log('   ❌ Parking: NOT FOUND');
        console.log('      Possible reasons:');
        console.log('      - Property doesn\'t have parking');
        console.log('      - Parking info is in amenities list');
        console.log('      - Different terminology used');
      }
      
      // Recommendations
      console.log('\n🔧 RECOMMENDATIONS:');
      
      if (!data.lot_size_sqft) {
        console.log('   📐 Lot Size:');
        console.log('      - Check if property has land size information');
        console.log('      - Look for "plot size", "land area", "site area" terms');
        console.log('      - Check if size is mentioned in different units (m2, are)');
      }
      
      if (!data.lease_duration_text) {
        console.log('   📋 Lease Duration:');
        console.log('      - Check property details section');
        console.log('      - Look for "lease term", "tenure", "remaining years"');
        console.log('      - Check if it\'s a standard lease (25, 30 years)');
      }
      
      if (!data.parking_spaces) {
        console.log('   🚗 Parking:');
        console.log('      - Check amenities list for parking mentions');
        console.log('      - Look for "garage", "carport", "covered parking"');
        console.log('      - Some properties may not have parking');
      }
      
    } else {
      console.log('❌ No data extracted - check extraction process');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

// Run debug
if (require.main === module) {
  debugBaliVillaRealty().catch(console.error);
}

module.exports = { debugBaliVillaRealty };
