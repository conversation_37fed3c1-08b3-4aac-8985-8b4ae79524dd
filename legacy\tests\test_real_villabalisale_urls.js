// Test Villa Bali Sale fixes with real URLs from database
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { db, properties, scrapingQueue } = require('./drizzle_client');
const { desc, sql, eq, and } = require('drizzle-orm');

async function testRealVillaBaliSaleUrls() {
  console.log('🧪 Testing Villa Bali Sale Fixes with Real URLs');
  console.log('='.repeat(60));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  try {
    // Get initial count
    const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`📊 Initial properties in database: ${initialCount[0].count}`);
    
    // Get 3 real URLs from villabalisale.com queue
    const queueUrls = await db
      .select({ 
        url: scrapingQueue.url, 
        id: scrapingQueue.id,
        attempts: scrapingQueue.attempts
      })
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, 'villabalisale.com'),
        eq(scrapingQueue.status, 'pending')
      ))
      .limit(3);
    
    if (queueUrls.length === 0) {
      console.log('❌ No villabalisale.com URLs found in queue');
      process.exit(1);
    }
    
    console.log(`📋 Found ${queueUrls.length} real villabalisale.com URLs to test:`);
    queueUrls.forEach((row, i) => {
      console.log(`   ${i + 1}. ${row.url}`);
      console.log(`      Queue ID: ${row.id}, Attempts: ${row.attempts}`);
    });
    
    // Extract URLs for processing
    const urls = queueUrls.map(row => row.url);
    
    console.log(`\n🔄 Processing ${urls.length} real villabalisale.com URLs...`);
    const startTime = Date.now();
    
    // Use the existing batch processing function with villabalisale.com
    const results = await runExtractBatch('villabalisale.com', urls, {
      concurrency: 1, // Process one at a time for better debugging
      timeout: 120000  // Longer timeout for Villa Bali Sale
    });
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    
    if (results && results.length > 0) {
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      
      console.log(`\n✅ Completed in ${duration}s:`);
      console.log(`   Processed: ${results.length}`);
      console.log(`   Successful: ${successful}`);
      console.log(`   Failed: ${failed}`);
      
      // Show successful results with detailed fix verification
      const successfulResults = results.filter(r => r.success);
      if (successfulResults.length > 0) {
        console.log(`\n🏠 Successfully processed properties:`);
        successfulResults.forEach((result, i) => {
          console.log(`\n   ${i + 1}. ✅ ${result.title || 'Property'}`);
          if (result.price) {
            console.log(`      💰 IDR ${result.price.toLocaleString()}`);
          }
          console.log(`      🏠 ${result.bedrooms} bed | 🚿 ${result.bathrooms} bath`);
          console.log(`      📍 ${result.city}, ${result.state || 'NO STATE'}`);
          console.log(`      🏛️  ${result.ownership_type}`);
          
          // Check state fix
          if (result.state && result.state.length > 0) {
            console.log(`      ✅ State fix: "${result.state}" (WORKING)`);
          } else {
            console.log(`      ❌ State fix: Still empty (FAILED)`);
          }
          
          // Check description fix
          if (result.description && result.description.length > 0) {
            console.log(`      ✅ Description fix: ${result.description.length} chars (WORKING)`);
            console.log(`      📝 Preview: ${result.description.substring(0, 80)}...`);
          } else {
            console.log(`      ❌ Description fix: Still empty (FAILED)`);
          }
          
          // Check amenities
          if (result.amenities && result.amenities.raw_amenities && result.amenities.raw_amenities.length > 0) {
            console.log(`      🎯 Amenities (${result.amenities.raw_amenities.length}): ${result.amenities.raw_amenities.slice(0, 3).join(', ')}${result.amenities.raw_amenities.length > 3 ? '...' : ''}`);
          } else {
            console.log(`      ⚠️  No amenities extracted`);
          }
          
          console.log(`      🆔 External ID: ${result.media?.external_id || 'No ID'}`);
        });
      }
      
      // Show failed results
      const failedResults = results.filter(r => !r.success);
      if (failedResults.length > 0) {
        console.log(`\n❌ Failed properties:`);
        failedResults.forEach((result, i) => {
          console.log(`   ${i + 1}. ❌ ${result.error || 'Unknown error'}`);
          if (result.url) {
            console.log(`      URL: ${result.url}`);
          }
        });
      }
      
    } else {
      console.log(`⚠️  No results returned (${duration}s)`);
    }
    
    // Check final count and show new properties in database
    const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
    const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
    console.log(`\n🆕 New properties added to database: ${newProperties}`);
    
    if (newProperties > 0) {
      console.log('\n🏠 Latest Villa Bali Sale Properties in Database:');
      const latestProps = await db
        .select()
        .from(properties)
        .where(eq(properties.source_id, 'villa_bali_sale'))
        .orderBy(desc(properties.created_at))
        .limit(newProperties);
      
      latestProps.forEach((prop, i) => {
        console.log(`\n   ${i + 1}. [${prop.external_id}] ${prop.title}`);
        if (prop.price) {
          console.log(`      💰 IDR ${prop.price.toLocaleString()}`);
        }
        console.log(`      🏠 ${prop.bedrooms} bed | 🚿 ${prop.bathrooms} bath`);
        console.log(`      📍 ${prop.city}, ${prop.state || 'NO STATE'}`);
        console.log(`      🏛️  ${prop.ownership_type}`);
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
        
        // Verify fixes in database
        if (prop.state && prop.state.length > 0) {
          console.log(`      ✅ State in DB: "${prop.state}" (SAVED CORRECTLY)`);
        } else {
          console.log(`      ❌ State in DB: Empty (NOT SAVED)`);
        }
        
        if (prop.description && prop.description.length > 0) {
          console.log(`      ✅ Description in DB: ${prop.description.length} chars (SAVED CORRECTLY)`);
          console.log(`      📝 DB Preview: ${prop.description.substring(0, 80)}...`);
        } else {
          console.log(`      ❌ Description in DB: Empty (NOT SAVED)`);
        }
        
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`      ✅ Amenities in DB (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        } else {
          console.log(`      ❌ No amenities in database`);
        }
      });
      
      // Summary of fixes
      console.log('\n📊 Fix Verification Summary:');
      const propsWithState = latestProps.filter(p => p.state && p.state.length > 0);
      const propsWithDescription = latestProps.filter(p => p.description && p.description.length > 0);
      
      console.log(`   State field: ${propsWithState.length}/${latestProps.length} properties (${((propsWithState.length / latestProps.length) * 100).toFixed(1)}%)`);
      console.log(`   Description field: ${propsWithDescription.length}/${latestProps.length} properties (${((propsWithDescription.length / latestProps.length) * 100).toFixed(1)}%)`);
      
      if (propsWithState.length === latestProps.length && propsWithDescription.length === latestProps.length) {
        console.log('\n🎉 ALL FIXES SUCCESSFUL! Both state and description fields are now populated.');
      } else if (propsWithState.length === latestProps.length) {
        console.log('\n✅ State fix successful! Description still needs work.');
      } else if (propsWithDescription.length === latestProps.length) {
        console.log('\n✅ Description fix successful! State still needs work.');
      } else {
        console.log('\n⚠️  Fixes need more work. Some fields still not populated.');
      }
    } else {
      console.log('\n⚠️  No new properties were saved to database. Check validation errors.');
    }
    
    console.log('\n✅ Real Villa Bali Sale URL test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testRealVillaBaliSaleUrls();
