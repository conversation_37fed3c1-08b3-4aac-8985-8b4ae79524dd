// Analyze property table structure and content
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc, sql } = require('drizzle-orm');

async function analyzePropertyTable() {
  console.log('🔍 Analyzing Property Table Structure and Content');
  console.log('='.repeat(60));
  
  try {
    // Get table schema information
    console.log('📋 Table Schema Analysis:');
    
    // Get recent properties to analyze structure
    const recentProperties = await db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(5);
    
    if (recentProperties.length > 0) {
      const sampleProperty = recentProperties[0];
      
      console.log('\n📊 Property Table Fields:');
      Object.keys(sampleProperty).forEach((field, index) => {
        const value = sampleProperty[field];
        const type = typeof value;
        const isNull = value === null;
        const hasValue = value !== null && value !== undefined && value !== '';
        
        console.log(`${index + 1}. ${field}:`);
        console.log(`   Type: ${type}`);
        console.log(`   Has Value: ${hasValue ? '✅' : '❌'}`);
        if (hasValue && type === 'string' && value.length > 50) {
          console.log(`   Sample: "${value.substring(0, 50)}..."`);
        } else if (hasValue) {
          console.log(`   Value: ${JSON.stringify(value)}`);
        }
        console.log('');
      });
    }
    
    // Analyze data quality across recent properties
    console.log('\n📈 Data Quality Analysis (Last 10 Properties):');
    
    const analysisProperties = await db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(10);
    
    const fieldStats = {};
    
    analysisProperties.forEach(prop => {
      Object.keys(prop).forEach(field => {
        if (!fieldStats[field]) {
          fieldStats[field] = {
            total: 0,
            filled: 0,
            empty: 0,
            nullCount: 0
          };
        }
        
        fieldStats[field].total++;
        
        const value = prop[field];
        if (value === null || value === undefined) {
          fieldStats[field].nullCount++;
          fieldStats[field].empty++;
        } else if (value === '' || (Array.isArray(value) && value.length === 0)) {
          fieldStats[field].empty++;
        } else {
          fieldStats[field].filled++;
        }
      });
    });
    
    // Sort fields by fill rate
    const sortedFields = Object.entries(fieldStats)
      .map(([field, stats]) => ({
        field,
        fillRate: (stats.filled / stats.total * 100).toFixed(1),
        filled: stats.filled,
        total: stats.total
      }))
      .sort((a, b) => parseFloat(b.fillRate) - parseFloat(a.fillRate));
    
    console.log('\nField Fill Rates (sorted by completeness):');
    sortedFields.forEach(({ field, fillRate, filled, total }) => {
      const status = parseFloat(fillRate) >= 80 ? '✅' : parseFloat(fillRate) >= 50 ? '⚠️' : '❌';
      console.log(`${status} ${field}: ${fillRate}% (${filled}/${total})`);
    });
    
    // Analyze by source
    console.log('\n🌐 Data Quality by Source:');
    
    const sourceStats = await db
      .select({
        source: sql`media->>'source_id'`.as('source'),
        count: sql`count(*)`.as('count'),
        avg_bedrooms: sql`avg(bedrooms)`.as('avg_bedrooms'),
        avg_bathrooms: sql`avg(bathrooms)`.as('avg_bathrooms'),
        avg_size: sql`avg(size_sqft)`.as('avg_size'),
        avg_lot_size: sql`avg(lot_size_sqft)`.as('avg_lot_size'),
        price_count: sql`count(price)`.as('price_count'),
        rent_count: sql`count(rent_price)`.as('rent_count')
      })
      .from(properties)
      .groupBy(sql`media->>'source_id'`)
      .orderBy(desc(sql`count(*)`));
    
    sourceStats.forEach(stat => {
      console.log(`\n📊 ${stat.source}:`);
      console.log(`   Total Properties: ${stat.count}`);
      console.log(`   Avg Bedrooms: ${stat.avg_bedrooms ? parseFloat(stat.avg_bedrooms).toFixed(1) : 'N/A'}`);
      console.log(`   Avg Bathrooms: ${stat.avg_bathrooms ? parseFloat(stat.avg_bathrooms).toFixed(1) : 'N/A'}`);
      console.log(`   Avg Building Size: ${stat.avg_size ? parseFloat(stat.avg_size).toFixed(0) : 'N/A'} sqft`);
      console.log(`   Avg Lot Size: ${stat.avg_lot_size ? parseFloat(stat.avg_lot_size).toFixed(0) : 'N/A'} sqft`);
      console.log(`   Properties with Sale Price: ${stat.price_count}`);
      console.log(`   Properties with Rent Price: ${stat.rent_count}`);
    });
    
    // Check for data issues
    console.log('\n⚠️ Potential Data Issues:');
    
    // Check for extreme values
    const extremeValues = await db
      .select({
        id: properties.id,
        title: properties.title,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        size_sqft: properties.size_sqft,
        lot_size_sqft: properties.lot_size_sqft,
        price: properties.price,
        rent_price: properties.rent_price,
        media: properties.media
      })
      .from(properties)
      .where(sql`
        bedrooms > 20 OR 
        bathrooms > 20 OR 
        size_sqft > 50000 OR 
        lot_size_sqft > 500000 OR
        price > 50000000000 OR
        rent_price > 1000000000
      `)
      .limit(5);
    
    if (extremeValues.length > 0) {
      console.log('\nProperties with extreme values:');
      extremeValues.forEach(prop => {
        console.log(`- ${prop.title} (${prop.media?.source_id})`);
        if (prop.bedrooms > 20) console.log(`  ⚠️ Bedrooms: ${prop.bedrooms}`);
        if (prop.bathrooms > 20) console.log(`  ⚠️ Bathrooms: ${prop.bathrooms}`);
        if (prop.size_sqft > 50000) console.log(`  ⚠️ Size: ${prop.size_sqft} sqft`);
        if (prop.lot_size_sqft > 500000) console.log(`  ⚠️ Lot: ${prop.lot_size_sqft} sqft`);
        if (prop.price > 50000000000) console.log(`  ⚠️ Price: ${prop.price} IDR`);
        if (prop.rent_price > 1000000000) console.log(`  ⚠️ Rent: ${prop.rent_price} IDR`);
      });
    } else {
      console.log('✅ No extreme values found');
    }
    
    // Check for missing critical fields
    const missingCritical = await db
      .select({
        id: properties.id,
        title: properties.title,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        media: properties.media
      })
      .from(properties)
      .where(sql`bedrooms IS NULL OR bathrooms IS NULL`)
      .limit(5);
    
    if (missingCritical.length > 0) {
      console.log('\nProperties missing critical fields:');
      missingCritical.forEach(prop => {
        console.log(`- ${prop.title} (${prop.media?.source_id})`);
        if (!prop.bedrooms) console.log(`  ❌ Missing bedrooms`);
        if (!prop.bathrooms) console.log(`  ❌ Missing bathrooms`);
      });
    } else {
      console.log('✅ All recent properties have bedrooms and bathrooms');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

analyzePropertyTable().then(() => process.exit(0));
