// Test BetterPlace markdown parsing with mock data
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { mapBetterPlace } = require('./scrape_worker/mappers');

async function testBetterPlaceMock() {
  console.log('🧪 Testing BetterPlace Markdown Parsing with Mock Data');
  console.log('='.repeat(60));
  
  // Mock markdown content based on BetterPlace structure
  const mockMarkdown = `
# Modern 3 Bedroom Villa with Rooftop in Tumbak Bayuh

**Price:** USD 450,000

**Location:** Tumbak Bayuh, Canggu, Bali

**Property Details:**
- 3 bedrooms
- 3 bathrooms  
- Building size: 180 sqm
- Land size: 250 sqm
- Year built: 2022
- Parking: 2 cars
- Ownership: Freehold

**Description:**
This stunning modern villa features contemporary design with high-quality finishes throughout. Located in the peaceful area of Tumbak Bayuh, just minutes from Canggu's famous beaches and vibrant dining scene.

**Amenities:**
- Swimming pool
- Garden
- Air conditioning
- Fully furnished
- Kitchen
- WiFi
- Security system
- Balcony

![Villa Image](https://betterplace.cc/image1.jpg)
![Pool View](https://betterplace.cc/image2.jpg)
![Bedroom](https://betterplace.cc/image3.jpg)
`;

  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02270';
  
  try {
    console.log('🔄 Step 1: Testing markdown parsing');
    
    // Test our markdown parser
    const mappedData = await mapBetterPlace({
      markdown: mockMarkdown,
      url: testUrl
    });
    
    if (!mappedData) {
      console.log('❌ Mapping failed - no data returned');
      return;
    }
    
    console.log('✅ Mapping successful');
    console.log('📊 Mapped data:');
    console.log(JSON.stringify(mappedData, null, 2));
    
    console.log('\n🔄 Step 2: Testing database storage');
    
    // Test database insertion
    const testProperty = {
      ...mappedData,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    console.log('📋 Property to insert:');
    console.log(`   Title: ${testProperty.title}`);
    console.log(`   Price: ${testProperty.price}`);
    console.log(`   Address: ${testProperty.address}`);
    console.log(`   City: ${testProperty.city}`);
    console.log(`   Bedrooms: ${testProperty.bedrooms}`);
    console.log(`   Bathrooms: ${testProperty.bathrooms}`);
    console.log(`   Size: ${testProperty.size_sqft} sqft`);
    console.log(`   External ID: ${testProperty.media?.external_id}`);
    
    // Check if property already exists
    const existingProperty = await db
      .select()
      .from(properties)
      .where(properties.external_id.eq(testProperty.media?.external_id || 'TEST_BPVL02270'))
      .limit(1);
    
    if (existingProperty.length > 0) {
      console.log('⚠️  Test property already exists, skipping insertion');
      console.log(`📋 Existing property ID: ${existingProperty[0].id}`);
    } else {
      console.log('➕ Inserting test property...');
      
      const insertResult = await db
        .insert(properties)
        .values(testProperty)
        .returning();
      
      console.log('✅ Property inserted successfully');
      console.log(`📋 New property ID: ${insertResult[0].id}`);
      
      // Verify the insertion
      const verifyProperty = await db
        .select()
        .from(properties)
        .where(properties.id.eq(insertResult[0].id))
        .limit(1);
      
      if (verifyProperty.length > 0) {
        const prop = verifyProperty[0];
        console.log('\n✅ Database verification successful');
        console.log('📋 Stored property details:');
        console.log(`   ID: ${prop.id}`);
        console.log(`   Title: ${prop.title}`);
        console.log(`   Price: ${prop.price}`);
        console.log(`   Location: ${prop.address}, ${prop.city}`);
        console.log(`   Bedrooms: ${prop.bedrooms}`);
        console.log(`   Bathrooms: ${prop.bathrooms}`);
        console.log(`   Images: ${prop.media?.image_count || 0} images`);
      }
    }
    
    console.log('\n🎉 Test completed successfully!');
    console.log('✅ BetterPlace markdown parsing works correctly');
    console.log('✅ Database storage works correctly');
    console.log('💰 Ready for cost-effective markdown-only scraping');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testBetterPlaceMock().catch(console.error);
