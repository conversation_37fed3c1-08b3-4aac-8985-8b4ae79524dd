// Debug Villa Bali Sale size extraction
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function debugVillaBaliSaleSize() {
  console.log('🔍 Debug Villa Bali Sale Size Extraction');
  console.log('='.repeat(50));
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/lovina/property-for-sale-in-singaraja---lovina';
  
  try {
    console.log(`📡 Scraping: ${testUrl}`);
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.markdown) {
      const content = result.data.markdown;
      console.log(`📝 Markdown length: ${content.length} chars`);
      
      // Test land size patterns manually
      console.log('\n🏞️ Testing Land Size Patterns:');
      
      const landSizePatterns = [
        /\*\*Land:\*\*\s*(\d+)\s*Are/i,   // "**Land:** 27 Are"
        /land.*?(\d+)\s*are/i,           // "land 27 are"
        /(\d+)\s*are.*?land/i,           // "27 are land"
        /land.*?(\d+)\s*m2/i,            // "land 2700 m2"
        /(\d+)\s*m2.*?land/i             // "2700 m2 land"
      ];
      
      landSizePatterns.forEach((pattern, i) => {
        const landMatch = content.match(pattern);
        if (landMatch) {
          const value = parseInt(landMatch[1]);
          console.log(`\nPattern ${i+1} (${pattern.source}) MATCHED:`);
          console.log(`   Raw match: "${landMatch[0]}"`);
          console.log(`   Extracted value: ${value}`);
          
          if (pattern.source.includes('are')) {
            const lot_size_sqft = value * 100 * 10.764;
            console.log(`   Calculation: ${value} Are × 100 × 10.764 = ${lot_size_sqft} sqft`);
            console.log(`   Expected: 27 × 100 × 10.764 = ${27 * 100 * 10.764} sqft`);
          } else {
            const lot_size_sqft = value * 10.764;
            console.log(`   Calculation: ${value} m2 × 10.764 = ${lot_size_sqft} sqft`);
          }
        } else {
          console.log(`\nPattern ${i+1} (${pattern.source}): NO MATCH`);
        }
      });
      
      // Test building size patterns
      console.log('\n🏠 Testing Building Size Patterns:');
      
      const buildingSizePatterns = [
        /\*\*Building:\*\*\s*(\d+)m2/i,  // "**Building:** 400m2"
        /building.*?(\d+)\s*m2/i,        // "building 400 m2"
        /(\d+)\s*m2.*?building/i         // "400 m2 building"
      ];
      
      buildingSizePatterns.forEach((pattern, i) => {
        const sizeMatch = content.match(pattern);
        if (sizeMatch) {
          const value = parseInt(sizeMatch[1]);
          const size_sqft = value * 10.764;
          console.log(`\nPattern ${i+1} (${pattern.source}) MATCHED:`);
          console.log(`   Raw match: "${sizeMatch[0]}"`);
          console.log(`   Extracted value: ${value} m2`);
          console.log(`   Calculation: ${value} × 10.764 = ${size_sqft} sqft`);
        } else {
          console.log(`\nPattern ${i+1} (${pattern.source}): NO MATCH`);
        }
      });
      
      // Show relevant parts of markdown
      console.log('\n📄 Relevant Markdown Sections:');
      
      const landMatch = content.match(/\*\*Land:\*\*[^*]+/i);
      if (landMatch) {
        console.log(`Land section: "${landMatch[0]}"`);
      }
      
      const buildingMatch = content.match(/\*\*Building:\*\*[^*]+/i);
      if (buildingMatch) {
        console.log(`Building section: "${buildingMatch[0]}"`);
      }
      
    } else {
      console.log('❌ Failed to get markdown');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugVillaBaliSaleSize().then(() => process.exit(0));
