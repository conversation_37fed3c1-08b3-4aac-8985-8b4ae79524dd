// Check Villa Bali Sale properties field population
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq, desc } = require('drizzle-orm');

async function checkVillaBaliSaleFields() {
  try {
    console.log('🏠 Checking Villa Bali Sale Properties Field Population');
    console.log('='.repeat(60));
    
    const villaBaliSaleProps = await db
      .select({
        id: properties.id,
        title: properties.title,
        external_id: properties.external_id,
        source_url: properties.source_url,
        price: properties.price,
        rent_price: properties.rent_price,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        city: properties.city,
        state: properties.state,
        ownership_type: properties.ownership_type,
        size_sqft: properties.size_sqft,
        lot_size_sqft: properties.lot_size_sqft,
        year_built: properties.year_built,
        amenities: properties.amenities,
        description: properties.description,
        media: properties.media,
        created_at: properties.created_at
      })
      .from(properties)
      .where(eq(properties.source_id, 'villa_bali_sale'))
      .orderBy(desc(properties.created_at))
      .limit(3);
    
    console.log(`Found ${villaBaliSaleProps.length} Villa Bali Sale properties:`);
    
    villaBaliSaleProps.forEach((prop, i) => {
      console.log(`\n${i + 1}. [${prop.external_id}] ${prop.title}`);
      console.log(`   ID: ${prop.id}`);
      console.log(`   Source URL: ${prop.source_url}`);
      
      // Basic info
      console.log('\n   📊 Basic Information:');
      console.log(`      💰 Price: ${prop.price ? `IDR ${prop.price.toLocaleString()}` : 'No price'}`);
      console.log(`      💰 Rent Price: ${prop.rent_price ? `IDR ${prop.rent_price.toLocaleString()}/month` : 'No rent price'}`);
      console.log(`      🏠 Bedrooms: ${prop.bedrooms || 'No bedrooms'}`);
      console.log(`      🚿 Bathrooms: ${prop.bathrooms || 'No bathrooms'}`);
      console.log(`      📍 City: ${prop.city || 'No city'}`);
      console.log(`      🏛️  State: ${prop.state || 'No state'}`);
      console.log(`      🏛️  Ownership: ${prop.ownership_type || 'No ownership type'}`);
      console.log(`      🏗️  Year Built: ${prop.year_built || 'No year built'}`);
      
      // Size info
      console.log('\n   📐 Size Information:');
      console.log(`      🏠 Building Size: ${prop.size_sqft ? `${Math.round(prop.size_sqft)} sqft` : 'No building size'}`);
      console.log(`      🌿 Lot Size: ${prop.lot_size_sqft ? `${Math.round(prop.lot_size_sqft)} sqft` : 'No lot size'}`);
      
      // Amenities
      console.log('\n   🎯 Amenities:');
      if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
        console.log(`      ✅ Count: ${prop.amenities.raw_amenities.length}`);
        console.log(`      📝 List: ${prop.amenities.raw_amenities.slice(0, 5).join(', ')}${prop.amenities.raw_amenities.length > 5 ? '...' : ''}`);
      } else {
        console.log(`      ❌ No amenities found`);
      }
      
      // Description
      console.log('\n   📝 Description:');
      if (prop.description) {
        console.log(`      ✅ Length: ${prop.description.length} characters`);
        console.log(`      📄 Preview: ${prop.description.substring(0, 100)}...`);
      } else {
        console.log(`      ❌ No description`);
      }
      
      // Media
      console.log('\n   📸 Media:');
      if (prop.media) {
        console.log(`      🖼️  Images: ${prop.media.images?.length || 0}`);
        console.log(`      🆔 External ID: ${prop.media.external_id || 'No external ID'}`);
        console.log(`      🔗 Source URL: ${prop.media.source_url ? 'Yes' : 'No'}`);
      } else {
        console.log(`      ❌ No media information`);
      }
      
      console.log(`\n   ⏰ Created: ${new Date(prop.created_at).toLocaleString()}`);
      console.log('   ' + '-'.repeat(50));
    });
    
    // Field population summary
    console.log('\n📊 Field Population Summary:');
    const fieldStats = {
      price: villaBaliSaleProps.filter(p => p.price).length,
      rent_price: villaBaliSaleProps.filter(p => p.rent_price).length,
      bedrooms: villaBaliSaleProps.filter(p => p.bedrooms).length,
      bathrooms: villaBaliSaleProps.filter(p => p.bathrooms).length,
      city: villaBaliSaleProps.filter(p => p.city).length,
      ownership_type: villaBaliSaleProps.filter(p => p.ownership_type).length,
      size_sqft: villaBaliSaleProps.filter(p => p.size_sqft).length,
      lot_size_sqft: villaBaliSaleProps.filter(p => p.lot_size_sqft).length,
      amenities: villaBaliSaleProps.filter(p => p.amenities?.raw_amenities?.length > 0).length,
      description: villaBaliSaleProps.filter(p => p.description).length,
      media: villaBaliSaleProps.filter(p => p.media?.images?.length > 0).length
    };
    
    Object.entries(fieldStats).forEach(([field, count]) => {
      const percentage = ((count / villaBaliSaleProps.length) * 100).toFixed(1);
      console.log(`   ${field}: ${count}/${villaBaliSaleProps.length} (${percentage}%)`);
    });
    
    console.log('\n✅ Villa Bali Sale field analysis completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

checkVillaBaliSaleFields();
