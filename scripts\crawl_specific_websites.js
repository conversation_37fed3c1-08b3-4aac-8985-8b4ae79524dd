// Crawl specific websites with improved URL patterns
require('dotenv').config();
const { SmartCrawler } = require('../scrape_worker/smart_crawler');
const { FirecrawlKeyManager } = require('../scrape_worker/firecrawl_key_manager');
const { SitemapParser } = require('../scrape_worker/sitemap_parser');
const { db, websiteConfigs, closeConnection } = require('../drizzle_client');
const { inArray } = require('drizzle-orm');

async function crawlSpecificWebsites() {
  console.log('🕷️  Crawling Specific Websites with Improved Patterns\n');
  
  const websitesToCrawl = ['betterplace', 'bali_home_immo', 'villabalisale.com'];
  
  try {
    // Initialize components
    const keyManager = new FirecrawlKeyManager();
    const sitemapParser = new SitemapParser();
    const crawler = new SmartCrawler(keyManager, sitemapParser);
    
    // Get website configurations
    const websites = await db
      .select()
      .from(websiteConfigs)
      .where(inArray(websiteConfigs.website_id, websitesToCrawl));
    
    console.log(`📊 Found ${websites.length} websites to crawl\n`);
    
    for (const website of websites) {
      console.log('='.repeat(80));
      console.log(`🌐 Crawling ${website.name} (${website.website_id})`);
      console.log('='.repeat(80));
      
      try {
        const result = await crawler.crawlWebsite(website.website_id);
        
        if (result.success) {
          console.log(`✅ ${website.name} crawled successfully:`);
          console.log(`   📊 Type: ${result.type}`);
          console.log(`   🔍 Total discovered: ${result.totalDiscovered}`);
          console.log(`   ✨ New URLs added: ${result.newUrlsAdded}`);
          console.log(`   🗺️  Sitemaps processed: ${result.sitemapsProcessed || 0}`);
        } else {
          console.log(`❌ ${website.name} crawling failed: ${result.error}`);
        }
        
      } catch (error) {
        console.log(`❌ Error crawling ${website.name}: ${error.message}`);
      }
      
      // Wait between websites
      if (websites.indexOf(website) < websites.length - 1) {
        console.log('\n⏳ Waiting 10 seconds before next website...');
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 CRAWLING RESULTS');
    console.log('='.repeat(80));
    console.log('✅ All specified websites crawled with improved patterns!');
    console.log('🎯 Ready to test property scraping with better URL filtering');
    
  } catch (error) {
    console.error('❌ Error during crawling:', error.message);
    process.exit(1);
  } finally {
    closeConnection();
  }
}

crawlSpecificWebsites();
