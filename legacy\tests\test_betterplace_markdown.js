// Test BetterPlace markdown scraping to understand structure
require('dotenv').config();
const Firecrawl = require('firecrawl').default;

async function testBetterPlaceMarkdown() {
  console.log('🔍 Testing BetterPlace Markdown Structure');
  console.log('='.repeat(60));
  
  const firecrawl = new Firecrawl({ apiKey: process.env.FIRECRAWL_API_KEY });
  const testUrl = 'https://betterplace.cc/buy/properties/BPVL02270';
  
  try {
    console.log(`🔄 Testing URL: ${testUrl}`);
    
    // Test both JSON and Markdown to compare
    console.log('\n📡 Step 1: JSON Extraction (current method)');
    const jsonResult = await firecrawl.scrapeUrl(testUrl, {
      formats: ['json'],
      jsonOptions: {
        prompt: "Extract property details including title, price, bedrooms, bathrooms, location, description, amenities, images, property_id, ownership_type, size information",
        schema: {
          type: "object",
          properties: {
            title: { type: "string" },
            price: { type: "string" },
            location: { type: "string" },
            bedrooms: { type: "integer" },
            bathrooms: { type: "integer" },
            description: { type: "string" },
            amenities: { type: "array", items: { type: "string" } },
            images: { type: "array", items: { type: "string" } },
            property_id: { type: "string" },
            ownership_type: { type: "string" },
            size: {
              type: "object",
              properties: {
                building_size_sqm: { type: "number" },
                land_size_sqm: { type: "number" }
              }
            }
          }
        }
      },
      onlyMainContent: true,
      timeout: 60000
    });
    
    console.log('📊 Full JSON result:');
    console.log(JSON.stringify(jsonResult, null, 2));

    if (jsonResult.success) {
      console.log('✅ JSON extraction successful');
      console.log('📊 JSON data:');
      console.log(JSON.stringify(jsonResult.data?.json || jsonResult.data, null, 2));
    } else {
      console.log('❌ JSON extraction failed');
      console.log('Error:', jsonResult.error);
    }
    
    console.log('\n📡 Step 2: Markdown Extraction (new method)');
    const markdownResult = await firecrawl.scrapeUrl(testUrl, {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 60000
    });
    
    console.log('📊 Full Markdown result:');
    console.log(JSON.stringify(markdownResult, null, 2));

    if (markdownResult.success) {
      console.log('✅ Markdown extraction successful');
      const markdownContent = markdownResult.data?.markdown || markdownResult.markdown || '';
      console.log(`📄 Markdown length: ${markdownContent.length} characters`);

      if (markdownContent) {
        console.log('\n📄 Markdown content (first 2000 chars):');
        console.log(markdownContent.substring(0, 2000));
        console.log('\n...(truncated)');

        // Save full markdown to file for analysis
        const fs = require('fs');
        fs.writeFileSync('betterplace_sample.md', markdownContent);
        console.log('\n💾 Full markdown saved to betterplace_sample.md');

        // Test our markdown parsing
        console.log('\n🔍 Step 3: Testing Markdown Parsing');
        const parsedData = parseBetterPlaceMarkdown(markdownContent, testUrl);
        console.log('📊 Parsed data:');
        console.log(JSON.stringify(parsedData, null, 2));
      } else {
        console.log('❌ No markdown content received');
      }

    } else {
      console.log('❌ Markdown extraction failed');
      console.log('Error:', markdownResult.error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

function parseBetterPlaceMarkdown(markdown, url) {
  try {
    console.log(`   🔍 Parsing BetterPlace markdown (${markdown.length} chars)...`);

    // Extract property ID from URL
    const propertyIdMatch = url.match(/\/([A-Z0-9]+)$/i);
    const property_id = propertyIdMatch ? propertyIdMatch[1] : 'unknown';

    // Extract title - BetterPlace usually has title as first heading
    const titleMatch = markdown.match(/^#\s+(.+)$/m) ||
                      markdown.match(/^##\s+(.+)$/m) ||
                      markdown.match(/!\[([^\]]+)\]/) ||
                      markdown.match(/\*\*([^*]+)\*\*/);
    const title = titleMatch ? titleMatch[1].trim() : 'Property Title Not Found';

    // Extract price - BetterPlace often shows USD prices
    const priceMatch = markdown.match(/(?:USD|US\$|\$)\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i) ||
                      markdown.match(/(?:IDR|Rp)\s*([\d,\.]+(?:\s*(?:million|billion|juta|miliar))?)/i) ||
                      markdown.match(/([\d,\.]+)\s*(?:USD|US\$|\$|IDR|Rp)/i);
    const price = priceMatch ? priceMatch[0] : null;

    // Extract bedrooms - look for various patterns
    const bedroomMatch = markdown.match(/(\d+)\s*(?:bed|bedroom|kamar tidur|BR)/i) ||
                        markdown.match(/bedroom[s]?[:\s]*(\d+)/i) ||
                        markdown.match(/(\d+)\s*BR/i);
    const bedrooms = bedroomMatch ? parseInt(bedroomMatch[1]) : null;

    // Extract bathrooms
    const bathroomMatch = markdown.match(/(\d+)\s*(?:bath|bathroom|kamar mandi|BA)/i) ||
                         markdown.match(/bathroom[s]?[:\s]*(\d+)/i) ||
                         markdown.match(/(\d+)\s*BA/i);
    const bathrooms = bathroomMatch ? parseInt(bathroomMatch[1]) : null;

    // Extract location - BetterPlace shows location info
    const locationMatch = markdown.match(/(Canggu|Seminyak|Ubud|Kerobokan|Sanur|Denpasar|Jimbaran|Nusa Dua|Uluwatu|Pecatu|Bukit|Umalas|Berawa|Pererenan|Kedungu|Tanah Lot|Seseh|Cemagi|Tabanan|Badung|Gianyar)/i) ||
                         markdown.match(/Location[:\s]*([^\n]+)/i) ||
                         markdown.match(/Address[:\s]*([^\n]+)/i);
    const location = locationMatch ? locationMatch[1].trim() : 'Bali, Indonesia';

    // Extract images - look for image URLs
    const imageMatches = markdown.match(/!\[.*?\]\((https?:\/\/[^\)]+)\)/g) || [];
    const images = imageMatches.map(match => {
      const urlMatch = match.match(/\((https?:\/\/[^\)]+)\)/);
      return urlMatch ? urlMatch[1] : null;
    }).filter(Boolean);

    // Extract size information
    const buildingSizeMatch = markdown.match(/(?:building|built)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)/i) ||
                             markdown.match(/(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²).*(?:building|built)/i);
    const building_size_sqm = buildingSizeMatch ? parseFloat(buildingSizeMatch[1].replace(',', '')) : null;

    const landSizeMatch = markdown.match(/(?:land|lot)[:\s]*(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²)/i) ||
                         markdown.match(/(\d+(?:[,\.]\d+)?)\s*(?:sqm|m2|m²).*(?:land|lot)/i);
    const land_size_sqm = landSizeMatch ? parseFloat(landSizeMatch[1].replace(',', '')) : null;

    // Extract amenities - BetterPlace specific features
    const amenityKeywords = [
      'swimming pool', 'pool', 'garden', 'parking', 'garage', 'kitchen', 
      'air conditioning', 'wifi', 'security', 'furnished', 'unfurnished',
      'balcony', 'terrace', 'gym', 'spa', 'jacuzzi', 'bbq', 'maid service',
      'internet', 'cable tv', 'washing machine', 'dryer', 'dishwasher'
    ];
    const amenities = [];
    const lowerMarkdown = markdown.toLowerCase();

    amenityKeywords.forEach(keyword => {
      if (lowerMarkdown.includes(keyword)) {
        amenities.push(keyword.charAt(0).toUpperCase() + keyword.slice(1));
      }
    });

    // Extract ownership type - BetterPlace often mentions this
    let ownership_type = null;
    if (lowerMarkdown.includes('freehold')) {
      ownership_type = 'FREEHOLD';
    } else if (lowerMarkdown.includes('leasehold')) {
      ownership_type = 'LEASEHOLD';
    }

    // Extract parking info
    const parkingMatch = markdown.match(/(\d+)\s*(?:car\s*)?(?:parking|garage)/i) ||
                        markdown.match(/parking[:\s]*(\d+)/i);
    const parking_spaces = parkingMatch ? parseInt(parkingMatch[1]) : null;

    // Extract year built
    const yearMatch = markdown.match(/(?:built|year|constructed)[:\s]*(\d{4})/i) ||
                     markdown.match(/(\d{4})\s*(?:built|year|constructed)/i);
    const year_built = yearMatch ? parseInt(yearMatch[1]) : null;

    // Extract description - first meaningful paragraph
    const paragraphs = markdown.split('\n').filter(line =>
      line.trim().length > 50 &&
      !line.includes('|') &&
      !line.startsWith('#') &&
      !line.includes('USD') &&
      !line.includes('IDR') &&
      !line.includes('bedroom') &&
      !line.includes('bathroom') &&
      !line.includes('sqm')
    );
    const description = paragraphs.length > 0 ? paragraphs[0].trim() : markdown.substring(0, 500) + '...';

    const extractedData = {
      title: title,
      price: price,
      location: location,
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      description: description,
      images: images.slice(0, 10), // Max 10 images
      property_id: property_id,
      detail_url: url,
      property_type: 'villa', // Default for BetterPlace
      status: 'available',
      amenities: amenities,
      year_built: year_built,
      parking_spaces: parking_spaces,
      ownership_type: ownership_type,
      size: {
        building_size_sqm: building_size_sqm,
        land_size_sqm: land_size_sqm
      }
    };

    console.log(`   ✅ Extracted: ${title} (${bedrooms}bed/${bathrooms}bath) in ${location} - ${price}`);
    return extractedData;

  } catch (error) {
    console.log(`   ❌ BetterPlace markdown parsing failed: ${error.message}`);
    return null;
  }
}

// Run the test
testBetterPlaceMarkdown().catch(console.error);
