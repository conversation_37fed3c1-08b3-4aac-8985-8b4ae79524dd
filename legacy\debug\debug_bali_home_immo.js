// Debug Bali Home Immo price extraction issue
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function debugBaliHomeImmo() {
  console.log('🔍 Debug Bali Home Immo Price Extraction');
  console.log('='.repeat(50));
  
  const testUrl = 'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/4-bedroom-family-villa-for-rent-in-buduk-canggu-residential-side-rf6558';
  
  console.log(`📡 Testing URL: ${testUrl}`);
  console.log('');
  
  try {
    // Override console.log temporarily to capture mapper debug info
    const originalLog = console.log;
    const logs = [];
    console.log = (...args) => {
      const message = args.join(' ');
      logs.push(message);
      originalLog(...args);
    };
    
    const results = await runExtractBatch('bali_home_immo', [testUrl], {});
    
    // Restore console.log
    console.log = originalLog;
    
    if (results && results.processedResults && results.processedResults.length > 0) {
      const result = results.processedResults[0];
      if (result && result.ok && result.data) {
        const prop = result.data;
        
        console.log('\n📊 Extracted Data:');
        console.log(`Title: ${prop.title}`);
        console.log(`Bedrooms: ${prop.bedrooms}`);
        console.log(`Bathrooms: ${prop.bathrooms}`);
        console.log(`Price: ${prop.price}`);
        console.log(`Rent Price: ${prop.rent_price}`);
        console.log(`Building Size: ${prop.size_sqft} sqft`);
        console.log(`Lot Size: ${prop.lot_size_sqft} sqft`);
        console.log(`Year Built: ${prop.year_built}`);
        
        // Look for price related logs
        console.log('\n💰 Price Debug Logs:');
        const priceLogs = logs.filter(log => 
          log.includes('price') || 
          log.includes('Price') || 
          log.includes('IDR') || 
          log.includes('USD') ||
          log.includes('💰') ||
          log.includes('rent')
        );
        
        priceLogs.forEach(log => console.log(`   ${log}`));
        
        if (!prop.price && !prop.rent_price) {
          console.log('\n❌ No price extracted! Need to check patterns...');
        }
        
      } else {
        console.log('❌ No valid data extracted');
        console.log('Result:', JSON.stringify(result, null, 2));
      }
    } else {
      console.log('❌ No results returned');
      console.log('Results:', JSON.stringify(results, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugBaliHomeImmo().then(() => process.exit(0));
