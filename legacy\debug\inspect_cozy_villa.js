// Inspect the Cozy Villa property to see what data is missing
require('dotenv').config();
const { db } = require('./drizzle_client');
const { sql } = require('drizzle-orm');

async function inspectCozyVilla() {
  console.log('🔍 Inspecting Cozy Villa 3 Bedrooms property...\n');
  
  try {
    // Get the specific property
    const property = await db.execute(sql`
      SELECT *
      FROM property
      WHERE title = 'Cozy Villa 3 Bedrooms For Rental in Umalas, Bali'
      ORDER BY created_at DESC
      LIMIT 1;
    `);
    
    if (property.length === 0) {
      console.log('❌ Property not found');
      return;
    }
    
    const prop = property[0];
    console.log('📋 Complete Property Data:');
    console.log('='.repeat(50));
    
    // Basic info
    console.log('🏠 Basic Information:');
    console.log(`   ID: ${prop.id}`);
    console.log(`   Title: ${prop.title}`);
    console.log(`   Address: ${prop.address}`);
    console.log(`   City: ${prop.city}`);
    console.log(`   State: ${prop.state}`);
    console.log(`   Country: ${prop.country}`);
    console.log(`   Postal Code: ${prop.postal_code}`);
    
    // Property details
    console.log('\n🏡 Property Details:');
    console.log(`   Bedrooms: ${prop.bedrooms}`);
    console.log(`   Bathrooms: ${prop.bathrooms}`);
    console.log(`   Parking Spaces: ${prop.parking_spaces}`);
    console.log(`   Size (sqft): ${prop.size_sqft}`);
    console.log(`   Lot Size (sqft): ${prop.lot_size_sqft}`);
    console.log(`   Year Built: ${prop.year_built}`);
    
    // Pricing
    console.log('\n💰 Pricing:');
    console.log(`   Sale Price: ${prop.price ? `IDR ${prop.price}` : 'Not set'}`);
    console.log(`   Rent Price: ${prop.rent_price ? `IDR ${prop.rent_price}` : 'Not set'}`);
    
    // JSON data
    console.log('\n📦 JSON Data:');
    console.log(`   Amenities:`, JSON.stringify(prop.amenities, null, 2));
    console.log(`   Media:`, JSON.stringify(prop.media, null, 2));
    
    // Source tracking
    console.log('\n🔗 Source Information:');
    console.log(`   Source ID: ${prop.source_id}`);
    console.log(`   External ID: ${prop.external_id}`);
    console.log(`   Source URL: ${prop.source_url}`);
    console.log(`   Scraped At: ${prop.scraped_at}`);
    
    // Vector
    console.log('\n🔍 Vector Embedding:');
    console.log(`   Has Vector: ${prop.vector ? 'YES' : 'NO'}`);
    if (prop.vector) {
      console.log(`   Vector Length: ${JSON.parse(prop.vector).length} dimensions`);
    }
    
    // Timestamps
    console.log('\n⏰ Timestamps:');
    console.log(`   Created: ${prop.created_at}`);
    console.log(`   Updated: ${prop.updated_at}`);
    
    // Analysis
    console.log('\n📊 Missing Data Analysis:');
    const missing = [];
    if (!prop.parking_spaces) missing.push('parking_spaces');
    if (!prop.size_sqft) missing.push('size_sqft');
    if (!prop.lot_size_sqft) missing.push('lot_size_sqft');
    if (!prop.year_built) missing.push('year_built');
    if (!prop.price && !prop.rent_price) missing.push('pricing');
    if (!prop.postal_code) missing.push('postal_code');
    if (!prop.state) missing.push('state');
    
    if (missing.length > 0) {
      console.log('❌ Missing fields:');
      missing.forEach(field => console.log(`   - ${field}`));
    } else {
      console.log('✅ All expected fields are present');
    }
    
  } catch (error) {
    console.error('❌ Error inspecting property:', error);
  }
}

if (require.main === module) {
  inspectCozyVilla().catch(console.error);
}

module.exports = { inspectCozyVilla };
