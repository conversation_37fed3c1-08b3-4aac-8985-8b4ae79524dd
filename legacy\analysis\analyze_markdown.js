// Analyze markdown content for land size patterns
require('dotenv').config();
const Firecrawl = require('firecrawl').default;
const { getKeyManager } = require('./scrape_worker/key_manager');

async function analyzeMarkdown() {
  console.log('🔍 Analyzing Markdown for Land Size Patterns');
  console.log('='.repeat(50));
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();

  const testUrl = 'https://balivillarealty.com/property/modern-2-bedroom-villa-for-sale-leasehold-in-prime-petitenget/';

  try {
    console.log(`📡 Scraping: ${testUrl}`);

    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });

    const result = await response.json();
    
    if (result.success && result.data && result.data.markdown) {
      const markdown = result.data.markdown;
      console.log(`📝 Markdown length: ${markdown.length} chars`);
      
      // Look for size-related content
      console.log('\n🔍 Searching for size patterns...');
      
      // Search for various size patterns
      const sizePatterns = [
        /(\d+)\s*sqm/gi,
        /(\d+)\s*m2/gi,
        /(\d+)\s*m²/gi,
        /land.*?(\d+)/gi,
        /lot.*?(\d+)/gi,
        /area.*?(\d+)/gi,
        /size.*?(\d+)/gi,
        /300.*?sqm/gi,
        /90.*?sqm/gi
      ];
      
      sizePatterns.forEach((pattern, i) => {
        const matches = [...markdown.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\nPattern ${i+1} (${pattern.source}):`);
          matches.slice(0, 5).forEach(match => {
            const start = Math.max(0, match.index - 30);
            const end = Math.min(markdown.length, match.index + match[0].length + 30);
            const context = markdown.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      // Look for specific Bali Villa Realty patterns
      console.log('\n🏠 Looking for Bali Villa Realty specific patterns...');
      
      const bvrPatterns = [
        /land\s*area/gi,
        /area\s*size/gi,
        /property\s*type/gi,
        /bedroom/gi,
        /bathroom/gi,
        /year\s*built/gi,
        /ownership/gi,
        /leasehold/gi
      ];
      
      bvrPatterns.forEach(pattern => {
        const matches = [...markdown.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\n${pattern.source} found ${matches.length} times:`);
          matches.slice(0, 3).forEach(match => {
            const start = Math.max(0, match.index - 50);
            const end = Math.min(markdown.length, match.index + match[0].length + 50);
            const context = markdown.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      // Show a sample of the markdown
      console.log('\n📄 Markdown Sample (first 1000 chars):');
      console.log(markdown.substring(0, 1000));
      
    } else {
      console.log('❌ Failed to get markdown');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

analyzeMarkdown().then(() => process.exit(0));
