// Test villabalisale.com URLs scraping
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { db, properties, scrapingQueue } = require('./drizzle_client');
const { desc, sql, eq, and } = require('drizzle-orm');

async function testVillaBaliSaleScraping() {
  console.log('🧪 Testing villabalisale.com URLs Scraping');
  console.log('='.repeat(60));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  try {
    // Get 3 villabalisale.com URLs from queue
    const queueUrls = await db
      .select({ url: scrapingQueue.url, id: scrapingQueue.id })
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, 'villabalisale.com'),
        eq(scrapingQueue.status, 'pending')
      ))
      .limit(3);
    
    if (queueUrls.length === 0) {
      console.log('❌ No villabalisale.com URLs found in queue');
      process.exit(1);
    }
    
    console.log(`📋 Found ${queueUrls.length} villabalisale.com URLs to test:`);
    queueUrls.forEach((row, i) => {
      console.log(`   ${i + 1}. ${row.url}`);
    });
    
    // Get initial count
    const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`\n📊 Initial properties in database: ${initialCount[0].count}`);
    
    // Check existing villabalisale properties
    const existingVillaBaliSale = await db
      .select({ count: sql`count(*)` })
      .from(properties)
      .where(eq(properties.source_id, 'villa_bali_sale'));
    console.log(`📊 Existing villa_bali_sale properties: ${existingVillaBaliSale[0].count}`);
    
    // Extract URLs for processing
    const urls = queueUrls.map(row => row.url);
    
    console.log(`\n🔄 Processing ${urls.length} villabalisale.com URLs...`);
    const startTime = Date.now();
    
    // Use the existing batch processing function with villa_bali_sale mapper
    const results = await runExtractBatch('villa_bali_sale', urls, {
      concurrency: 1, // Process one at a time for better debugging
      timeout: 90000  // Longer timeout for Villa Bali Sale
    });
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    
    if (results && results.length > 0) {
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      
      console.log(`\n✅ Completed in ${duration}s:`);
      console.log(`   Processed: ${results.length}`);
      console.log(`   Successful: ${successful}`);
      console.log(`   Failed: ${failed}`);
      
      // Show successful results with detailed amenities check
      const successfulResults = results.filter(r => r.success);
      if (successfulResults.length > 0) {
        console.log(`\n🏠 Successfully scraped properties:`);
        successfulResults.forEach((result, i) => {
          console.log(`\n   ${i + 1}. ✅ ${result.title || 'Property'}`);
          if (result.price) {
            console.log(`      💰 IDR ${result.price.toLocaleString()}`);
          }
          if (result.bedrooms) {
            console.log(`      🏠 ${result.bedrooms} bedrooms`);
          }
          if (result.city) {
            console.log(`      📍 ${result.city}`);
          }
          if (result.ownership_type) {
            console.log(`      🏛️  ${result.ownership_type}`);
          }
          
          // Detailed amenities check
          if (result.amenities && result.amenities.raw_amenities && result.amenities.raw_amenities.length > 0) {
            console.log(`      🎯 Amenities (${result.amenities.raw_amenities.length}):`);
            result.amenities.raw_amenities.slice(0, 5).forEach((amenity, idx) => {
              console.log(`         ${idx + 1}. ${amenity}`);
            });
            if (result.amenities.raw_amenities.length > 5) {
              console.log(`         ... and ${result.amenities.raw_amenities.length - 5} more`);
            }
          } else {
            console.log(`      ⚠️  No amenities extracted`);
          }
          
          if (result.description) {
            console.log(`      📝 Description: ${result.description.substring(0, 100)}...`);
          }
        });
      }
      
      // Show failed results
      const failedResults = results.filter(r => !r.success);
      if (failedResults.length > 0) {
        console.log(`\n❌ Failed properties:`);
        failedResults.forEach((result, i) => {
          console.log(`   ${i + 1}. ❌ ${result.error || 'Unknown error'}`);
          if (result.url) {
            console.log(`      URL: ${result.url}`);
          }
        });
      }
      
    } else {
      console.log(`⚠️  No results returned (${duration}s)`);
    }
    
    // Check final count and show new properties
    const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
    const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
    console.log(`\n🆕 New properties added to database: ${newProperties}`);
    
    if (newProperties > 0) {
      console.log('\n🏠 Latest Villa Bali Sale Properties in Database:');
      const latestProps = await db
        .select({
          id: properties.id,
          title: properties.title,
          external_id: properties.external_id,
          price: properties.price,
          bedrooms: properties.bedrooms,
          city: properties.city,
          ownership_type: properties.ownership_type,
          amenities: properties.amenities,
          description: properties.description,
          created_at: properties.created_at
        })
        .from(properties)
        .where(eq(properties.source_id, 'villa_bali_sale'))
        .orderBy(desc(properties.created_at))
        .limit(5);
      
      latestProps.forEach((prop, i) => {
        console.log(`\n   ${i + 1}. [${prop.id}] ${prop.title}`);
        if (prop.price) {
          console.log(`      💰 IDR ${prop.price.toLocaleString()}`);
        }
        console.log(`      🏠 ${prop.bedrooms} bed | 📍 ${prop.city}`);
        if (prop.ownership_type) {
          console.log(`      🏛️  ${prop.ownership_type}`);
        }
        console.log(`      🆔 External ID: ${prop.external_id}`);
        console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
        
        // Check amenities in database
        if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
          console.log(`      ✅ Amenities in DB (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
        } else {
          console.log(`      ❌ No amenities in database`);
        }
        
        if (prop.description) {
          console.log(`      📝 Description: ${prop.description.substring(0, 80)}...`);
        } else {
          console.log(`      ⚠️  No description in database`);
        }
      });
    }
    
    console.log('\n✅ Villa Bali Sale scraping test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testVillaBaliSaleScraping();
