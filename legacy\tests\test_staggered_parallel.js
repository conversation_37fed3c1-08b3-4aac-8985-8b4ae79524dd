// Test staggered parallel processing (6 jobs with 10s intervals)
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testStaggeredParallel() {
  console.log('🚀 Testing staggered parallel processing...\n');
  
  // Test with 6 URLs - should all start within 60 seconds with 10s intervals
  const testUrls = [
    'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/',
    'https://balivillarealty.com/property/cozy-villa-3-bedrooms-for-rental-in-umalas-bali/',
    'https://balivillarealty.com/property/charming-2-bedrooms-villa-for-rental-in-babakan-canggu-bali/',
    'https://balivillarealty.com/property/cozy-villa-2-bedrooms-for-rental-in-canggu-bali/',
    'https://balivillarealty.com/property/moa-villa-canggu-by-ilot-property-bali/',
    'https://balivillarealty.com/property/tranquil-3-bedroom-villa-for-yearly-rental-in-bali-umalas/'
  ];
  
  console.log(`📊 Testing with ${testUrls.length} URLs (staggered parallel)`);
  console.log('⏰ Expected behavior:');
  console.log('   - Job 1 starts at 0s');
  console.log('   - Job 2 starts at 10s');
  console.log('   - Job 3 starts at 20s');
  console.log('   - Job 4 starts at 30s');
  console.log('   - Job 5 starts at 40s');
  console.log('   - Job 6 starts at 50s');
  console.log('   - All jobs run in parallel after starting');
  console.log('   - Total time: ~60-90 seconds (much faster than 324s sequential)');
  console.log('   - No rate limit errors\n');
  
  try {
    const startTime = Date.now();
    console.log(`⏰ Start time: ${new Date(startTime).toLocaleTimeString()}`);
    
    const results = await runExtractBatch('bali_villa_realty', testUrls, {});
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`\n⏰ End time: ${new Date(endTime).toLocaleTimeString()}`);
    console.log(`⚡ Total duration: ${duration} seconds`);
    console.log(`📊 Average per URL: ${(duration / testUrls.length).toFixed(2)} seconds`);
    
    // Performance analysis
    console.log('\n📈 Performance Analysis:');
    const expectedSequentialTime = testUrls.length * 54; // 54s per URL from previous test
    const expectedParallelTime = 90; // ~90 seconds for staggered parallel
    
    if (duration < expectedParallelTime * 1.3) {
      console.log('   🎉 EXCELLENT - Staggered parallel processing working!');
      console.log(`   🎯 Target time: ~${expectedParallelTime}s`);
      console.log(`   ⚡ Actual time: ${duration}s`);
      console.log(`   📊 vs Sequential: ${((expectedSequentialTime / duration)).toFixed(1)}x faster`);
    } else if (duration < expectedSequentialTime * 0.7) {
      console.log('   ✅ GOOD - Significant improvement over sequential');
      console.log(`   📈 Sequential would be: ~${expectedSequentialTime}s`);
      console.log(`   ⚡ Parallel actual: ${duration}s`);
      console.log(`   📊 Improvement: ${((expectedSequentialTime / duration)).toFixed(1)}x faster`);
    } else {
      console.log('   ⚠️  Performance similar to sequential - check implementation');
    }
    
    // Results summary
    console.log('\n📊 Results Summary:');
    const successful = results.filter(r => r.ok);
    const failed = results.filter(r => !r.ok);
    
    console.log(`   ✅ Successful: ${successful.length}/${testUrls.length}`);
    console.log(`   ❌ Failed: ${failed.length}/${testUrls.length}`);
    console.log(`   📈 Success Rate: ${((successful.length / testUrls.length) * 100).toFixed(1)}%`);
    
    // Rate limit check
    const rateLimitErrors = failed.filter(r => r.error && r.error.includes('429'));
    if (rateLimitErrors.length === 0) {
      console.log('   ✅ No rate limit errors - staggering working perfectly!');
    } else {
      console.log(`   ❌ ${rateLimitErrors.length} rate limit errors - need adjustment`);
    }
    
    if (successful.length > 0) {
      console.log('\n🎉 Successfully processed properties:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title.substring(0, 50)}...`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ Failed properties:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. Error: ${result.error}`);
      });
    }
    
  } catch (error) {
    console.error(`❌ Test failed: ${error.message}`);
  }
  
  console.log('\n🎯 Staggered parallel test completed!');
}

if (require.main === module) {
  testStaggeredParallel().catch(console.error);
}

module.exports = { testStaggeredParallel };
