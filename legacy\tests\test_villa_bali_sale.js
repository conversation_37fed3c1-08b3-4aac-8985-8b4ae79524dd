// Test Villa Bali Sale scraping
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testVillaBaliSale() {
  console.log('🔍 Testing Villa Bali Sale Scraping');
  console.log('='.repeat(50));
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/lovina/property-for-sale-in-singaraja---lovina';
  
  console.log(`📡 Testing URL: ${testUrl}`);
  console.log('');
  
  try {
    const startTime = Date.now();
    const results = await runExtractBatch('villabalisale', [testUrl], {});
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);
    
    console.log(`⏱️ Duration: ${duration}s`);
    
    if (results && results.processedResults && results.processedResults.length > 0) {
      const result = results.processedResults[0];
      if (result && result.ok && result.data) {
        const prop = result.data;
        
        console.log('\n📊 Extracted Data:');
        console.log(`Title: ${prop.title}`);
        console.log(`Bedrooms: ${prop.bedrooms}`);
        console.log(`Bathrooms: ${prop.bathrooms}`);
        console.log(`Building Size: ${prop.size_sqft} sqft`);
        console.log(`Lot Size: ${prop.lot_size_sqft} sqft`);
        console.log(`Year Built: ${prop.year_built}`);
        console.log(`Ownership: ${prop.ownership_type}`);
        console.log(`Price: ${prop.price || prop.rent_price}`);
        console.log(`Location: ${prop.city}, ${prop.state}`);
        
        if (prop.description) {
          console.log(`Description: ${prop.description.substring(0, 100)}...`);
        }
        
        console.log('\n✅ Villa Bali Sale scraping successful!');
        
      } else {
        console.log('❌ No valid data extracted');
        console.log('Result:', JSON.stringify(result, null, 2));
      }
    } else {
      console.log('❌ No results returned');
      console.log('Results:', JSON.stringify(results, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testVillaBaliSale().then(() => process.exit(0));
