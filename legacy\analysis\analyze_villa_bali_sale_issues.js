// Analyze all Villa Bali Sale issues in detail
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { eq, desc, sql } = require('drizzle-orm');

async function analyzeVillaBaliSaleIssues() {
  console.log('🔍 Analyzing Villa Bali Sale Issues in Detail');
  console.log('='.repeat(60));
  
  try {
    // Get all Villa Bali Sale properties
    const villaBaliSaleProps = await db
      .select()
      .from(properties)
      .where(sql`media->>'source_id' = 'villa_bali_sale'`)
      .orderBy(desc(properties.created_at))
      .limit(20);
    
    console.log(`📋 Found ${villaBaliSaleProps.length} Villa Bali Sale properties`);
    
    // Analyze issues
    const issues = {
      genericTitles: [],
      missingBedrooms: [],
      missingBathrooms: [],
      missingSize: [],
      missingLotSize: [],
      missingOwnership: [],
      missingYear: [],
      missingDescription: [],
      extremePrices: []
    };
    
    villaBaliSaleProps.forEach(prop => {
      // Generic titles
      if (prop.title === 'Villa Bali Sale Property' || prop.title.includes('Property For Sale In')) {
        issues.genericTitles.push(prop);
      }
      
      // Missing critical fields
      if (!prop.bedrooms) issues.missingBedrooms.push(prop);
      if (!prop.bathrooms) issues.missingBathrooms.push(prop);
      if (!prop.size_sqft) issues.missingSize.push(prop);
      if (!prop.lot_size_sqft) issues.missingLotSize.push(prop);
      if (!prop.ownership_type) issues.missingOwnership.push(prop);
      if (!prop.year_built) issues.missingYear.push(prop);
      if (!prop.description) issues.missingDescription.push(prop);
      
      // Extreme prices (over 20 billion IDR = ~$1.2M)
      const price = parseFloat(prop.price || 0);
      const rentPrice = parseFloat(prop.rent_price || 0);
      if (price > 20000000000 || rentPrice > 20000000000) {
        issues.extremePrices.push(prop);
      }
    });
    
    // Report issues
    console.log('\n🚨 Issue Summary:');
    console.log(`❌ Generic Titles: ${issues.genericTitles.length}`);
    console.log(`❌ Missing Bedrooms: ${issues.missingBedrooms.length}`);
    console.log(`❌ Missing Bathrooms: ${issues.missingBathrooms.length}`);
    console.log(`❌ Missing Building Size: ${issues.missingSize.length}`);
    console.log(`❌ Missing Lot Size: ${issues.missingLotSize.length}`);
    console.log(`❌ Missing Ownership: ${issues.missingOwnership.length}`);
    console.log(`❌ Missing Year Built: ${issues.missingYear.length}`);
    console.log(`❌ Missing Description: ${issues.missingDescription.length}`);
    console.log(`❌ Extreme Prices: ${issues.extremePrices.length}`);
    
    // Show examples of each issue
    if (issues.genericTitles.length > 0) {
      console.log('\n📝 Generic Title Examples:');
      issues.genericTitles.slice(0, 3).forEach(prop => {
        console.log(`   - "${prop.title}" (${prop.id})`);
        console.log(`     URL: ${prop.source_url}`);
        console.log(`     Created: ${new Date(prop.created_at).toLocaleString()}`);
      });
    }
    
    if (issues.missingBedrooms.length > 0) {
      console.log('\n🛏️ Missing Bedrooms Examples:');
      issues.missingBedrooms.slice(0, 3).forEach(prop => {
        console.log(`   - "${prop.title}" (${prop.id})`);
        console.log(`     Bathrooms: ${prop.bathrooms || 'NULL'}`);
        console.log(`     Size: ${prop.size_sqft || 'NULL'} sqft`);
      });
    }
    
    if (issues.extremePrices.length > 0) {
      console.log('\n💰 Extreme Price Examples:');
      issues.extremePrices.slice(0, 3).forEach(prop => {
        const price = parseFloat(prop.price || 0);
        const rentPrice = parseFloat(prop.rent_price || 0);
        const priceUSD = price > 0 ? (price / 16000).toFixed(0) : 'N/A';
        const rentUSD = rentPrice > 0 ? (rentPrice / 16000).toFixed(0) : 'N/A';
        
        console.log(`   - "${prop.title}"`);
        console.log(`     Price: ${price.toLocaleString()} IDR (~$${priceUSD})`);
        console.log(`     Rent: ${rentPrice.toLocaleString()} IDR (~$${rentUSD})`);
      });
    }
    
    // Check recent vs old properties
    const recentProps = villaBaliSaleProps.filter(p => 
      new Date(p.created_at) > new Date('2025-08-19')
    );
    const oldProps = villaBaliSaleProps.filter(p => 
      new Date(p.created_at) <= new Date('2025-08-19')
    );
    
    console.log('\n📅 Recent vs Old Properties:');
    console.log(`Recent (after 2025-08-19): ${recentProps.length}`);
    console.log(`Old (before 2025-08-19): ${oldProps.length}`);
    
    if (recentProps.length > 0) {
      const recentComplete = recentProps.filter(p => 
        p.bedrooms && p.bathrooms && p.size_sqft && p.title !== 'Villa Bali Sale Property'
      );
      console.log(`Recent Complete: ${recentComplete.length}/${recentProps.length} (${(recentComplete.length/recentProps.length*100).toFixed(1)}%)`);
    }
    
    if (oldProps.length > 0) {
      const oldComplete = oldProps.filter(p => 
        p.bedrooms && p.bathrooms && p.size_sqft && p.title !== 'Villa Bali Sale Property'
      );
      console.log(`Old Complete: ${oldComplete.length}/${oldProps.length} (${(oldComplete.length/oldProps.length*100).toFixed(1)}%)`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

analyzeVillaBaliSaleIssues().then(() => process.exit(0));
