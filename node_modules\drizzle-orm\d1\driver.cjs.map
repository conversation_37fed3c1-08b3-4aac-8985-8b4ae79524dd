{"version": 3, "sources": ["../../src/d1/driver.ts"], "sourcesContent": ["/// <reference types=\"@cloudflare/workers-types\" />\nimport type { D1Database as MiniflareD1Database } from '@miniflare/d1';\nimport type { BatchItem, BatchResponse } from '~/batch.ts';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype ExtractTablesWithRelations,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig, IfNotImported } from '~/utils.ts';\nimport { SQLiteD1Session } from './session.ts';\n\nexport type AnyD1Database = IfNotImported<\n\tD1Database,\n\tMiniflareD1Database,\n\tD1Database | IfNotImported<MiniflareD1Database, never, MiniflareD1Database>\n>;\n\nexport class DrizzleD1Database<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends BaseSQLiteDatabase<'async', D1Result, TSchema> {\n\tstatic override readonly [entityKind]: string = 'D1Database';\n\n\t/** @internal */\n\tdeclare readonly session: SQLiteD1Session<TSchema, ExtractTablesWithRelations<TSchema>>;\n\n\tasync batch<U extends BatchItem<'sqlite'>, T extends Readonly<[U, ...U[]]>>(\n\t\tbatch: T,\n\t): Promise<BatchResponse<T>> {\n\t\treturn this.session.batch(batch) as Promise<BatchResponse<T>>;\n\t}\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends AnyD1Database = AnyD1Database,\n>(\n\tclient: TClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): DrizzleD1Database<TSchema> & {\n\t$client: TClient;\n} {\n\tconst dialect = new SQLiteAsyncDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new SQLiteD1Session(client as D1Database, dialect, schema, { logger, cache: config.cache });\n\tconst db = new DrizzleD1Database('async', dialect, session, schema) as DrizzleD1Database<TSchema>;\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\n\treturn db as any;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,oBAA2B;AAC3B,oBAA8B;AAC9B,uBAMO;AACP,gBAAmC;AACnC,qBAAmC;AAEnC,qBAAgC;AAQzB,MAAM,0BAEH,6BAA+C;AAAA,EACxD,QAA0B,wBAAU,IAAY;AAAA,EAKhD,MAAM,MACL,OAC4B;AAC5B,WAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,EAChC;AACD;AAEO,SAAS,QAIf,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,kCAAmB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAChE,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,+BAAgB,QAAsB,SAAS,QAAQ,EAAE,QAAQ,OAAO,OAAO,MAAM,CAAC;AAC1G,QAAM,KAAK,IAAI,kBAAkB,SAAS,SAAS,SAAS,MAAM;AAClE,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AAEA,SAAO;AACR;", "names": []}