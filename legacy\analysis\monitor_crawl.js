// Monitor active crawl jobs
require('dotenv').config();
const { SmartCrawler } = require('./scrape_worker/smart_crawler');
const { db, crawlJobs } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function monitorCrawls() {
  console.log('🔍 Monitoring active crawl jobs...\n');

  const crawler = new SmartCrawler();

  try {
    // Get active crawl jobs
    const activeJobs = await db.select().from(crawlJobs)
      .where(eq(crawlJobs.status, 'scraping'));

    if (activeJobs.length === 0) {
      console.log('📭 No active crawl jobs found');
      return;
    }

    console.log(`📊 Found ${activeJobs.length} active crawl job(s):\n`);

    for (const job of activeJobs) {
      console.log(`🕷️  Checking job ${job.id} (${job.website_id})`);
      console.log(`   Firecrawl ID: ${job.firecrawl_job_id}`);
      console.log(`   Current progress: ${job.processed_urls}/${job.total_urls}`);
      
      try {
        const status = await crawler.checkCrawlStatus(job.id);
        console.log(`   ✅ Status: ${status.status}`);
        console.log(`   📊 Progress: ${status.completed}/${status.total}`);
        
        if (status.status === 'completed') {
          console.log(`   🎉 Crawl completed! Found ${status.data?.length || 0} URLs`);
        } else if (status.status === 'failed') {
          console.log(`   ❌ Crawl failed`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error checking status: ${error.message}`);
      }
      
      console.log('');
    }

  } catch (error) {
    console.error('❌ Error monitoring crawls:', error.message);
  } finally {
    process.exit(0);
  }
}

monitorCrawls();
