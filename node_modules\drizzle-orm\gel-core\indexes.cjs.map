{"version": 3, "sources": ["../../src/gel-core/indexes.ts"], "sourcesContent": ["import { SQL } from '~/sql/sql.ts';\n\nimport { entityKind, is } from '~/entity.ts';\nimport type { GelColumn, GelExtraConfigColumn } from './columns/index.ts';\nimport { IndexedColumn } from './columns/index.ts';\nimport type { GelTable } from './table.ts';\n\ninterface IndexConfig {\n\tname?: string;\n\n\tcolumns: Partial<IndexedColumn | SQL>[];\n\n\t/**\n\t * If true, the index will be created as `create unique index` instead of `create index`.\n\t */\n\tunique: boolean;\n\n\t/**\n\t * If true, the index will be created as `create index concurrently` instead of `create index`.\n\t */\n\tconcurrently?: boolean;\n\n\t/**\n\t * If true, the index will be created as `create index ... on only <table>` instead of `create index ... on <table>`.\n\t */\n\tonly: boolean;\n\n\t/**\n\t * Condition for partial index.\n\t */\n\twhere?: SQL;\n\n\t/**\n\t * The optional WITH clause specifies storage parameters for the index\n\t */\n\twith?: Record<string, any>;\n\n\t/**\n\t * The optional WITH clause method for the index\n\t */\n\tmethod?: 'btree' | string;\n}\n\nexport type IndexColumn = GelColumn;\n\nexport type GelIndexMethod =\n\t| 'btree'\n\t| 'hash'\n\t| 'gist'\n\t| 'sGelist'\n\t| 'gin'\n\t| 'brin'\n\t| 'hnsw'\n\t| 'ivfflat'\n\t| (string & {});\n\nexport type GelIndexOpClass =\n\t| 'abstime_ops'\n\t| 'access_method'\n\t| 'anyarray_eq'\n\t| 'anyarray_ge'\n\t| 'anyarray_gt'\n\t| 'anyarray_le'\n\t| 'anyarray_lt'\n\t| 'anyarray_ne'\n\t| 'bigint_ops'\n\t| 'bit_ops'\n\t| 'bool_ops'\n\t| 'box_ops'\n\t| 'bpchar_ops'\n\t| 'char_ops'\n\t| 'cidr_ops'\n\t| 'cstring_ops'\n\t| 'date_ops'\n\t| 'float_ops'\n\t| 'int2_ops'\n\t| 'int4_ops'\n\t| 'int8_ops'\n\t| 'interval_ops'\n\t| 'jsonb_ops'\n\t| 'macaddr_ops'\n\t| 'name_ops'\n\t| 'numeric_ops'\n\t| 'oid_ops'\n\t| 'oidint4_ops'\n\t| 'oidint8_ops'\n\t| 'oidname_ops'\n\t| 'oidvector_ops'\n\t| 'point_ops'\n\t| 'polygon_ops'\n\t| 'range_ops'\n\t| 'record_eq'\n\t| 'record_ge'\n\t| 'record_gt'\n\t| 'record_le'\n\t| 'record_lt'\n\t| 'record_ne'\n\t| 'text_ops'\n\t| 'time_ops'\n\t| 'timestamp_ops'\n\t| 'timestamptz_ops'\n\t| 'timetz_ops'\n\t| 'uuid_ops'\n\t| 'varbit_ops'\n\t| 'varchar_ops'\n\t| 'xml_ops'\n\t| 'vector_l2_ops'\n\t| 'vector_ip_ops'\n\t| 'vector_cosine_ops'\n\t| 'vector_l1_ops'\n\t| 'bit_hamming_ops'\n\t| 'bit_jaccard_ops'\n\t| 'halfvec_l2_ops'\n\t| 'sparsevec_l2_op'\n\t| (string & {});\n\nexport class IndexBuilderOn {\n\tstatic readonly [entityKind]: string = 'GelIndexBuilderOn';\n\n\tconstructor(private unique: boolean, private name?: string) {}\n\n\ton(...columns: [Partial<GelExtraConfigColumn> | SQL, ...Partial<GelExtraConfigColumn | SQL>[]]): IndexBuilder {\n\t\treturn new IndexBuilder(\n\t\t\tcolumns.map((it) => {\n\t\t\t\tif (is(it, SQL)) {\n\t\t\t\t\treturn it;\n\t\t\t\t}\n\t\t\t\tit = it as GelExtraConfigColumn;\n\t\t\t\tconst clonedIndexedColumn = new IndexedColumn(it.name, !!it.keyAsName, it.columnType!, it.indexConfig!);\n\t\t\t\tit.indexConfig = JSON.parse(JSON.stringify(it.defaultConfig));\n\t\t\t\treturn clonedIndexedColumn;\n\t\t\t}),\n\t\t\tthis.unique,\n\t\t\tfalse,\n\t\t\tthis.name,\n\t\t);\n\t}\n\n\tonOnly(...columns: [Partial<GelExtraConfigColumn | SQL>, ...Partial<GelExtraConfigColumn | SQL>[]]): IndexBuilder {\n\t\treturn new IndexBuilder(\n\t\t\tcolumns.map((it) => {\n\t\t\t\tif (is(it, SQL)) {\n\t\t\t\t\treturn it;\n\t\t\t\t}\n\t\t\t\tit = it as GelExtraConfigColumn;\n\t\t\t\tconst clonedIndexedColumn = new IndexedColumn(it.name, !!it.keyAsName, it.columnType!, it.indexConfig!);\n\t\t\t\tit.indexConfig = it.defaultConfig;\n\t\t\t\treturn clonedIndexedColumn;\n\t\t\t}),\n\t\t\tthis.unique,\n\t\t\ttrue,\n\t\t\tthis.name,\n\t\t);\n\t}\n\n\t/**\n\t * Specify what index method to use. Choices are `btree`, `hash`, `gist`, `sGelist`, `gin`, `brin`, or user-installed access methods like `bloom`. The default method is `btree.\n\t *\n\t * If you have the `Gel_vector` extension installed in your database, you can use the `hnsw` and `ivfflat` options, which are predefined types.\n\t *\n\t * **You can always specify any string you want in the method, in case Drizzle doesn't have it natively in its types**\n\t *\n\t * @param method The name of the index method to be used\n\t * @param columns\n\t * @returns\n\t */\n\tusing(\n\t\tmethod: GelIndexMethod,\n\t\t...columns: [Partial<GelExtraConfigColumn | SQL>, ...Partial<GelExtraConfigColumn | SQL>[]]\n\t): IndexBuilder {\n\t\treturn new IndexBuilder(\n\t\t\tcolumns.map((it) => {\n\t\t\t\tif (is(it, SQL)) {\n\t\t\t\t\treturn it;\n\t\t\t\t}\n\t\t\t\tit = it as GelExtraConfigColumn;\n\t\t\t\tconst clonedIndexedColumn = new IndexedColumn(it.name, !!it.keyAsName, it.columnType!, it.indexConfig!);\n\t\t\t\tit.indexConfig = JSON.parse(JSON.stringify(it.defaultConfig));\n\t\t\t\treturn clonedIndexedColumn;\n\t\t\t}),\n\t\t\tthis.unique,\n\t\t\ttrue,\n\t\t\tthis.name,\n\t\t\tmethod,\n\t\t);\n\t}\n}\n\nexport interface AnyIndexBuilder {\n\tbuild(table: GelTable): Index;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface IndexBuilder extends AnyIndexBuilder {}\n\nexport class IndexBuilder implements AnyIndexBuilder {\n\tstatic readonly [entityKind]: string = 'GelIndexBuilder';\n\n\t/** @internal */\n\tconfig: IndexConfig;\n\n\tconstructor(\n\t\tcolumns: Partial<IndexedColumn | SQL>[],\n\t\tunique: boolean,\n\t\tonly: boolean,\n\t\tname?: string,\n\t\tmethod: string = 'btree',\n\t) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tunique,\n\t\t\tonly,\n\t\t\tmethod,\n\t\t};\n\t}\n\n\tconcurrently(): this {\n\t\tthis.config.concurrently = true;\n\t\treturn this;\n\t}\n\n\twith(obj: Record<string, any>): this {\n\t\tthis.config.with = obj;\n\t\treturn this;\n\t}\n\n\twhere(condition: SQL): this {\n\t\tthis.config.where = condition;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: GelTable): Index {\n\t\treturn new Index(this.config, table);\n\t}\n}\n\nexport class Index {\n\tstatic readonly [entityKind]: string = 'GelIndex';\n\n\treadonly config: IndexConfig & { table: GelTable };\n\n\tconstructor(config: IndexConfig, table: GelTable) {\n\t\tthis.config = { ...config, table };\n\t}\n}\n\nexport type GetColumnsTableName<TColumns> = TColumns extends GelColumn ? TColumns['_']['name']\n\t: TColumns extends GelColumn[] ? TColumns[number]['_']['name']\n\t: never;\n\nexport function index(name?: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(false, name);\n}\n\nexport function uniqueIndex(name?: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(true, name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAoB;AAEpB,oBAA+B;AAE/B,qBAA8B;AAgHvB,MAAM,eAAe;AAAA,EAG3B,YAAoB,QAAyB,MAAe;AAAxC;AAAyB;AAAA,EAAgB;AAAA,EAF7D,QAAiB,wBAAU,IAAY;AAAA,EAIvC,MAAM,SAAwG;AAC7G,WAAO,IAAI;AAAA,MACV,QAAQ,IAAI,CAAC,OAAO;AACnB,gBAAI,kBAAG,IAAI,cAAG,GAAG;AAChB,iBAAO;AAAA,QACR;AACA,aAAK;AACL,cAAM,sBAAsB,IAAI,6BAAc,GAAG,MAAM,CAAC,CAAC,GAAG,WAAW,GAAG,YAAa,GAAG,WAAY;AACtG,WAAG,cAAc,KAAK,MAAM,KAAK,UAAU,GAAG,aAAa,CAAC;AAC5D,eAAO;AAAA,MACR,CAAC;AAAA,MACD,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AAAA,EAEA,UAAU,SAAwG;AACjH,WAAO,IAAI;AAAA,MACV,QAAQ,IAAI,CAAC,OAAO;AACnB,gBAAI,kBAAG,IAAI,cAAG,GAAG;AAChB,iBAAO;AAAA,QACR;AACA,aAAK;AACL,cAAM,sBAAsB,IAAI,6BAAc,GAAG,MAAM,CAAC,CAAC,GAAG,WAAW,GAAG,YAAa,GAAG,WAAY;AACtG,WAAG,cAAc,GAAG;AACpB,eAAO;AAAA,MACR,CAAC;AAAA,MACD,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MACC,WACG,SACY;AACf,WAAO,IAAI;AAAA,MACV,QAAQ,IAAI,CAAC,OAAO;AACnB,gBAAI,kBAAG,IAAI,cAAG,GAAG;AAChB,iBAAO;AAAA,QACR;AACA,aAAK;AACL,cAAM,sBAAsB,IAAI,6BAAc,GAAG,MAAM,CAAC,CAAC,GAAG,WAAW,GAAG,YAAa,GAAG,WAAY;AACtG,WAAG,cAAc,KAAK,MAAM,KAAK,UAAU,GAAG,aAAa,CAAC;AAC5D,eAAO;AAAA,MACR,CAAC;AAAA,MACD,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACD;AAAA,EACD;AACD;AASO,MAAM,aAAwC;AAAA,EACpD,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EAEA,YACC,SACA,QACA,MACA,MACA,SAAiB,SAChB;AACD,SAAK,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,eAAqB;AACpB,SAAK,OAAO,eAAe;AAC3B,WAAO;AAAA,EACR;AAAA,EAEA,KAAK,KAAgC;AACpC,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,WAAsB;AAC3B,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,OAAwB;AAC7B,WAAO,IAAI,MAAM,KAAK,QAAQ,KAAK;AAAA,EACpC;AACD;AAEO,MAAM,MAAM;AAAA,EAClB,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EAET,YAAY,QAAqB,OAAiB;AACjD,SAAK,SAAS,EAAE,GAAG,QAAQ,MAAM;AAAA,EAClC;AACD;AAMO,SAAS,MAAM,MAA+B;AACpD,SAAO,IAAI,eAAe,OAAO,IAAI;AACtC;AAEO,SAAS,YAAY,MAA+B;AAC1D,SAAO,IAAI,eAAe,MAAM,IAAI;AACrC;", "names": []}