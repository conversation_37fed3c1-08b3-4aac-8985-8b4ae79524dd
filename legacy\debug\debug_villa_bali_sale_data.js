// Debug what data Villa Bali Sale actually receives
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function debugVillaBaliSaleData() {
  console.log('🔍 Debug Villa Bali Sale Raw Data');
  console.log('='.repeat(50));
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/lovina/property-for-sale-in-singaraja---lovina';
  
  try {
    console.log(`📡 Scraping: ${testUrl}`);
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown', 'html'],
        onlyMainContent: true
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data) {
      console.log('📊 Raw Data Structure:');
      console.log(`- Has markdown: ${!!result.data.markdown}`);
      console.log(`- Has html: ${!!result.data.html}`);
      console.log(`- Has json: ${!!result.data.json}`);
      console.log(`- Has metadata: ${!!result.data.metadata}`);
      
      if (result.data.markdown) {
        console.log(`\n📝 Markdown length: ${result.data.markdown.length} chars`);
        console.log('\nMarkdown sample (first 500 chars):');
        console.log(result.data.markdown.substring(0, 500));
        console.log('...');
      }
      
      if (result.data.html) {
        console.log(`\n🌐 HTML length: ${result.data.html.length} chars`);
        console.log('\nHTML sample (first 300 chars):');
        console.log(result.data.html.substring(0, 300));
        console.log('...');
      }
      
      if (result.data.json) {
        console.log('\n📋 JSON Data:');
        console.log(JSON.stringify(result.data.json, null, 2));
      } else {
        console.log('\n❌ No JSON data available');
      }
      
      if (result.data.metadata) {
        console.log('\n🏷️ Metadata:');
        console.log(JSON.stringify(result.data.metadata, null, 2));
      }
      
      // Test the current mapper logic
      console.log('\n🧪 Testing Current Mapper Logic:');
      
      const raw = {
        markdown: result.data.markdown,
        html: result.data.html,
        json: result.data.json,
        url: testUrl
      };
      
      console.log(`jsonData exists: ${!!raw.json}`);
      
      if (!raw.json) {
        console.log('⚠️ No JSON data - mapper will use markdown fallback');
        
        // Test markdown extraction patterns
        const content = raw.markdown || '';
        
        // Test title extraction
        const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>|<h1[^>]*>([^<]+)<\/h1>/i);
        if (titleMatch) {
          console.log(`Title found: "${(titleMatch[1] || titleMatch[2]).replace(/\s+/g, ' ').trim()}"`);
        } else {
          console.log('❌ No title found with current patterns');
        }
        
        // Test bedroom/bathroom patterns from our recent fix
        const bedroomPatterns = [
          /(\w+)\s*bedroom/i,  // "six bedroom"
          /(\d+)\s*bed/i,      // "6 bed"
          /bed.*?(\d+)/i       // "bed 6"
        ];
        
        console.log('\n🛏️ Testing bedroom patterns:');
        bedroomPatterns.forEach((pattern, i) => {
          const match = content.match(pattern);
          if (match) {
            console.log(`Pattern ${i+1} matched: "${match[0]}" -> ${match[1]}`);
          }
        });
        
        const bathroomPatterns = [
          /bathtub\.svg\)(\d+)/i,  // "bathtub.svg)7"
          /(\d+)\s*bath/i,         // "7 bath"
          /bath.*?(\d+)/i          // "bath 7"
        ];
        
        console.log('\n🚿 Testing bathroom patterns:');
        bathroomPatterns.forEach((pattern, i) => {
          const match = content.match(pattern);
          if (match) {
            console.log(`Pattern ${i+1} matched: "${match[0]}" -> ${match[1]}`);
          }
        });
      }
      
    } else {
      console.log('❌ Failed to get data');
      console.log(JSON.stringify(result, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugVillaBaliSaleData().then(() => process.exit(0));
