// Test Villa Bali Sale fixes for description and state
require('dotenv').config();
const { QueueManager } = require('./scrape_worker/queue_manager');
const { db, properties, scrapingQueue } = require('./drizzle_client');
const { desc, sql, eq, and } = require('drizzle-orm');

async function testVillaBaliSaleFixes() {
  console.log('🧪 Testing Villa Bali Sale Fixes (Description & State)');
  console.log('='.repeat(60));
  console.log(`⏰ Started at: ${new Date().toLocaleTimeString()}`);
  
  const queueManager = new QueueManager();
  
  try {
    // Get initial count
    const initialCount = await db.select({ count: sql`count(*)` }).from(properties);
    console.log(`📊 Initial properties in database: ${initialCount[0].count}`);
    
    // Check existing villa_bali_sale properties
    const existingVillaBaliSale = await db
      .select({ count: sql`count(*)` })
      .from(properties)
      .where(eq(properties.source_id, 'villa_bali_sale'));
    console.log(`📊 Existing villa_bali_sale properties: ${existingVillaBaliSale[0].count}`);
    
    // Check available villabalisale.com URLs
    const availableUrls = await db
      .select({ count: sql`count(*)` })
      .from(scrapingQueue)
      .where(and(
        eq(scrapingQueue.website_id, 'villabalisale.com'),
        eq(scrapingQueue.status, 'pending')
      ));
    console.log(`📋 Available villabalisale.com URLs in queue: ${availableUrls[0].count}`);
    
    if (parseInt(availableUrls[0].count) === 0) {
      console.log('⚠️  No pending villabalisale.com URLs available for testing');
      console.log('📊 Showing existing properties to verify fixes...');
    } else {
      console.log(`\n🔄 Processing villabalisale.com URLs to test fixes...`);
      const startTime = Date.now();

      // Get 3 specific URLs from villabalisale.com queue
      const urlsToProcess = await db
        .select({ url: scrapingQueue.url, id: scrapingQueue.id })
        .from(scrapingQueue)
        .where(and(
          eq(scrapingQueue.website_id, 'villabalisale.com'),
          eq(scrapingQueue.status, 'pending')
        ))
        .limit(3);

      if (urlsToProcess.length > 0) {
        // Use runExtractBatch directly to test the fixes
        const { runExtractBatch } = require('./scrape_worker/run_batch');
        const urls = urlsToProcess.map(item => item.url);

        console.log(`📋 Testing ${urls.length} URLs:`);
        urls.forEach((url, i) => {
          console.log(`   ${i + 1}. ${url.substring(0, 80)}...`);
        });

        const results = await runExtractBatch('villabalisale.com', urls, {
          concurrency: 1,
          timeout: 90000
        });

        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;

        var result = {
          processed: results.length,
          successful: successful,
          failed: failed
        };
      } else {
        var result = { processed: 0, successful: 0, failed: 0 };
      }

      const duration = ((Date.now() - startTime) / 1000).toFixed(1);

      if (result && result.processed > 0) {
        console.log(`✅ Successfully processed ${result.processed} URLs in ${duration}s`);
        console.log(`   Successful: ${result.successful || 0}`);
        console.log(`   Failed: ${result.failed || 0}`);
        console.log(`   Skipped: ${result.skipped || 0}`);
      } else {
        console.log(`⚠️  No URLs processed in ${duration}s`);
      }
    }
    
    // Check final count and show new properties
    const finalCount = await db.select({ count: sql`count(*)` }).from(properties);
    const newProperties = parseInt(finalCount[0].count) - parseInt(initialCount[0].count);
    console.log(`\n🆕 New properties added to database: ${newProperties}`);
    
    // Show latest Villa Bali Sale properties to verify fixes
    console.log('\n🏠 Latest Villa Bali Sale Properties (Verifying Fixes):');
    const latestProps = await db
      .select()
      .from(properties)
      .where(eq(properties.source_id, 'villa_bali_sale'))
      .orderBy(desc(properties.created_at))
      .limit(newProperties > 0 ? newProperties : 3);
    
    latestProps.forEach((prop, i) => {
      console.log(`\n   ${i + 1}. [${prop.external_id}] ${prop.title}`);
      if (prop.price) {
        console.log(`      💰 IDR ${prop.price.toLocaleString()}`);
      }
      console.log(`      🏠 ${prop.bedrooms} bed | 🚿 ${prop.bathrooms} bath`);
      console.log(`      📍 ${prop.city}, ${prop.state || 'No state'}`);
      console.log(`      🏛️  ${prop.ownership_type}`);
      
      // Check state fix
      if (prop.state) {
        console.log(`      ✅ State field: ${prop.state} (FIXED)`);
      } else {
        console.log(`      ❌ State field: Still empty (NEEDS FIX)`);
      }
      
      // Check description fix
      if (prop.description && prop.description.length > 0) {
        console.log(`      ✅ Description: ${prop.description.length} chars (FIXED)`);
        console.log(`      📝 Preview: ${prop.description.substring(0, 100)}...`);
      } else {
        console.log(`      ❌ Description: Still empty (NEEDS FIX)`);
      }
      
      // Check amenities
      if (prop.amenities && prop.amenities.raw_amenities && prop.amenities.raw_amenities.length > 0) {
        console.log(`      ✅ Amenities (${prop.amenities.raw_amenities.length}): ${prop.amenities.raw_amenities.slice(0, 3).join(', ')}${prop.amenities.raw_amenities.length > 3 ? '...' : ''}`);
      } else {
        console.log(`      ❌ No amenities found`);
      }
      
      console.log(`      ⏰ ${new Date(prop.created_at).toLocaleString()}`);
    });
    
    // Summary of fixes
    console.log('\n📊 Fix Verification Summary:');
    const propsWithState = latestProps.filter(p => p.state && p.state.length > 0);
    const propsWithDescription = latestProps.filter(p => p.description && p.description.length > 0);
    
    console.log(`   State field: ${propsWithState.length}/${latestProps.length} properties (${((propsWithState.length / latestProps.length) * 100).toFixed(1)}%)`);
    console.log(`   Description field: ${propsWithDescription.length}/${latestProps.length} properties (${((propsWithDescription.length / latestProps.length) * 100).toFixed(1)}%)`);
    
    if (propsWithState.length === latestProps.length && propsWithDescription.length === latestProps.length) {
      console.log('\n🎉 ALL FIXES SUCCESSFUL! Both state and description fields are now populated.');
    } else if (propsWithState.length === latestProps.length) {
      console.log('\n✅ State fix successful! Description still needs work.');
    } else if (propsWithDescription.length === latestProps.length) {
      console.log('\n✅ Description fix successful! State still needs work.');
    } else {
      console.log('\n⚠️  Fixes need more work. Some fields still not populated.');
    }
    
    console.log('\n✅ Villa Bali Sale fixes test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testVillaBaliSaleFixes();
