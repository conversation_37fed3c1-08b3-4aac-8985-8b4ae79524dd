// Test the improved price parsing
require('dotenv').config();

const { runExtractBatch } = require('./scrape_worker/run_batch');

async function testPriceParsing() {
  console.log('🧪 Testing improved price parsing...');
  console.log('='.repeat(50));

  // Test URLs that previously failed with price validation
  const testUrls = [
    'https://balicoconutliving.com/bali-villa-sale-freehold/Umalas/2495-V001-2428/Blackpool-Villa',
    'https://balicoconutliving.com/bali-villa-sale-freehold/Ubud/4315-V018-3988/Villa-Amati',
    'https://balicoconutliving.com/bali-villa-monthly-rental/Jimbaran/3629-V013-3416/Bee-Balm-Villa'
  ];

  for (let i = 0; i < testUrls.length; i++) {
    const url = testUrls[i];
    console.log(`\n${i + 1}. Testing: ${url}`);
    
    try {
      const results = await runExtractBatch('bali_coconut_living', [url], {});
      
      if (results && results.processedResults && results.processedResults.length > 0) {
        const result = results.processedResults[0];
        
        if (result && result.ok && result.data) {
          console.log(`   ✅ SUCCESS: ${result.data.title}`);
          console.log(`   💰 Price: ${result.data.price || 'NULL'} IDR`);
          console.log(`   🏠 Rent: ${result.data.rent_price || 'NULL'} IDR/month`);
          console.log(`   🛏️  Bedrooms: ${result.data.bedrooms || 'NULL'}`);
          console.log(`   🚿 Bathrooms: ${result.data.bathrooms || 'NULL'}`);
          console.log(`   🏠 Ownership: ${result.data.ownership_type || 'NULL'}`);
          console.log(`   💾 Database ID: ${result.id}`);
        } else {
          console.log(`   ❌ FAILED: ${result ? result.error || 'No data extracted' : 'No result'}`);
        }
      } else {
        console.log(`   ❌ FAILED: No results returned`);
      }
      
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
    }
    
    // Wait between requests
    if (i < testUrls.length - 1) {
      console.log('   ⏳ Waiting 5s...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }

  console.log('\n📊 Price parsing test completed!');
}

// Run if called directly
if (require.main === module) {
  testPriceParsing()
    .then(() => {
      console.log('\n🎯 Test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testPriceParsing };
