{"version": 3, "sources": ["../../../src/gel-core/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { GelDialectConfig } from '~/gel-core/dialect.ts';\nimport { GelDialect } from '~/gel-core/dialect.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { GelColumn } from '../columns/index.ts';\nimport type { WithSubqueryWithSelection } from '../subquery.ts';\nimport { GelSelectBuilder } from './select.ts';\nimport type { SelectedFields } from './select.types.ts';\n\nexport class QueryBuilder {\n\tstatic readonly [entityKind]: string = 'GelQueryBuilder';\n\n\tprivate dialect: GelDialect | undefined;\n\tprivate dialectConfig: GelDialectConfig | undefined;\n\n\tconstructor(dialect?: GelDialect | GelDialectConfig) {\n\t\tthis.dialect = is(dialect, GelDialect) ? dialect : undefined;\n\t\tthis.dialectConfig = is(dialect, GelDialect) ? undefined : dialect;\n\t}\n\n\t$with<TAlias extends string>(alias: TAlias) {\n\t\tconst queryBuilder = this;\n\n\t\treturn {\n\t\t\tas<TSelection extends ColumnsSelection>(\n\t\t\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t\t\t): WithSubqueryWithSelection<TSelection, TAlias> {\n\t\t\t\tif (typeof qb === 'function') {\n\t\t\t\t\tqb = qb(queryBuilder);\n\t\t\t\t}\n\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tnew WithSubquery(qb.getSQL(), qb.getSelectedFields() as SelectedFields, alias, true),\n\t\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t\t) as WithSubqueryWithSelection<TSelection, TAlias>;\n\t\t\t},\n\t\t};\n\t}\n\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\tfunction select(): GelSelectBuilder<undefined, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): GelSelectBuilder<TSelection, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): GelSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new GelSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinct(): GelSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): GelSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinct(fields?: SelectedFields): GelSelectBuilder<SelectedFields | undefined, 'qb'> {\n\t\t\treturn new GelSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinctOn(on: (GelColumn | SQLWrapper)[]): GelSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (GelColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): GelSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinctOn(\n\t\t\ton: (GelColumn | SQLWrapper)[],\n\t\t\tfields?: SelectedFields,\n\t\t): GelSelectBuilder<SelectedFields | undefined, 'qb'> {\n\t\t\treturn new GelSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn };\n\t}\n\n\tselect(): GelSelectBuilder<undefined, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): GelSelectBuilder<TSelection, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields?: TSelection): GelSelectBuilder<TSelection | undefined, 'qb'> {\n\t\treturn new GelSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t});\n\t}\n\n\tselectDistinct(): GelSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): GelSelectBuilder<TSelection>;\n\tselectDistinct(fields?: SelectedFields): GelSelectBuilder<SelectedFields | undefined> {\n\t\treturn new GelSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\tselectDistinctOn(on: (GelColumn | SQLWrapper)[]): GelSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (GelColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): GelSelectBuilder<TSelection>;\n\tselectDistinctOn(\n\t\ton: (GelColumn | SQLWrapper)[],\n\t\tfields?: SelectedFields,\n\t): GelSelectBuilder<SelectedFields | undefined> {\n\t\treturn new GelSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t// Lazy load dialect to avoid circular dependency\n\tprivate getDialect() {\n\t\tif (!this.dialect) {\n\t\t\tthis.dialect = new GelDialect(this.dialectConfig);\n\t\t}\n\n\t\treturn this.dialect;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA+B;AAE/B,qBAA2B;AAE3B,6BAAsC;AAEtC,sBAA6B;AAG7B,oBAAiC;AAG1B,MAAM,aAAa;AAAA,EACzB,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EAER,YAAY,SAAyC;AACpD,SAAK,cAAU,kBAAG,SAAS,yBAAU,IAAI,UAAU;AACnD,SAAK,oBAAgB,kBAAG,SAAS,yBAAU,IAAI,SAAY;AAAA,EAC5D;AAAA,EAEA,MAA6B,OAAe;AAC3C,UAAM,eAAe;AAErB,WAAO;AAAA,MACN,GACC,IACgD;AAChD,YAAI,OAAO,OAAO,YAAY;AAC7B,eAAK,GAAG,YAAY;AAAA,QACrB;AAEA,eAAO,IAAI;AAAA,UACV,IAAI,6BAAa,GAAG,OAAO,GAAG,GAAG,kBAAkB,GAAqB,OAAO,IAAI;AAAA,UACnF,IAAI,6CAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,QACvF;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAIb,aAAS,OACR,QACiD;AACjD,aAAO,IAAI,+BAAiB;AAAA,QAC3B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAIA,aAAS,eAAe,QAA6E;AACpG,aAAO,IAAI,+BAAiB;AAAA,QAC3B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAOA,aAAS,iBACR,IACA,QACqD;AACrD,aAAO,IAAI,+BAAiB;AAAA,QAC3B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU,EAAE,GAAG;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,WAAO,EAAE,QAAQ,gBAAgB,iBAAiB;AAAA,EACnD;AAAA,EAIA,OAA0C,QAAqE;AAC9G,WAAO,IAAI,+BAAiB;AAAA,MAC3B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,IAC1B,CAAC;AAAA,EACF;AAAA,EAIA,eAAe,QAAuE;AACrF,WAAO,IAAI,+BAAiB;AAAA,MAC3B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA,EAOA,iBACC,IACA,QAC+C;AAC/C,WAAO,IAAI,+BAAiB;AAAA,MAC3B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU,EAAE,GAAG;AAAA,IAChB,CAAC;AAAA,EACF;AAAA;AAAA,EAGQ,aAAa;AACpB,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,UAAU,IAAI,0BAAW,KAAK,aAAa;AAAA,IACjD;AAEA,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}