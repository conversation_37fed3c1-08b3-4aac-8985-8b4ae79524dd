// Website Testing Tool
// Test and validate website mappers

require('dotenv').config();
const { websiteRegistry } = require('../scrape_worker/website_registry');
const { runExtractBatch } = require('../scrape_worker/run_batch');

class WebsiteTester {
  constructor() {
    this.results = [];
  }

  async testAllWebsites(testUrls = {}) {
    console.log('🧪 Testing All Registered Websites');
    console.log('='.repeat(50));

    const activeWebsites = websiteRegistry.getActiveWebsites();
    console.log(`Found ${activeWebsites.length} active websites\n`);

    for (const websiteId of activeWebsites) {
      await this.testWebsite(websiteId, testUrls[websiteId]);
    }

    this.printSummary();
  }

  async testWebsite(websiteId, testUrl = null) {
    console.log(`🔍 Testing ${websiteId}...`);
    
    const websiteInfo = websiteRegistry.getWebsiteInfo(websiteId);
    if (!websiteInfo) {
      console.log(`❌ Website not found: ${websiteId}\n`);
      return;
    }

    console.log(`   Type: ${websiteInfo.type}`);
    console.log(`   Domain: ${websiteInfo.config.domain}`);

    if (!testUrl) {
      console.log(`⚠️  No test URL provided for ${websiteId}\n`);
      this.results.push({
        websiteId,
        status: 'skipped',
        reason: 'No test URL'
      });
      return;
    }

    try {
      const startTime = Date.now();
      
      // Test scraping and mapping
      const results = await runExtractBatch(websiteId, [testUrl], {});
      
      const duration = Date.now() - startTime;
      
      if (results && results.processedResults && results.processedResults.length > 0) {
        const result = results.processedResults[0];
        
        if (result.ok && result.data) {
          const property = result.data;
          
          // Calculate quality metrics
          const qualityScore = websiteRegistry.calculateQualityScore(property);
          const validation = websiteRegistry.validateMappedProperty(property, websiteInfo.config);
          
          console.log(`✅ Success (${(duration/1000).toFixed(1)}s)`);
          console.log(`   Title: "${property.title}"`);
          console.log(`   Bedrooms: ${property.bedrooms || 'N/A'}`);
          console.log(`   Bathrooms: ${property.bathrooms || 'N/A'}`);
          console.log(`   Building Size: ${property.size_sqft || 'N/A'} sqft`);
          console.log(`   Price: ${this.formatPrice(property.price || property.rent_price)}`);
          console.log(`   Quality Score: ${qualityScore}%`);
          
          if (validation.warnings.length > 0) {
            console.log(`   Warnings: ${validation.warnings.join(', ')}`);
          }
          
          this.results.push({
            websiteId,
            status: 'success',
            duration,
            qualityScore,
            validation,
            property,
            hasPrice: !!(property.price || property.rent_price),
            hasBasicInfo: !!(property.title && property.bedrooms && property.bathrooms)
          });
          
        } else {
          console.log(`❌ Failed: ${result.error || 'No data extracted'}`);
          this.results.push({
            websiteId,
            status: 'failed',
            duration,
            error: result.error || 'No data extracted'
          });
        }
      } else {
        console.log(`❌ Failed: No results returned`);
        this.results.push({
          websiteId,
          status: 'failed',
          duration,
          error: 'No results returned'
        });
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      this.results.push({
        websiteId,
        status: 'error',
        error: error.message
      });
    }
    
    console.log('');
  }

  printSummary() {
    console.log('📊 Test Summary');
    console.log('='.repeat(50));
    
    const successful = this.results.filter(r => r.status === 'success');
    const failed = this.results.filter(r => r.status === 'failed');
    const errors = this.results.filter(r => r.status === 'error');
    const skipped = this.results.filter(r => r.status === 'skipped');
    
    console.log(`✅ Successful: ${successful.length}`);
    console.log(`❌ Failed: ${failed.length}`);
    console.log(`🚫 Errors: ${errors.length}`);
    console.log(`⚠️  Skipped: ${skipped.length}`);
    console.log(`📈 Success Rate: ${((successful.length / (this.results.length - skipped.length)) * 100).toFixed(1)}%`);
    
    if (successful.length > 0) {
      console.log('\n🏆 Successful Tests:');
      successful.forEach(result => {
        console.log(`   ${result.websiteId}: ${result.qualityScore}% quality, ${(result.duration/1000).toFixed(1)}s`);
      });
    }
    
    if (failed.length > 0 || errors.length > 0) {
      console.log('\n❌ Failed Tests:');
      [...failed, ...errors].forEach(result => {
        console.log(`   ${result.websiteId}: ${result.error}`);
      });
    }
    
    // Quality analysis
    if (successful.length > 0) {
      const avgQuality = successful.reduce((sum, r) => sum + r.qualityScore, 0) / successful.length;
      const avgDuration = successful.reduce((sum, r) => sum + r.duration, 0) / successful.length;
      
      console.log('\n📈 Performance Metrics:');
      console.log(`   Average Quality Score: ${avgQuality.toFixed(1)}%`);
      console.log(`   Average Duration: ${(avgDuration/1000).toFixed(1)}s`);
      console.log(`   Properties with Price: ${successful.filter(r => r.hasPrice).length}/${successful.length}`);
      console.log(`   Properties with Basic Info: ${successful.filter(r => r.hasBasicInfo).length}/${successful.length}`);
    }
  }

  formatPrice(price) {
    if (!price) return 'N/A';
    if (price > 1000000) {
      return `${(price / 1000000).toFixed(1)}M IDR`;
    }
    return `${price.toLocaleString()} IDR`;
  }

  // Test specific patterns
  async testExtractionPatterns(websiteId, testContent) {
    console.log(`🔍 Testing extraction patterns for ${websiteId}`);
    
    const websiteInfo = websiteRegistry.getWebsiteInfo(websiteId);
    if (!websiteInfo) {
      console.log(`❌ Website not found: ${websiteId}`);
      return;
    }

    const config = websiteInfo.config;
    
    // Test price patterns
    if (config.extraction.pricePatterns) {
      console.log('\n💰 Price Pattern Tests:');
      config.extraction.pricePatterns.forEach((pattern, i) => {
        const match = testContent.match(pattern);
        console.log(`   Pattern ${i+1}: ${match ? `✅ "${match[0]}"` : '❌ No match'}`);
      });
    }
    
    // Test bedroom patterns
    if (config.extraction.bedroomPatterns) {
      console.log('\n🛏️  Bedroom Pattern Tests:');
      config.extraction.bedroomPatterns.forEach((pattern, i) => {
        const match = testContent.match(pattern);
        console.log(`   Pattern ${i+1}: ${match ? `✅ "${match[0]}" → ${match[1]}` : '❌ No match'}`);
      });
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const tester = new WebsiteTester();
  
  if (command === 'all') {
    // Test all websites with sample URLs
    const testUrls = {
      betterplace: 'https://betterplace.cc/buy/properties/BPVL02163',
      bali_villa_realty: 'https://balivillarealty.com/property/luxury-6-bedrooms-villa-for-sale-freehold-in-seminyak-bali/',
      bali_home_immo: 'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/jimbaran/cozy-and-charming-2-bedroom-house-for-rent-in-jimbaran-rf6745',
      villabalisale: 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/amed/ocean-views-three-bedroom-freehold-villa-in-amed-vl3341'
    };
    
    await tester.testAllWebsites(testUrls);
    
  } else if (command === 'website' && args[1]) {
    const websiteId = args[1];
    const testUrl = args[2];
    
    if (!testUrl) {
      console.log('❌ Please provide a test URL');
      console.log('Usage: node test_website.js website <websiteId> <testUrl>');
      return;
    }
    
    await tester.testWebsite(websiteId, testUrl);
    
  } else if (command === 'patterns' && args[1]) {
    const websiteId = args[1];
    const testContent = args[2] || 'Sample content with $50,000 price and 3 bedrooms, 2 bathrooms';
    
    await tester.testExtractionPatterns(websiteId, testContent);
    
  } else {
    console.log('Website Testing Tool');
    console.log('Usage:');
    console.log('  node test_website.js all                           - Test all websites');
    console.log('  node test_website.js website <id> <url>            - Test specific website');
    console.log('  node test_website.js patterns <id> [content]       - Test extraction patterns');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { WebsiteTester };
