// Check the very latest property to see if our improvements worked
require('dotenv').config();
const { db, properties } = require('./drizzle_client');
const { desc } = require('drizzle-orm');

async function checkLatestProperty() {
  console.log('🔍 Check Latest Property');
  console.log('='.repeat(30));
  
  try {
    // Get the absolute latest property
    const latestProperty = await db
      .select()
      .from(properties)
      .orderBy(desc(properties.created_at))
      .limit(1);
    
    if (latestProperty.length === 0) {
      console.log('❌ No properties found');
      return;
    }
    
    const prop = latestProperty[0];
    console.log(`🏠 Latest Property: ${prop.title}`);
    console.log(`🌐 Source: ${prop.media?.source_id || 'NULL'}`);
    console.log(`📅 Created: ${new Date(prop.created_at).toLocaleString()}`);
    console.log('');
    
    // Check all our target fields
    console.log('🎯 TARGET FIELDS CHECK:');
    console.log(`   🛏️  Bedrooms: ${prop.bedrooms || 'NULL'}`);
    console.log(`   🚿 Bathrooms: ${prop.bathrooms || 'NULL'}`);
    console.log(`   🚗 Parking: ${prop.parking_spaces || 'NULL'}`);
    console.log(`   🏞️ Lot Size: ${prop.lot_size_sqft || 'NULL'} sqft`);
    console.log(`   📋 Lease Text: ${prop.lease_duration_text || 'NULL'}`);
    console.log(`   📋 Lease Years: ${prop.lease_duration_years || 'NULL'}`);
    console.log(`   🔗 Source URL ID: ${prop.source_url_id || 'NULL'}`);
    console.log('');
    
    // Check if this is the BetterPlace property we just scraped
    if (prop.title && prop.title.includes('Stylish 3 Bedroom Villa')) {
      console.log('✅ This is the property we just scraped!');
      console.log('');
      
      // Detailed analysis
      console.log('📊 IMPROVEMENT ANALYSIS:');
      
      // Bedrooms/Bathrooms (should be working)
      if (prop.bedrooms === 3 && prop.bathrooms === 4) {
        console.log('   ✅ Bedrooms/Bathrooms: PERFECT (3 bed / 4 bath)');
      } else {
        console.log(`   ❌ Bedrooms/Bathrooms: FAILED (${prop.bedrooms} bed / ${prop.bathrooms} bath)`);
      }
      
      // Parking (new improvement)
      if (prop.parking_spaces) {
        console.log(`   ✅ Parking: WORKING (${prop.parking_spaces} spaces)`);
      } else {
        console.log('   ❌ Parking: NOT FOUND');
      }
      
      // Lot Size (new improvement)
      if (prop.lot_size_sqft) {
        console.log(`   ✅ Lot Size: WORKING (${prop.lot_size_sqft} sqft)`);
      } else {
        console.log('   ❌ Lot Size: NOT FOUND');
      }
      
      // Lease Duration (should be working for BetterPlace)
      if (prop.lease_duration_text && prop.lease_duration_years) {
        console.log(`   ✅ Lease Duration: WORKING (${prop.lease_duration_text})`);
      } else {
        console.log('   ❌ Lease Duration: NOT FOUND');
      }
      
      // Source URL ID (new fix)
      if (prop.source_url_id) {
        console.log(`   ✅ Source URL ID: WORKING (${prop.source_url_id})`);
      } else {
        console.log('   ❌ Source URL ID: NOT SET');
      }
      
    } else {
      console.log('⚠️  This is not the property we just scraped');
      console.log('   The latest property might be from a previous run');
    }
    
    // Show full property details
    console.log('\n📋 FULL PROPERTY DETAILS:');
    console.log(`   💰 Price: ${prop.price || 'NULL'} | Rent: ${prop.rent_price || 'NULL'}`);
    console.log(`   📐 Size: ${prop.size_sqft || 'NULL'} sqft`);
    console.log(`   📅 Year: ${prop.year_built || 'NULL'}`);
    console.log(`   🏠 Ownership: ${prop.ownership_type || 'NULL'}`);
    console.log(`   📍 Address: ${prop.address || 'NULL'}`);
    console.log(`   🏙️  City: ${prop.city || 'NULL'}`);
    
    if (prop.description) {
      console.log(`   📝 Description: "${prop.description.substring(0, 100)}..."`);
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
  }
}

// Run check
if (require.main === module) {
  checkLatestProperty().catch(console.error);
}

module.exports = { checkLatestProperty };
