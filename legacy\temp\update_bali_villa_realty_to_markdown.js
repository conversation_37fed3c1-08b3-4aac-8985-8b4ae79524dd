// Update Bali Villa Realty configuration to use markdown-only scraping
require('dotenv').config();
const { db, websiteConfigs } = require('./drizzle_client');
const { eq } = require('drizzle-orm');

async function updateBaliVillaRealtyToMarkdown() {
  console.log('🔄 Updating Bali Villa Realty configuration to markdown-only...');
  
  try {
    // Update Bali Villa Realty crawl_options to use markdown instead of JSON
    const result = await db
      .update(websiteConfigs)
      .set({
        crawl_options: {
          formats: ['markdown'],
          onlyMainContent: true,
          timeout: 60000
        },
        updated_at: new Date()
      })
      .where(eq(websiteConfigs.website_id, 'bali_villa_realty'))
      .returning();

    if (result.length > 0) {
      console.log('✅ Bali Villa Realty configuration updated successfully');
      console.log('📊 New crawl_options:', JSON.stringify(result[0].crawl_options, null, 2));
    } else {
      console.log('❌ Bali Villa Realty configuration not found');
    }

    // Verify the update
    const config = await db
      .select()
      .from(websiteConfigs)
      .where(eq(websiteConfigs.website_id, 'bali_villa_realty'))
      .limit(1);

    if (config.length > 0) {
      console.log('\n📋 Current Bali Villa Realty configuration:');
      console.log(`   Website ID: ${config[0].website_id}`);
      console.log(`   Name: ${config[0].name}`);
      console.log(`   Base URL: ${config[0].base_url}`);
      console.log(`   Crawl Options: ${JSON.stringify(config[0].crawl_options, null, 2)}`);
      console.log(`   Is Active: ${config[0].is_active}`);
      console.log(`   Last Updated: ${config[0].updated_at}`);
    }

  } catch (error) {
    console.error('❌ Error updating Bali Villa Realty configuration:', error);
  }
}

// Run the update
updateBaliVillaRealtyToMarkdown().catch(console.error);
