// Test new batch scraping with smart currency conversion
require('dotenv').config();
const { runExtractBatch } = require('./scrape_worker/run_batch');
const { getCurrencyService } = require('./scrape_worker/currency_service');

async function testNewBatchScraping() {
  console.log('🚀 Testing new batch scraping with smart currency conversion...\n');
  
  const currencyService = getCurrencyService();
  
  // Show current exchange rates
  console.log('💱 Current Exchange Rates:');
  const usdRate = await currencyService.getExchangeRate('USD', 'IDR');
  const envRate = Number(process.env.USD_TO_IDR || 15800);
  console.log(`   Database USD→IDR: ${usdRate.toLocaleString()}`);
  console.log(`   ENV USD→IDR: ${envRate.toLocaleString()}`);
  console.log(`   Accuracy improvement: ${((usdRate/envRate - 1) * 100).toFixed(2)}%\n`);
  
  // Test URLs for each site (3 properties each)
  const testSites = {
    betterplace: [
      'https://betterplace.cc/buy/properties/BPVL02232',
      'https://betterplace.cc/buy/properties/BPVL02231', 
      'https://betterplace.cc/buy/properties/BPVL02230'
    ],
    bali_home_immo: [
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/canggu/2-bedroom-townhouse-for-rent-in-berawa-rf1508',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/seminyak/3-bedroom-villa-for-rent-in-seminyak-rf1507',
      'https://bali-home-immo.com/realestate-property/for-rent/villa/monthly/ubud/2-bedroom-villa-for-rent-in-ubud-rf1506'
    ],
    bali_villa_realty: [
      'https://balivillarealty.com/property/brand-new-charming-2-bedrooms-villa-for-sale-leasehold-in-tumbak-bayuh-bali/',
      'https://balivillarealty.com/property/cozy-villa-3-bedrooms-for-rental-in-umalas-bali/',
      'https://balivillarealty.com/property/charming-2-bedrooms-villa-for-rental-in-babakan-canggu-bali/'
    ]
  };
  
  const allResults = {};
  let totalProperties = 0;
  let totalSuccessful = 0;
  let totalConversions = 0;
  let totalConversionValue = 0;
  
  // Process each site
  for (const [siteId, urls] of Object.entries(testSites)) {
    console.log(`🔄 Processing ${siteId.toUpperCase()} (${urls.length} URLs)...`);
    console.log('URLs:');
    urls.forEach((url, index) => {
      const shortUrl = url.split('/').pop() || url.split('/').slice(-2).join('/');
      console.log(`   ${index + 1}. ${shortUrl}`);
    });
    
    try {
      const startTime = Date.now();
      const results = await runExtractBatch(siteId, urls, {});
      const endTime = Date.now();
      const duration = ((endTime - startTime) / 1000).toFixed(2);
      
      const successful = results.filter(r => r.ok);
      const failed = results.filter(r => !r.ok);
      
      console.log(`   ⏱️  Processing time: ${duration}s (${(duration / urls.length).toFixed(1)}s per URL)`);
      console.log(`   ✅ Successful: ${successful.length}/${urls.length}`);
      console.log(`   ❌ Failed: ${failed.length}/${urls.length}`);
      
      // Analyze successful properties
      if (successful.length > 0) {
        console.log('\n   📋 Successfully scraped properties:');
        successful.forEach((property, index) => {
          console.log(`      ${index + 1}. ${property.title.substring(0, 60)}...`);
          
          // Show price information
          if (property.price) {
            console.log(`         💰 Sale Price: ${property.price.toLocaleString()} IDR`);
            totalConversions++;
            totalConversionValue += parseFloat(property.price);
          }
          if (property.rent_price) {
            console.log(`         🏠 Rent Price: ${property.rent_price.toLocaleString()} IDR/month`);
            totalConversions++;
            totalConversionValue += parseFloat(property.rent_price);
          }
          
          // Show other details
          if (property.bedrooms) console.log(`         🛏️  ${property.bedrooms} bedrooms`);
          if (property.bathrooms) console.log(`         🚿 ${property.bathrooms} bathrooms`);
          if (property.parking_spaces) console.log(`         🚗 ${property.parking_spaces} parking spaces`);
        });
      }
      
      if (failed.length > 0) {
        console.log('\n   ❌ Failed properties:');
        failed.forEach((result, index) => {
          console.log(`      ${index + 1}. Error: ${result.error}`);
        });
      }
      
      allResults[siteId] = {
        total: urls.length,
        successful: successful.length,
        failed: failed.length,
        duration: duration,
        properties: successful
      };
      
      totalProperties += urls.length;
      totalSuccessful += successful.length;
      
    } catch (error) {
      console.error(`   ❌ ${siteId} processing failed: ${error.message}`);
      allResults[siteId] = {
        total: urls.length,
        successful: 0,
        failed: urls.length,
        error: error.message
      };
      totalProperties += urls.length;
    }
    
    console.log(''); // Empty line between sites
  }
  
  // Overall summary
  console.log('📊 BATCH SCRAPING SUMMARY:');
  console.log('='.repeat(60));
  console.log(`Total Properties Processed: ${totalProperties}`);
  console.log(`Successful: ${totalSuccessful} (${((totalSuccessful/totalProperties) * 100).toFixed(1)}%)`);
  console.log(`Failed: ${totalProperties - totalSuccessful} (${(((totalProperties - totalSuccessful)/totalProperties) * 100).toFixed(1)}%)`);
  
  // Site-by-site breakdown
  console.log('\n🏢 Site Performance:');
  Object.entries(allResults).forEach(([siteId, result]) => {
    const successRate = ((result.successful / result.total) * 100).toFixed(1);
    const avgTime = result.duration ? (result.duration / result.total).toFixed(1) : 'N/A';
    console.log(`   ${siteId.padEnd(20)}: ${result.successful}/${result.total} (${successRate}%) - ${avgTime}s/URL`);
  });
  
  // Currency conversion summary
  console.log('\n💱 Currency Conversion Summary:');
  console.log(`   Total conversions: ${totalConversions}`);
  console.log(`   Total value converted: ${totalConversionValue.toLocaleString()} IDR`);
  console.log(`   Average property value: ${totalConversions > 0 ? (totalConversionValue / totalConversions).toLocaleString() : 'N/A'} IDR`);
  
  // Show currency service stats
  const stats = currencyService.getCacheStats();
  console.log('\n🔧 Currency Service Performance:');
  console.log(`   Cache entries: ${stats.cacheSize}`);
  console.log(`   Cached rates:`);
  stats.cachedRates.forEach(rate => {
    console.log(`     ${rate.pair}: ${rate.rate} (${rate.source})`);
  });
  
  // Calculate savings from accurate rates
  if (totalConversions > 0) {
    const oldMethodValue = totalConversionValue * (envRate / usdRate);
    const savings = totalConversionValue - oldMethodValue;
    console.log('\n💰 Accuracy Benefits:');
    console.log(`   Old method total: ${oldMethodValue.toLocaleString()} IDR`);
    console.log(`   New method total: ${totalConversionValue.toLocaleString()} IDR`);
    console.log(`   Accuracy improvement: ${savings.toLocaleString()} IDR (${((savings/oldMethodValue) * 100).toFixed(2)}%)`);
  }
  
  console.log('\n🎯 New batch scraping with smart currency conversion completed!');
  console.log('\n✨ Key Improvements:');
  console.log('   ✅ Staggered parallel processing (6 URLs per minute)');
  console.log('   ✅ Multi-key rotation for reliability');
  console.log('   ✅ Smart currency conversion with database rates');
  console.log('   ✅ Automatic fallback to ENV rates');
  console.log('   ✅ Performance caching for batch processing');
  console.log('   ✅ Markdown fallback for complex sites');
  
  process.exit(0);
}

if (require.main === module) {
  testNewBatchScraping().catch(console.error);
}

module.exports = { testNewBatchScraping };
