// Debug Villa Bali Sale missing fields: photos, amenities, year built
require('dotenv').config();
const { getKeyManager } = require('./scrape_worker/key_manager');

async function debugVillaBaliSaleMissingFields() {
  console.log('🔍 Debug Villa Bali Sale Missing Fields');
  console.log('='.repeat(50));
  
  const keyManager = getKeyManager();
  const currentKey = keyManager.getCurrentKey();
  
  const testUrl = 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/amed/ocean-views-three-bedroom-freehold-villa-in-amed-vl3341';
  
  try {
    console.log(`📡 Scraping: ${testUrl}`);
    
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentKey.key}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: testUrl,
        formats: ['markdown'],
        onlyMainContent: true
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.markdown) {
      const content = result.data.markdown;
      console.log(`📝 Markdown length: ${content.length} chars`);
      
      // Search for year built patterns
      console.log('\n📅 Searching for Year Built patterns:');
      
      const yearPatterns = [
        /year.*?built.*?(\d{4})/i,
        /built.*?year.*?(\d{4})/i,
        /built.*?(\d{4})/i,
        /(\d{4}).*?built/i,
        /construction.*?(\d{4})/i,
        /(\d{4}).*?construction/i,
        /completed.*?(\d{4})/i,
        /(\d{4}).*?completed/i,
        /delivered.*?(\d{4})/i,
        /(\d{4}).*?delivered/i,
        /september.*?(\d{4})/i,
        /(\d{4}).*?september/i
      ];
      
      yearPatterns.forEach((pattern, i) => {
        const matches = [...content.matchAll(new RegExp(pattern.source, 'gi'))];
        if (matches.length > 0) {
          console.log(`\n✅ Year Pattern ${i+1} (${pattern.source}) found ${matches.length} matches:`);
          matches.slice(0, 3).forEach(match => {
            const start = Math.max(0, match.index - 30);
            const end = Math.min(content.length, match.index + match[0].length + 30);
            const context = content.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
            console.log(`   → Year: ${match[1]}`);
          });
        }
      });
      
      // Search for amenities patterns
      console.log('\n🏖️ Searching for Amenities patterns:');
      
      const amenityPatterns = [
        /amenities/i,
        /features/i,
        /facilities/i,
        /includes/i,
        /pool/i,
        /swimming/i,
        /kitchen/i,
        /garden/i,
        /parking/i,
        /wifi/i,
        /air.*?conditioning/i,
        /furnished/i,
        /balcony/i,
        /terrace/i,
        /view/i
      ];
      
      amenityPatterns.forEach((pattern, i) => {
        const matches = [...content.matchAll(new RegExp(pattern.source, 'gi'))];
        if (matches.length > 0) {
          console.log(`\n✅ Amenity Pattern ${i+1} (${pattern.source}) found ${matches.length} matches:`);
          matches.slice(0, 2).forEach(match => {
            const start = Math.max(0, match.index - 40);
            const end = Math.min(content.length, match.index + match[0].length + 40);
            const context = content.substring(start, end);
            console.log(`   "${context.replace(/\n/g, ' ')}"`);
          });
        }
      });
      
      // Search for image patterns
      console.log('\n📸 Searching for Image patterns:');
      
      const imagePatterns = [
        /!\[.*?\]\((https?:\/\/[^)]+)\)/g,
        /<img[^>]+src=["']([^"']+)["']/gi,
        /https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)/gi
      ];
      
      imagePatterns.forEach((pattern, i) => {
        const matches = [...content.matchAll(pattern)];
        if (matches.length > 0) {
          console.log(`\n✅ Image Pattern ${i+1} found ${matches.length} matches:`);
          matches.slice(0, 5).forEach(match => {
            const imageUrl = match[1] || match[0];
            console.log(`   ${imageUrl.substring(0, 80)}...`);
          });
        }
      });
      
      // Look for specific sections
      console.log('\n📋 Looking for specific sections:');
      
      const lines = content.split('\n');
      lines.forEach((line, i) => {
        if (/delivered.*september.*2026/i.test(line)) {
          console.log(`\nLine ${i+1} (Year Built): "${line.trim()}"`);
        }
        if (/pool|kitchen|garden|parking|wifi|furnished/i.test(line) && line.trim().length < 100) {
          console.log(`\nLine ${i+1} (Amenity): "${line.trim()}"`);
        }
        if (/watermark|uploads|assets.*img/i.test(line)) {
          console.log(`\nLine ${i+1} (Image): "${line.trim().substring(0, 100)}..."`);
        }
      });
      
      // Show a sample of the markdown around key areas
      console.log('\n📄 Key Markdown Sections:');
      
      // Find year section
      const yearMatch = content.match(/(delivered.*september.*2026)/i);
      if (yearMatch) {
        const start = Math.max(0, yearMatch.index - 100);
        const end = Math.min(content.length, yearMatch.index + 200);
        console.log('\n📅 Year Section:');
        console.log(content.substring(start, end));
      }
      
      // Find amenities section
      const amenityMatch = content.match(/(amenities|features|facilities)/i);
      if (amenityMatch) {
        const start = Math.max(0, amenityMatch.index - 50);
        const end = Math.min(content.length, amenityMatch.index + 300);
        console.log('\n🏖️ Amenities Section:');
        console.log(content.substring(start, end));
      }
      
    } else {
      console.log('❌ Failed to get markdown');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugVillaBaliSaleMissingFields().then(() => process.exit(0));
