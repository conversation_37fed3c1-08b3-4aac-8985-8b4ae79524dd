// Test Villa Bali Sale mapper fixes directly
require('dotenv').config();
const { mapVillaBaliSale } = require('./scrape_worker/mappers');

async function testVillaBaliSaleMapperFixes() {
  console.log('🧪 Testing Villa Bali Sale Mapper Fixes');
  console.log('='.repeat(50));
  
  // Test data simulating what Firecrawl returns
  const testData1 = {
    url: 'https://www.villabalisale.com/realestate-property/for-sale/villa/freehold/amed/test-villa',
    json: {
      title: 'Beautiful 3 Bedroom Villa in Amed',
      price: 'USD 450,000',
      location: 'Amed',
      bedrooms: 3,
      bathrooms: 2,
      amenities: ['Pool', 'WiFi', 'Kitchen'],
      images: ['img1.jpg', 'img2.jpg'],
      propertyID: 'VL1234',
      ownershipType: 'Freehold',
      size: { building_size_sqm: 150 },
      landSize: { land_size_sqm: 300 }
    },
    markdown: `# Beautiful 3 Bedroom Villa in Amed

This stunning villa offers breathtaking ocean views and modern amenities. Located in the peaceful area of Amed, this property features a private pool, fully equipped kitchen, and spacious living areas. Perfect for those seeking tranquility and luxury.

## Features
- 3 bedrooms
- 2 bathrooms  
- Private pool
- Ocean views
- Modern kitchen

The villa is situated on a 300 sqm plot with 150 sqm of building space.`
  };
  
  const testData2 = {
    url: 'https://www.villabalisale.com/realestate-property/for-sale/villa/leasehold/ubud/test-villa-2',
    json: {
      title: 'Tropical Villa in Ubud',
      price: 'IDR 2,500,000,000',
      location: 'Ubud, Bali',
      bedrooms: 2,
      bathrooms: 2,
      amenities: ['Garden', 'Parking'],
      images: ['img3.jpg'],
      propertyID: 'VL5678',
      ownershipType: 'Leasehold'
    },
    markdown: `# Tropical Villa in Ubud

Experience the magic of Ubud in this charming tropical villa. Surrounded by lush greenery and rice fields, this property offers a peaceful retreat from the bustling city life.

The villa features traditional Balinese architecture with modern comforts. Enjoy your morning coffee while listening to the sounds of nature.

Perfect for investment or personal use.`
  };
  
  try {
    console.log('\n🏠 Test 1: Villa with comprehensive data');
    const result1 = await mapVillaBaliSale(testData1);
    
    console.log('✅ Mapping successful');
    console.log('📊 Results:');
    console.log(`   Title: ${result1.title}`);
    console.log(`   Price: IDR ${result1.price?.toLocaleString()}`);
    console.log(`   City: ${result1.city}`);
    console.log(`   State: ${result1.state || 'NO STATE'}`);
    console.log(`   Bedrooms: ${result1.bedrooms}`);
    console.log(`   Ownership: ${result1.ownership_type}`);
    console.log(`   Description length: ${result1.description?.length || 0}`);
    console.log(`   Description: ${result1.description?.substring(0, 100)}...`);
    console.log(`   Amenities: ${result1.amenities?.raw_amenities?.length || 0} items`);
    
    // Check fixes
    console.log('\n🔍 Fix Verification:');
    if (result1.state && result1.state.length > 0) {
      console.log(`   ✅ State fix: ${result1.state}`);
    } else {
      console.log(`   ❌ State fix: Still empty`);
    }
    
    if (result1.description && result1.description.length > 0) {
      console.log(`   ✅ Description fix: ${result1.description.length} chars`);
    } else {
      console.log(`   ❌ Description fix: Still empty`);
    }
    
    console.log('\n🏠 Test 2: Villa with minimal JSON data (testing markdown fallback)');
    const result2 = await mapVillaBaliSale(testData2);
    
    console.log('✅ Mapping successful');
    console.log('📊 Results:');
    console.log(`   Title: ${result2.title}`);
    console.log(`   Price: IDR ${result2.price?.toLocaleString()}`);
    console.log(`   City: ${result2.city}`);
    console.log(`   State: ${result2.state || 'NO STATE'}`);
    console.log(`   Bedrooms: ${result2.bedrooms}`);
    console.log(`   Ownership: ${result2.ownership_type}`);
    console.log(`   Description length: ${result2.description?.length || 0}`);
    console.log(`   Description: ${result2.description?.substring(0, 100)}...`);
    console.log(`   Amenities: ${result2.amenities?.raw_amenities?.length || 0} items`);
    
    // Check fixes
    console.log('\n🔍 Fix Verification:');
    if (result2.state && result2.state.length > 0) {
      console.log(`   ✅ State fix: ${result2.state}`);
    } else {
      console.log(`   ❌ State fix: Still empty`);
    }
    
    if (result2.description && result2.description.length > 0) {
      console.log(`   ✅ Description fix: ${result2.description.length} chars`);
    } else {
      console.log(`   ❌ Description fix: Still empty`);
    }
    
    console.log('\n📊 Summary:');
    const stateFixed = (result1.state && result2.state);
    const descriptionFixed = (result1.description?.length > 0 && result2.description?.length > 0);
    
    if (stateFixed && descriptionFixed) {
      console.log('🎉 ALL FIXES SUCCESSFUL! Both state and description are working.');
    } else if (stateFixed) {
      console.log('✅ State fix successful! Description needs more work.');
    } else if (descriptionFixed) {
      console.log('✅ Description fix successful! State needs more work.');
    } else {
      console.log('⚠️  Both fixes need more work.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

testVillaBaliSaleMapperFixes();
